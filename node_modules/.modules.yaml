hoistPattern:
  - '*'
hoistedDependencies:
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': private
  '@babel/code-frame@7.26.2':
    '@babel/code-frame': private
  '@babel/compat-data@7.26.8':
    '@babel/compat-data': private
  '@babel/core@7.26.10':
    '@babel/core': private
  '@babel/generator@7.27.0':
    '@babel/generator': private
  '@babel/helper-annotate-as-pure@7.25.9':
    '@babel/helper-annotate-as-pure': private
  '@babel/helper-compilation-targets@7.27.0':
    '@babel/helper-compilation-targets': private
  '@babel/helper-create-class-features-plugin@7.27.0(@babel/core@7.26.10)':
    '@babel/helper-create-class-features-plugin': private
  '@babel/helper-create-regexp-features-plugin@7.27.0(@babel/core@7.26.10)':
    '@babel/helper-create-regexp-features-plugin': private
  '@babel/helper-define-polyfill-provider@0.6.4(@babel/core@7.26.10)':
    '@babel/helper-define-polyfill-provider': private
  '@babel/helper-member-expression-to-functions@7.25.9':
    '@babel/helper-member-expression-to-functions': private
  '@babel/helper-module-imports@7.25.9':
    '@babel/helper-module-imports': private
  '@babel/helper-module-transforms@7.26.0(@babel/core@7.26.10)':
    '@babel/helper-module-transforms': private
  '@babel/helper-optimise-call-expression@7.25.9':
    '@babel/helper-optimise-call-expression': private
  '@babel/helper-plugin-utils@7.26.5':
    '@babel/helper-plugin-utils': private
  '@babel/helper-remap-async-to-generator@7.25.9(@babel/core@7.26.10)':
    '@babel/helper-remap-async-to-generator': private
  '@babel/helper-replace-supers@7.26.5(@babel/core@7.26.10)':
    '@babel/helper-replace-supers': private
  '@babel/helper-skip-transparent-expression-wrappers@7.25.9':
    '@babel/helper-skip-transparent-expression-wrappers': private
  '@babel/helper-string-parser@7.25.9':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.25.9':
    '@babel/helper-validator-identifier': private
  '@babel/helper-validator-option@7.25.9':
    '@babel/helper-validator-option': private
  '@babel/helper-wrap-function@7.25.9':
    '@babel/helper-wrap-function': private
  '@babel/helpers@7.27.0':
    '@babel/helpers': private
  '@babel/parser@7.27.0':
    '@babel/parser': private
  '@babel/plugin-bugfix-firefox-class-in-computed-class-key@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-bugfix-firefox-class-in-computed-class-key': private
  '@babel/plugin-bugfix-safari-class-field-initializer-scope@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-bugfix-safari-class-field-initializer-scope': private
  '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression': private
  '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining': private
  '@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly': private
  '@babel/plugin-proposal-private-property-in-object@7.21.0-placeholder-for-preset-env.2(@babel/core@7.26.10)':
    '@babel/plugin-proposal-private-property-in-object': private
  '@babel/plugin-syntax-import-assertions@7.26.0(@babel/core@7.26.10)':
    '@babel/plugin-syntax-import-assertions': private
  '@babel/plugin-syntax-import-attributes@7.26.0(@babel/core@7.26.10)':
    '@babel/plugin-syntax-import-attributes': private
  '@babel/plugin-syntax-unicode-sets-regex@7.18.6(@babel/core@7.26.10)':
    '@babel/plugin-syntax-unicode-sets-regex': private
  '@babel/plugin-transform-arrow-functions@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-arrow-functions': private
  '@babel/plugin-transform-async-generator-functions@7.26.8(@babel/core@7.26.10)':
    '@babel/plugin-transform-async-generator-functions': private
  '@babel/plugin-transform-async-to-generator@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-async-to-generator': private
  '@babel/plugin-transform-block-scoped-functions@7.26.5(@babel/core@7.26.10)':
    '@babel/plugin-transform-block-scoped-functions': private
  '@babel/plugin-transform-block-scoping@7.27.0(@babel/core@7.26.10)':
    '@babel/plugin-transform-block-scoping': private
  '@babel/plugin-transform-class-properties@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-class-properties': private
  '@babel/plugin-transform-class-static-block@7.26.0(@babel/core@7.26.10)':
    '@babel/plugin-transform-class-static-block': private
  '@babel/plugin-transform-classes@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-classes': private
  '@babel/plugin-transform-computed-properties@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-computed-properties': private
  '@babel/plugin-transform-destructuring@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-destructuring': private
  '@babel/plugin-transform-dotall-regex@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-dotall-regex': private
  '@babel/plugin-transform-duplicate-keys@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-duplicate-keys': private
  '@babel/plugin-transform-duplicate-named-capturing-groups-regex@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-duplicate-named-capturing-groups-regex': private
  '@babel/plugin-transform-dynamic-import@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-dynamic-import': private
  '@babel/plugin-transform-exponentiation-operator@7.26.3(@babel/core@7.26.10)':
    '@babel/plugin-transform-exponentiation-operator': private
  '@babel/plugin-transform-export-namespace-from@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-export-namespace-from': private
  '@babel/plugin-transform-for-of@7.26.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-for-of': private
  '@babel/plugin-transform-function-name@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-function-name': private
  '@babel/plugin-transform-json-strings@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-json-strings': private
  '@babel/plugin-transform-literals@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-literals': private
  '@babel/plugin-transform-logical-assignment-operators@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-logical-assignment-operators': private
  '@babel/plugin-transform-member-expression-literals@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-member-expression-literals': private
  '@babel/plugin-transform-modules-amd@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-modules-amd': private
  '@babel/plugin-transform-modules-commonjs@7.26.3(@babel/core@7.26.10)':
    '@babel/plugin-transform-modules-commonjs': private
  '@babel/plugin-transform-modules-systemjs@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-modules-systemjs': private
  '@babel/plugin-transform-modules-umd@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-modules-umd': private
  '@babel/plugin-transform-named-capturing-groups-regex@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-named-capturing-groups-regex': private
  '@babel/plugin-transform-new-target@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-new-target': private
  '@babel/plugin-transform-nullish-coalescing-operator@7.26.6(@babel/core@7.26.10)':
    '@babel/plugin-transform-nullish-coalescing-operator': private
  '@babel/plugin-transform-numeric-separator@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-numeric-separator': private
  '@babel/plugin-transform-object-rest-spread@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-object-rest-spread': private
  '@babel/plugin-transform-object-super@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-object-super': private
  '@babel/plugin-transform-optional-catch-binding@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-optional-catch-binding': private
  '@babel/plugin-transform-optional-chaining@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-optional-chaining': private
  '@babel/plugin-transform-parameters@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-parameters': private
  '@babel/plugin-transform-private-methods@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-private-methods': private
  '@babel/plugin-transform-private-property-in-object@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-private-property-in-object': private
  '@babel/plugin-transform-property-literals@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-property-literals': private
  '@babel/plugin-transform-regenerator@7.27.0(@babel/core@7.26.10)':
    '@babel/plugin-transform-regenerator': private
  '@babel/plugin-transform-regexp-modifiers@7.26.0(@babel/core@7.26.10)':
    '@babel/plugin-transform-regexp-modifiers': private
  '@babel/plugin-transform-reserved-words@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-reserved-words': private
  '@babel/plugin-transform-shorthand-properties@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-shorthand-properties': private
  '@babel/plugin-transform-spread@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-spread': private
  '@babel/plugin-transform-sticky-regex@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-sticky-regex': private
  '@babel/plugin-transform-template-literals@7.26.8(@babel/core@7.26.10)':
    '@babel/plugin-transform-template-literals': private
  '@babel/plugin-transform-typeof-symbol@7.27.0(@babel/core@7.26.10)':
    '@babel/plugin-transform-typeof-symbol': private
  '@babel/plugin-transform-unicode-escapes@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-unicode-escapes': private
  '@babel/plugin-transform-unicode-property-regex@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-unicode-property-regex': private
  '@babel/plugin-transform-unicode-regex@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-unicode-regex': private
  '@babel/plugin-transform-unicode-sets-regex@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-transform-unicode-sets-regex': private
  '@babel/preset-env@7.26.9(@babel/core@7.26.10)':
    '@babel/preset-env': private
  '@babel/preset-modules@0.1.6-no-external-plugins(@babel/core@7.26.10)':
    '@babel/preset-modules': private
  '@babel/runtime@7.27.0':
    '@babel/runtime': private
  '@babel/template@7.27.0':
    '@babel/template': private
  '@babel/traverse@7.27.0':
    '@babel/traverse': private
  '@babel/types@7.27.0':
    '@babel/types': private
  '@esbuild/aix-ppc64@0.25.2':
    '@esbuild/aix-ppc64': private
  '@esbuild/android-arm64@0.25.2':
    '@esbuild/android-arm64': private
  '@esbuild/android-arm@0.25.2':
    '@esbuild/android-arm': private
  '@esbuild/android-x64@0.25.2':
    '@esbuild/android-x64': private
  '@esbuild/darwin-arm64@0.25.2':
    '@esbuild/darwin-arm64': private
  '@esbuild/darwin-x64@0.25.2':
    '@esbuild/darwin-x64': private
  '@esbuild/freebsd-arm64@0.25.2':
    '@esbuild/freebsd-arm64': private
  '@esbuild/freebsd-x64@0.25.2':
    '@esbuild/freebsd-x64': private
  '@esbuild/linux-arm64@0.25.2':
    '@esbuild/linux-arm64': private
  '@esbuild/linux-arm@0.25.2':
    '@esbuild/linux-arm': private
  '@esbuild/linux-ia32@0.25.2':
    '@esbuild/linux-ia32': private
  '@esbuild/linux-loong64@0.25.2':
    '@esbuild/linux-loong64': private
  '@esbuild/linux-mips64el@0.25.2':
    '@esbuild/linux-mips64el': private
  '@esbuild/linux-ppc64@0.25.2':
    '@esbuild/linux-ppc64': private
  '@esbuild/linux-riscv64@0.25.2':
    '@esbuild/linux-riscv64': private
  '@esbuild/linux-s390x@0.25.2':
    '@esbuild/linux-s390x': private
  '@esbuild/linux-x64@0.25.2':
    '@esbuild/linux-x64': private
  '@esbuild/netbsd-arm64@0.25.2':
    '@esbuild/netbsd-arm64': private
  '@esbuild/netbsd-x64@0.25.2':
    '@esbuild/netbsd-x64': private
  '@esbuild/openbsd-arm64@0.25.2':
    '@esbuild/openbsd-arm64': private
  '@esbuild/openbsd-x64@0.25.2':
    '@esbuild/openbsd-x64': private
  '@esbuild/sunos-x64@0.25.2':
    '@esbuild/sunos-x64': private
  '@esbuild/win32-arm64@0.25.2':
    '@esbuild/win32-arm64': private
  '@esbuild/win32-ia32@0.25.2':
    '@esbuild/win32-ia32': private
  '@esbuild/win32-x64@0.25.2':
    '@esbuild/win32-x64': private
  '@intlify/core-base@11.1.3':
    '@intlify/core-base': private
  '@intlify/message-compiler@11.1.3':
    '@intlify/message-compiler': private
  '@intlify/shared@11.1.3':
    '@intlify/shared': private
  '@jridgewell/gen-mapping@0.3.8':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/set-array@1.2.1':
    '@jridgewell/set-array': private
  '@jridgewell/source-map@0.3.6':
    '@jridgewell/source-map': private
  '@jridgewell/sourcemap-codec@1.5.0':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.25':
    '@jridgewell/trace-mapping': private
  '@parcel/watcher-android-arm64@2.5.1':
    '@parcel/watcher-android-arm64': private
  '@parcel/watcher-darwin-arm64@2.5.1':
    '@parcel/watcher-darwin-arm64': private
  '@parcel/watcher-darwin-x64@2.5.1':
    '@parcel/watcher-darwin-x64': private
  '@parcel/watcher-freebsd-x64@2.5.1':
    '@parcel/watcher-freebsd-x64': private
  '@parcel/watcher-linux-arm-glibc@2.5.1':
    '@parcel/watcher-linux-arm-glibc': private
  '@parcel/watcher-linux-arm-musl@2.5.1':
    '@parcel/watcher-linux-arm-musl': private
  '@parcel/watcher-linux-arm64-glibc@2.5.1':
    '@parcel/watcher-linux-arm64-glibc': private
  '@parcel/watcher-linux-arm64-musl@2.5.1':
    '@parcel/watcher-linux-arm64-musl': private
  '@parcel/watcher-linux-x64-glibc@2.5.1':
    '@parcel/watcher-linux-x64-glibc': private
  '@parcel/watcher-linux-x64-musl@2.5.1':
    '@parcel/watcher-linux-x64-musl': private
  '@parcel/watcher-win32-arm64@2.5.1':
    '@parcel/watcher-win32-arm64': private
  '@parcel/watcher-win32-ia32@2.5.1':
    '@parcel/watcher-win32-ia32': private
  '@parcel/watcher-win32-x64@2.5.1':
    '@parcel/watcher-win32-x64': private
  '@parcel/watcher@2.5.1':
    '@parcel/watcher': private
  '@popperjs/core@2.11.8':
    '@popperjs/core': private
  '@rollup/rollup-android-arm-eabi@4.38.0':
    '@rollup/rollup-android-arm-eabi': private
  '@rollup/rollup-android-arm64@4.38.0':
    '@rollup/rollup-android-arm64': private
  '@rollup/rollup-darwin-arm64@4.38.0':
    '@rollup/rollup-darwin-arm64': private
  '@rollup/rollup-darwin-x64@4.38.0':
    '@rollup/rollup-darwin-x64': private
  '@rollup/rollup-freebsd-arm64@4.38.0':
    '@rollup/rollup-freebsd-arm64': private
  '@rollup/rollup-freebsd-x64@4.38.0':
    '@rollup/rollup-freebsd-x64': private
  '@rollup/rollup-linux-arm-gnueabihf@4.38.0':
    '@rollup/rollup-linux-arm-gnueabihf': private
  '@rollup/rollup-linux-arm-musleabihf@4.38.0':
    '@rollup/rollup-linux-arm-musleabihf': private
  '@rollup/rollup-linux-arm64-gnu@4.38.0':
    '@rollup/rollup-linux-arm64-gnu': private
  '@rollup/rollup-linux-arm64-musl@4.38.0':
    '@rollup/rollup-linux-arm64-musl': private
  '@rollup/rollup-linux-loongarch64-gnu@4.38.0':
    '@rollup/rollup-linux-loongarch64-gnu': private
  '@rollup/rollup-linux-powerpc64le-gnu@4.38.0':
    '@rollup/rollup-linux-powerpc64le-gnu': private
  '@rollup/rollup-linux-riscv64-gnu@4.38.0':
    '@rollup/rollup-linux-riscv64-gnu': private
  '@rollup/rollup-linux-riscv64-musl@4.38.0':
    '@rollup/rollup-linux-riscv64-musl': private
  '@rollup/rollup-linux-s390x-gnu@4.38.0':
    '@rollup/rollup-linux-s390x-gnu': private
  '@rollup/rollup-linux-x64-gnu@4.38.0':
    '@rollup/rollup-linux-x64-gnu': private
  '@rollup/rollup-linux-x64-musl@4.38.0':
    '@rollup/rollup-linux-x64-musl': private
  '@rollup/rollup-win32-arm64-msvc@4.38.0':
    '@rollup/rollup-win32-arm64-msvc': private
  '@rollup/rollup-win32-ia32-msvc@4.38.0':
    '@rollup/rollup-win32-ia32-msvc': private
  '@rollup/rollup-win32-x64-msvc@4.38.0':
    '@rollup/rollup-win32-x64-msvc': private
  '@types/estree@1.0.7':
    '@types/estree': private
  '@types/web-bluetooth@0.0.21':
    '@types/web-bluetooth': private
  '@varlet/icons@3.11.0':
    '@varlet/icons': private
  '@varlet/shared@3.11.0':
    '@varlet/shared': private
  '@varlet/use@3.11.0(vue@3.5.13(typescript@5.7.3))':
    '@varlet/use': private
  '@volar/language-core@2.4.12':
    '@volar/language-core': private
  '@volar/source-map@2.4.12':
    '@volar/source-map': private
  '@volar/typescript@2.4.12':
    '@volar/typescript': private
  '@vue/compiler-core@3.5.13':
    '@vue/compiler-core': private
  '@vue/compiler-dom@3.5.13':
    '@vue/compiler-dom': private
  '@vue/compiler-sfc@3.5.13':
    '@vue/compiler-sfc': private
  '@vue/compiler-ssr@3.5.13':
    '@vue/compiler-ssr': private
  '@vue/compiler-vue2@2.7.16':
    '@vue/compiler-vue2': private
  '@vue/devtools-api@7.7.5':
    '@vue/devtools-api': private
  '@vue/devtools-kit@7.7.5':
    '@vue/devtools-kit': private
  '@vue/devtools-shared@7.7.5':
    '@vue/devtools-shared': private
  '@vue/language-core@2.2.8(typescript@5.7.3)':
    '@vue/language-core': private
  '@vue/reactivity@3.5.13':
    '@vue/reactivity': private
  '@vue/runtime-core@3.5.13':
    '@vue/runtime-core': private
  '@vue/runtime-dom@3.5.13':
    '@vue/runtime-dom': private
  '@vue/server-renderer@3.5.13(vue@3.5.13(typescript@5.7.3))':
    '@vue/server-renderer': private
  '@vue/shared@3.5.13':
    '@vue/shared': private
  '@vueuse/metadata@13.1.0':
    '@vueuse/metadata': private
  '@vueuse/shared@13.1.0(vue@3.5.13(typescript@5.7.3))':
    '@vueuse/shared': private
  acorn@8.14.1:
    acorn: private
  alien-signals@1.0.13:
    alien-signals: private
  babel-plugin-polyfill-corejs2@0.4.13(@babel/core@7.26.10):
    babel-plugin-polyfill-corejs2: private
  babel-plugin-polyfill-corejs3@0.11.1(@babel/core@7.26.10):
    babel-plugin-polyfill-corejs3: private
  babel-plugin-polyfill-regenerator@0.6.4(@babel/core@7.26.10):
    babel-plugin-polyfill-regenerator: private
  balanced-match@1.0.2:
    balanced-match: private
  birpc@2.3.0:
    birpc: private
  brace-expansion@2.0.1:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  browserslist-to-esbuild@2.1.1(browserslist@4.24.4):
    browserslist-to-esbuild: private
  browserslist@4.24.4:
    browserslist: private
  buffer-from@1.1.2:
    buffer-from: private
  caniuse-lite@1.0.30001714:
    caniuse-lite: private
  chokidar@4.0.3:
    chokidar: private
  commander@2.20.3:
    commander: private
  convert-source-map@2.0.0:
    convert-source-map: private
  copy-anything@3.0.5:
    copy-anything: private
  core-js-compat@3.41.0:
    core-js-compat: private
  core-js@3.41.0:
    core-js: private
  csstype@3.1.3:
    csstype: private
  dayjs@1.11.13:
    dayjs: private
  de-indent@1.0.2:
    de-indent: private
  debug@4.4.0:
    debug: private
  decimal.js@10.6.0:
    decimal.js: private
  detect-libc@1.0.3:
    detect-libc: private
  electron-to-chromium@1.5.137:
    electron-to-chromium: private
  entities@4.5.0:
    entities: private
  esbuild@0.25.2:
    esbuild: private
  escalade@3.2.0:
    escalade: private
  estree-walker@2.0.2:
    estree-walker: private
  esutils@2.0.3:
    esutils: private
  fill-range@7.1.1:
    fill-range: private
  fraction.js@4.3.7:
    fraction.js: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  gensync@1.0.0-beta.2:
    gensync: private
  globals@11.12.0:
    globals: private
  hasown@2.0.2:
    hasown: private
  he@1.2.0:
    he: private
  hookable@5.5.3:
    hookable: private
  immutable@5.1.1:
    immutable: private
  is-core-module@2.16.1:
    is-core-module: private
  is-extglob@2.1.1:
    is-extglob: private
  is-glob@4.0.3:
    is-glob: private
  is-number@7.0.0:
    is-number: private
  is-what@4.1.16:
    is-what: private
  jiti@1.21.7:
    jiti: private
  js-tokens@4.0.0:
    js-tokens: private
  jsesc@3.1.0:
    jsesc: private
  json5@2.2.3:
    json5: private
  lodash.debounce@4.0.8:
    lodash.debounce: private
  lru-cache@5.1.1:
    lru-cache: private
  magic-string@0.30.17:
    magic-string: private
  meow@13.2.0:
    meow: private
  micromatch@4.0.8:
    micromatch: private
  minimatch@9.0.5:
    minimatch: private
  mitt@3.0.1:
    mitt: private
  ms@2.1.3:
    ms: private
  muggle-string@0.4.1:
    muggle-string: private
  nanoid@3.3.11:
    nanoid: private
  node-addon-api@7.1.1:
    node-addon-api: private
  node-releases@2.0.19:
    node-releases: private
  normalize-range@0.1.2:
    normalize-range: private
  path-browserify@1.0.1:
    path-browserify: private
  path-parse@1.0.7:
    path-parse: private
  perfect-debounce@1.0.0:
    perfect-debounce: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@2.3.1:
    picomatch: private
  postcss-value-parser@4.2.0:
    postcss-value-parser: private
  rattail@1.0.17:
    rattail: private
  readdirp@4.1.2:
    readdirp: private
  regenerate-unicode-properties@10.2.0:
    regenerate-unicode-properties: private
  regenerate@1.4.2:
    regenerate: private
  regenerator-runtime@0.14.1:
    regenerator-runtime: private
  regenerator-transform@0.15.2:
    regenerator-transform: private
  regexpu-core@6.2.0:
    regexpu-core: private
  regjsgen@0.8.0:
    regjsgen: private
  regjsparser@0.12.0:
    regjsparser: private
  resolve@1.22.10:
    resolve: private
  rfdc@1.4.1:
    rfdc: private
  rollup@4.38.0:
    rollup: private
  semver@6.3.1:
    semver: private
  source-map-js@1.2.1:
    source-map-js: private
  source-map-support@0.5.21:
    source-map-support: private
  source-map@0.6.1:
    source-map: private
  speakingurl@14.0.1:
    speakingurl: private
  superjson@2.2.2:
    superjson: private
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: private
  systemjs@6.15.1:
    systemjs: private
  terser@5.39.0:
    terser: private
  to-regex-range@5.0.1:
    to-regex-range: private
  undici-types@6.20.0:
    undici-types: private
  unicode-canonical-property-names-ecmascript@2.0.1:
    unicode-canonical-property-names-ecmascript: private
  unicode-match-property-ecmascript@2.0.0:
    unicode-match-property-ecmascript: private
  unicode-match-property-value-ecmascript@2.2.0:
    unicode-match-property-value-ecmascript: private
  unicode-property-aliases-ecmascript@2.1.0:
    unicode-property-aliases-ecmascript: private
  update-browserslist-db@1.1.3(browserslist@4.24.4):
    update-browserslist-db: private
  vscode-uri@3.1.0:
    vscode-uri: private
  yallist@3.1.1:
    yallist: private
  yaml@2.7.1:
    yaml: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.7.0
pendingBuilds: []
prunedAt: Wed, 23 Jul 2025 01:51:02 GMT
publicHoistPattern: []
registries:
  default: https://registry.npmjs.org/
skipped:
  - '@esbuild/aix-ppc64@0.25.2'
  - '@esbuild/android-arm64@0.25.2'
  - '@esbuild/android-arm@0.25.2'
  - '@esbuild/android-x64@0.25.2'
  - '@esbuild/darwin-x64@0.25.2'
  - '@esbuild/freebsd-arm64@0.25.2'
  - '@esbuild/freebsd-x64@0.25.2'
  - '@esbuild/linux-arm64@0.25.2'
  - '@esbuild/linux-arm@0.25.2'
  - '@esbuild/linux-ia32@0.25.2'
  - '@esbuild/linux-loong64@0.25.2'
  - '@esbuild/linux-mips64el@0.25.2'
  - '@esbuild/linux-ppc64@0.25.2'
  - '@esbuild/linux-riscv64@0.25.2'
  - '@esbuild/linux-s390x@0.25.2'
  - '@esbuild/linux-x64@0.25.2'
  - '@esbuild/netbsd-arm64@0.25.2'
  - '@esbuild/netbsd-x64@0.25.2'
  - '@esbuild/openbsd-arm64@0.25.2'
  - '@esbuild/openbsd-x64@0.25.2'
  - '@esbuild/sunos-x64@0.25.2'
  - '@esbuild/win32-arm64@0.25.2'
  - '@esbuild/win32-ia32@0.25.2'
  - '@esbuild/win32-x64@0.25.2'
  - '@parcel/watcher-android-arm64@2.5.1'
  - '@parcel/watcher-darwin-x64@2.5.1'
  - '@parcel/watcher-freebsd-x64@2.5.1'
  - '@parcel/watcher-linux-arm-glibc@2.5.1'
  - '@parcel/watcher-linux-arm-musl@2.5.1'
  - '@parcel/watcher-linux-arm64-glibc@2.5.1'
  - '@parcel/watcher-linux-arm64-musl@2.5.1'
  - '@parcel/watcher-linux-x64-glibc@2.5.1'
  - '@parcel/watcher-linux-x64-musl@2.5.1'
  - '@parcel/watcher-win32-arm64@2.5.1'
  - '@parcel/watcher-win32-ia32@2.5.1'
  - '@parcel/watcher-win32-x64@2.5.1'
  - '@rollup/rollup-android-arm-eabi@4.38.0'
  - '@rollup/rollup-android-arm64@4.38.0'
  - '@rollup/rollup-darwin-x64@4.38.0'
  - '@rollup/rollup-freebsd-arm64@4.38.0'
  - '@rollup/rollup-freebsd-x64@4.38.0'
  - '@rollup/rollup-linux-arm-gnueabihf@4.38.0'
  - '@rollup/rollup-linux-arm-musleabihf@4.38.0'
  - '@rollup/rollup-linux-arm64-gnu@4.38.0'
  - '@rollup/rollup-linux-arm64-musl@4.38.0'
  - '@rollup/rollup-linux-loongarch64-gnu@4.38.0'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.38.0'
  - '@rollup/rollup-linux-riscv64-gnu@4.38.0'
  - '@rollup/rollup-linux-riscv64-musl@4.38.0'
  - '@rollup/rollup-linux-s390x-gnu@4.38.0'
  - '@rollup/rollup-linux-x64-gnu@4.38.0'
  - '@rollup/rollup-linux-x64-musl@4.38.0'
  - '@rollup/rollup-win32-arm64-msvc@4.38.0'
  - '@rollup/rollup-win32-ia32-msvc@4.38.0'
  - '@rollup/rollup-win32-x64-msvc@4.38.0'
storeDir: /Users/<USER>/Library/pnpm/store/v10
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
