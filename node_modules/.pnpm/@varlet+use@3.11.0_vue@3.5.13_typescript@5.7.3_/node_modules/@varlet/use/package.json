{"name": "@varlet/use", "version": "3.11.0", "description": "composable utils of varlet", "keywords": ["composable", "utils", "varlet"], "bugs": {"url": "https://github.com/varletjs/varlet/issues"}, "repository": {"type": "git", "url": "https://github.com/varletjs/varlet.git"}, "license": "MIT", "author": "haoziqaq <<EMAIL>>", "sideEffects": false, "type": "module", "exports": {".": {"import": "./lib/index.js", "require": "./lib/index.cjs", "types": "./lib/index.d.ts"}}, "main": "lib/index.cjs", "module": "lib/index.js", "types": "lib/index.d.ts", "files": ["lib"], "dependencies": {"@varlet/shared": "3.11.0"}, "devDependencies": {"@types/node": "^18.7.20", "tsup": "7.2.0", "typescript": "5.3.3", "vue": "3.5.13"}, "peerDependencies": {"vue": "^3.2.0"}, "scripts": {"build": "tsup src/index.ts --format esm,cjs --out-dir=lib --dts --clean", "dev": "tsup src/index.ts --format esm --out-dir=lib --watch --dts"}}