import { defineListenerProp } from "../utils/components.mjs";
const props = {
  show: <PERSON><PERSON><PERSON>,
  disabled: Boolean,
  trigger: {
    type: String,
    default: "click"
  },
  reference: [String, Object],
  placement: {
    type: String,
    default: "cover-top-start"
  },
  strategy: {
    type: String,
    default: "absolute"
  },
  offsetX: {
    type: [Number, String],
    default: 0
  },
  offsetY: {
    type: [Number, String],
    default: 0
  },
  teleport: {
    type: [String, Object, Boolean],
    default: "body"
  },
  sameWidth: Boolean,
  elevation: {
    type: [Boolean, String, Number],
    default: true
  },
  defaultStyle: {
    type: Boolean,
    default: true
  },
  popoverClass: String,
  closeOnClickReference: Boolean,
  closeOnKeyEscape: {
    type: Boolean,
    default: true
  },
  onOpen: defineListenerProp(),
  onOpened: defineListenerProp(),
  onClose: defineListenerProp(),
  onClosed: defineListenerProp(),
  onClickOutside: defineListenerProp(),
  "onUpdate:show": defineListenerProp(),
  // internal start
  cascadeOptimization: Boolean
  // internal end
};
export {
  props
};
