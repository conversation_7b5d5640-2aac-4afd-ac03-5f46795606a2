import { defineComponent } from "vue";
import { createNamespace, formatElevation, useTeleport } from "../utils/components.mjs";
import { toSizeUnit } from "../utils/elements.mjs";
import { props } from "./props.mjs";
import { usePopover } from "./usePopover.mjs";
const { name, n, classes } = createNamespace("menu");
import { renderSlot as _renderSlot, vShow as _vShow, withModifiers as _withModifiers, normalizeClass as _normalizeClass, normalizeStyle as _normalizeStyle, createElementVNode as _createElementVNode, withDirectives as _withDirectives, Transition as _Transition, withCtx as _withCtx, createVNode as _createVNode, Teleport as _Teleport, openBlock as _openBlock, createBlock as _createBlock, createElementBlock as _createElementBlock } from "vue";
function __render__(_ctx, _cache) {
  return _openBlock(), _createElementBlock(
    "div",
    {
      ref: "host",
      class: _normalizeClass(_ctx.classes(_ctx.n(), _ctx.n("$--box")))
    },
    [
      _renderSlot(_ctx.$slots, "default"),
      (_openBlock(), _createBlock(_Teleport, {
        to: _ctx.teleport === false ? void 0 : _ctx.teleport,
        disabled: _ctx.teleportDisabled || _ctx.teleport === false
      }, [
        _createVNode(_Transition, {
          name: _ctx.n(),
          onAfterEnter: _ctx.onOpened,
          onAfterLeave: _ctx.handleClosed,
          persisted: ""
        }, {
          default: _withCtx(() => [
            _withDirectives(_createElementVNode(
              "div",
              {
                ref: "popover",
                style: _normalizeStyle({
                  zIndex: _ctx.zIndex,
                  width: _ctx.sameWidth ? _ctx.toSizeUnit(Math.ceil(_ctx.referenceSize.width)) : void 0
                }),
                class: _normalizeClass(
                  _ctx.classes(
                    _ctx.n("menu"),
                    _ctx.n("$--box"),
                    _ctx.popoverClass,
                    [_ctx.defaultStyle, _ctx.n("--menu-background-color")],
                    [_ctx.defaultStyle, _ctx.formatElevation(_ctx.elevation, 3)]
                  )
                ),
                onClick: _cache[0] || (_cache[0] = _withModifiers(() => {
                }, ["stop"])),
                onMouseenter: _cache[1] || (_cache[1] = (...args) => _ctx.handlePopoverMouseenter && _ctx.handlePopoverMouseenter(...args)),
                onMouseleave: _cache[2] || (_cache[2] = (...args) => _ctx.handlePopoverMouseleave && _ctx.handlePopoverMouseleave(...args))
              },
              [
                _renderSlot(_ctx.$slots, "menu")
              ],
              38
              /* CLASS, STYLE, NEED_HYDRATION */
            ), [
              [_vShow, _ctx.show]
            ])
          ]),
          _: 3
          /* FORWARDED */
        }, 8, ["name", "onAfterEnter", "onAfterLeave"])
      ], 8, ["to", "disabled"]))
    ],
    2
    /* CLASS */
  );
}
const __sfc__ = defineComponent({
  name,
  props,
  setup(props2) {
    const { disabled: teleportDisabled } = useTeleport();
    const {
      popover,
      host,
      referenceSize,
      show,
      zIndex,
      handlePopoverMouseenter,
      handlePopoverMouseleave,
      handlePopoverClose,
      handleClosed,
      setAllowClose,
      // expose
      open,
      // expose
      close,
      // expose
      resize,
      // expose
      setReference
    } = usePopover(props2);
    function allowClose() {
      setAllowClose(true);
    }
    return {
      popover,
      host,
      referenceSize,
      show,
      zIndex,
      teleportDisabled,
      allowClose,
      formatElevation,
      toSizeUnit,
      n,
      classes,
      handlePopoverMouseenter,
      handlePopoverMouseleave,
      handlePopoverClose,
      handleClosed,
      resize,
      open,
      close,
      setReference
    };
  }
});
__sfc__.render = __render__;
var stdin_default = __sfc__;
export {
  stdin_default as default
};
