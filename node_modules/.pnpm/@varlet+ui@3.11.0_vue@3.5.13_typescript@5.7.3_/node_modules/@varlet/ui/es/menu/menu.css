:root { --menu-background-color: var(--color-surface-container-high); --menu-border-radius: 2px;}.var-menu { display: inline-flex; outline: none;}.var-menu__menu { border-radius: var(--menu-border-radius);}.var-menu-enter-from,.var-menu-leave-to { opacity: 0; transform: scale(0.8);}.var-menu-enter-active,.var-menu-leave-active { transition-property: opacity, transform; transition-duration: 0.2s;}.var-menu--menu-background-color { background: var(--menu-background-color);}