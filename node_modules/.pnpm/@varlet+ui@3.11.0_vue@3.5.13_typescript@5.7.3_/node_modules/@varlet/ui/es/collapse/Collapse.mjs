import { computed, defineComponent, nextTick, watch } from "vue";
import { call, normalizeToArray, removeArrayBlank } from "@varlet/shared";
import { createNamespace } from "../utils/components.mjs";
import { props } from "./props.mjs";
import { useCollapseItem } from "./provide.mjs";
const { name, n } = createNamespace("collapse");
import { renderSlot as _renderSlot, normalizeClass as _normalizeClass, openBlock as _openBlock, createElementBlock as _createElementBlock } from "vue";
function __render__(_ctx, _cache) {
  return _openBlock(), _createElementBlock(
    "div",
    {
      class: _normalizeClass(_ctx.n())
    },
    [
      _renderSlot(_ctx.$slots, "default")
    ],
    2
    /* CLASS */
  );
}
const __sfc__ = defineComponent({
  name,
  props,
  setup(props2) {
    const offset = computed(() => props2.offset);
    const divider = computed(() => props2.divider);
    const elevation = computed(() => props2.elevation);
    const accordion = computed(() => props2.accordion);
    const normalizeValues = computed(() => normalizeToArray(props2.modelValue));
    const { length, collapseItems, bindCollapseItems } = useCollapseItem();
    const collapseProvider = {
      offset,
      divider,
      elevation,
      accordion,
      updateItem
    };
    watch(
      () => length.value,
      () => nextTick().then(resize)
    );
    watch(
      () => props2.modelValue,
      () => nextTick().then(resize)
    );
    bindCollapseItems(collapseProvider);
    function updateItem(itemValue, targetExpand) {
      if (props2.accordion) {
        const modelValue2 = targetExpand ? itemValue : void 0;
        updateModelValue(modelValue2);
        return;
      }
      const modelValue = targetExpand ? [...normalizeValues.value, itemValue] : normalizeValues.value.filter((value) => value !== itemValue);
      updateModelValue(modelValue);
    }
    function updateModelValue(modelValue) {
      call(props2["onUpdate:modelValue"], modelValue);
      call(props2.onChange, modelValue);
    }
    function matchItems() {
      if (props2.accordion) {
        const [value] = normalizeValues.value;
        if (value == null) {
          return;
        }
        const matchedNameItem = collapseItems.find(({ name: name2 }) => value === name2.value);
        if (matchedNameItem == null) {
          return collapseItems.find(({ index, name: name2 }) => name2.value == null && value === index.value);
        }
        return matchedNameItem;
      }
      const matchedNameItems = collapseItems.filter(
        ({ name: name2 }) => name2.value != null && normalizeValues.value.includes(name2.value)
      );
      const matchedIndexItems = collapseItems.filter(
        ({ index, name: name2 }) => name2.value == null && normalizeValues.value.includes(index.value)
      );
      return [...matchedNameItems, ...matchedIndexItems];
    }
    function resize() {
      const matchedItems = removeArrayBlank(normalizeToArray(matchItems()));
      collapseItems.forEach((collapseItem) => {
        collapseItem.init(matchedItems.includes(collapseItem));
      });
    }
    const toggleAll = (options) => {
      if (props2.accordion) {
        return;
      }
      const matchedItems = collapseItems.filter((item) => {
        var _a;
        const itemValue = (_a = item.name.value) != null ? _a : item.index.value;
        const expanded = normalizeValues.value.includes(itemValue);
        if (options.skipDisabled && item.disabled.value) {
          return expanded;
        }
        if (options.expand === "inverse") {
          return !expanded;
        }
        return options.expand;
      });
      const modelValue = matchedItems.map((item) => {
        var _a;
        return (_a = item.name.value) != null ? _a : item.index.value;
      });
      updateModelValue(modelValue);
    };
    return {
      divider,
      n,
      toggleAll
    };
  }
});
__sfc__.render = __render__;
var stdin_default = __sfc__;
export {
  stdin_default as default
};
