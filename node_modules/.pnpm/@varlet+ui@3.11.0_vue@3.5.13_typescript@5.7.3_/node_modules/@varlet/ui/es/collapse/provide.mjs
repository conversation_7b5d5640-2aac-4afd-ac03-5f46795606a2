import { useChildren } from "@varlet/use";
const COLLAPSE_BIND_COLLAPSE_ITEM_KEY = Symbol("COLLAPSE_BIND_COLLAPSE_ITEM_KEY");
function useCollapseItem() {
  const { childProviders, length, bindChildren } = useChildren(
    COLLAPSE_BIND_COLLAPSE_ITEM_KEY
  );
  return {
    length,
    collapseItems: childProviders,
    bindCollapseItems: bindChildren
  };
}
export {
  COLLAPSE_BIND_COLLAPSE_ITEM_KEY,
  useCollapseItem
};
