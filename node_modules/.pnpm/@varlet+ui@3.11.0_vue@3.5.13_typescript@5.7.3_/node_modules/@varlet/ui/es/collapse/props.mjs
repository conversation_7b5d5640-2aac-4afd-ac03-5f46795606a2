import { defineListenerProp } from "../utils/components.mjs";
const props = {
  modelValue: [A<PERSON><PERSON>, String, Number],
  accordion: Boolean,
  offset: {
    type: Boolean,
    default: true
  },
  divider: {
    type: Boolean,
    default: true
  },
  elevation: {
    type: [<PERSON><PERSON><PERSON>, String, Number],
    default: true
  },
  onChange: defineListenerProp(),
  "onUpdate:modelValue": defineListenerProp()
};
export {
  props
};
