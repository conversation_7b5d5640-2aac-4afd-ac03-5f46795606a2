:root { --breadcrumb-active-color: var(--color-primary); --breadcrumb-inactive-color: #888; --breadcrumb-separator-margin: 0 10px; --breadcrumb-separator-font-size: 14px;}.var-breadcrumb { display: flex; align-items: center; color: var(--breadcrumb-inactive-color);}.var-breadcrumb__separator { margin: var(--breadcrumb-separator-margin); font-size: var(--breadcrumb-separator-font-size);}.var-breadcrumb--active { color: var(--breadcrumb-active-color); transition: opacity 0.25s;}.var-breadcrumb--active:hover { opacity: 0.7; cursor: pointer;}