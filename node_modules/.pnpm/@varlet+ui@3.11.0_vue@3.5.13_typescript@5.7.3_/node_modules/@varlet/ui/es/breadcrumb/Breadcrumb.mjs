import { computed, defineComponent } from "vue";
import { call } from "@varlet/shared";
import { createNamespace } from "../utils/components.mjs";
import { props } from "./props.mjs";
import { useBreadcrumb } from "./provide.mjs";
const { name, n, classes } = createNamespace("breadcrumb");
import { renderSlot as _renderSlot, normalizeClass as _normalizeClass, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, createCommentVNode as _createCommentVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from "vue";
function __render__(_ctx, _cache) {
  return _openBlock(), _createElementBlock(
    "div",
    {
      class: _normalizeClass(_ctx.n())
    },
    [
      _createElementVNode(
        "div",
        {
          class: _normalizeClass(_ctx.classes(_ctx.n("content"), [!_ctx.isLast, _ctx.n("--active")])),
          onClick: _cache[0] || (_cache[0] = (...args) => _ctx.handleClick && _ctx.handleClick(...args))
        },
        [
          _renderSlot(_ctx.$slots, "default")
        ],
        2
        /* CLASS */
      ),
      !_ctx.isLast ? _renderSlot(_ctx.$slots, "separator", { key: 0 }, () => {
        var _a;
        return [
          _createElementVNode(
            "div",
            {
              class: _normalizeClass(_ctx.n("separator")),
              "aria-hidden": "true"
            },
            _toDisplayString((_a = _ctx.separator) != null ? _a : _ctx.parentSeparator),
            3
            /* TEXT, CLASS */
          )
        ];
      }) : _createCommentVNode("v-if", true)
    ],
    2
    /* CLASS */
  );
}
const __sfc__ = defineComponent({
  name,
  props,
  setup(props2) {
    const { index, breadcrumb, bindBreadcrumb } = useBreadcrumb();
    const isLast = computed(() => index.value === breadcrumb.length.value - 1);
    const parentSeparator = computed(() => breadcrumb.separator.value);
    bindBreadcrumb(null);
    function handleClick(e) {
      if (isLast.value) {
        return;
      }
      call(props2.onClick, e);
    }
    return {
      n,
      classes,
      isLast,
      parentSeparator,
      handleClick
    };
  }
});
__sfc__.render = __render__;
var stdin_default = __sfc__;
export {
  stdin_default as default
};
