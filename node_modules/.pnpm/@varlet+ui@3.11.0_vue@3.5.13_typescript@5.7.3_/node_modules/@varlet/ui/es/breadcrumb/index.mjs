import { withInstall, withPropsDefaultsSetter } from "../utils/components.mjs";
import Breadcrumb from "./Breadcrumb.mjs";
import { props as breadcrumbProps } from "./props.mjs";
withInstall(Breadcrumb);
withPropsDefaultsSetter(Breadcrumb, breadcrumbProps);
const _BreadcrumbComponent = Breadcrumb;
var stdin_default = Breadcrumb;
export {
  _BreadcrumbComponent,
  breadcrumbProps,
  stdin_default as default
};
