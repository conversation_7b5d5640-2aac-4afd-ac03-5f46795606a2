import { assert } from "@varlet/shared";
import { useParent } from "@varlet/use";
import { BREADCRUMBS_BIND_BREADCRUMB_ITEM_KEY } from "../breadcrumbs/provide.mjs";
function useBreadcrumb() {
  const { parentProvider, bindParent, index } = useParent(
    BREADCRUMBS_BIND_BREADCRUMB_ITEM_KEY
  );
  assert(!!bindParent, "Breadcrumb", "<var-breadcrumb/> must in <var-breadcrumbs/>");
  return {
    index,
    breadcrumb: parentProvider,
    bindBreadcrumb: bindParent
  };
}
export {
  useBreadcrumb
};
