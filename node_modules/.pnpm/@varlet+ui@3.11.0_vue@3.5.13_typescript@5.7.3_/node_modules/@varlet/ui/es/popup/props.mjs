import { defineListenerProp } from "../utils/components.mjs";
const props = {
  show: <PERSON>olean,
  position: {
    type: String,
    default: "center"
  },
  transition: String,
  overlay: {
    type: Boolean,
    default: true
  },
  overlayClass: String,
  overlayStyle: Object,
  lockScroll: {
    type: Boolean,
    default: true
  },
  closeOnClickOverlay: {
    type: Boolean,
    default: true
  },
  closeOnKeyEscape: {
    type: Boolean,
    default: true
  },
  defaultStyle: {
    type: Boolean,
    default: true
  },
  zIndex: Number,
  safeArea: Boolean,
  safeAreaTop: Boolean,
  teleport: {
    type: [String, Object, Boolean],
    default: "body"
  },
  onOpen: defineListenerProp(),
  onOpened: defineListenerProp(),
  onClose: defineListenerProp(),
  onClosed: defineListenerProp(),
  onKeyEscape: defineListenerProp(),
  onClickOverlay: defineListenerProp(),
  "onUpdate:show": defineListenerProp(),
  // internal for Dialog
  onRouteChange: defineListenerProp()
};
export {
  props
};
