import { useChildren, useParent } from "@varlet/use";
const POPUP_BIND_POPUP_ITEM_KEY = Symbol("POPUP_BIND_POPUP_ITEM_KEY");
function usePopup() {
  const { bindParent, parentProvider, index } = useParent(POPUP_BIND_POPUP_ITEM_KEY);
  return {
    index,
    popup: parentProvider,
    bindPopup: bindParent
  };
}
function usePopupItems() {
  const { bindChildren, childProviders, length } = useChildren(POPUP_BIND_POPUP_ITEM_KEY);
  return {
    length,
    popupItems: childProviders,
    bindPopupItems: bindChildren
  };
}
export {
  POPUP_BIND_POPUP_ITEM_KEY,
  usePopup,
  usePopupItems
};
