var __defProp = Object.defineProperty;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
import { computed, defineComponent, Teleport, Transition, watch, createVNode as _createVNode, vShow as _vShow, mergeProps as _mergeProps, withDirectives as _withDirectives } from "vue";
import { call, preventDefault } from "@varlet/shared";
import { useEventListener, useInitialized } from "@varlet/use";
import { useLock } from "../context/lock.mjs";
import { useStack } from "../context/stack.mjs";
import { useZIndex } from "../context/zIndex.mjs";
import { createNamespace, useRouteListener, useTeleport } from "../utils/components.mjs";
import { props } from "./props.mjs";
import { usePopupItems } from "./provide.mjs";


const {
  name,
  n,
  classes
} = createNamespace("popup");
var stdin_default = defineComponent({
  name,
  inheritAttrs: false,
  props,
  setup(props2, {
    slots,
    attrs
  }) {
    const rendered = useInitialized(() => props2.show, true);
    const {
      zIndex
    } = useZIndex(() => props2.show, 3);
    const normalizedZIndex = computed(() => {
      var _a;
      return (_a = props2.zIndex) != null ? _a : zIndex.value;
    });
    const {
      onStackTop
    } = useStack(() => props2.show, normalizedZIndex);
    const {
      disabled
    } = useTeleport();
    const {
      bindPopupItems
    } = usePopupItems();
    useLock(() => props2.show, () => props2.lockScroll);
    watch(() => props2.show, (newValue) => {
      newValue ? call(props2.onOpen) : call(props2.onClose);
    });
    bindPopupItems({
      show: computed(() => props2.show)
    });
    useEventListener(() => window, "keydown", handleKeydown);
    useRouteListener(() => call(props2.onRouteChange));
    function hidePopup() {
      const {
        closeOnClickOverlay,
        onClickOverlay
      } = props2;
      call(onClickOverlay);
      if (!closeOnClickOverlay) {
        return;
      }
      call(props2["onUpdate:show"], false);
    }
    function renderOverlay() {
      const {
        overlayClass = "",
        overlayStyle
      } = props2;
      return _createVNode("div", {
        "class": classes(n("overlay"), overlayClass),
        "style": __spreadValues({
          zIndex: normalizedZIndex.value - 1
        }, overlayStyle),
        "onClick": hidePopup
      }, null);
    }
    function renderContent() {
      return _withDirectives(_createVNode("div", _mergeProps({
        "class": classes(n("content"), n(`--${props2.position}`), [props2.defaultStyle, n("--content-background-color")], [props2.defaultStyle, n("$-elevation--3")], [props2.safeArea, n("--safe-area")], [props2.safeAreaTop, n("--safe-area-top")]),
        "style": {
          zIndex: normalizedZIndex.value
        },
        "role": "dialog",
        "aria-modal": "true"
      }, attrs), [rendered.value && call(slots.default)]), [[_vShow, props2.show]]);
    }
    function renderPopup() {
      return _createVNode(Transition, {
        "name": n("$-fade"),
        "onAfterEnter": props2.onOpened,
        "onAfterLeave": props2.onClosed
      }, {
        default: () => [_withDirectives(_createVNode("div", {
          "class": classes(n("$--box"), n(), [!props2.overlay, n("--pointer-events-none")]),
          "style": {
            zIndex: normalizedZIndex.value - 2
          }
        }, [props2.overlay && renderOverlay(), _createVNode(Transition, {
          "name": props2.transition || n(`$-pop-${props2.position}`)
        }, {
          default: () => [renderContent()]
        })]), [[_vShow, props2.show]])]
      });
    }
    function handleKeydown(event) {
      if (!onStackTop() || event.key !== "Escape" || !props2.show) {
        return;
      }
      call(props2.onKeyEscape);
      if (!props2.closeOnKeyEscape) {
        return;
      }
      preventDefault(event);
      call(props2["onUpdate:show"], false);
    }
    return () => {
      const {
        teleport
      } = props2;
      if (teleport) {
        return _createVNode(Teleport, {
          "to": teleport,
          "disabled": disabled.value
        }, {
          default: () => [renderPopup()]
        });
      }
      return renderPopup();
    };
  }
});
export {
  stdin_default as default
};
