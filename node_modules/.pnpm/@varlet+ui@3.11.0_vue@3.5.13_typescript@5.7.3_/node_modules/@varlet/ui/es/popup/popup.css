:root { --popup-content-background-color: var(--color-surface-container-low); --popup-overlay-background-color: rgba(0, 0, 0, 0.6);}.var-fade-enter-from,.var-fade-leave-to { opacity: 0;}.var-fade-enter-active,.var-fade-leave-active { transition: opacity 0.25s;}.var-pop-center-enter-from,.var-pop-center-leave-to { transform: scale(0.3);}.var-pop-center-enter-active,.var-pop-center-leave-active { transition: all 0.25s;}.var-pop-bottom-enter-from,.var-pop-bottom-leave-to { transform: translateY(100%);}.var-pop-bottom-enter-active,.var-pop-bottom-leave-active { transition: all 0.25s;}.var-pop-top-enter-from,.var-pop-top-leave-to { transform: translateY(-100%);}.var-pop-top-enter-active,.var-pop-top-leave-active { transition: all 0.25s;}.var-pop-left-enter-from,.var-pop-left-leave-to { transform: translateX(-100%);}.var-pop-left-enter-active,.var-pop-left-leave-active { transition: all 0.25s;}.var-pop-right-enter-from,.var-pop-right-leave-to { transform: translateX(100%);}.var-pop-right-enter-active,.var-pop-right-leave-active { transition: all 0.25s;}.var-popup { display: flex; justify-content: center; align-items: center; position: fixed; top: 0; right: 0; bottom: 0; left: 0; overflow: auto; scrollbar-width: none;}.var-popup__overlay { position: fixed; top: 0; right: 0; bottom: 0; left: 0; background-color: var(--popup-overlay-background-color); transition: all 0.25s;}.var-popup__content { overflow: auto; transition: all 0.25s; pointer-events: auto;}.var-popup--content-background-color { background-color: var(--popup-content-background-color);}.var-popup--center { position: relative;}.var-popup--bottom { width: 100%; position: absolute; left: 0; bottom: 0;}.var-popup--top { width: 100%; position: absolute; left: 0; top: 0;}.var-popup--left { height: 100%; position: absolute; left: 0; top: 0;}.var-popup--right { height: 100%; position: absolute; right: 0; top: 0;}.var-popup--safe-area { padding-bottom: constant(safe-area-inset-bottom); padding-bottom: env(safe-area-inset-bottom);}.var-popup--safe-area-top { padding-top: constant(safe-area-inset-top); padding-top: env(safe-area-inset-top);}.var-popup--pointer-events-none { pointer-events: none;}