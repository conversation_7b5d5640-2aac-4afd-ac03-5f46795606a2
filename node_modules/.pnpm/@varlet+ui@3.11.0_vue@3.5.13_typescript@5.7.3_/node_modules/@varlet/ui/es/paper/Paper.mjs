import { defineComponent } from "vue";
import { call } from "@varlet/shared";
import Ripple from "../ripple/index.mjs";
import { createNamespace, formatElevation } from "../utils/components.mjs";
import { toSizeUnit } from "../utils/elements.mjs";
import { props } from "./props.mjs";
const { name, n, classes } = createNamespace("paper");
import { renderSlot as _renderSlot, normalizeClass as _normalizeClass, normalizeStyle as _normalizeStyle, resolveDirective as _resolveDirective, openBlock as _openBlock, createElementBlock as _createElementBlock, withDirectives as _withDirectives } from "vue";
function __render__(_ctx, _cache) {
  const _directive_ripple = _resolveDirective("ripple");
  return _withDirectives((_openBlock(), _createElementBlock(
    "div",
    {
      class: _normalizeClass(
        _ctx.classes(
          _ctx.n(),
          _ctx.n("$--box"),
          _ctx.formatElevation(_ctx.elevation, 2),
          [_ctx.onClick, _ctx.n("--cursor")],
          [_ctx.round, _ctx.n("--round")],
          [_ctx.inline, _ctx.n("$--inline-flex")]
        )
      ),
      style: _normalizeStyle({
        width: _ctx.toSizeUnit(_ctx.width),
        height: _ctx.toSizeUnit(_ctx.height),
        "border-radius": _ctx.toSizeUnit(_ctx.radius)
      }),
      onClick: _cache[0] || (_cache[0] = (...args) => _ctx.handleClick && _ctx.handleClick(...args))
    },
    [
      _renderSlot(_ctx.$slots, "default")
    ],
    6
    /* CLASS, STYLE */
  )), [
    [_directive_ripple, { disabled: !_ctx.ripple }]
  ]);
}
const __sfc__ = defineComponent({
  name,
  directives: { Ripple },
  props,
  setup(props2) {
    function handleClick(e) {
      call(props2.onClick, e);
    }
    return {
      n,
      classes,
      formatElevation,
      toSizeUnit,
      handleClick
    };
  }
});
__sfc__.render = __render__;
var stdin_default = __sfc__;
export {
  stdin_default as default
};
