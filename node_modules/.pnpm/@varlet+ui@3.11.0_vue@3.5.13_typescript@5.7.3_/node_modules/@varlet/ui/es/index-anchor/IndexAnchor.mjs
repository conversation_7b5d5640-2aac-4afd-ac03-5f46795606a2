import { computed, defineComponent, ref, Transition } from "vue";
import VarSticky from "../sticky/index.mjs";
import { createNamespace } from "../utils/components.mjs";
import { props } from "./props.mjs";
import { useIndexBar } from "./provide.mjs";
const { name, n, classes } = createNamespace("index-anchor");
import { renderSlot as _renderSlot, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, mergeProps as _mergeProps, createElementVNode as _createElementVNode, resolveDynamicComponent as _resolveDynamicComponent, withCtx as _withCtx, openBlock as _openBlock, createBlock as _createBlock } from "vue";
function __render__(_ctx, _cache) {
  return _openBlock(), _createBlock(_resolveDynamicComponent(_ctx.sticky ? _ctx.n("$-sticky") : _ctx.Transition), {
    ref: "anchorEl",
    "offset-top": _ctx.sticky ? _ctx.stickyOffsetTop : null,
    "z-index": _ctx.sticky ? _ctx.zIndex : null,
    disabled: _ctx.disabled && !_ctx.cssMode,
    "css-mode": _ctx.cssMode
  }, {
    default: _withCtx(() => [
      _createElementVNode(
        "div",
        _mergeProps({
          class: _ctx.n()
        }, _ctx.$attrs),
        [
          _renderSlot(_ctx.$slots, "default", {}, () => [
            _createTextVNode(
              _toDisplayString(_ctx.name),
              1
              /* TEXT */
            )
          ])
        ],
        16
        /* FULL_PROPS */
      )
    ]),
    _: 3
    /* FORWARDED */
  }, 8, ["offset-top", "z-index", "disabled", "css-mode"]);
}
const __sfc__ = defineComponent({
  name,
  components: { VarSticky },
  inheritAttrs: false,
  props,
  setup(props2) {
    const disabled = ref(false);
    const name2 = computed(() => props2.index);
    const anchorEl = ref(null);
    const { index, indexBar, bindIndexBar } = useIndexBar();
    const { active, sticky, cssMode, stickyOffsetTop, zIndex } = indexBar;
    const indexAnchorProvider = {
      index,
      name: name2,
      setDisabled,
      getOffsetTop
    };
    bindIndexBar(indexAnchorProvider);
    function getOffsetTop() {
      if (!anchorEl.value) {
        return 0;
      }
      return anchorEl.value.$el ? anchorEl.value.$el.offsetTop : anchorEl.value.offsetTop;
    }
    function setDisabled(value) {
      disabled.value = value;
    }
    return {
      n,
      classes,
      name: name2,
      anchorEl,
      active,
      sticky,
      zIndex,
      disabled,
      cssMode,
      stickyOffsetTop,
      Transition
    };
  }
});
__sfc__.render = __render__;
var stdin_default = __sfc__;
export {
  stdin_default as default
};
