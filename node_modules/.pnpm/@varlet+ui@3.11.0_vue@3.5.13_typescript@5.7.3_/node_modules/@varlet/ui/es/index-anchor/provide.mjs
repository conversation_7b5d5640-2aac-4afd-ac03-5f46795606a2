import { assert } from "@varlet/shared";
import { useParent } from "@varlet/use";
import { INDEX_BAR_BIND_INDEX_ANCHOR_KEY } from "../index-bar/provide.mjs";
function useIndexBar() {
  const { parentProvider, index, bindParent } = useParent(
    INDEX_BAR_BIND_INDEX_ANCHOR_KEY
  );
  assert(!!bindParent, "IndexAnchor", 'You should use this component in "IndexBar"');
  return {
    index,
    indexBar: parentProvider,
    bindIndexBar: bindParent
  };
}
export {
  useIndexBar
};
