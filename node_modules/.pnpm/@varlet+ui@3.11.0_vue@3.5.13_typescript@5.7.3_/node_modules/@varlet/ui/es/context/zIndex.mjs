import { ref, watch } from "vue";
import context from "./index.mjs";
function useZIndex(source, count) {
  const zIndex = ref(context.zIndex);
  watch(
    source,
    (newValue) => {
      if (newValue) {
        context.zIndex += process.env.NODE_ENV === "test" ? 0 : count;
        zIndex.value = context.zIndex;
      }
    },
    { immediate: true }
  );
  return { zIndex };
}
export {
  useZIndex
};
