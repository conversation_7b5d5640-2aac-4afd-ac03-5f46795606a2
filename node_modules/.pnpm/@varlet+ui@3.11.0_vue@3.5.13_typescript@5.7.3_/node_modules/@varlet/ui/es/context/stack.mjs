import { getCurrentInstance, watch } from "vue";
import { removeItem } from "@varlet/shared";
import { onSmartMounted, onSmartUnmounted } from "@varlet/use";
const stack = [];
function useStack(activeGetter, zIndex) {
  const { uid } = getCurrentInstance();
  watch(activeGetter, (isActive) => {
    if (isActive && !getStackItem(uid)) {
      pushStackItem();
    } else {
      setTimeout(() => {
        removeItem(stack, getStackItem(uid));
      });
    }
  });
  onSmartMounted(() => {
    if (activeGetter()) {
      pushStackItem();
    }
  });
  onSmartUnmounted(() => {
    removeItem(stack, getStackItem(uid));
  });
  function onStackTop() {
    if (stack.length === 0) {
      return true;
    }
    stack.sort((a, b) => a.zIndex.value - b.zIndex.value);
    return stack[stack.length - 1].uid === uid;
  }
  function pushStackItem() {
    if (getStackItem(uid)) {
      return;
    }
    stack.push({ uid, zIndex });
  }
  function getStackItem(uid2) {
    return stack.find((item) => item.uid === uid2);
  }
  return {
    onStackTop
  };
}
export {
  useStack
};
