import { getCurrentInstance, onActivated, onBeforeMount, onDeactivated, onUnmounted, watch } from "vue";
import context from "./index.mjs";
import { createNamespace } from "../utils/components.mjs";
const { n } = createNamespace("");
function resolveLock() {
  const lockCounts = Object.keys(context.locks).length;
  lockCounts <= 0 ? document.body.classList.remove(n("$--lock")) : document.body.classList.add(n("$--lock"));
}
function addLock(uid) {
  context.locks[uid] = 1;
  resolveLock();
}
function releaseLock(uid) {
  delete context.locks[uid];
  resolveLock();
}
function useLock(source, useSource) {
  const { uid } = getCurrentInstance();
  if (useSource) {
    watch(useSource, (newValue) => {
      if (newValue === false) {
        releaseLock(uid);
      } else if (newValue === true && source() === true) {
        addLock(uid);
      }
    });
  }
  watch(source, (newValue) => {
    if (useSource && useSource() === false) {
      return;
    }
    if (newValue === true) {
      addLock(uid);
    } else {
      releaseLock(uid);
    }
  });
  onBeforeMount(() => {
    if (useSource && useSource() === false) {
      return;
    }
    if (source() === true) {
      addLock(uid);
    }
  });
  onUnmounted(() => {
    if (useSource && useSource() === false) {
      return;
    }
    if (source() === true) {
      releaseLock(uid);
    }
  });
  onActivated(() => {
    if (useSource && useSource() === false) {
      return;
    }
    if (source() === true) {
      addLock(uid);
    }
  });
  onDeactivated(() => {
    if (useSource && useSource() === false) {
      return;
    }
    if (source() === true) {
      releaseLock(uid);
    }
  });
}
export {
  addLock,
  releaseLock,
  resolveLock,
  useLock
};
