import { computed, defineComponent, nextTick, ref } from "vue";
import { call, preventDefault } from "@varlet/shared";
import { useEventListener, useVModel } from "@varlet/use";
import VarFormDetails from "../form-details/index.mjs";
import { useForm } from "../form/provide.mjs";
import Hover from "../hover/index.mjs";
import VarHoverOverlay, { useHoverOverlay } from "../hover-overlay/index.mjs";
import VarIcon from "../icon/index.mjs";
import Ripple from "../ripple/index.mjs";
import { createNamespace, useValidation } from "../utils/components.mjs";
import { props } from "./props.mjs";
import { useRadioGroup } from "./provide.mjs";
const { name, n, classes } = createNamespace("radio");
import { renderSlot as _renderSlot, resolveComponent as _resolveComponent, normalizeClass as _normalizeClass, createVNode as _createVNode, createCommentVNode as _createCommentVNode, normalizeStyle as _normalizeStyle, resolveDirective as _resolveDirective, openBlock as _openBlock, createElementBlock as _createElementBlock, withDirectives as _withDirectives, mergeProps as _mergeProps, createElementVNode as _createElementVNode } from "vue";
const _hoisted_1 = ["aria-checked"];
const _hoisted_2 = ["tabindex"];
function __render__(_ctx, _cache) {
  const _component_var_icon = _resolveComponent("var-icon");
  const _component_var_hover_overlay = _resolveComponent("var-hover-overlay");
  const _component_var_form_details = _resolveComponent("var-form-details");
  const _directive_ripple = _resolveDirective("ripple");
  const _directive_hover = _resolveDirective("hover");
  return _openBlock(), _createElementBlock(
    "div",
    {
      class: _normalizeClass(_ctx.n("wrap"))
    },
    [
      _createElementVNode("div", _mergeProps({
        role: "radio",
        "aria-checked": _ctx.checked,
        class: _ctx.n()
      }, _ctx.$attrs, {
        onClick: _cache[3] || (_cache[3] = (...args) => _ctx.handleClick && _ctx.handleClick(...args))
      }), [
        _withDirectives((_openBlock(), _createElementBlock("div", {
          ref: "action",
          class: _normalizeClass(
            _ctx.classes(
              _ctx.n("action"),
              [_ctx.checked, _ctx.n("--checked"), _ctx.n("--unchecked")],
              [_ctx.errorMessage || _ctx.radioGroupErrorMessage, _ctx.n("--error")],
              [_ctx.formDisabled || _ctx.disabled, _ctx.n("--disabled")]
            )
          ),
          tabindex: _ctx.tabIndex,
          style: _normalizeStyle({ color: _ctx.checked ? _ctx.checkedColor : _ctx.uncheckedColor }),
          onFocus: _cache[0] || (_cache[0] = ($event) => _ctx.isFocusing = true),
          onBlur: _cache[1] || (_cache[1] = ($event) => _ctx.isFocusing = false)
        }, [
          _ctx.checked ? _renderSlot(_ctx.$slots, "checked-icon", { key: 0 }, () => [
            _createVNode(_component_var_icon, {
              class: _normalizeClass(_ctx.n("icon")),
              "var-radio-cover": "",
              name: "radio-marked",
              size: _ctx.iconSize
            }, null, 8, ["class", "size"])
          ]) : _renderSlot(_ctx.$slots, "unchecked-icon", { key: 1 }, () => [
            _createVNode(_component_var_icon, {
              class: _normalizeClass(_ctx.n("icon")),
              "var-radio-cover": "",
              name: "radio-blank",
              size: _ctx.iconSize
            }, null, 8, ["class", "size"])
          ]),
          _createVNode(_component_var_hover_overlay, {
            hovering: !_ctx.disabled && !_ctx.formDisabled && _ctx.hovering,
            focusing: !_ctx.disabled && !_ctx.formDisabled && _ctx.isFocusing
          }, null, 8, ["hovering", "focusing"])
        ], 46, _hoisted_2)), [
          [_directive_ripple, { disabled: _ctx.formReadonly || _ctx.readonly || _ctx.formDisabled || _ctx.disabled || !_ctx.ripple }],
          [_directive_hover, _ctx.handleHovering, "desktop"]
        ]),
        _ctx.$slots.default ? (_openBlock(), _createElementBlock(
          "div",
          {
            key: 0,
            class: _normalizeClass(
              _ctx.classes(
                _ctx.n("text"),
                [_ctx.errorMessage || _ctx.radioGroupErrorMessage, _ctx.n("--error")],
                [_ctx.formDisabled || _ctx.disabled, _ctx.n("--disabled")]
              )
            ),
            onClick: _cache[2] || (_cache[2] = (...args) => _ctx.handleTextClick && _ctx.handleTextClick(...args))
          },
          [
            _renderSlot(_ctx.$slots, "default", { checked: _ctx.checked })
          ],
          2
          /* CLASS */
        )) : _createCommentVNode("v-if", true)
      ], 16, _hoisted_1),
      _createVNode(_component_var_form_details, { "error-message": _ctx.errorMessage }, null, 8, ["error-message"])
    ],
    2
    /* CLASS */
  );
}
const __sfc__ = defineComponent({
  name,
  directives: { Ripple, Hover },
  components: {
    VarIcon,
    VarFormDetails,
    VarHoverOverlay
  },
  inheritAttrs: false,
  props,
  setup(props2) {
    const action = ref();
    const isFocusing = ref(false);
    const value = useVModel(props2, "modelValue");
    const checked = computed(() => value.value === props2.checkedValue);
    const { radioGroup, bindRadioGroup } = useRadioGroup();
    const { hovering, handleHovering } = useHoverOverlay();
    const { form, bindForm } = useForm();
    const tabIndex = computed(() => {
      const disabled = (form == null ? void 0 : form.disabled.value) || props2.disabled;
      const isChecked = checked.value;
      const hasCheckedInRadioGroup = radioGroup == null ? void 0 : radioGroup.hasChecked.value;
      if (disabled) {
        return;
      }
      if (radioGroup && hasCheckedInRadioGroup && !isChecked) {
        return "-1";
      }
      return "0";
    });
    const {
      errorMessage,
      validateWithTrigger: vt,
      validate: v,
      // expose
      resetValidation
    } = useValidation();
    const radioProvider = {
      sync,
      validate,
      resetValidation,
      reset,
      isFocusing: computed(() => isFocusing.value),
      // keyboard arrow move
      move() {
        action.value.focus();
        action.value.click();
      },
      moveable() {
        return !(form == null ? void 0 : form.disabled.value) && !props2.disabled && !(form == null ? void 0 : form.readonly.value) && !props2.readonly;
      }
    };
    call(bindRadioGroup, radioProvider);
    call(bindForm, radioProvider);
    useEventListener(() => window, "keydown", handleKeydown);
    useEventListener(() => window, "keyup", handleKeyup);
    function handleKeydown(event) {
      if (!isFocusing.value) {
        return;
      }
      const { key } = event;
      if (key === "Enter" || key === " ") {
        preventDefault(event);
      }
      if (key === "Enter") {
        action.value.click();
      }
    }
    function handleKeyup(event) {
      if (!isFocusing.value) {
        return;
      }
      if (event.key === " ") {
        preventDefault(event);
        action.value.click();
      }
    }
    function validateWithTrigger(trigger) {
      nextTick(() => {
        const { validateTrigger, rules, modelValue } = props2;
        vt(validateTrigger, trigger, rules, modelValue);
      });
    }
    function change(changedValue) {
      const { checkedValue, onChange } = props2;
      if (radioGroup && value.value === checkedValue) {
        return;
      }
      value.value = changedValue;
      call(onChange, value.value);
      radioGroup == null ? void 0 : radioGroup.onToggle(checkedValue);
      validateWithTrigger("onChange");
    }
    function handleClick(e) {
      const { disabled, readonly, uncheckedValue, checkedValue, onClick } = props2;
      if ((form == null ? void 0 : form.disabled.value) || disabled) {
        return;
      }
      call(onClick, e);
      if ((form == null ? void 0 : form.readonly.value) || readonly) {
        return;
      }
      change(checked.value ? uncheckedValue : checkedValue);
    }
    function handleTextClick() {
      action.value.focus();
    }
    function sync(v2) {
      const { checkedValue, uncheckedValue } = props2;
      const checked2 = v2 === checkedValue;
      value.value = checked2 ? checkedValue : uncheckedValue;
      return checked2;
    }
    function reset() {
      value.value = props2.uncheckedValue;
      resetValidation();
    }
    function validate() {
      return v(props2.rules, props2.modelValue);
    }
    function toggle(changedValue) {
      const { uncheckedValue, checkedValue } = props2;
      const shouldReverse = ![uncheckedValue, checkedValue].includes(changedValue);
      if (shouldReverse) {
        changedValue = checked.value ? uncheckedValue : checkedValue;
      }
      change(changedValue);
    }
    return {
      action,
      isFocusing,
      checked,
      errorMessage,
      radioGroupErrorMessage: radioGroup == null ? void 0 : radioGroup.errorMessage,
      formDisabled: form == null ? void 0 : form.disabled,
      formReadonly: form == null ? void 0 : form.readonly,
      hovering,
      tabIndex,
      handleHovering,
      n,
      classes,
      handleClick,
      handleTextClick,
      toggle,
      reset,
      validate,
      resetValidation
    };
  }
});
__sfc__.render = __render__;
var stdin_default = __sfc__;
export {
  stdin_default as default
};
