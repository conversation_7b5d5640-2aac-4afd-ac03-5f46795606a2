:root { --radio-checked-color: var(--color-primary); --radio-unchecked-color: #555; --radio-disabled-color: var(--color-text-disabled); --radio-error-color: var(--color-danger); --radio-icon-size: 24px; --radio-action-padding: 6px; --radio-text-color: #555;}.var-radio { display: flex; align-items: center; transform: translateX(calc(-1 * var(--radio-action-padding))); cursor: pointer; -webkit-tap-highlight-color: transparent;}.var-radio__wrap { display: inline-flex; flex-direction: column;}.var-radio__action { position: relative; display: flex; justify-content: center; align-items: center; flex-shrink: 0; padding: var(--radio-action-padding); border-radius: 50%; outline: none; transition: background-color 0.25s, color 0.25s;}.var-radio__icon[var-radio-cover] { display: block; font-size: var(--radio-icon-size);}.var-radio__text { color: var(--radio-text-color);}.var-radio--checked { color: var(--radio-checked-color);}.var-radio--unchecked { color: var(--radio-unchecked-color);}.var-radio--disabled { color: var(--radio-disabled-color); cursor: not-allowed;}.var-radio--error { color: var(--radio-error-color);}