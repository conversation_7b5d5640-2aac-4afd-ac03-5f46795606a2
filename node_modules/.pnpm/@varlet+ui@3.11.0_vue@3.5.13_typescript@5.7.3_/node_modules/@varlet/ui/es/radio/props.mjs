import { defineListenerProp } from "../utils/components.mjs";
const props = {
  modelValue: {
    type: [String, Number, Boolean, Object, Array],
    default: false
  },
  checkedValue: {
    type: [String, Number, Boolean, Object, Array],
    default: true
  },
  uncheckedValue: {
    type: [String, Number, Boolean, Object, Array],
    default: false
  },
  disabled: <PERSON><PERSON>an,
  readonly: Boolean,
  checkedColor: String,
  uncheckedColor: String,
  iconSize: [String, Number],
  ripple: {
    type: Boolean,
    default: true
  },
  validateTrigger: {
    type: Array,
    default: () => ["onChange"]
  },
  rules: [Array, Function, Object],
  onClick: defineListenerProp(),
  onChange: defineListenerProp(),
  "onUpdate:modelValue": defineListenerProp()
};
export {
  props
};
