import { defineListenerProp } from "../utils/components.mjs";
const props = {
  active: {
    type: [Number, String],
    default: 0
  },
  zIndex: {
    type: [Number, String],
    default: 1
  },
  fixed: Boolean,
  border: Boolean,
  variant: Boolean,
  safeArea: Boolean,
  activeColor: String,
  inactiveColor: String,
  placeholder: Boolean,
  fabProps: Object,
  onChange: defineListenerProp(),
  onBeforeChange: defineListenerProp(),
  onFabClick: defineListenerProp(),
  "onUpdate:active": defineListenerProp()
};
export {
  props
};
