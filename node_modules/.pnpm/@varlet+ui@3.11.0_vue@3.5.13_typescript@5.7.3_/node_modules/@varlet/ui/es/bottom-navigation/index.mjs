import { withInstall, withPropsDefaultsSetter } from "../utils/components.mjs";
import BottomNavigation from "./BottomNavigation.mjs";
import { props as bottomNavigationProps } from "./props.mjs";
withInstall(BottomNavigation);
withPropsDefaultsSetter(BottomNavigation, bottomNavigationProps);
const _BottomNavigationComponent = BottomNavigation;
var stdin_default = BottomNavigation;
export {
  _BottomNavigationComponent,
  bottomNavigationProps,
  stdin_default as default
};
