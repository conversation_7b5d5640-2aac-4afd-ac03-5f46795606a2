import { useChildren } from "@varlet/use";
const BOTTOM_NAVIGATION_BIND_BOTTOM_NAVIGATION_ITEM_KEY = Symbol(
  "BOTTOM_NAVIGATION_BIND_BOTTOM_NAVIGATION_ITEM_KEY"
);
function useBottomNavigationItems() {
  const { childProviders, length, bindChildren } = useChildren(
    BOTTOM_NAVIGATION_BIND_BOTTOM_NAVIGATION_ITEM_KEY
  );
  return {
    length,
    bottomNavigationItems: childProviders,
    bindBottomNavigationItem: bindChildren
  };
}
export {
  BOTTOM_NAVIGATION_BIND_BOTTOM_NAVIGATION_ITEM_KEY,
  useBottomNavigationItems
};
