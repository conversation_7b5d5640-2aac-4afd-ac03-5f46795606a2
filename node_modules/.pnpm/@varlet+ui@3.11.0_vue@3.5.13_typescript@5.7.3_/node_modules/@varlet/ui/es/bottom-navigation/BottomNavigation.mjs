var __defProp = Object.defineProperty;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
import { computed, defineComponent, onUpdated, ref, watch } from "vue";
import { call, getRect, isNumber, normalizeToArray } from "@varlet/shared";
import { onSmartMounted, onWindowResize } from "@varlet/use";
import VarButton from "../button/index.mjs";
import { createNamespace } from "../utils/components.mjs";
import { toSizeUnit } from "../utils/elements.mjs";
import { props } from "./props.mjs";
import { useBottomNavigationItems } from "./provide.mjs";
const { name, n, classes } = createNamespace("bottom-navigation");
const { n: nItem } = createNamespace("bottom-navigation-item");
const RIGHT_HALF_SPACE_CLASS = nItem("--right-half-space");
const LEFT_HALF_SPACE_CLASS = nItem("--left-half-space");
const RIGHT_SPACE_CLASS = nItem("--right-space");
const defaultFabProps = {
  type: "primary"
};
import { renderSlot as _renderSlot, resolveComponent as _resolveComponent, mergeProps as _mergeProps, withCtx as _withCtx, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, normalizeClass as _normalizeClass, normalizeStyle as _normalizeStyle, createElementBlock as _createElementBlock, Fragment as _Fragment } from "vue";
function __render__(_ctx, _cache) {
  const _component_var_button = _resolveComponent("var-button");
  return _openBlock(), _createElementBlock(
    _Fragment,
    null,
    [
      _createElementVNode(
        "div",
        _mergeProps({
          ref: "bottomNavigationDom",
          class: _ctx.classes(
            _ctx.n(),
            _ctx.n("$--box"),
            [_ctx.fixed, _ctx.n("--fixed")],
            [_ctx.border, _ctx.n("--border")],
            [_ctx.safeArea, _ctx.n("--safe-area")],
            [_ctx.variant, _ctx.n("--variant")]
          ),
          style: `z-index:${_ctx.zIndex}`
        }, _ctx.$attrs),
        [
          _renderSlot(_ctx.$slots, "default"),
          _ctx.$slots.fab ? (_openBlock(), _createBlock(_component_var_button, _mergeProps({
            key: 0,
            class: _ctx.classes(_ctx.n("fab"), [_ctx.length % 2, _ctx.n("--fab-right"), _ctx.n("--fab-center")]),
            "var-bottom-navigation__fab": ""
          }, _ctx.fabProps, { onClick: _ctx.handleFabClick }), {
            default: _withCtx(() => [
              _renderSlot(_ctx.$slots, "fab")
            ]),
            _: 3
            /* FORWARDED */
          }, 16, ["class", "onClick"])) : _createCommentVNode("v-if", true)
        ],
        16
        /* FULL_PROPS */
      ),
      _ctx.fixed && _ctx.placeholder ? (_openBlock(), _createElementBlock(
        "div",
        {
          key: 0,
          class: _normalizeClass(_ctx.n("placeholder")),
          style: _normalizeStyle({ height: _ctx.placeholderHeight })
        },
        null,
        6
        /* CLASS, STYLE */
      )) : _createCommentVNode("v-if", true)
    ],
    64
    /* STABLE_FRAGMENT */
  );
}
const __sfc__ = defineComponent({
  name,
  components: { VarButton },
  props,
  setup(props2, { slots }) {
    const bottomNavigationDom = ref(null);
    const active = computed(() => props2.active);
    const activeColor = computed(() => props2.activeColor);
    const inactiveColor = computed(() => props2.inactiveColor);
    const variant = computed(() => props2.variant);
    const placeholderHeight = ref();
    const fabProps = ref({});
    const { length, bottomNavigationItems, bindBottomNavigationItem } = useBottomNavigationItems();
    const bottomNavigationProvider = {
      active,
      activeColor,
      inactiveColor,
      variant,
      onToggle
    };
    bindBottomNavigationItem(bottomNavigationProvider);
    watch(() => length.value, matchBoundary);
    watch(
      () => props2.fabProps,
      (newValue) => {
        fabProps.value = __spreadValues(__spreadValues({}, defaultFabProps), newValue);
      },
      { immediate: true, deep: true }
    );
    onWindowResize(resizePlaceholder);
    onSmartMounted(() => {
      resizePlaceholder();
      if (!slots.fab) {
        return;
      }
      addMarginClass(length.value);
    });
    onUpdated(() => {
      removeMarginClass();
      if (!slots.fab) {
        return;
      }
      addMarginClass(length.value);
    });
    function matchBoundary() {
      if (length.value === 0 || matchName() || matchIndex()) {
        return;
      }
      handleActiveIndex();
    }
    function matchName() {
      return bottomNavigationItems.find(({ name: name2 }) => active.value === name2.value);
    }
    function matchIndex() {
      return bottomNavigationItems.find(({ index }) => active.value === index.value);
    }
    function handleActiveIndex() {
      if (!isNumber(active.value)) {
        return;
      }
      if (active.value < 0) {
        call(props2["onUpdate:active"], 0);
      } else if (active.value > length.value - 1) {
        call(props2["onUpdate:active"], length.value - 1);
      }
    }
    function onToggle(changedValue) {
      if (active.value === changedValue) {
        return;
      }
      props2.onBeforeChange ? handleBeforeChange(changedValue) : handleChange(changedValue);
    }
    function handleBeforeChange(changedValue) {
      const results = normalizeToArray(call(props2.onBeforeChange, changedValue));
      Promise.all(results).then((results2) => {
        if (results2.every(Boolean)) {
          handleChange(changedValue);
        }
      });
    }
    function handleChange(changedValue) {
      call(props2["onUpdate:active"], changedValue);
      call(props2.onChange, changedValue);
    }
    function removeMarginClass() {
      const bottomNavigationItems2 = getBottomNavigationItems();
      bottomNavigationItems2.forEach((dom) => {
        dom.classList.remove(RIGHT_HALF_SPACE_CLASS, LEFT_HALF_SPACE_CLASS, RIGHT_SPACE_CLASS);
      });
    }
    function addMarginClass(length2) {
      const bottomNavigationItems2 = getBottomNavigationItems();
      const itemsNum = bottomNavigationItems2.length;
      const isEven = length2 % 2 === 0;
      bottomNavigationItems2.forEach((bottomNavigationItem, i) => {
        handleMarginClass(isEven, bottomNavigationItem, i, itemsNum);
      });
    }
    function handleMarginClass(isEven, dom, i, length2) {
      const isLast = i === length2 - 1;
      if (!isEven && isLast) {
        dom.classList.add(RIGHT_SPACE_CLASS);
        return;
      }
      const isFabLeft = i === length2 / 2 - 1;
      const isFabRight = i === length2 / 2;
      if (isFabLeft) {
        dom.classList.add(RIGHT_HALF_SPACE_CLASS);
      } else if (isFabRight) {
        dom.classList.add(LEFT_HALF_SPACE_CLASS);
      }
    }
    function getBottomNavigationItems() {
      return Array.from(bottomNavigationDom.value.querySelectorAll(`.${nItem()}`));
    }
    function handleFabClick() {
      call(props2.onFabClick);
    }
    function resizePlaceholder() {
      if (!props2.fixed || !props2.placeholder) {
        return;
      }
      const { height } = getRect(bottomNavigationDom.value);
      placeholderHeight.value = toSizeUnit(height);
    }
    return {
      length,
      bottomNavigationDom,
      fabProps,
      placeholderHeight,
      n,
      classes,
      handleFabClick
    };
  }
});
__sfc__.render = __render__;
var stdin_default = __sfc__;
export {
  stdin_default as default
};
