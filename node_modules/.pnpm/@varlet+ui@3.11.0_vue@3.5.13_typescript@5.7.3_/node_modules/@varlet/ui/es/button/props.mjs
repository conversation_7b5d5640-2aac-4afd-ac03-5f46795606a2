var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
import { loadingProps } from "../loading/index.mjs";
import { defineListenerProp, pickProps } from "../utils/components.mjs";
const props = {
  type: String,
  nativeType: {
    type: String,
    default: "button"
  },
  size: String,
  loading: Boolean,
  round: Boolean,
  block: Boolean,
  text: Boolean,
  outline: Boolean,
  disabled: Boolean,
  autoLoading: Boolean,
  iconContainer: Boolean,
  ripple: {
    type: Boolean,
    default: true
  },
  focusable: {
    type: Boolean,
    default: true
  },
  color: String,
  textColor: String,
  elevation: {
    type: [Boolean, Number, String],
    default: true
  },
  loadingRadius: [Number, String],
  loadingType: pickProps(loadingProps, "type"),
  loadingSize: __spreadProps(__spreadValues({}, pickProps(loadingProps, "size")), {
    default: void 0
  }),
  loadingColor: __spreadProps(__spreadValues({}, pickProps(loadingProps, "color")), {
    default: "currentColor"
  }),
  onClick: defineListenerProp(),
  onTouchstart: defineListenerProp()
};
export {
  props
};
