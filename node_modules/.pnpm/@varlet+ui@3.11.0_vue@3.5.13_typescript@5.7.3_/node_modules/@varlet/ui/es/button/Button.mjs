import { computed, defineComponent, ref } from "vue";
import { call, normalizeToArray } from "@varlet/shared";
import Hover from "../hover/index.mjs";
import VarHoverOverlay, { useHoverOverlay } from "../hover-overlay/index.mjs";
import VarLoading from "../loading/index.mjs";
import Ripple from "../ripple/index.mjs";
import { createNamespace, formatElevation } from "../utils/components.mjs";
import { props } from "./props.mjs";
import { useButtonGroup } from "./provide.mjs";
const { name, n, classes } = createNamespace("button");
import { resolveComponent as _resolveComponent, normalizeClass as _normalizeClass, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, renderSlot as _renderSlot, createElementVNode as _createElementVNode, createVNode as _createVNode, normalizeStyle as _normalizeStyle, resolveDirective as _resolveDirective, createElementBlock as _createElementBlock, withDirectives as _withDirectives } from "vue";
const _hoisted_1 = ["tabindex", "type", "disabled"];
function __render__(_ctx, _cache) {
  const _component_var_loading = _resolveComponent("var-loading");
  const _component_var_hover_overlay = _resolveComponent("var-hover-overlay");
  const _directive_ripple = _resolveDirective("ripple");
  const _directive_hover = _resolveDirective("hover");
  return _withDirectives((_openBlock(), _createElementBlock("button", {
    class: _normalizeClass(
      _ctx.classes(
        _ctx.n(),
        _ctx.n("$--box"),
        _ctx.n(`--${_ctx.states.size}`),
        [_ctx.block, `${_ctx.n("$--flex")} ${_ctx.n("--block")}`, _ctx.n("$--inline-flex")],
        [!_ctx.states.text, _ctx.states.elevation],
        [!_ctx.states.iconContainer && !_ctx.states.text, _ctx.n(`--${_ctx.states.type}`)],
        [_ctx.states.text, `${_ctx.n("--text")} ${_ctx.n(`--text-${_ctx.states.type}`)}`],
        [_ctx.states.iconContainer, _ctx.n(`--icon-container-${_ctx.states.type}`)],
        [_ctx.round, _ctx.n("--round")],
        [_ctx.states.outline, _ctx.n("--outline")],
        [_ctx.loading || _ctx.pending, _ctx.n("--loading")],
        [_ctx.disabled, _ctx.n("--disabled")],
        [_ctx.states.text && _ctx.disabled, _ctx.n("--text-disabled")]
      )
    ),
    tabindex: _ctx.focusable ? void 0 : "-1",
    style: _normalizeStyle({
      color: _ctx.states.textColor,
      background: _ctx.states.color
    }),
    type: _ctx.nativeType,
    disabled: _ctx.disabled,
    onClick: _cache[0] || (_cache[0] = (...args) => _ctx.handleClick && _ctx.handleClick(...args)),
    onTouchstart: _cache[1] || (_cache[1] = (...args) => _ctx.handleTouchstart && _ctx.handleTouchstart(...args)),
    onFocus: _cache[2] || (_cache[2] = (...args) => _ctx.handleFocus && _ctx.handleFocus(...args)),
    onBlur: _cache[3] || (_cache[3] = ($event) => _ctx.isFocusing = false)
  }, [
    _ctx.loading || _ctx.pending ? (_openBlock(), _createBlock(_component_var_loading, {
      key: 0,
      class: _normalizeClass(_ctx.n("loading")),
      "var-button-cover": "",
      color: _ctx.loadingColor,
      type: _ctx.loadingType,
      size: _ctx.loadingSize || _ctx.states.size,
      radius: _ctx.loadingRadius
    }, null, 8, ["class", "color", "type", "size", "radius"])) : _createCommentVNode("v-if", true),
    _createElementVNode(
      "div",
      {
        class: _normalizeClass(_ctx.classes(_ctx.n("content"), [_ctx.loading || _ctx.pending, _ctx.n("--hidden")]))
      },
      [
        _renderSlot(_ctx.$slots, "default")
      ],
      2
      /* CLASS */
    ),
    _createVNode(_component_var_hover_overlay, {
      hovering: _ctx.disabled || _ctx.loading || _ctx.pending ? false : _ctx.hovering,
      focusing: _ctx.disabled || _ctx.loading || _ctx.pending ? false : _ctx.isFocusing
    }, null, 8, ["hovering", "focusing"])
  ], 46, _hoisted_1)), [
    [_directive_ripple, { disabled: _ctx.disabled || !_ctx.ripple || _ctx.loading || _ctx.pending }],
    [_directive_hover, _ctx.handleHovering, "desktop"]
  ]);
}
const __sfc__ = defineComponent({
  name,
  components: {
    VarLoading,
    VarHoverOverlay
  },
  directives: { Ripple, Hover },
  props,
  setup(props2) {
    const isFocusing = ref(false);
    const pending = ref(false);
    const { buttonGroup } = useButtonGroup();
    const { hovering, handleHovering } = useHoverOverlay();
    const states = computed(() => {
      var _a, _b, _c, _d, _e, _f;
      if (!buttonGroup) {
        return {
          elevation: formatElevation(props2.elevation, 2),
          type: (_a = props2.type) != null ? _a : "default",
          size: (_b = props2.size) != null ? _b : "normal",
          color: props2.color,
          text: props2.text,
          textColor: props2.textColor,
          outline: props2.outline,
          iconContainer: props2.iconContainer
        };
      }
      const { type, size, color, textColor, mode } = buttonGroup;
      return {
        elevation: "",
        type: (_c = props2.type) != null ? _c : type.value,
        size: (_d = props2.size) != null ? _d : size.value,
        color: (_e = props2.color) != null ? _e : color.value,
        textColor: (_f = props2.textColor) != null ? _f : textColor.value,
        text: mode.value === "text" || mode.value === "outline",
        outline: mode.value === "outline",
        iconContainer: mode.value === "icon-container"
      };
    });
    function attemptAutoLoading(result) {
      if (props2.autoLoading) {
        pending.value = true;
        Promise.all(normalizeToArray(result)).then(() => {
          pending.value = false;
        }).catch(() => {
          pending.value = false;
        });
      }
    }
    function handleClick(e) {
      const { loading, disabled, onClick } = props2;
      if (!onClick || loading || disabled || pending.value) {
        return;
      }
      attemptAutoLoading(call(onClick, e));
    }
    function handleTouchstart(e) {
      const { loading, disabled, onTouchstart } = props2;
      if (!onTouchstart || loading || disabled || pending.value) {
        return;
      }
      attemptAutoLoading(call(onTouchstart, e));
    }
    function handleFocus() {
      if (!props2.focusable) {
        return;
      }
      isFocusing.value = true;
    }
    return {
      pending,
      states,
      hovering,
      isFocusing,
      n,
      classes,
      handleHovering,
      handleClick,
      handleTouchstart,
      handleFocus
    };
  }
});
__sfc__.render = __render__;
var stdin_default = __sfc__;
export {
  stdin_default as default
};
