import { defineListenerProp } from "../utils/components.mjs";
const props = {
  src: String,
  fit: {
    type: String,
    default: "cover"
  },
  imageHeight: [String, Number],
  imageWidth: [String, Number],
  variant: {
    type: String,
    default: "standard"
  },
  layout: {
    type: String,
    default: "column"
  },
  floating: Boolean,
  floatingDuration: {
    type: Number,
    default: 250
  },
  alt: String,
  title: String,
  subtitle: String,
  description: String,
  elevation: {
    type: [Boolean, Number, String],
    default: true
  },
  ripple: Boolean,
  onClick: defineListenerProp(),
  "onUpdate:floating": defineListenerProp(),
  /**
   * @deprecated use outlined variant instead
   */
  outline: Boolean
};
export {
  props
};
