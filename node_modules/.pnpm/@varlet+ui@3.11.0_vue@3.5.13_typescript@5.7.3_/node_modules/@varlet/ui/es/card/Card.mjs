var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
import { computed, defineComponent, nextTick, ref, watch } from "vue";
import { call, doubleRaf, getRect } from "@varlet/shared";
import VarButton from "../button/index.mjs";
import { useLock } from "../context/lock.mjs";
import { useZIndex } from "../context/zIndex.mjs";
import VarIcon from "../icon/index.mjs";
import Ripple from "../ripple/index.mjs";
import { createNamespace, formatElevation } from "../utils/components.mjs";
import { toSizeUnit } from "../utils/elements.mjs";
import { props } from "./props.mjs";
const { name, n, classes } = createNamespace("card");
const RIPPLE_DELAY = 500;
import { renderSlot as _renderSlot, normalizeClass as _normalizeClass, normalizeStyle as _normalizeStyle, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, withModifiers as _withModifiers, withCtx as _withCtx, resolveDirective as _resolveDirective, withDirectives as _withDirectives } from "vue";
const _hoisted_1 = ["src", "alt"];
function __render__(_ctx, _cache) {
  const _component_var_icon = _resolveComponent("var-icon");
  const _component_var_button = _resolveComponent("var-button");
  const _directive_ripple = _resolveDirective("ripple");
  return _withDirectives((_openBlock(), _createElementBlock(
    "div",
    {
      ref: "card",
      class: _normalizeClass(
        _ctx.classes(
          _ctx.n(),
          [_ctx.isRow, _ctx.n("--layout-row")],
          [_ctx.variant === "outlined" || _ctx.outline, _ctx.n("--outline")],
          [_ctx.variant === "filled", _ctx.n("--filled")],
          [_ctx.variant === "standard" || _ctx.outline, _ctx.formatElevation(_ctx.elevation, 1)]
        )
      ),
      style: _normalizeStyle({
        zIndex: _ctx.floated ? _ctx.zIndex : void 0
      }),
      onClick: _cache[0] || (_cache[0] = (...args) => _ctx.handleClick && _ctx.handleClick(...args))
    },
    [
      _createElementVNode(
        "div",
        {
          ref: "cardFloater",
          class: _normalizeClass(_ctx.n("floater")),
          style: _normalizeStyle({
            width: _ctx.floaterWidth,
            height: _ctx.floaterHeight,
            top: _ctx.floaterTop,
            left: _ctx.floaterLeft,
            overflow: _ctx.floaterOverflow,
            position: _ctx.floaterPosition,
            zIndex: _ctx.floated ? _ctx.zIndex : void 0,
            background: _ctx.outline && !_ctx.floated ? "transparent" : void 0,
            transition: _ctx.floated ? `background-color ${_ctx.floatingDuration}ms, color ${_ctx.floatingDuration}ms, width ${_ctx.floatingDuration}ms, height ${_ctx.floatingDuration}ms, top ${_ctx.floatingDuration}ms, left ${_ctx.floatingDuration}ms` : void 0
          })
        },
        [
          _renderSlot(_ctx.$slots, "image", {}, () => [
            _ctx.src ? (_openBlock(), _createElementBlock("img", {
              key: 0,
              class: _normalizeClass(_ctx.n("image")),
              style: _normalizeStyle({
                objectFit: _ctx.fit,
                height: _ctx.toSizeUnit(_ctx.imageHeight),
                width: _ctx.toSizeUnit(_ctx.imageWidth)
              }),
              src: _ctx.src,
              alt: _ctx.alt
            }, null, 14, _hoisted_1)) : _createCommentVNode("v-if", true)
          ]),
          _createElementVNode(
            "div",
            {
              class: _normalizeClass(_ctx.n("container"))
            },
            [
              _renderSlot(_ctx.$slots, "title", {
                slotClass: _ctx.n("title")
              }, () => [
                _ctx.title ? (_openBlock(), _createElementBlock(
                  "div",
                  {
                    key: 0,
                    class: _normalizeClass(_ctx.n("title"))
                  },
                  _toDisplayString(_ctx.title),
                  3
                  /* TEXT, CLASS */
                )) : _createCommentVNode("v-if", true)
              ]),
              _renderSlot(_ctx.$slots, "subtitle", {
                slotClass: _ctx.n("subtitle")
              }, () => [
                _ctx.subtitle ? (_openBlock(), _createElementBlock(
                  "div",
                  {
                    key: 0,
                    class: _normalizeClass(_ctx.n("subtitle"))
                  },
                  _toDisplayString(_ctx.subtitle),
                  3
                  /* TEXT, CLASS */
                )) : _createCommentVNode("v-if", true)
              ]),
              _ctx.$slots.default ? (_openBlock(), _createElementBlock(
                "div",
                {
                  key: 0,
                  class: _normalizeClass(_ctx.n("content"))
                },
                [
                  _renderSlot(_ctx.$slots, "default")
                ],
                2
                /* CLASS */
              )) : _createCommentVNode("v-if", true),
              _renderSlot(_ctx.$slots, "description", {
                slotClass: _ctx.n("description")
              }, () => [
                _ctx.description ? (_openBlock(), _createElementBlock(
                  "div",
                  {
                    key: 0,
                    class: _normalizeClass(_ctx.n("description"))
                  },
                  _toDisplayString(_ctx.description),
                  3
                  /* TEXT, CLASS */
                )) : _createCommentVNode("v-if", true)
              ]),
              _ctx.$slots.extra ? (_openBlock(), _createElementBlock(
                "div",
                {
                  key: 1,
                  class: _normalizeClass(_ctx.n("footer"))
                },
                [
                  _renderSlot(_ctx.$slots, "extra")
                ],
                2
                /* CLASS */
              )) : _createCommentVNode("v-if", true),
              _ctx.$slots["floating-content"] && !_ctx.isRow ? (_openBlock(), _createElementBlock(
                "div",
                {
                  key: 2,
                  class: _normalizeClass(_ctx.n("floating-content")),
                  style: _normalizeStyle({
                    height: _ctx.contentHeight,
                    opacity: _ctx.opacity,
                    transition: `opacity ${_ctx.floatingDuration * 2}ms`
                  })
                },
                [
                  _renderSlot(_ctx.$slots, "floating-content")
                ],
                6
                /* CLASS, STYLE */
              )) : _createCommentVNode("v-if", true)
            ],
            2
            /* CLASS */
          ),
          _ctx.showFloatingButtons ? (_openBlock(), _createElementBlock(
            "div",
            {
              key: 0,
              class: _normalizeClass(_ctx.classes(_ctx.n("floating-buttons"), _ctx.n("$--box"))),
              style: _normalizeStyle({
                zIndex: _ctx.zIndex,
                opacity: _ctx.opacity,
                transition: `opacity ${_ctx.floatingDuration * 2}ms`
              })
            },
            [
              _renderSlot(_ctx.$slots, "close-button", {}, () => [
                _createVNode(_component_var_button, {
                  "var-card-cover": "",
                  class: _normalizeClass(_ctx.classes(_ctx.n("close-button"), _ctx.n("$-elevation--6"))),
                  onClick: _withModifiers(_ctx.close, ["stop"])
                }, {
                  default: _withCtx(() => [
                    _createVNode(_component_var_icon, {
                      "var-card-cover": "",
                      name: "window-close",
                      class: _normalizeClass(_ctx.n("close-button-icon"))
                    }, null, 8, ["class"])
                  ]),
                  _: 1
                  /* STABLE */
                }, 8, ["class", "onClick"])
              ])
            ],
            6
            /* CLASS, STYLE */
          )) : _createCommentVNode("v-if", true)
        ],
        6
        /* CLASS, STYLE */
      ),
      _createElementVNode(
        "div",
        {
          class: _normalizeClass(_ctx.n("holder")),
          style: _normalizeStyle({
            width: _ctx.holderWidth,
            height: _ctx.holderHeight
          })
        },
        null,
        6
        /* CLASS, STYLE */
      )
    ],
    6
    /* CLASS, STYLE */
  )), [
    [_directive_ripple, { disabled: !_ctx.ripple || _ctx.floater }]
  ]);
}
const __sfc__ = defineComponent({
  name,
  directives: { Ripple },
  components: {
    VarIcon,
    VarButton
  },
  props,
  setup(props2) {
    const card = ref(null);
    const cardFloater = ref(null);
    const holderWidth = ref("auto");
    const holderHeight = ref("auto");
    const floaterWidth = ref("100%");
    const floaterHeight = ref("100%");
    const floaterTop = ref("auto");
    const floaterLeft = ref("auto");
    const floaterPosition = ref(void 0);
    const floaterOverflow = ref("hidden");
    const contentHeight = ref("0px");
    const opacity = ref("0");
    const isRow = computed(() => props2.layout === "row");
    const showFloatingButtons = ref(false);
    const floated = ref(false);
    const { zIndex } = useZIndex(() => props2.floating, 1);
    let dropdownFloaterTop = "auto";
    let dropdownFloaterLeft = "auto";
    let dropper = null;
    const floater = ref(null);
    useLock(
      () => props2.floating,
      () => !isRow.value
    );
    watch(
      () => props2.floating,
      (value) => {
        if (isRow.value) {
          return;
        }
        nextTick(() => {
          value ? floating() : dropdown();
        });
      },
      { immediate: true }
    );
    function floating() {
      clearTimeout(floater.value);
      clearTimeout(dropper);
      floater.value = null;
      floater.value = setTimeout(
        () => __async(this, null, function* () {
          const { width, height, left, top } = getRect(card.value);
          holderWidth.value = toSizeUnit(width);
          holderHeight.value = toSizeUnit(height);
          floaterWidth.value = holderWidth.value;
          floaterHeight.value = holderHeight.value;
          floaterTop.value = toSizeUnit(top);
          floaterLeft.value = toSizeUnit(left);
          floaterPosition.value = "fixed";
          dropdownFloaterTop = floaterTop.value;
          dropdownFloaterLeft = floaterLeft.value;
          showFloatingButtons.value = true;
          yield doubleRaf();
          floaterTop.value = "0";
          floaterLeft.value = "0";
          floaterWidth.value = "100vw";
          floaterHeight.value = "100vh";
          contentHeight.value = "auto";
          opacity.value = "1";
          floaterOverflow.value = "auto";
          floated.value = true;
        }),
        props2.ripple ? RIPPLE_DELAY : 0
      );
    }
    function dropdown() {
      clearTimeout(dropper);
      clearTimeout(floater.value);
      floater.value = null;
      floaterWidth.value = holderWidth.value;
      floaterHeight.value = holderHeight.value;
      floaterTop.value = dropdownFloaterTop;
      floaterLeft.value = dropdownFloaterLeft;
      contentHeight.value = "0px";
      opacity.value = "0";
      showFloatingButtons.value = false;
      dropper = setTimeout(() => {
        holderWidth.value = "auto";
        holderHeight.value = "auto";
        floaterWidth.value = "100%";
        floaterHeight.value = "100%";
        floaterTop.value = "auto";
        floaterLeft.value = "auto";
        dropdownFloaterTop = "auto";
        dropdownFloaterLeft = "auto";
        floaterOverflow.value = "hidden";
        floaterPosition.value = void 0;
        floated.value = false;
      }, props2.floatingDuration);
    }
    function close() {
      call(props2["onUpdate:floating"], false);
    }
    function handleClick(e) {
      call(props2.onClick, e);
    }
    return {
      card,
      cardFloater,
      holderWidth,
      holderHeight,
      floater,
      floaterWidth,
      floaterHeight,
      floaterTop,
      floaterLeft,
      floaterPosition,
      floaterOverflow,
      contentHeight,
      opacity,
      zIndex,
      isRow,
      showFloatingButtons,
      floated,
      n,
      classes,
      toSizeUnit,
      close,
      formatElevation,
      handleClick
    };
  }
});
__sfc__.render = __render__;
var stdin_default = __sfc__;
export {
  stdin_default as default
};
