import { defineListenerProp } from "../utils/components.mjs";
const props = {
  round: {
    type: Boolean,
    default: true
  },
  size: {
    type: [String, Number],
    default: "normal"
  },
  alt: String,
  color: String,
  src: String,
  fit: {
    type: String,
    default: "cover"
  },
  bordered: Boolean,
  borderColor: String,
  loading: String,
  error: String,
  lazy: Boolean,
  hoverable: Boolean,
  onClick: defineListenerProp(),
  onLoad: defineListenerProp(),
  onError: defineListenerProp()
};
export {
  props
};
