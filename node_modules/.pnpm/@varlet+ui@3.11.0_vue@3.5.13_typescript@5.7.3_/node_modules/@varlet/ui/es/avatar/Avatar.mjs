import { defineComponent, onUpdated, ref } from "vue";
import { call } from "@varlet/shared";
import { onSmartMounted } from "@varlet/use";
import Lazy from "../lazy/index.mjs";
import { createNamespace } from "../utils/components.mjs";
import { toSizeUnit } from "../utils/elements.mjs";
import { props } from "./props.mjs";
const isInternalSize = (size) => ["mini", "small", "normal", "large"].includes(size);
const { name, n, classes } = createNamespace("avatar");
import { normalizeClass as _normalizeClass, normalizeStyle as _normalizeStyle, resolveDirective as _resolveDirective, withDirectives as _withDirectives, openBlock as _openBlock, createElementBlock as _createElementBlock, Fragment as _Fragment, renderSlot as _renderSlot } from "vue";
const _hoisted_1 = ["src", "alt", "lazy-loading", "lazy-error"];
const _hoisted_2 = ["src", "alt"];
function __render__(_ctx, _cache) {
  const _directive_lazy = _resolveDirective("lazy");
  return _openBlock(), _createElementBlock(
    "div",
    {
      ref: "avatarElement",
      class: _normalizeClass(
        _ctx.classes(
          _ctx.n(),
          _ctx.n("$--box"),
          [_ctx.isInternalSize(_ctx.size), _ctx.n(`--${_ctx.size}`)],
          [_ctx.round, _ctx.n("--round")],
          [_ctx.bordered, _ctx.n("--bordered")],
          [_ctx.hoverable, _ctx.n("--hoverable")]
        )
      ),
      style: _normalizeStyle({
        width: !_ctx.isInternalSize(_ctx.size) ? _ctx.toSizeUnit(_ctx.size) : void 0,
        height: !_ctx.isInternalSize(_ctx.size) ? _ctx.toSizeUnit(_ctx.size) : void 0,
        borderColor: _ctx.borderColor,
        backgroundColor: _ctx.color
      }),
      onClick: _cache[3] || (_cache[3] = (...args) => _ctx.handleClick && _ctx.handleClick(...args))
    },
    [
      _ctx.src ? (_openBlock(), _createElementBlock(
        _Fragment,
        { key: 0 },
        [
          _ctx.lazy ? _withDirectives((_openBlock(), _createElementBlock("img", {
            key: 0,
            role: "img",
            class: _normalizeClass(_ctx.n("image")),
            src: _ctx.src,
            alt: _ctx.alt,
            style: _normalizeStyle({ objectFit: _ctx.fit }),
            "lazy-loading": _ctx.loading,
            "lazy-error": _ctx.error,
            onLoad: _cache[0] || (_cache[0] = (...args) => _ctx.handleLoad && _ctx.handleLoad(...args))
          }, null, 46, _hoisted_1)), [
            [_directive_lazy, _ctx.src]
          ]) : (_openBlock(), _createElementBlock("img", {
            key: 1,
            role: "img",
            class: _normalizeClass(_ctx.n("image")),
            src: _ctx.src,
            alt: _ctx.alt,
            style: _normalizeStyle({ objectFit: _ctx.fit }),
            onLoad: _cache[1] || (_cache[1] = (...args) => _ctx.handleLoad && _ctx.handleLoad(...args)),
            onError: _cache[2] || (_cache[2] = (...args) => _ctx.handleError && _ctx.handleError(...args))
          }, null, 46, _hoisted_2))
        ],
        64
        /* STABLE_FRAGMENT */
      )) : (_openBlock(), _createElementBlock(
        "div",
        {
          key: 1,
          ref: "textElement",
          class: _normalizeClass(_ctx.n("text")),
          style: _normalizeStyle({ transform: `scale(${_ctx.scale})` })
        },
        [
          _renderSlot(_ctx.$slots, "default")
        ],
        6
        /* CLASS, STYLE */
      ))
    ],
    6
    /* CLASS, STYLE */
  );
}
const __sfc__ = defineComponent({
  name,
  directives: { Lazy },
  props,
  setup(props2) {
    const avatarElement = ref(null);
    const textElement = ref(null);
    const scale = ref(1);
    onSmartMounted(getScale);
    onUpdated(getScale);
    function getScale() {
      if (!avatarElement.value || !textElement.value) {
        scale.value = 1;
        return;
      }
      const avatarElementWidth = avatarElement.value.offsetWidth;
      const textElementWidth = textElement.value.offsetWidth;
      if (avatarElementWidth > textElementWidth) {
        scale.value = 1;
      } else {
        scale.value = avatarElementWidth / textElementWidth;
      }
    }
    function handleLoad(e) {
      const el = e.currentTarget;
      const { lazy, onLoad, onError } = props2;
      if (lazy) {
        el._lazy.state === "success" && call(onLoad, e);
        el._lazy.state === "error" && call(onError, e);
      } else {
        call(onLoad, e);
      }
    }
    function handleError(e) {
      call(props2.onError, e);
    }
    function handleClick(e) {
      call(props2.onClick, e);
    }
    return {
      avatarElement,
      textElement,
      scale,
      n,
      classes,
      isInternalSize,
      toSizeUnit,
      handleLoad,
      handleError,
      handleClick
    };
  }
});
__sfc__.render = __render__;
var stdin_default = __sfc__;
export {
  stdin_default as default
};
