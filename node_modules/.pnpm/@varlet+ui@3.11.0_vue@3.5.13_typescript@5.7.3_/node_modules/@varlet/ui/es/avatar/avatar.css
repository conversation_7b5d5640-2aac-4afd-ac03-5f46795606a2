:root { --avatar-text-color: #f5f5f5; --avatar-border-radius: 4px; --avatar-mini-size: 28px; --avatar-small-size: 36px; --avatar-normal-size: 48px; --avatar-large-size: 64px; --avatar-border: 2px solid #fff; --avatar-background-color: #bebebe; --avatar-hover-transform: scale(1.1);}.var-avatar { display: inline-flex; overflow: hidden; align-items: center; justify-content: center; color: var(--avatar-text-color); border-radius: var(--avatar-border-radius); background-color: var(--avatar-background-color); transition: background-color 0.25s; -webkit-tap-highlight-color: transparent;}.var-avatar img { width: 100%; height: 100%;}.var-avatar--mini { width: var(--avatar-mini-size); height: var(--avatar-mini-size);}.var-avatar--small { width: var(--avatar-small-size); height: var(--avatar-small-size);}.var-avatar--normal { width: var(--avatar-normal-size); height: var(--avatar-normal-size);}.var-avatar--large { width: var(--avatar-large-size); height: var(--avatar-large-size);}.var-avatar--round { border-radius: 50%;}.var-avatar--bordered { border: var(--avatar-border);}.var-avatar__text { padding: 0 4px; white-space: nowrap;}.var-avatar--hoverable { transition: transform 0.2s;}.var-avatar--hoverable:hover { transform: var(--avatar-hover-transform); cursor: pointer;}