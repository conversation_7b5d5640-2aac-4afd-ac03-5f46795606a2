import { nextTick, reactive } from "vue";
import { call, isArray } from "@varlet/shared";
import { mountInstance, withInstall, withPropsDefaultsSetter } from "../utils/components.mjs";
import VarPicker from "./Picker.mjs";
import { props as pickerProps } from "./props.mjs";
let singletonOptions;
function Picker(options) {
  return new Promise((resolve) => {
    Picker.close();
    const pickerOptions = isArray(options) ? { columns: options } : options;
    const reactivePickerOptions = reactive(pickerOptions);
    reactivePickerOptions.dynamic = true;
    reactivePickerOptions.teleport = "body";
    singletonOptions = reactivePickerOptions;
    function resetSingletonOptions() {
      singletonOptions === reactivePickerOptions && (singletonOptions = null);
    }
    const { unmountInstance } = mountInstance(VarPicker, reactivePickerOptions, {
      onConfirm: (values, indexes, options2) => {
        call(reactivePickerOptions.onConfirm, values, indexes, options2);
        resolve({
          state: "confirm",
          values,
          indexes,
          options: options2
        });
        reactivePickerOptions.show = false;
        resetSingletonOptions();
      },
      onCancel: (values, indexes, options2) => {
        call(reactivePickerOptions.onCancel, values, indexes, options2);
        resolve({
          state: "cancel",
          values,
          indexes,
          options: options2
        });
        reactivePickerOptions.show = false;
        resetSingletonOptions();
      },
      onClose: () => {
        call(reactivePickerOptions.onClose);
        resolve({
          state: "close"
        });
        resetSingletonOptions();
      },
      onClosed: () => {
        call(reactivePickerOptions.onClosed);
        unmountInstance();
        resetSingletonOptions();
      },
      onRouteChange: () => {
        unmountInstance();
        resetSingletonOptions();
      },
      "onUpdate:show": (value) => {
        reactivePickerOptions.show = value;
      }
    });
    reactivePickerOptions.show = true;
  });
}
Picker.close = function() {
  if (singletonOptions == null) {
    return;
  }
  const prevSingletonOptions = singletonOptions;
  singletonOptions = null;
  nextTick().then(() => {
    prevSingletonOptions.show = false;
  });
};
Picker.Component = VarPicker;
withInstall(VarPicker);
withInstall(VarPicker, Picker);
withPropsDefaultsSetter(Picker, pickerProps);
const _PickerComponent = VarPicker;
var stdin_default = Picker;
export {
  _PickerComponent,
  stdin_default as default,
  pickerProps
};
