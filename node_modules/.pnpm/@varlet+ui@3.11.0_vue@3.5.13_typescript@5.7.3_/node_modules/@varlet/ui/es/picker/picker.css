:root { --picker-background: #fff; --picker-toolbar-height: 44px; --picker-confirm-button-text-color: var(--color-primary); --picker-cancel-button-text-color: #888; --picker-picked-border: 1px solid var(--color-outline); --picker-title-font-size: var(--font-size-lg); --picker-title-text-color: #555; --picker-option-font-size: var(--font-size-lg); --picker-option-text-color: #555; --picker-toolbar-padding: 0 4px; --picker-mask-background-image: linear-gradient(180deg, hsla(0, 0%, 100%, 0.9), hsla(0, 0%, 100%, 0.4)), linear-gradient(0deg, hsla(0, 0%, 100%, 0.9), hsla(0, 0%, 100%, 0.4));}.var-picker { width: 100%; background: var(--picker-background); user-select: none; transition: 0.25s background-color;}.var-picker__popup[var-picker-cover] { width: 100%;}.var-picker__columns { position: relative; display: flex; width: 100%; cursor: grab;}.var-picker__column { flex: 1; overflow: hidden;}.var-picker__option { display: flex; justify-content: center; align-items: center; width: 100%; padding: 0 4px;}.var-picker__text { overflow: hidden; white-space: nowrap; text-overflow: ellipsis; color: var(--picker-option-text-color); font-size: var(--picker-option-font-size);}.var-picker__scroller { width: 100%; transition-timing-function: cubic-bezier(0.23, 1, 0.68, 1);}.var-picker__picked { position: absolute; z-index: 2; pointer-events: none; width: 100%; left: 0; border-top: var(--picker-picked-border); border-bottom: var(--picker-picked-border); transition: 0.25s border;}.var-picker__mask { position: absolute; pointer-events: none; top: 0; left: 0; width: 100%; height: 100%; background-image: var(--picker-mask-background-image); background-repeat: no-repeat; background-position: top, bottom;}.var-picker__toolbar { display: flex; justify-content: space-between; align-items: center; width: 100%; height: var(--picker-toolbar-height); padding: var(--picker-toolbar-padding);}.var-picker__confirm-button[var-picker-cover] { color: var(--picker-confirm-button-text-color);}.var-picker__cancel-button[var-picker-cover] { color: var(--picker-cancel-button-text-color);}.var-picker__title { color: var(--picker-title-text-color); font-size: var(--picker-title-font-size);}