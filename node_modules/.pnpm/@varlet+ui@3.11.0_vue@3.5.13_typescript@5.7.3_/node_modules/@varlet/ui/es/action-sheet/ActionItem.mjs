import { defineComponent } from "vue";
import Hover from "../hover/index.mjs";
import VarHoverOverlay, { useHoverOverlay } from "../hover-overlay/index.mjs";
import VarIcon from "../icon/index.mjs";
import Ripple from "../ripple/index.mjs";
import { createNamespace } from "../utils/components.mjs";
const { name, n, classes } = createNamespace("action-sheet");
import { resolveComponent as _resolveComponent, normalizeClass as _normalizeClass, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, createVNode as _createVNode, normalizeStyle as _normalizeStyle, resolveDirective as _resolveDirective, createElementBlock as _createElementBlock, withDirectives as _withDirectives } from "vue";
function __render__(_ctx, _cache) {
  const _component_var_icon = _resolveComponent("var-icon");
  const _component_var_hover_overlay = _resolveComponent("var-hover-overlay");
  const _directive_ripple = _resolveDirective("ripple");
  const _directive_hover = _resolveDirective("hover");
  return _withDirectives((_openBlock(), _createElementBlock(
    "div",
    {
      class: _normalizeClass(_ctx.classes(_ctx.n("action-item"), _ctx.className, [_ctx.disabled, _ctx.n("--disabled")])),
      style: _normalizeStyle({ color: _ctx.color })
    },
    [
      _ctx.icon ? (_openBlock(), _createBlock(_component_var_icon, {
        key: 0,
        "var-action-sheet-cover": "",
        class: _normalizeClass(_ctx.n("action-icon")),
        namespace: _ctx.namespace,
        name: _ctx.icon,
        size: _ctx.iconSize
      }, null, 8, ["class", "namespace", "name", "size"])) : _createCommentVNode("v-if", true),
      _createElementVNode(
        "div",
        {
          class: _normalizeClass(_ctx.n("action-name"))
        },
        _toDisplayString(_ctx.name),
        3
        /* TEXT, CLASS */
      ),
      _createVNode(_component_var_hover_overlay, {
        hovering: _ctx.disabled ? false : _ctx.hovering
      }, null, 8, ["hovering"])
    ],
    6
    /* CLASS, STYLE */
  )), [
    [_directive_ripple, { disabled: _ctx.disabled }],
    [_directive_hover, _ctx.handleHovering, "desktop"]
  ]);
}
const __sfc__ = defineComponent({
  name,
  components: {
    VarHoverOverlay,
    VarIcon
  },
  directives: { Ripple, Hover },
  props: {
    name: String,
    className: String,
    disabled: Boolean,
    color: String,
    namespace: String,
    iconSize: [String, Number],
    icon: String
  },
  setup() {
    const { hovering, handleHovering } = useHoverOverlay();
    return {
      hovering,
      n,
      classes,
      handleHovering
    };
  }
});
__sfc__.render = __render__;
var stdin_default = __sfc__;
export {
  stdin_default as default
};
