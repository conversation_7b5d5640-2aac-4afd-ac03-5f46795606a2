import { defineComponent } from "vue";
import { call } from "@varlet/shared";
import { useVModel } from "@varlet/use";
import { t } from "../locale/index.mjs";
import { injectLocaleProvider } from "../locale-provider/provide.mjs";
import VarPopup from "../popup/index.mjs";
import Ripple from "../ripple/index.mjs";
import { createNamespace } from "../utils/components.mjs";
import VarActionItem from "./ActionItem.mjs";
import { props } from "./props.mjs";
const { name, n, classes } = createNamespace("action-sheet");
import { renderSlot as _renderSlot, toDisplayString as _toDisplayString, normalizeClass as _normalizeClass, createElementVNode as _createElementVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, resolveComponent as _resolveComponent, createBlock as _createBlock, mergeProps as _mergeProps, withCtx as _withCtx } from "vue";
function __render__(_ctx, _cache) {
  const _component_var_action_item = _resolveComponent("var-action-item");
  const _component_var_popup = _resolveComponent("var-popup");
  return _openBlock(), _createBlock(_component_var_popup, {
    show: _ctx.show,
    "onUpdate:show": _cache[0] || (_cache[0] = ($event) => _ctx.show = $event),
    position: "bottom",
    class: _normalizeClass(_ctx.n("popup-radius")),
    overlay: _ctx.overlay,
    "overlay-class": _ctx.overlayClass,
    "overlay-style": _ctx.overlayStyle,
    "lock-scroll": _ctx.lockScroll,
    "close-on-click-overlay": _ctx.closeOnClickOverlay,
    "close-on-key-escape": _ctx.closeOnKeyEscape,
    teleport: _ctx.teleport,
    "safe-area": _ctx.safeArea,
    onOpen: _ctx.onOpen,
    onClose: _ctx.onClose,
    onClosed: _ctx.onClosed,
    onOpened: _ctx.onOpened,
    onRouteChange: _ctx.onRouteChange,
    onKeyEscape: _ctx.onKeyEscape
  }, {
    default: _withCtx(() => [
      _createElementVNode(
        "div",
        _mergeProps({
          class: _ctx.classes(_ctx.n(), _ctx.n("$--box"))
        }, _ctx.$attrs),
        [
          _renderSlot(_ctx.$slots, "title", {}, () => {
            var _a;
            return [
              _createElementVNode(
                "div",
                {
                  class: _normalizeClass(_ctx.n("title"))
                },
                _toDisplayString((_a = _ctx.title) != null ? _a : (_ctx.pt ? _ctx.pt : _ctx.t)("actionSheetTitle")),
                3
                /* TEXT, CLASS */
              )
            ];
          }),
          _renderSlot(_ctx.$slots, "actions", {}, () => [
            (_openBlock(true), _createElementBlock(
              _Fragment,
              null,
              _renderList(_ctx.actions, (action) => {
                return _openBlock(), _createBlock(_component_var_action_item, {
                  key: action.name,
                  name: action.name,
                  namespace: action.namespace,
                  icon: action.icon,
                  "icon-size": action.iconSize,
                  "class-name": action.className,
                  color: action.color,
                  onClick: ($event) => _ctx.handleSelect(action)
                }, null, 8, ["name", "namespace", "icon", "icon-size", "class-name", "color", "onClick"]);
              }),
              128
              /* KEYED_FRAGMENT */
            ))
          ])
        ],
        16
        /* FULL_PROPS */
      )
    ]),
    _: 3
    /* FORWARDED */
  }, 8, ["show", "class", "overlay", "overlay-class", "overlay-style", "lock-scroll", "close-on-click-overlay", "close-on-key-escape", "teleport", "safe-area", "onOpen", "onClose", "onClosed", "onOpened", "onRouteChange", "onKeyEscape"]);
}
const __sfc__ = defineComponent({
  name,
  directives: { Ripple },
  components: {
    VarPopup,
    VarActionItem
  },
  inheritAttrs: false,
  props,
  setup(props2) {
    const show = useVModel(props2, "show");
    const { t: pt } = injectLocaleProvider();
    function handleSelect(action) {
      if (action.disabled) {
        return;
      }
      const { closeOnClickAction, onSelect } = props2;
      call(onSelect, action);
      if (closeOnClickAction) {
        show.value = false;
      }
    }
    return {
      show,
      pt,
      t,
      n,
      classes,
      handleSelect
    };
  }
});
__sfc__.render = __render__;
var stdin_default = __sfc__;
export {
  stdin_default as default
};
