var __defProp = Object.defineProperty;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
import { nextTick, reactive } from "vue";
import { call, inBrowser } from "@varlet/shared";
import { mountInstance, withInstall, withPropsDefaultsSetter } from "../utils/components.mjs";
import VarActionSheet from "./ActionSheet.mjs";
import { props as actionSheetProps } from "./props.mjs";
let singletonOptions;
let defaultOptions = {};
function normalizeOptions(options = {}) {
  return __spreadValues(__spreadValues({}, defaultOptions), options);
}
function ActionSheet(options) {
  if (!inBrowser()) {
    return Promise.resolve();
  }
  return new Promise((resolve) => {
    ActionSheet.close();
    const reactiveActionSheetOptions = reactive(normalizeOptions(options));
    reactiveActionSheetOptions.teleport = "body";
    singletonOptions = reactiveActionSheetOptions;
    const { unmountInstance } = mountInstance(VarActionSheet, reactiveActionSheetOptions, {
      onSelect: (action) => {
        call(reactiveActionSheetOptions.onSelect, action);
        resolve(action);
      },
      onClose: () => {
        call(reactiveActionSheetOptions.onClose);
        resolve("close");
      },
      onClosed: () => {
        call(reactiveActionSheetOptions.onClosed);
        unmountInstance();
        singletonOptions === reactiveActionSheetOptions && (singletonOptions = null);
      },
      onRouteChange: () => {
        unmountInstance();
        singletonOptions === reactiveActionSheetOptions && (singletonOptions = null);
      },
      "onUpdate:show": (value) => {
        reactiveActionSheetOptions.show = value;
      }
    });
    reactiveActionSheetOptions.show = true;
  });
}
ActionSheet.setDefaultOptions = function(options) {
  defaultOptions = options;
};
ActionSheet.resetDefaultOptions = function() {
  defaultOptions = {};
};
ActionSheet.close = function() {
  if (singletonOptions != null) {
    const prevSingletonOptions = singletonOptions;
    singletonOptions = null;
    nextTick().then(() => {
      prevSingletonOptions.show = false;
    });
  }
};
ActionSheet.Component = VarActionSheet;
withInstall(VarActionSheet);
withInstall(VarActionSheet, ActionSheet);
withPropsDefaultsSetter(ActionSheet, actionSheetProps);
const _ActionSheetComponent = VarActionSheet;
var stdin_default = ActionSheet;
export {
  _ActionSheetComponent,
  actionSheetProps,
  stdin_default as default
};
