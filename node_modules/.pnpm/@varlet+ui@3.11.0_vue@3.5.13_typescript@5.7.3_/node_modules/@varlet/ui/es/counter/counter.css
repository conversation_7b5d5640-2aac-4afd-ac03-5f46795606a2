:root { --counter-padding: 0 4px; --counter-font-color: #fff; --counter-background: var(--color-primary); --counter-input-width: 28px; --counter-input-margin: 0 4px; --counter-input-font-size: 14px; --counter-button-size: 28px; --counter-button-text-color: #fff; --counter-button-icon-size: 100%; --counter-disabled-color: var(--color-disabled); --counter-disabled-opacity: var(--opacity-disabled); --counter-error-color: var(--color-danger);}.var-counter { display: inline-flex; flex-direction: column; align-items: flex-start;}.var-counter__controller { display: flex; align-items: center; color: var(--counter-font-color); border-radius: var(--counter-button-size); padding: var(--counter-padding); background: var(--counter-background); transition: color 0.25s, background-color 0.25s, opacity 0.25s;}.var-counter__input { width: var(--counter-input-width); font-size: var(--counter-input-font-size); outline: none; border: none; background: transparent; padding: 0; text-align: center; color: var(--counter-font-color); margin: var(--counter-input-margin);}.var-counter__input[disabled] { background: transparent;}.var-counter__increment-button[var-counter-cover],.var-counter__decrement-button[var-counter-cover] { width: var(--counter-button-size); height: var(--counter-button-size); font-size: var(--counter-button-icon-size); color: var(--counter-button-text-color); border-radius: 50%; padding: 0; background-color: transparent; box-shadow: none; -webkit-tap-highlight-color: transparent; -webkit-user-select: none; user-select: none;}.var-counter__increment-button[var-counter-cover]:active,.var-counter__decrement-button[var-counter-cover]:active { box-shadow: none;}.var-counter--disabled { opacity: var(--counter-disabled-opacity); cursor: not-allowed;}.var-counter--disabled .var-hover-overlay { opacity: 0;}.var-counter--not-allowed { cursor: not-allowed !important;}.var-counter--hidden { opacity: 0;}.var-counter--error { background: var(--counter-error-color);}