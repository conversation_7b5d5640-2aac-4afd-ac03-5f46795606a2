import { computed, defineComponent, nextTick, ref, watch } from "vue";
import { call, toNumber } from "@varlet/shared";
import { Decimal } from "decimal.js";
import VarButton from "../button/index.mjs";
import VarFormDetails from "../form-details/index.mjs";
import { useForm } from "../form/provide.mjs";
import VarIcon from "../icon/index.mjs";
import Ripple from "../ripple/index.mjs";
import { createNamespace, formatElevation, useValidation } from "../utils/components.mjs";
import { toSizeUnit } from "../utils/elements.mjs";
import { props } from "./props.mjs";
const SPEED = 100;
const DELAY = 600;
const { name, n, classes } = createNamespace("counter");
import { resolveComponent as _resolveComponent, createVNode as _createVNode, normalizeClass as _normalizeClass, normalizeStyle as _normalizeStyle, withCtx as _withCtx, vModelText as _vModelText, createElementVNode as _createElementVNode, withDirectives as _withDirectives, mergeProps as _mergeProps, openBlock as _openBlock, createElementBlock as _createElementBlock } from "vue";
const _hoisted_1 = ["inputmode", "readonly", "disabled"];
function __render__(_ctx, _cache) {
  const _component_var_icon = _resolveComponent("var-icon");
  const _component_var_button = _resolveComponent("var-button");
  const _component_var_form_details = _resolveComponent("var-form-details");
  return _openBlock(), _createElementBlock(
    "div",
    {
      class: _normalizeClass(_ctx.classes(_ctx.n(), _ctx.n("$--box")))
    },
    [
      _createElementVNode(
        "div",
        _mergeProps({
          class: _ctx.classes(
            _ctx.n("controller"),
            _ctx.formatElevation(_ctx.elevation, 2),
            [_ctx.disabled || _ctx.formDisabled, _ctx.n("--disabled")],
            [_ctx.errorMessage, _ctx.n("--error")]
          ),
          style: { background: _ctx.color }
        }, _ctx.$attrs),
        [
          _createVNode(_component_var_button, {
            class: _normalizeClass(
              _ctx.classes(
                _ctx.n("decrement-button"),
                [!_ctx.decrementButton, _ctx.n("--hidden")],
                [_ctx.disabled || _ctx.formDisabled, _ctx.n("--not-allowed")]
              )
            ),
            style: _normalizeStyle({
              width: _ctx.toSizeUnit(_ctx.buttonSize),
              height: _ctx.toSizeUnit(_ctx.buttonSize)
            }),
            round: "",
            "var-counter-cover": "",
            ripple: _ctx.ripple && _ctx.decrementButton && !_ctx.disabled && !_ctx.formDisabled && !_ctx.readonly && !_ctx.formReadonly && !_ctx.disableDecrement && !_ctx.isMin,
            onClick: _ctx.decrement,
            onTouchstart: _ctx.pressDecrement,
            onTouchend: _ctx.releaseDecrement,
            onTouchcancel: _ctx.releaseDecrement
          }, {
            default: _withCtx(() => [
              _createVNode(_component_var_icon, { name: "minus" })
            ]),
            _: 1
            /* STABLE */
          }, 8, ["class", "style", "ripple", "onClick", "onTouchstart", "onTouchend", "onTouchcancel"]),
          _withDirectives(_createElementVNode("input", {
            "onUpdate:modelValue": _cache[0] || (_cache[0] = ($event) => _ctx.inputValue = $event),
            class: _normalizeClass(_ctx.classes(_ctx.n("input"), [_ctx.disabled || _ctx.formDisabled, _ctx.n("--not-allowed")])),
            style: _normalizeStyle({
              width: _ctx.toSizeUnit(_ctx.inputWidth),
              fontSize: _ctx.toSizeUnit(_ctx.inputTextSize)
            }),
            inputmode: _ctx.toNumber(_ctx.decimalLength) === 0 ? "numeric" : "decimal",
            readonly: _ctx.readonly || _ctx.formReadonly,
            disabled: _ctx.disabled || _ctx.formDisabled || _ctx.disableInput,
            onChange: _cache[1] || (_cache[1] = (...args) => _ctx.handleChange && _ctx.handleChange(...args))
          }, null, 46, _hoisted_1), [
            [_vModelText, _ctx.inputValue]
          ]),
          _createVNode(_component_var_button, {
            class: _normalizeClass(
              _ctx.classes(
                _ctx.n("increment-button"),
                [!_ctx.incrementButton, _ctx.n("--hidden")],
                [_ctx.disabled || _ctx.formDisabled, _ctx.n("--not-allowed")]
              )
            ),
            style: _normalizeStyle({
              width: _ctx.toSizeUnit(_ctx.buttonSize),
              height: _ctx.toSizeUnit(_ctx.buttonSize)
            }),
            round: "",
            "var-counter-cover": "",
            ripple: _ctx.ripple && _ctx.incrementButton && !_ctx.disabled && !_ctx.formDisabled && !_ctx.readonly && !_ctx.formReadonly && !_ctx.disableIncrement && !_ctx.isMax,
            onClick: _ctx.increment,
            onTouchstart: _ctx.pressIncrement,
            onTouchend: _ctx.releaseIncrement,
            onTouchcancel: _ctx.releaseIncrement
          }, {
            default: _withCtx(() => [
              _createVNode(_component_var_icon, { name: "plus" })
            ]),
            _: 1
            /* STABLE */
          }, 8, ["class", "style", "ripple", "onClick", "onTouchstart", "onTouchend", "onTouchcancel"])
        ],
        16
        /* FULL_PROPS */
      ),
      _createVNode(_component_var_form_details, { "error-message": _ctx.errorMessage }, null, 8, ["error-message"])
    ],
    2
    /* CLASS */
  );
}
const __sfc__ = defineComponent({
  name,
  components: {
    VarButton,
    VarIcon,
    VarFormDetails
  },
  directives: { Ripple },
  inheritAttrs: false,
  props,
  setup(props2) {
    const inputValue = ref("");
    const { bindForm, form } = useForm();
    const {
      errorMessage,
      validateWithTrigger: vt,
      validate: v,
      // expose
      resetValidation
    } = useValidation();
    const { readonly: formReadonly, disabled: formDisabled } = form != null ? form : {};
    const isMax = computed(() => {
      const { max, modelValue } = props2;
      return max != null && toNumber(modelValue) >= toNumber(max);
    });
    const isMin = computed(() => {
      const { min, modelValue } = props2;
      return min != null && toNumber(modelValue) <= toNumber(min);
    });
    let incrementTimer;
    let decrementTimer;
    let incrementDelayTimer;
    let decrementDelayTimer;
    const counterProvider = {
      reset,
      validate,
      resetValidation
    };
    call(bindForm, counterProvider);
    watch(
      () => props2.modelValue,
      (newValue) => {
        setNormalizedValue(normalizeValue(String(newValue)));
        call(props2.onChange, toNumber(newValue));
      }
    );
    setNormalizedValue(normalizeValue(String(props2.modelValue)));
    function validate() {
      return v(props2.rules, props2.modelValue);
    }
    function validateWithTrigger(trigger) {
      nextTick(() => {
        const { validateTrigger, rules, modelValue } = props2;
        vt(validateTrigger, trigger, rules, modelValue);
      });
    }
    function reset() {
      const { min } = props2;
      call(props2["onUpdate:modelValue"], min != null ? toNumber(min) : 0);
      resetValidation();
    }
    function normalizeValue(value) {
      const { decimalLength, max, min } = props2;
      let num = toNumber(value);
      if (max != null && num > toNumber(max)) {
        num = toNumber(max);
      }
      if (min != null && num < toNumber(min)) {
        num = toNumber(min);
      }
      value = String(num);
      if (decimalLength != null) {
        value = num.toFixed(toNumber(decimalLength));
      }
      return value;
    }
    function handleChange(event) {
      const { lazyChange, onBeforeChange } = props2;
      const { value } = event.target;
      const normalizedValue = normalizeValue(value);
      lazyChange ? call(onBeforeChange, toNumber(normalizedValue), change) : setNormalizedValue(normalizedValue);
      validateWithTrigger("onInputChange");
    }
    function decrement() {
      const {
        disabled,
        readonly,
        disableDecrement,
        decrementButton,
        lazyChange,
        step,
        modelValue,
        onDecrement,
        onBeforeChange
      } = props2;
      if ((formDisabled == null ? void 0 : formDisabled.value) || (formReadonly == null ? void 0 : formReadonly.value) || disabled || readonly || disableDecrement || !decrementButton) {
        return;
      }
      if (isMin.value) {
        return;
      }
      const value = new Decimal(toNumber(modelValue)).minus(new Decimal(toNumber(step))).toString();
      const normalizedValue = normalizeValue(value);
      const normalizedValueNum = toNumber(normalizedValue);
      call(onDecrement, normalizedValueNum);
      if (lazyChange) {
        call(onBeforeChange, normalizedValueNum, change);
      } else {
        setNormalizedValue(normalizedValue);
        validateWithTrigger("onDecrement");
      }
    }
    function increment() {
      const {
        disabled,
        readonly,
        disableIncrement,
        incrementButton,
        lazyChange,
        step,
        modelValue,
        onIncrement,
        onBeforeChange
      } = props2;
      if ((formDisabled == null ? void 0 : formDisabled.value) || (formReadonly == null ? void 0 : formReadonly.value) || disabled || readonly || disableIncrement || !incrementButton) {
        return;
      }
      if (isMax.value) {
        return;
      }
      const value = new Decimal(toNumber(modelValue)).plus(new Decimal(toNumber(step))).toString();
      const normalizedValue = normalizeValue(value);
      const normalizedValueNum = toNumber(normalizedValue);
      call(onIncrement, normalizedValueNum);
      if (lazyChange) {
        call(onBeforeChange, normalizedValueNum, change);
      } else {
        setNormalizedValue(normalizedValue);
        validateWithTrigger("onIncrement");
      }
    }
    function pressDecrement() {
      const { press, lazyChange } = props2;
      if (!press || lazyChange) {
        return;
      }
      decrementDelayTimer = window.setTimeout(() => {
        continuedDecrement();
      }, DELAY);
    }
    function pressIncrement() {
      const { press, lazyChange } = props2;
      if (!press || lazyChange) {
        return;
      }
      incrementDelayTimer = window.setTimeout(() => {
        continuedIncrement();
      }, DELAY);
    }
    function releaseDecrement() {
      decrementTimer && clearTimeout(decrementTimer);
      decrementDelayTimer && clearTimeout(decrementDelayTimer);
    }
    function releaseIncrement() {
      incrementTimer && clearTimeout(incrementTimer);
      incrementDelayTimer && clearTimeout(incrementDelayTimer);
    }
    function continuedIncrement() {
      incrementTimer = window.setTimeout(() => {
        increment();
        continuedIncrement();
      }, SPEED);
    }
    function continuedDecrement() {
      decrementTimer = window.setTimeout(() => {
        decrement();
        continuedDecrement();
      }, SPEED);
    }
    function setNormalizedValue(normalizedValue) {
      inputValue.value = normalizedValue;
      const normalizedValueNum = toNumber(normalizedValue);
      call(props2["onUpdate:modelValue"], normalizedValueNum);
    }
    function change(value) {
      setNormalizedValue(normalizeValue(String(value)));
      validateWithTrigger("onLazyChange");
    }
    return {
      inputValue,
      errorMessage,
      formDisabled,
      formReadonly,
      isMax,
      isMin,
      n,
      classes,
      formatElevation,
      validate,
      reset,
      resetValidation,
      handleChange,
      decrement,
      increment,
      pressDecrement,
      pressIncrement,
      releaseDecrement,
      releaseIncrement,
      toSizeUnit,
      toNumber
    };
  }
});
__sfc__.render = __render__;
var stdin_default = __sfc__;
export {
  stdin_default as default
};
