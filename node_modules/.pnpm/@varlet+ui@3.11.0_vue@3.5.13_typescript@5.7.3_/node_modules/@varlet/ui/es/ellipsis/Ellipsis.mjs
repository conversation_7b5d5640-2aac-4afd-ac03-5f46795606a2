var __defProp = Object.defineProperty;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
import { computed, defineComponent } from "vue";
import { useVModel } from "@varlet/use";
import VarTooltip from "../tooltip/index.mjs";
import { createNamespace } from "../utils/components.mjs";
import { props } from "./props.mjs";
const { name, n, classes } = createNamespace("ellipsis");
import { renderSlot as _renderSlot, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, openBlock as _openBlock, createElementBlock as _createElementBlock, resolveComponent as _resolveComponent, mergeProps as _mergeProps, withCtx as _withCtx, createBlock as _createBlock } from "vue";
const _hoisted_1 = { key: 0 };
function __render__(_ctx, _cache) {
  const _component_var_tooltip = _resolveComponent("var-tooltip");
  return _openBlock(), _createBlock(_component_var_tooltip, _mergeProps({
    class: _ctx.classes(_ctx.n(), [_ctx.lineClamp, _ctx.n("--clamp"), _ctx.n("--line")], [_ctx.expandTrigger, _ctx.n("--cursor")], [_ctx.expanding, _ctx.n("--expand")]),
    "var-ellipsis-cover": "",
    style: _ctx.rootStyles
  }, _ctx.tooltipProps, { onClick: _ctx.handleClick }), {
    content: _withCtx(() => [
      _renderSlot(_ctx.$slots, "tooltip-content", {}, () => {
        var _a;
        return [
          ((_a = _ctx.tooltipProps) == null ? void 0 : _a.content) ? (_openBlock(), _createElementBlock(
            "span",
            _hoisted_1,
            _toDisplayString(_ctx.tooltipProps.content),
            1
            /* TEXT */
          )) : _renderSlot(_ctx.$slots, "default", { key: 1 })
        ];
      })
    ]),
    default: _withCtx(() => [
      _createElementVNode("span", null, [
        _renderSlot(_ctx.$slots, "default")
      ])
    ]),
    _: 3
    /* FORWARDED */
  }, 16, ["class", "style", "onClick"]);
}
const __sfc__ = defineComponent({
  name,
  components: { VarTooltip },
  props,
  setup(props2) {
    const expanding = useVModel(props2, "expand");
    const rootStyles = computed(() => props2.lineClamp ? { "-webkit-line-clamp": props2.lineClamp } : {});
    const tooltipProps = computed(() => {
      if (props2.tooltip === false) {
        return {
          disabled: true
        };
      }
      if (props2.tooltip === true) {
        return {
          sameWidth: true
        };
      }
      return __spreadValues({ sameWidth: true }, props2.tooltip);
    });
    function handleClick() {
      if (!props2.expandTrigger) {
        return;
      }
      expanding.value = !expanding.value;
    }
    return {
      tooltipProps,
      expanding,
      rootStyles,
      n,
      classes,
      handleClick
    };
  }
});
__sfc__.render = __render__;
var stdin_default = __sfc__;
export {
  stdin_default as default
};
