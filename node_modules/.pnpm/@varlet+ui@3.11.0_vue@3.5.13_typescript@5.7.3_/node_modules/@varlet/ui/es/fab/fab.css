:root { --fab-top: 70px; --fab-bottom: 16px; --fab-left: 16px; --fab-right: 16px; --fab-trigger-size: 56px; --fab-trigger-border-radius: 50%; --fab-trigger-inactive-icon-size: 26px; --fab-trigger-active-icon-size: 22px; --fab-actions-padding: 10px 0; --fab-action-margin: 6px; --fab-action-size: 40px; --fab-action-border-radius: 50%; --fab-transition-standard-easing: cubic-bezier(0.4, 0, 0.2, 1);}.var-fab-transition-default-enter-active { transition: 0.3s var(--fab-transition-standard-easing) !important;}.var-fab-transition-default-leave-active { transition: 0.3s var(--fab-transition-standard-easing) !important;}.var-fab__trigger[var-fab-cover] { width: var(--fab-trigger-size); height: var(--fab-trigger-size); border-radius: var(--fab-trigger-border-radius);}.var-fab__trigger-inactive-icon[var-fab-cover] { font-size: var(--fab-trigger-inactive-icon-size);}.var-fab__trigger-active-icon[var-fab-cover] { font-size: var(--fab-trigger-active-icon-size);}.var-fab__actions { position: absolute; display: flex; justify-content: center; align-items: center; padding: var(--fab-actions-padding);}.var-fab__action { margin: var(--fab-action-margin);}.var-fab__action .var-button { width: var(--fab-action-size); height: var(--fab-action-size); border-radius: var(--fab-action-border-radius);}.var-fab--position-left-top { top: var(--fab-top); left: var(--fab-left);}.var-fab--position-right-top { top: var(--fab-top); right: var(--fab-right);}.var-fab--position-left-bottom { bottom: var(--fab-bottom); left: var(--fab-left);}.var-fab--position-right-bottom { bottom: var(--fab-bottom); right: var(--fab-right);}.var-fab--direction-left .var-fab__actions,.var-fab--direction-right .var-fab__actions { height: 100%; top: 0; padding: 0 var(--fab-actions-padding);}.var-fab--direction-left .var-fab__actions { flex-direction: row-reverse; right: 100%;}.var-fab--direction-right .var-fab__actions { flex-direction: row; left: 100%;}.var-fab--direction-top .var-fab__actions,.var-fab--direction-bottom .var-fab__actions { width: 100%; left: 0;}.var-fab--direction-top .var-fab__actions { flex-direction: column-reverse; bottom: 100%;}.var-fab--direction-bottom .var-fab__actions { flex-direction: column; top: 100%;}.var-fab--fixed { position: fixed;}.var-fab--absolute { position: absolute;}.var-fab--active-transition-enter-active { transition: 0.3s var(--fab-transition-standard-easing) !important;}.var-fab--active-transition-leave-active { transition: 0.3s var(--fab-transition-standard-easing) !important;}.var-fab--active-transition-enter-from,.var-fab--active-transition-leave-to { transform: scale(0);}.var-fab--actions-transition-top-enter-active { transition: 0.3s var(--fab-transition-standard-easing) !important;}.var-fab--actions-transition-top-leave-active { transition: 0.3s var(--fab-transition-standard-easing) !important;}.var-fab--actions-transition-top-enter-from,.var-fab--actions-transition-top-leave-to { opacity: 0; transform: translateY(40px);}.var-fab--actions-transition-bottom-enter-active { transition: 0.3s var(--fab-transition-standard-easing) !important;}.var-fab--actions-transition-bottom-leave-active { transition: 0.3s var(--fab-transition-standard-easing) !important;}.var-fab--actions-transition-bottom-enter-from,.var-fab--actions-transition-bottom-leave-to { opacity: 0; transform: translateY(-40px);}.var-fab--actions-transition-left-enter-active { transition: 0.3s var(--fab-transition-standard-easing) !important;}.var-fab--actions-transition-left-leave-active { transition: 0.3s var(--fab-transition-standard-easing) !important;}.var-fab--actions-transition-left-enter-from,.var-fab--actions-transition-left-leave-to { opacity: 0; transform: translateX(40px);}.var-fab--actions-transition-right-enter-active { transition: 0.3s var(--fab-transition-standard-easing) !important;}.var-fab--actions-transition-right-leave-active { transition: 0.3s var(--fab-transition-standard-easing) !important;}.var-fab--actions-transition-right-enter-from,.var-fab--actions-transition-right-leave-to { opacity: 0; transform: translateX(-40px);}.var-fab--trigger-icon-animation { transform: scale(0.4); opacity: 0; transition-property: transform, opacity;}.var-fab--safe-area { margin-bottom: constant(safe-area-inset-bottom); margin-bottom: env(safe-area-inset-bottom);}