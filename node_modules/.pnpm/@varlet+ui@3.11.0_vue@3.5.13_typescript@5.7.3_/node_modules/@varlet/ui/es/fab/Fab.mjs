import { defineComponent, ref, Transition, watch, createVNode as _createVNode, vShow as _vShow, withDirectives as _withDirectives, mergeProps as _mergeProps } from "vue";
import { call, isBoolean } from "@varlet/shared";
import { useClickOutside, useVModel } from "@varlet/use";
import Button from "../button/index.mjs";
import Drag from "../drag/index.mjs";
import Icon from "../icon/index.mjs";
import { createNamespace, flatFragment } from "../utils/components.mjs";
import { toSizeUnit } from "../utils/elements.mjs";
import { props } from "./props.mjs";








const {
  name,
  classes,
  n
} = createNamespace("fab");
var stdin_default = defineComponent({
  name,
  inheritAttrs: false,
  props,
  setup(props2, {
    slots,
    attrs
  }) {
    const isActive = useVModel(props2, "active");
    const host = ref(null);
    const dragRef = ref(null);
    watch(() => props2.trigger, () => {
      isActive.value = false;
    });
    watch(() => props2.disabled, () => {
      isActive.value = false;
    });
    watch(() => [props2.position, props2.fixed, props2.top, props2.bottom, props2.left, props2.right], () => {
      var _a;
      (_a = dragRef.value) == null ? void 0 : _a.reset();
    });
    useClickOutside(host, "click", handleClickOutside);
    function handleClick(e, value, childrenLength) {
      e.stopPropagation();
      if (props2.trigger !== "click" || props2.disabled) {
        return;
      }
      if (childrenLength === 0) {
        call(props2.onClick, isActive.value, e);
        return;
      }
      isActive.value = value;
      call(props2.onClick, isActive.value, e);
      call(isActive.value ? props2.onOpen : props2.onClose);
    }
    function handleMouse(value, childrenLength) {
      if (props2.trigger !== "hover" || props2.disabled || childrenLength === 0) {
        return;
      }
      isActive.value = value;
      call(isActive.value ? props2.onOpen : props2.onClose);
    }
    function handleClickOutside() {
      if (props2.trigger !== "click" || props2.disabled) {
        return;
      }
      if (isActive.value !== false) {
        isActive.value = false;
        call(props2.onClose);
      }
    }
    function renderTrigger() {
      if (slots.trigger) {
        return props2.show ? slots.trigger({
          active: isActive.value
        }) : null;
      }
      return _withDirectives(_createVNode(Button, {
        "var-fab-cover": true,
        "class": n("trigger"),
        "type": props2.type,
        "color": props2.color,
        "disabled": props2.disabled,
        "elevation": props2.elevation,
        "iconContainer": true
      }, {
        default: () => [_createVNode(Icon, {
          "var-fab-cover": true,
          "class": classes([isActive.value, n("trigger-active-icon"), n("trigger-inactive-icon")]),
          "name": isActive.value ? props2.activeIcon : props2.inactiveIcon,
          "size": isActive.value ? props2.activeIconSize : props2.inactiveIconSize,
          "namespace": isActive.value ? props2.activeIconNamespace : props2.inactiveIconNamespace,
          "transition": 200,
          "animationClass": n("--trigger-icon-animation")
        }, null)]
      }), [[_vShow, props2.show]]);
    }
    return () => {
      var _a;
      const children = flatFragment((_a = call(slots.default)) != null ? _a : []);
      const dragProps = isBoolean(props2.drag) ? {} : props2.drag;
      return _createVNode(Drag, _mergeProps({
        "ref": dragRef,
        "class": classes(n(`--position-${props2.position}`), [!props2.fixed, n("--absolute")]),
        "style": {
          top: toSizeUnit(props2.top),
          bottom: toSizeUnit(props2.bottom),
          left: toSizeUnit(props2.left),
          right: toSizeUnit(props2.right)
        },
        "zIndex": props2.zIndex,
        "teleport": props2.teleport,
        "disabled": props2.disabled || !props2.drag || !props2.fixed,
        "direction": dragProps.direction,
        "attraction": dragProps.attraction,
        "boundary": dragProps.boundary,
        "onClick": (e) => handleClick(e, !isActive.value, children.length)
      }, attrs), {
        default: () => [_createVNode("div", {
          "class": classes(n(), n(`--direction-${props2.direction}`), [props2.safeArea, n("--safe-area")]),
          "ref": host,
          "onMouseleave": () => handleMouse(false, children.length),
          "onMouseenter": () => handleMouse(true, children.length)
        }, [_createVNode(Transition, {
          "name": n(`--active-transition`)
        }, {
          default: () => [renderTrigger()]
        }), _createVNode(Transition, {
          "name": n(`--actions-transition-${props2.direction}`),
          "onAfterEnter": props2.onOpened,
          "onAfterLeave": props2.onClosed
        }, {
          default: () => [_withDirectives(_createVNode("div", {
            "class": n("actions"),
            "onClick": (e) => e.stopPropagation()
          }, [children.map((child) => {
            var _a2;
            return _createVNode("div", {
              "class": n("action"),
              "key": (_a2 = child.key) != null ? _a2 : void 0
            }, [child]);
          })]), [[_vShow, props2.show && isActive.value && children.length]])]
        })])]
      });
    };
  }
});
export {
  stdin_default as default
};
