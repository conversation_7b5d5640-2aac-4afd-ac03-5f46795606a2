import { iconProps } from "../icon/index.mjs";
import { defineListenerProp, pickProps } from "../utils/components.mjs";
const props = {
  active: Boolean,
  show: {
    type: Boolean,
    default: true
  },
  drag: {
    type: [Object, Boolean],
    default: false
  },
  type: {
    type: String,
    default: "primary"
  },
  position: {
    type: String,
    default: "right-bottom"
  },
  direction: {
    type: String,
    default: "top"
  },
  trigger: {
    type: String,
    default: "click"
  },
  disabled: Boolean,
  color: String,
  inactiveIcon: {
    type: String,
    default: "plus"
  },
  activeIcon: {
    type: String,
    default: "window-close"
  },
  inactiveIconSize: pickProps(iconProps, "size"),
  activeIconSize: pickProps(iconProps, "size"),
  inactiveIconNamespace: pickProps(iconProps, "namespace"),
  activeIconNamespace: pickProps(iconProps, "namespace"),
  fixed: {
    type: Boolean,
    default: true
  },
  zIndex: {
    type: [Number, String],
    default: 90
  },
  top: [Number, String],
  bottom: [Number, String],
  left: [Number, String],
  right: [Number, String],
  elevation: {
    type: [Boolean, Number, String],
    default: true
  },
  safeArea: Boolean,
  teleport: {
    type: [String, Object, Boolean],
    default: "body"
  },
  onClick: defineListenerProp(),
  onOpen: defineListenerProp(),
  onOpened: defineListenerProp(),
  onClose: defineListenerProp(),
  onClosed: defineListenerProp(),
  "onUpdate:active": defineListenerProp()
};
export {
  props
};
