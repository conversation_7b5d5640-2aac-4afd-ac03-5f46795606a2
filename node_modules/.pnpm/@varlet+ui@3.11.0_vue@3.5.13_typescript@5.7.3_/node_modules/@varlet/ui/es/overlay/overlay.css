:root { --overlay-background-color: rgba(0, 0, 0, 0.6);}.var-overlay { display: flex; justify-content: center; align-items: center; position: fixed; top: 0; right: 0; bottom: 0; left: 0;}.var-overlay--fade-enter-from,.var-overlay--fade-leave-to { opacity: 0;}.var-overlay--fade-enter-active,.var-overlay--fade-leave-active { transition: opacity 0.25s;}.var-overlay__overlay { position: fixed; top: 0; right: 0; bottom: 0; left: 0; background-color: var(--overlay-background-color);}.var-overlay__content { position: relative;}