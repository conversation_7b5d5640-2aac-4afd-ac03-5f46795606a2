import { defineComponent, Teleport, Transition, createVNode as _createVNode, mergeProps as _mergeProps } from "vue";
import { call, preventDefault } from "@varlet/shared";
import { useEventListener } from "@varlet/use";
import { useLock } from "../context/lock.mjs";
import { useStack } from "../context/stack.mjs";
import { useZIndex } from "../context/zIndex.mjs";
import { createNamespace, useTeleport } from "../utils/components.mjs";
import { props } from "./props.mjs";


const {
  name,
  n
} = createNamespace("overlay");
var stdin_default = defineComponent({
  name,
  inheritAttrs: false,
  props,
  setup(props2, {
    slots,
    attrs
  }) {
    const {
      zIndex
    } = useZIndex(() => props2.show, 3);
    const {
      onStackTop
    } = useStack(() => props2.show, zIndex);
    const {
      disabled
    } = useTeleport();
    useLock(() => props2.show, () => props2.lockScroll);
    useEventListener(() => window, "keydown", handleKeydown);
    function handleKeydown(event) {
      if (!onStackTop() || event.key !== "Escape" || !props2.show) {
        return;
      }
      call(props2.onKeyEscape);
      if (!props2.closeOnKeyEscape) {
        return;
      }
      preventDefault(event);
      call(props2["onUpdate:show"], false);
    }
    function handleClickOverlay() {
      call(props2.onClick);
      call(props2["onUpdate:show"], false);
    }
    function renderOverlay() {
      return _createVNode("div", _mergeProps({
        "class": n(),
        "style": {
          zIndex: zIndex.value - 2
        }
      }, attrs), [_createVNode("div", {
        "class": n("overlay"),
        "style": {
          zIndex: zIndex.value - 1
        },
        "onClick": handleClickOverlay
      }, null), _createVNode("div", {
        "class": n("content"),
        "style": {
          zIndex: zIndex.value
        }
      }, [call(slots.default)])]);
    }
    function renderTransitionOverlay() {
      return _createVNode(Transition, {
        "name": n("--fade")
      }, {
        default: () => [props2.show && renderOverlay()]
      });
    }
    return () => {
      const {
        teleport
      } = props2;
      if (teleport) {
        return _createVNode(Teleport, {
          "to": teleport,
          "disabled": disabled.value
        }, {
          default: () => [renderTransitionOverlay()]
        });
      }
      return renderTransitionOverlay();
    };
  }
});
export {
  stdin_default as default
};
