var stdin_default = {
  // Dialog
  dialogTitle: "\u0627\u0634\u0627\u0631\u0647",
  dialogConfirmButtonText: "\u062A\u0627\u06CC\u06CC\u062F",
  dialogCancelButtonText: "\u0644\u063A\u0648",
  // ActionSheet
  actionSheetTitle: "\u0627\u0646\u062A\u062E\u0627\u0628 \u06CC\u06A9 \u0645\u0648\u0631\u062F",
  // List
  listLoadingText: "\u062F\u0631 \u062D\u0627\u0644 \u0628\u0627\u0631\u06AF\u0632\u0627\u0631\u06CC",
  listFinishedText: "\u0645\u0648\u0631\u062F \u062F\u06CC\u06AF\u0631\u06CC \u0648\u062C\u0648\u062F \u0646\u062F\u0627\u0631\u062F",
  listErrorText: "\u0628\u0627\u0631\u06AF\u0632\u0627\u0631\u06CC \u0646\u0627\u0645\u0648\u0641\u0642",
  // Picker
  pickerTitle: "\u0627\u0646\u062A\u062E\u0627\u0628 \u06A9\u0646\u06CC\u062F",
  pickerConfirmButtonText: "\u062A\u0627\u06CC\u06CC\u062F",
  pickerCancelButtonText: "\u0644\u063A\u0648",
  // date-picker
  datePickerMonthDict: {
    "01": {
      name: "\u0698\u0627\u0646\u0648\u06CC\u0647",
      abbr: "JAN"
    },
    "02": {
      name: "\u0641\u0648\u0631\u06CC\u0647",
      abbr: "FEB"
    },
    "03": {
      name: "\u0645\u0627\u0631\u0633",
      abbr: "MAR"
    },
    "04": {
      name: "\u0622\u0648\u0631\u06CC\u0644",
      abbr: "APR"
    },
    "05": {
      name: "\u0645\u0647",
      abbr: "MAY"
    },
    "06": {
      name: "\u0698\u0648\u0626\u0646",
      abbr: "JUN"
    },
    "07": {
      name: "\u062C\u0648\u0644\u0627\u06CC",
      abbr: "JUL"
    },
    "08": {
      name: "\u0622\u06AF\u0648\u0633\u062A",
      abbr: "AUG"
    },
    "09": {
      name: "\u0633\u067E\u062A\u0627\u0645\u0628\u0631",
      abbr: "SEP"
    },
    "10": {
      name: "\u0627\u0648\u06A9\u062A\u0628\u0631",
      abbr: "OCT"
    },
    "11": {
      name: "\u0646\u0648\u0627\u0645\u0628\u0631",
      abbr: "NOV"
    },
    "12": {
      name: "\u062F\u0633\u0627\u0645\u0628\u0631",
      abbr: "DEC"
    }
  },
  datePickerWeekDict: {
    "0": {
      name: "\u06CC\u06A9\u0634\u0646\u0628\u0647",
      abbr: "S"
    },
    "1": {
      name: "\u062F\u0648\u0634\u0646\u0628\u0647",
      abbr: "M"
    },
    "2": {
      name: "\u0633\u0647\u200C\u0634\u0646\u0628\u0647",
      abbr: "T"
    },
    "3": {
      name: "\u0686\u0647\u0627\u0631\u0634\u0646\u0628\u0647",
      abbr: "W"
    },
    "4": {
      name: "\u067E\u0646\u062C\u0634\u0646\u0628\u0647",
      abbr: "T"
    },
    "5": {
      name: "\u062C\u0645\u0639\u0647",
      abbr: "F"
    },
    "6": {
      name: "\u0634\u0646\u0628\u0647",
      abbr: "S"
    }
  },
  datePickerSelected: " \u0627\u0646\u062A\u062E\u0627\u0628 \u0634\u062F\u0647",
  datePickerHint: "\u0627\u0646\u062A\u062E\u0627\u0628 \u062A\u0627\u0631\u06CC\u062E",
  // pagination
  paginationItem: "",
  paginationPage: "\u0635\u0641\u062D\u0647",
  paginationJump: "\u0628\u0631\u0648 \u0628\u0647",
  // time-picker
  timePickerHint: "\u0627\u0646\u062A\u062E\u0627\u0628 \u0632\u0645\u0627\u0646"
};
export {
  stdin_default as default
};
