var stdin_default = {
  // Dialog
  dialogTitle: "\u78BA\u8A8D",
  dialogConfirmButtonText: "\u78BA\u8A8D",
  dialogCancelButtonText: "\u30AD\u30E3\u30F3\u30BB\u30EB",
  // ActionSheet
  actionSheetTitle: "\u3044\u305A\u308C\u304B\u3092\u9078\u629E",
  // List
  listLoadingText: "\u8AAD\u307F\u8FBC\u307F\u4E2D",
  listFinishedText: "\u4EE5\u4E0A\u3067\u3059",
  listErrorText: "\u8AAD\u307F\u8FBC\u307F\u5931\u6557",
  // Picker
  pickerTitle: "\u9078\u629E\u3057\u3066\u304F\u3060\u3055\u3044",
  pickerConfirmButtonText: "\u78BA\u8A8D",
  pickerCancelButtonText: "\u30AD\u30E3\u30F3\u30BB\u30EB",
  // date-picker
  datePickerMonthDict: {
    "01": {
      name: "1\u6708",
      abbr: "1\u6708"
    },
    "02": {
      name: "2\u6708",
      abbr: "2\u6708"
    },
    "03": {
      name: "3\u6708",
      abbr: "3\u6708"
    },
    "04": {
      name: "4\u6708",
      abbr: "4\u6708"
    },
    "05": {
      name: "5\u6708",
      abbr: "5\u6708"
    },
    "06": {
      name: "6\u6708",
      abbr: "6\u6708"
    },
    "07": {
      name: "7\u6708",
      abbr: "7\u6708"
    },
    "08": {
      name: "8\u6708",
      abbr: "8\u6708"
    },
    "09": {
      name: "9\u6708",
      abbr: "9\u6708"
    },
    "10": {
      name: "10\u6708",
      abbr: "10\u6708"
    },
    "11": {
      name: "11\u6708",
      abbr: "11\u6708"
    },
    "12": {
      name: "12\u6708",
      abbr: "12\u6708"
    }
  },
  datePickerWeekDict: {
    "0": {
      name: "\u65E5\u66DC\u65E5",
      abbr: "\u65E5"
    },
    "1": {
      name: "\u6708\u66DC\u65E5",
      abbr: "\u6708"
    },
    "2": {
      name: "\u706B\u66DC\u65E5",
      abbr: "\u706B"
    },
    "3": {
      name: "\u6C34\u66DC\u65E5",
      abbr: "\u6C34"
    },
    "4": {
      name: "\u6728\u66DC\u65E5",
      abbr: "\u6728"
    },
    "5": {
      name: "\u91D1\u66DC\u65E5",
      abbr: "\u91D1"
    },
    "6": {
      name: "\u571F\u66DC\u65E5",
      abbr: "\u571F"
    }
  },
  datePickerSelected: "\u4EF6\u9078\u629E\u6E08\u307F",
  datePickerHint: "\u65E5\u4ED8\u3092\u9078\u629E",
  // pagination
  paginationItem: "\u4EF6",
  paginationPage: "\u30DA\u30FC\u30B8",
  paginationJump: "\u30B8\u30E3\u30F3\u30D7",
  // time-picker
  timePickerHint: "\u6642\u9593\u3092\u9078\u629E"
};
export {
  stdin_default as default
};
