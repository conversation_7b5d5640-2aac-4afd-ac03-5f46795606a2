var stdin_default = {
  // Dialog
  dialogTitle: "\u63D0\u793A",
  dialogConfirmButtonText: "\u78BA\u8A8D",
  dialogCancelButtonText: "\u53D6\u6D88",
  // ActionSheet
  actionSheetTitle: "\u8ACB\u9078\u64C7",
  // List
  listLoadingText: "\u8F09\u5165\u4E2D",
  listFinishedText: "\u6C92\u6709\u66F4\u591A\u4E86",
  listErrorText: "\u8F09\u5165\u5931\u6557",
  // Picker
  pickerTitle: "\u8ACB\u9078\u64C7",
  pickerConfirmButtonText: "\u78BA\u8A8D",
  pickerCancelButtonText: "\u53D6\u6D88",
  // date-picker
  datePickerMonthDict: {
    "01": {
      name: "\u4E00\u6708",
      abbr: "\u4E00\u6708"
    },
    "02": {
      name: "\u4E8C\u6708",
      abbr: "\u4E8C\u6708"
    },
    "03": {
      name: "\u4E09\u6708",
      abbr: "\u4E09\u6708"
    },
    "04": {
      name: "\u56DB\u6708",
      abbr: "\u56DB\u6708"
    },
    "05": {
      name: "\u4E94\u6708",
      abbr: "\u4E94\u6708"
    },
    "06": {
      name: "\u516D\u6708",
      abbr: "\u516D\u6708"
    },
    "07": {
      name: "\u4E03\u6708",
      abbr: "\u4E03\u6708"
    },
    "08": {
      name: "\u516B\u6708",
      abbr: "\u516B\u6708"
    },
    "09": {
      name: "\u4E5D\u6708",
      abbr: "\u4E5D\u6708"
    },
    "10": {
      name: "\u5341\u6708",
      abbr: "\u5341\u6708"
    },
    "11": {
      name: "\u5341\u4E00\u6708",
      abbr: "\u5341\u4E00\u6708"
    },
    "12": {
      name: "\u5341\u4E8C\u6708",
      abbr: "\u5341\u4E8C\u6708"
    }
  },
  datePickerWeekDict: {
    "0": {
      name: "\u661F\u671F\u65E5",
      abbr: "\u65E5"
    },
    "1": {
      name: "\u661F\u671F\u4E00",
      abbr: "\u4E00"
    },
    "2": {
      name: "\u661F\u671F\u4E8C",
      abbr: "\u4E8C"
    },
    "3": {
      name: "\u661F\u671F\u4E09",
      abbr: "\u4E09"
    },
    "4": {
      name: "\u661F\u671F\u56DB",
      abbr: "\u56DB"
    },
    "5": {
      name: "\u661F\u671F\u4E94",
      abbr: "\u4E94"
    },
    "6": {
      name: "\u661F\u671F\u516D",
      abbr: "\u516D"
    }
  },
  datePickerSelected: "\u500B\u88AB\u9078\u64C7",
  datePickerHint: "\u9078\u64C7\u65E5\u671F",
  // pagination
  paginationItem: "\u689D",
  paginationPage: "\u9801",
  paginationJump: "\u524D\u5F80",
  // time-picker
  timePickerHint: "\u9078\u64C7\u6642\u9593"
};
export {
  stdin_default as default
};
