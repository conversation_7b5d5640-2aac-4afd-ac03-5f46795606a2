var __defProp = Object.defineProperty;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
import { ref } from "vue";
import { hasOwn } from "@varlet/shared";
import enUS from "./en-US.mjs";
import faIR from "./fa-IR.mjs";
import jaJP from "./ja-JP.mjs";
import zhCN from "./zh-CN.mjs";
import zhHK from "./zh-HK.mjs";
import zhTW from "./zh-TW.mjs";
function useLocale() {
  const messages2 = ref({});
  const currentMessage2 = ref({});
  const add2 = (lang, message) => {
    message.lang = lang;
    messages2.value[lang] = message;
  };
  const use2 = (lang) => {
    if (!messages2.value[lang]) {
      console.warn(`The ${lang} does not exist. You can mount a language message using the add method`);
      return {};
    }
    currentMessage2.value = messages2.value[lang];
  };
  const merge2 = (lang, message) => {
    if (!messages2.value[lang]) {
      console.warn(`The ${lang} does not exist. You can mount a language message using the add method`);
      return;
    }
    messages2.value[lang] = __spreadValues(__spreadValues({}, messages2.value[lang]), message);
    use2(lang);
  };
  const t2 = (id) => {
    if (hasOwn(currentMessage2.value, id)) {
      return currentMessage2.value[id];
    }
  };
  return {
    messages: messages2,
    currentMessage: currentMessage2,
    add: add2,
    use: use2,
    merge: merge2,
    t: t2
  };
}
const { messages, currentMessage, add, use, merge, t } = useLocale();
add("zh-CN", zhCN);
use("zh-CN");
const _LocaleComponent = {
  zhCN,
  enUS,
  zhTW,
  zhHK,
  faIR,
  jaJP,
  messages,
  currentMessage,
  add,
  use,
  merge,
  t,
  useLocale
};
var stdin_default = {
  zhCN,
  enUS,
  zhTW,
  zhHK,
  faIR,
  jaJP,
  messages,
  currentMessage,
  add,
  use,
  merge,
  t,
  useLocale
};
export {
  _LocaleComponent,
  add,
  currentMessage,
  stdin_default as default,
  enUS,
  faIR,
  merge,
  messages,
  t,
  use,
  useLocale,
  zhCN,
  zhHK,
  zhTW
};
