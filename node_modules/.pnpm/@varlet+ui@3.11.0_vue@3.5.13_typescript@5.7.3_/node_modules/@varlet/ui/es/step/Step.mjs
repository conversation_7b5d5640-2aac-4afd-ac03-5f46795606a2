import { computed, defineComponent } from "vue";
import { toNumber } from "@varlet/shared";
import VarIcon from "../icon/index.mjs";
import { createNamespace } from "../utils/components.mjs";
import { props } from "./props.mjs";
import { useSteps } from "./provide.mjs";
const { name, n, classes } = createNamespace("step");
import { resolveComponent as _resolveComponent, normalizeClass as _normalizeClass, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, createElementBlock as _createElementBlock, normalizeStyle as _normalizeStyle, createElementVNode as _createElementVNode, renderSlot as _renderSlot } from "vue";
const _hoisted_1 = { key: 3 };
function __render__(_ctx, _cache) {
  const _component_var_icon = _resolveComponent("var-icon");
  return _openBlock(), _createElementBlock(
    "div",
    {
      class: _normalizeClass(_ctx.n())
    },
    [
      _createElementVNode(
        "div",
        {
          class: _normalizeClass(_ctx.n(_ctx.direction))
        },
        [
          _createElementVNode(
            "div",
            {
              class: _normalizeClass(_ctx.classes(_ctx.n(`${_ctx.direction}-tag`), [_ctx.isActive || _ctx.isCurrent, _ctx.n(`${_ctx.direction}-tag--active`)])),
              style: _normalizeStyle({ backgroundColor: _ctx.isActive || _ctx.isCurrent ? _ctx.activeColor : _ctx.inactiveColor }),
              onClick: _cache[0] || (_cache[0] = (...args) => _ctx.click && _ctx.click(...args))
            },
            [
              _ctx.isActive ? (_openBlock(), _createBlock(_component_var_icon, {
                key: 0,
                class: _normalizeClass(_ctx.n("icon")),
                "var-step-cover": "",
                name: _ctx.activeIcon,
                namespace: _ctx.activeIconNamespace
              }, null, 8, ["class", "name", "namespace"])) : _ctx.isCurrent && _ctx.currentIcon ? (_openBlock(), _createBlock(_component_var_icon, {
                key: 1,
                class: _normalizeClass(_ctx.n("icon")),
                "var-step-cover": "",
                name: _ctx.currentIcon,
                namespace: _ctx.currentIconNamespace
              }, null, 8, ["class", "name", "namespace"])) : _ctx.inactiveIcon ? (_openBlock(), _createBlock(_component_var_icon, {
                key: 2,
                class: _normalizeClass(_ctx.n("icon")),
                "var-step-cover": "",
                name: _ctx.inactiveIcon,
                namespace: _ctx.inactiveIconNamespace
              }, null, 8, ["class", "name", "namespace"])) : (_openBlock(), _createElementBlock(
                "span",
                _hoisted_1,
                _toDisplayString(_ctx.index + 1),
                1
                /* TEXT */
              ))
            ],
            6
            /* CLASS, STYLE */
          ),
          _ctx.$slots.default ? (_openBlock(), _createElementBlock(
            "div",
            {
              key: 0,
              class: _normalizeClass(_ctx.classes(_ctx.n(`${_ctx.direction}-content`), [_ctx.isActive || _ctx.isCurrent, _ctx.n(`${_ctx.direction}-content--active`)])),
              onClick: _cache[1] || (_cache[1] = (...args) => _ctx.click && _ctx.click(...args))
            },
            [
              _renderSlot(_ctx.$slots, "default")
            ],
            2
            /* CLASS */
          )) : _createCommentVNode("v-if", true),
          _createElementVNode(
            "div",
            {
              class: _normalizeClass(_ctx.n(`${_ctx.direction}-line`))
            },
            null,
            2
            /* CLASS */
          )
        ],
        2
        /* CLASS */
      )
    ],
    2
    /* CLASS */
  );
}
const __sfc__ = defineComponent({
  name,
  components: { VarIcon },
  props,
  setup() {
    const { index, steps, bindSteps } = useSteps();
    const { active, activeColor, inactiveColor, direction, clickStep } = steps;
    const isCurrent = computed(() => active.value === index.value);
    const isActive = computed(() => index.value !== -1 && toNumber(active.value) > index.value);
    const stepProvider = { index };
    bindSteps(stepProvider);
    function click() {
      return clickStep(index.value);
    }
    return {
      index,
      isActive,
      isCurrent,
      direction,
      activeColor,
      inactiveColor,
      n,
      classes,
      click
    };
  }
});
__sfc__.render = __render__;
var stdin_default = __sfc__;
export {
  stdin_default as default
};
