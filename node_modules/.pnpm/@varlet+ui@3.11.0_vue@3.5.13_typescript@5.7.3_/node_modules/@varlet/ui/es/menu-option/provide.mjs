import { assert } from "@varlet/shared";
import { useParent } from "@varlet/use";
import { MENU_SELECT_BIND_MENU_OPTION_KEY } from "../menu-select/provide.mjs";
function useMenuSelect() {
  const { index, parentProvider, bindParent } = useParent(
    MENU_SELECT_BIND_MENU_OPTION_KEY
  );
  assert(!!bindParent, "MenuOption", "<var-menu-option/> must in <var-menu-select/>");
  return {
    index,
    menuSelect: parentProvider,
    bindMenuSelect: bindParent
  };
}
export {
  useMenuSelect
};
