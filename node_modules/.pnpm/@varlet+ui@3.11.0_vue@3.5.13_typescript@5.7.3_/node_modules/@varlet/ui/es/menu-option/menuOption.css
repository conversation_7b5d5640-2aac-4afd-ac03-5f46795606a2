:root { --menu-option-normal-height: 38px; --menu-option-small-height: 30px; --menu-option-mini-height: 24px; --menu-option-large-height: 46px; --menu-option-padding: 0 12px; --menu-option-normal-font-size: var(--font-size-md); --menu-option-small-font-size: var(--font-size-sm); --menu-option-mini-font-size: var(--font-size-xs); --menu-option-large-font-size: var(--font-size-lg); --menu-option-selected-background: var(--color-primary); --menu-option-text-color: #555; --menu-option-disabled-color: var(--color-text-disabled); --menu-option-arrow-padding: 0 6px 0 8px; --menu-option-arrow-icon-size: 18px;}.var-menu-option { position: relative; display: flex; align-items: center; padding: var(--menu-option-padding); cursor: pointer; -webkit-tap-highlight-color: transparent; color: var(--menu-option-text-color); outline: none;}.var-menu-option__cover { position: absolute; top: 0; left: 0; right: 0; bottom: 0; opacity: 0.2; background: transparent;}.var-menu-option__text { display: flex; align-items: center; overflow-x: hidden;}.var-menu-option__arrow { padding: var(--menu-option-arrow-padding);}.var-menu-option__arrow-icon[var-menu-option-cover] { font-size: var(--menu-option-arrow-icon-size);}.var-menu-option--children-trigger { padding-right: 0;}.var-menu-option--normal { height: var(--menu-option-normal-height); font-size: var(--menu-option-normal-font-size);}.var-menu-option--large { height: var(--menu-option-large-height); font-size: var(--menu-option-large-font-size);}.var-menu-option--small { height: var(--menu-option-small-height); font-size: var(--menu-option-small-font-size);}.var-menu-option--mini { height: var(--menu-option-mini-height); font-size: var(--menu-option-mini-font-size);}.var-menu-option--selected-background { background: var(--menu-option-selected-background);}.var-menu-option--selected-color { color: var(--menu-option-selected-background);}.var-menu-option--disabled { color: var(--menu-option-disabled-color); cursor: not-allowed;}