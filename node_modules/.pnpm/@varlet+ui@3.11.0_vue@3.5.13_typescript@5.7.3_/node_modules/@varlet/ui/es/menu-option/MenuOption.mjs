var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
import { computed, defineComponent, nextTick, ref, watch } from "vue";
import { call, isBoolean, isFunction, preventDefault } from "@varlet/shared";
import { useEventListener } from "@varlet/use";
import VarCheckbox from "../checkbox/index.mjs";
import Hover from "../hover/index.mjs";
import VarHoverOverlay, { useHoverOverlay } from "../hover-overlay/index.mjs";
import VarIcon from "../icon/index.mjs";
import Ripple from "../ripple/index.mjs";
import { createNamespace, MaybeVNode } from "../utils/components.mjs";
import { props } from "./props.mjs";
import { useMenuSelect } from "./provide.mjs";
const { name, n, classes } = createNamespace("menu-option");
import { normalizeClass as _normalizeClass, createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, withModifiers as _withModifiers, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, renderSlot as _renderSlot, createVNode as _createVNode, createElementBlock as _createElementBlock, resolveDirective as _resolveDirective, withDirectives as _withDirectives } from "vue";
const _hoisted_1 = ["tabindex"];
function __render__(_ctx, _cache) {
  const _component_var_checkbox = _resolveComponent("var-checkbox");
  const _component_maybe_v_node = _resolveComponent("maybe-v-node");
  const _component_var_icon = _resolveComponent("var-icon");
  const _component_var_hover_overlay = _resolveComponent("var-hover-overlay");
  const _directive_ripple = _resolveDirective("ripple");
  const _directive_hover = _resolveDirective("hover");
  return _withDirectives((_openBlock(), _createElementBlock("div", {
    ref: "root",
    class: _normalizeClass(
      _ctx.classes(
        _ctx.n(),
        _ctx.n("$--box"),
        _ctx.n(`--${_ctx.size}`),
        [_ctx.optionSelected, _ctx.n("--selected-color")],
        [_ctx.disabled, _ctx.n("--disabled")],
        [_ctx.childrenTrigger, _ctx.n("--children-trigger")]
      )
    ),
    tabindex: _ctx.disabled ? void 0 : "-1",
    onClick: _cache[3] || (_cache[3] = (...args) => _ctx.handleClick && _ctx.handleClick(...args)),
    onFocus: _cache[4] || (_cache[4] = ($event) => _ctx.isFocusing = true),
    onBlur: _cache[5] || (_cache[5] = ($event) => _ctx.isFocusing = false)
  }, [
    _createElementVNode(
      "div",
      {
        class: _normalizeClass(_ctx.classes(_ctx.n("cover"), [_ctx.optionSelected, _ctx.n("--selected-background")]))
      },
      null,
      2
      /* CLASS */
    ),
    _ctx.multiple ? (_openBlock(), _createBlock(_component_var_checkbox, {
      key: 0,
      ref: "checkbox",
      modelValue: _ctx.optionSelected,
      "onUpdate:modelValue": _cache[0] || (_cache[0] = ($event) => _ctx.optionSelected = $event),
      indeterminate: _ctx.optionIndeterminate,
      "onUpdate:indeterminate": _cache[1] || (_cache[1] = ($event) => _ctx.optionIndeterminate = $event),
      disabled: _ctx.disabled,
      onClick: _cache[2] || (_cache[2] = _withModifiers(() => {
      }, ["stop"])),
      onChange: _ctx.handleSelect
    }, null, 8, ["modelValue", "indeterminate", "disabled", "onChange"])) : _createCommentVNode("v-if", true),
    _renderSlot(_ctx.$slots, "default", {}, () => [
      _createElementVNode(
        "div",
        {
          class: _normalizeClass(_ctx.classes(_ctx.n("text")))
        },
        [
          _createVNode(_component_maybe_v_node, {
            is: _ctx.labelVNode,
            class: _normalizeClass(_ctx.n("$--ellipsis"))
          }, null, 8, ["is", "class"])
        ],
        2
        /* CLASS */
      )
    ]),
    _ctx.childrenTrigger ? (_openBlock(), _createElementBlock(
      "div",
      {
        key: 1,
        class: _normalizeClass(_ctx.n("arrow"))
      },
      [
        _createVNode(_component_var_icon, {
          "var-menu-option-cover": "",
          class: _normalizeClass(_ctx.n("arrow-icon")),
          name: "chevron-right"
        }, null, 8, ["class"])
      ],
      2
      /* CLASS */
    )) : _createCommentVNode("v-if", true),
    _createVNode(_component_var_hover_overlay, {
      hovering: (_ctx.hovering || _ctx.highlight) && !_ctx.disabled,
      focusing: _ctx.isFocusing && !_ctx.disabled
    }, null, 8, ["hovering", "focusing"])
  ], 42, _hoisted_1)), [
    [_directive_ripple, { disabled: _ctx.disabled || !_ctx.ripple }],
    [_directive_hover, _ctx.handleHovering, "desktop"]
  ]);
}
const __sfc__ = defineComponent({
  name,
  directives: { Ripple, Hover },
  components: {
    VarCheckbox,
    VarHoverOverlay,
    VarIcon,
    MaybeVNode
  },
  props,
  setup(props2) {
    const root = ref();
    const checkbox = ref();
    const isFocusing = ref(false);
    const optionSelected = ref(false);
    const optionIndeterminate = ref(false);
    const selected = computed(() => optionSelected.value);
    const indeterminate = computed(() => optionIndeterminate.value);
    const value = computed(() => props2.value);
    const disabled = computed(() => props2.disabled);
    const ripple = computed(() => props2.ripple);
    const { menuSelect, bindMenuSelect } = useMenuSelect();
    const { size, multiple, onSelect, computeLabel } = menuSelect;
    const { hovering, handleHovering } = useHoverOverlay();
    const labelVNode = computed(
      () => {
        var _a;
        return isFunction(props2.label) ? props2.label(
          (_a = props2.option) != null ? _a : {
            label: props2.label,
            value: props2.value,
            disabled: props2.disabled,
            ripple: props2.ripple
          },
          optionSelected.value
        ) : props2.label;
      }
    );
    const menuOptionProvider = {
      label: labelVNode,
      value,
      selected,
      disabled,
      ripple,
      indeterminate,
      sync
    };
    watch([() => props2.label, () => props2.value], computeLabel);
    bindMenuSelect(menuOptionProvider);
    useEventListener(() => window, "keydown", handleKeydown);
    useEventListener(() => window, "keyup", handleKeyup);
    function handleClick() {
      if (props2.disabled) {
        return;
      }
      if (!multiple.value && props2.childrenTrigger) {
        return;
      }
      if (multiple.value && optionIndeterminate.value) {
        optionIndeterminate.value = false;
        optionSelected.value = false;
        onSelect(menuOptionProvider);
        return;
      }
      if (multiple.value && !optionIndeterminate.value) {
        optionSelected.value = !optionSelected.value;
      }
      onSelect(menuOptionProvider);
    }
    function handleKeydown(event) {
      var _a;
      if (!isFocusing.value && !((_a = checkbox.value) == null ? void 0 : _a.isFocusing)) {
        return;
      }
      if (event.key === "ArrowRight" || event.key === "ArrowLeft") {
        preventDefault(event);
        call(props2.onKeyArrowX, event.key);
      }
      if (!isFocusing.value) {
        return;
      }
      if (event.key === " " || event.key === "Enter") {
        preventDefault(event);
      }
      if (event.key === "Enter") {
        root.value.click();
      }
    }
    function handleKeyup(event) {
      if (!isFocusing.value) {
        return;
      }
      if (event.key === " ") {
        preventDefault(event);
        root.value.click();
      }
    }
    function handleSelect() {
      return __async(this, null, function* () {
        yield nextTick();
        onSelect(menuOptionProvider);
      });
    }
    function sync(checked, indeterminate2) {
      optionSelected.value = checked;
      if (isBoolean(indeterminate2)) {
        optionIndeterminate.value = indeterminate2;
      }
    }
    return {
      root,
      checkbox,
      optionSelected,
      optionIndeterminate,
      size,
      multiple,
      hovering,
      isFocusing,
      labelVNode,
      n,
      classes,
      handleHovering,
      handleClick,
      handleSelect
    };
  }
});
__sfc__.render = __render__;
var stdin_default = __sfc__;
export {
  stdin_default as default
};
