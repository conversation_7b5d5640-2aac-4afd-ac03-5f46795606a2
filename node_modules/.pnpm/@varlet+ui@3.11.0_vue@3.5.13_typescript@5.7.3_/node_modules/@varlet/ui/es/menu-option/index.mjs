import { withInstall, withPropsDefaultsSetter } from "../utils/components.mjs";
import MenuOption from "./MenuOption.mjs";
import { props as menuOptionProps } from "./props.mjs";
withInstall(MenuOption);
withPropsDefaultsSetter(MenuOption, menuOptionProps);
const _MenuOptionComponent = MenuOption;
var stdin_default = MenuOption;
export {
  _MenuOptionComponent,
  stdin_default as default,
  menuOptionProps
};
