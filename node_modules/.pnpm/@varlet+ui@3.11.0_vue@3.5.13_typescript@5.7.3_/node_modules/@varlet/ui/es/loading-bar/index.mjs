import { reactive } from "vue";
import { mountInstance } from "../utils/components.mjs";
import LoadingBarComponent from "./LoadingBar.mjs";
import { props as loadingBarProps } from "./props.mjs";
const OPACITY_DELAY = 200;
let valueTimer;
let errorTimer;
let finishTimer;
let finishErrorTimer;
let opacityTimer;
let isMount;
let internalOptions = {};
const internalProps = {
  value: 0,
  opacity: 0,
  error: false
};
const ctx = reactive(internalProps);
function setDefaultOptions(options) {
  Object.assign(ctx, options);
  internalOptions = options;
}
const resetDefaultOptions = () => {
  Object.keys(internalOptions).forEach((key) => {
    if (ctx[key] !== void 0) {
      ctx[key] = void 0;
    }
  });
};
const mount = () => {
  if (!isMount) {
    isMount = true;
    mountInstance(LoadingBarComponent, ctx);
  }
};
const tick = () => {
  valueTimer = window.setTimeout(() => {
    ctx.transitionDuration = void 0;
    if (ctx.value >= 95) {
      return;
    }
    ctx.value += ctx.value < 70 ? Math.round(5 * Math.random()) : Math.random();
    tick();
  }, 200);
};
const clearTimer = () => {
  window.clearTimeout(valueTimer);
  window.clearTimeout(opacityTimer);
  window.clearTimeout(finishTimer);
  window.clearTimeout(errorTimer);
  window.clearTimeout(finishErrorTimer);
};
const finishTask = () => {
  clearTimer();
  ctx.value = 100;
  opacityTimer = window.setTimeout(() => {
    ctx.opacity = 0;
    finishErrorTimer = window.setTimeout(() => {
      ctx.error = false;
    }, 250);
  }, OPACITY_DELAY + 100);
};
const start = () => {
  clearTimer();
  ctx.error = false;
  ctx.value = 0;
  ctx.transitionDuration = 0;
  mount();
  opacityTimer = window.setTimeout(() => {
    ctx.opacity = 1;
  }, OPACITY_DELAY);
  tick();
};
const finish = () => {
  var _a;
  finishTimer = window.setTimeout(finishTask, (_a = ctx.finishDelay) != null ? _a : 0);
};
const error = () => {
  clearTimer();
  ctx.error = true;
  if (ctx.value === 100) {
    ctx.value = 0;
    ctx.transitionDuration = 0;
  }
  mount();
  opacityTimer = window.setTimeout(() => {
    ctx.opacity = 1;
  }, OPACITY_DELAY);
  tick();
  errorTimer = window.setTimeout(finishTask, 300);
};
const LoadingBar = {
  start,
  finish,
  error,
  setDefaultOptions,
  resetDefaultOptions
};
const _LoadingBarComponent = LoadingBar;
var stdin_default = LoadingBar;
export {
  _LoadingBarComponent,
  stdin_default as default,
  loadingBarProps
};
