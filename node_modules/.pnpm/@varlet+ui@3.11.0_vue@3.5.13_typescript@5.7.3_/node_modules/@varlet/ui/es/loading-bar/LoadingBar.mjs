import { defineComponent, createVNode as _createVNode } from "vue";
import context from "../context/index.mjs";
import { createNamespace } from "../utils/components.mjs";
import { toSizeUnit } from "../utils/elements.mjs";
import { props } from "./props.mjs";


const {
  name,
  classes,
  n
} = createNamespace("loading-bar");
var stdin_default = defineComponent({
  name,
  props,
  setup(props2) {
    return () => _createVNode("div", {
      "class": classes(n(), [props2.error, n("--error")]),
      "style": {
        zIndex: context.zIndex + 10,
        width: `${props2.value}%`,
        opacity: props2.opacity,
        height: toSizeUnit(props2.height),
        backgroundColor: props2.error ? props2.errorColor : props2.color,
        top: toSizeUnit(props2.top),
        transitionDuration: props2.transitionDuration == null ? void 0 : `${props2.transitionDuration}ms`
      }
    }, null);
  }
});
export {
  stdin_default as default
};
