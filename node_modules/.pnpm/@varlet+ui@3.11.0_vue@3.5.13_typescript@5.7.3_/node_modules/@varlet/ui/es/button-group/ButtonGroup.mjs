import { computed, defineComponent } from "vue";
import { createNamespace, formatElevation } from "../utils/components.mjs";
import { props } from "./props.mjs";
import { useButtons } from "./provide.mjs";
const { name, n, classes } = createNamespace("button-group");
import { renderSlot as _renderSlot, normalizeClass as _normalizeClass, openBlock as _openBlock, createElementBlock as _createElementBlock } from "vue";
function __render__(_ctx, _cache) {
  return _openBlock(), _createElementBlock(
    "div",
    {
      class: _normalizeClass(
        _ctx.classes(
          _ctx.n(),
          _ctx.n("$--box"),
          [_ctx.mode, `${_ctx.n(`--mode-${_ctx.mode}`)}`],
          [_ctx.vertical, _ctx.n("--vertical"), _ctx.n("--horizontal")],
          [_ctx.mode === "normal", _ctx.formatElevation(_ctx.elevation, 2)]
        )
      )
    },
    [
      _renderSlot(_ctx.$slots, "default")
    ],
    2
    /* CLASS */
  );
}
const __sfc__ = defineComponent({
  name,
  props,
  setup(props2) {
    const { bindButtons } = useButtons();
    const buttonGroupProvider = {
      elevation: computed(() => props2.elevation),
      type: computed(() => props2.type),
      size: computed(() => props2.size),
      color: computed(() => props2.color),
      textColor: computed(() => props2.textColor),
      mode: computed(() => props2.mode)
    };
    bindButtons(buttonGroupProvider);
    return {
      n,
      classes,
      formatElevation
    };
  }
});
__sfc__.render = __render__;
var stdin_default = __sfc__;
export {
  stdin_default as default
};
