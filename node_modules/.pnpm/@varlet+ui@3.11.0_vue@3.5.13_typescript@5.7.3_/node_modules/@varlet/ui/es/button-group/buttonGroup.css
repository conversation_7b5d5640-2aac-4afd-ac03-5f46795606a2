.var-button-group { display: inline-flex; border-radius: var(--button-border-radius); max-width: 100%; overflow: auto;}.var-button-group .var-button:active { box-shadow: none;}.var-button-group--horizontal .var-button:first-child { border-top-right-radius: 0; border-bottom-right-radius: 0;}.var-button-group--horizontal .var-button:last-child { border-top-left-radius: 0; border-bottom-left-radius: 0;}.var-button-group--horizontal .var-button:not(:first-child):not(:last-child) { border-radius: 0;}.var-button-group--horizontal.var-button-group--mode-text .var-button { border-right: thin solid currentColor;}.var-button-group--horizontal.var-button-group--mode-text .var-button:last-child { border: none;}.var-button-group--horizontal.var-button-group--mode-outline .var-button:not(:first-child) { border-left: none;}.var-button-group--vertical { flex-direction: column;}.var-button-group--vertical .var-button:first-child { border-bottom-left-radius: 0; border-bottom-right-radius: 0;}.var-button-group--vertical .var-button:last-child { border-top-left-radius: 0; border-top-right-radius: 0;}.var-button-group--vertical .var-button:not(:first-child):not(:last-child) { border-radius: 0;}.var-button-group--vertical.var-button-group--mode-text .var-button { border-bottom: thin solid currentColor;}.var-button-group--vertical.var-button-group--mode-text .var-button:last-child { border: none;}.var-button-group--vertical.var-button-group--mode-outline .var-button:not(:first-child) { border-top: none;}