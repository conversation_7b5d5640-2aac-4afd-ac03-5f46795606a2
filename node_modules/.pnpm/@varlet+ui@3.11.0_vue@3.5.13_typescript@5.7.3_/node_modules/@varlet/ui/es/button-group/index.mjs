import { withInst<PERSON>, withPropsDefaultsSetter } from "../utils/components.mjs";
import ButtonGroup from "./ButtonGroup.mjs";
import { props as buttonGroupProps } from "./props.mjs";
withInstall(ButtonGroup);
withPropsDefaultsSetter(ButtonGroup, buttonGroupProps);
const _ButtonGroupComponent = ButtonGroup;
var stdin_default = ButtonGroup;
export {
  _ButtonGroupComponent,
  buttonGroupProps,
  stdin_default as default
};
