import { iconProps } from "../icon/index.mjs";
import { pickProps } from "../utils/components.mjs";
const props = {
  type: {
    type: String,
    default: "default"
  },
  position: {
    type: String,
    default: "right-top"
  },
  hidden: Boolean,
  value: {
    type: [String, Number],
    default: 0
  },
  maxValue: [String, Number],
  dot: Boolean,
  icon: pickProps(iconProps, "name"),
  namespace: pickProps(iconProps, "namespace"),
  color: String,
  offsetX: {
    type: [String, Number],
    default: 0
  },
  offsetY: {
    type: [String, Number],
    default: 0
  }
};
export {
  props
};
