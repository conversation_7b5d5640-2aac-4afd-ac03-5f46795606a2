var __defProp = Object.defineProperty;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
import { computed, defineComponent } from "vue";
import { toNumber } from "@varlet/shared";
import VarIcon from "../icon/index.mjs";
import { createNamespace } from "../utils/components.mjs";
import { toSizeUnit } from "../utils/elements.mjs";
import { props } from "./props.mjs";
const { name, n, classes } = createNamespace("badge");
import { renderSlot as _renderSlot, resolveComponent as _resolveComponent, normalizeClass as _normalizeClass, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, createElementBlock as _createElementBlock, vShow as _vShow, mergeProps as _mergeProps, createElementVNode as _createElementVNode, withDirectives as _withDirectives, Transition as _Transition, withCtx as _withCtx, createVNode as _createVNode } from "vue";
const _hoisted_1 = { key: 0 };
function __render__(_ctx, _cache) {
  const _component_var_icon = _resolveComponent("var-icon");
  return _openBlock(), _createElementBlock(
    "div",
    {
      class: _normalizeClass(_ctx.classes(_ctx.n(), _ctx.n("$--box")))
    },
    [
      _renderSlot(_ctx.$slots, "default"),
      _createVNode(_Transition, {
        name: _ctx.n("$-badge-fade"),
        persisted: ""
      }, {
        default: _withCtx(() => [
          _withDirectives(_createElementVNode(
            "span",
            _mergeProps({
              class: _ctx.classes(
                _ctx.n("content"),
                _ctx.n(`--${_ctx.type}`),
                [_ctx.$slots.default, _ctx.n(`--${_ctx.position}`), _ctx.n("--offset")],
                [_ctx.dot, _ctx.n("--dot")],
                [_ctx.icon, _ctx.n("--icon")]
              ),
              style: __spreadValues({ background: _ctx.color }, _ctx.offsetStyle)
            }, _ctx.$attrs),
            [
              _ctx.icon ? (_openBlock(), _createBlock(_component_var_icon, {
                key: 0,
                class: _normalizeClass(_ctx.n("icon")),
                "var-badge-cover": "",
                name: _ctx.icon,
                namespace: _ctx.namespace
              }, null, 8, ["class", "name", "namespace"])) : _createCommentVNode("v-if", true),
              _renderSlot(_ctx.$slots, "value", {}, () => [
                !_ctx.icon && !_ctx.dot ? (_openBlock(), _createElementBlock(
                  "span",
                  _hoisted_1,
                  _toDisplayString(_ctx.value),
                  1
                  /* TEXT */
                )) : _createCommentVNode("v-if", true)
              ])
            ],
            16
            /* FULL_PROPS */
          ), [
            [_vShow, !_ctx.hidden]
          ])
        ]),
        _: 3
        /* FORWARDED */
      }, 8, ["name"])
    ],
    2
    /* CLASS */
  );
}
const __sfc__ = defineComponent({
  name,
  components: { VarIcon },
  inheritAttrs: false,
  props,
  setup(props2) {
    const value = computed(() => {
      const { value: value2, maxValue } = props2;
      return value2 != null && maxValue != null && toNumber(value2) > toNumber(maxValue) ? `${maxValue}+` : value2;
    });
    const offsetStyle = computed(() => ({
      [`--badge-offset-y`]: toSizeUnit(props2.offsetY),
      [`--badge-offset-x`]: toSizeUnit(props2.offsetX)
    }));
    return {
      value,
      offsetStyle,
      n,
      classes
    };
  }
});
__sfc__.render = __render__;
var stdin_default = __sfc__;
export {
  stdin_default as default
};
