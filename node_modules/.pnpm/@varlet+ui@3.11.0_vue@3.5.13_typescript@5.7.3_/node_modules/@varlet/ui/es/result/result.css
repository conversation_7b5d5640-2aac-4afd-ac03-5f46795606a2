:root { --result-background: #fff; --result-padding: 24px; --result-border-radius: 3px; --result-title-color: #444; --result-title-font-size: 32px; --result-title-margin: 15px 0 0 0; --result-image-size: 80px; --result-title-font-weight: 500; --result-description-margin: 10px 0 0 0; --result-description-font-size: 14px; --result-description-color: rgba(0, 0, 0, 0.6); --result-description-line-height: 1.6; --result-info-color: var(--color-info); --result-info-border-color: rgba(0, 175, 239, 0.3); --result-success-color: var(--color-success); --result-success-border-color: rgba(0, 196, 143, 0.3); --result-error-color: var(--color-danger); --result-error-border-color: rgba(244, 67, 54, 0.3); --result-warning-color: var(--color-warning); --result-warning-border-color: rgba(255, 159, 0, 0.3); --result-question-color: #607d8b; --result-question-border-color: rgba(96, 125, 139, 0.3); --result-empty-color: #9e9e9e; --result-empty-border-color: rgba(158, 158, 158, 0.3);}.var-result { display: flex; flex-direction: column; align-items: center; border-radius: var(--result-border-radius); width: 100%; padding: var(--result-padding); background-color: var(--result-background); transition: background-color 0.25s;}.var-result__image-container { overflow: hidden;}.var-result__image { margin: 0 auto; position: relative; border-radius: 50%; box-sizing: content-box; border-style: solid; border-color: transparent; width: var(--result-image-size); height: var(--result-image-size); display: flex; justify-content: center; align-items: center;}.var-result__image svg { margin: 0 auto; display: block; width: 100%; height: 100%;}.var-result__info { border-color: var(--result-info-border-color);}.var-result__info path { fill: var(--result-info-color);}.var-result__empty { border-color: var(--result-empty-border-color);}.var-result__empty path { fill: var(--result-empty-color);}.var-result__question { border-color: var(--result-question-border-color);}.var-result__question path { fill: var(--result-question-color);}.var-result__error { border-color: var(--result-error-border-color);}.var-result__error path { fill: var(--result-error-color);}.var-result__warning { border-color: var(--result-warning-border-color);}.var-result__warning path { fill: var(--result-warning-color);}.var-result__success { border-color: var(--result-success-color);}.var-result__success-cover-left { display: block; position: absolute; z-index: 1; top: -0.875%; left: -41.27%; width: 75%; height: 150%; border-radius: 150% 0 0 150%; transform: rotate(-45deg); background: var(--result-background); transition: background-color 0.25s;}.var-result__success-cover-right { display: block; position: absolute; top: -13.75%; left: 37.5%; width: 75%; height: 150%; border-radius: 0 125% 125% 0; animation-name: rotate-circle; animation-timing-function: ease-in; transform-origin: 0 50%; transform: rotate(-45deg); background: var(--result-background); transition: background-color 0.25s;}.var-result__success-line { height: 6.8%; background-color: var(--result-success-color); display: block; position: absolute; z-index: 10; transition: background-color 0.25s;}.var-result__success-line-tip { transform: rotate(45deg); animation-name: success-line-tip; animation-fill-mode: forwards;}.var-result__success-line-long { transform: rotate(-45deg); animation-name: success-line-long; animation-fill-mode: forwards;}.var-result__success-circle { z-index: 10; width: 100%; height: 100%; border-radius: 50%; position: absolute; box-sizing: content-box !important; border-style: solid; border-color: var(--result-success-border-color);}.var-result__success-line-fix { top: 9%; left: 34%; width: 8.75%; height: 112.5%; z-index: 1; position: absolute; transform: rotate(-45deg); background: var(--result-background); transition: background-color 0.25s;}@keyframes success-line-tip { 0% { top: 23.75%; left: 1.25%; width: 0; } 54% { top: 20.25%; left: 1%; width: 0; } 70% { top: 43.75%; left: -9.5%; width: 62.5%; } 84% { top: 60%; left: 26.25%; width: 21.25%; } 100% { top: 56.25%; left: 17.5%; width: 31.25%; }}@keyframes success-line-long { 0% { top: 67.5%; right: 57.5%; width: 0; } 65% { top: 67.5%; right: 57.5%; width: 0; } 84% { top: 43.75%; right: 0; width: 68.75%; } 100% { top: 47%; right: 9%; width: 58.75%; }}@keyframes rotate-circle { 0% { transform: rotate(-45deg); } 5% { transform: rotate(-45deg); } 12% { transform: rotate(-405deg); } 100% { transform: rotate(-405deg); }}.var-result__title { font-size: var(--result-title-font-size); word-break: break-word; margin: var(--result-title-margin); font-weight: var(--result-title-font-weight); color: var(--result-title-color); text-align: center;}.var-result__description { font-size: var(--result-description-font-size); word-break: break-word; color: var(--result-description-color); margin: var(--result-description-margin); line-height: var(--result-description-line-height); text-align: center;}.var-result__footer { z-index: 1; margin-top: 20px;}