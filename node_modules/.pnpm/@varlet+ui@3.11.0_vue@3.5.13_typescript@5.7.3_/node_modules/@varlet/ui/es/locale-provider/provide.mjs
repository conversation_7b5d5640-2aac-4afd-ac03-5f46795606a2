import { inject, provide } from "vue";
import { keyInProvides } from "@varlet/use";
const LOCALE_PROVIDER_KEY = Symbol("LOCALE_PROVIDER_KEY");
function provideLocaleProvider(localeProvider) {
  provide(LOCALE_PROVIDER_KEY, localeProvider);
}
function injectLocaleProvider() {
  if (!keyInProvides(LOCALE_PROVIDER_KEY)) {
    return { t: null };
  }
  return inject(LOCALE_PROVIDER_KEY);
}
export {
  LOCALE_PROVIDER_KEY,
  injectLocaleProvider,
  provideLocaleProvider
};
