import { withInstall, withPropsDefaultsSetter } from "../utils/components.mjs";
import LocaleProvider from "./LocaleProvider.mjs";
import { props as localeProviderProps } from "./props.mjs";
withInstall(LocaleProvider);
withPropsDefaultsSetter(LocaleProvider, localeProviderProps);
const _LocaleProviderComponent = LocaleProvider;
var stdin_default = LocaleProvider;
export {
  _LocaleProviderComponent,
  stdin_default as default
};
