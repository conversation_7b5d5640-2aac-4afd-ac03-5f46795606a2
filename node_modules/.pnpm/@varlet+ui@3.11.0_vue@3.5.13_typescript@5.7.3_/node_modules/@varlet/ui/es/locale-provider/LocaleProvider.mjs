var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
import { computed, defineComponent, h } from "vue";
import { call, hasOwn } from "@varlet/shared";
import { createNamespace } from "../utils/components.mjs";
import { props } from "./props.mjs";
import { provideLocaleProvider } from "./provide.mjs";
const { name, n } = createNamespace("locale-provider");
const __sfc__ = defineComponent({
  name,
  props,
  setup(props2, { slots }) {
    const messages = computed(
      () => {
        var _a;
        return Object.entries((_a = props2.messages) != null ? _a : {}).reduce(
          (messages2, [key, value]) => {
            messages2[key] = __spreadProps(__spreadValues({}, value), {
              lang: key
            });
            return messages2;
          },
          {}
        );
      }
    );
    provideLocaleProvider({
      t
    });
    function t(id) {
      if (hasOwn(messages.value, props2.locale) && hasOwn(messages.value[props2.locale], id)) {
        return messages.value[props2.locale][id];
      }
    }
    return () => h(
      props2.tag,
      {
        class: n()
      },
      call(slots.default)
    );
  }
});
var stdin_default = __sfc__;
export {
  stdin_default as default
};
