import { defineComponent, onActivated, onMounted, ref } from "vue";
import { call, getScrollLeft, getScrollTop, throttle } from "@varlet/shared";
import { onSmartUnmounted } from "@varlet/use";
import VarButton from "../button/index.mjs";
import VarIcon from "../icon/index.mjs";
import { createNamespace } from "../utils/components.mjs";
import { getParentScroller, getTarget, scrollTo, toPxNum, toSizeUnit } from "../utils/elements.mjs";
import { easeInOutCubic } from "../utils/shared.mjs";
import { props } from "./props.mjs";
const { name, n, classes } = createNamespace("back-top");
import { renderSlot as _renderSlot, resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, withModifiers as _withModifiers, mergeProps as _mergeProps, createElementVNode as _createElementVNode, Teleport as _Teleport, openBlock as _openBlock, createBlock as _createBlock } from "vue";
function __render__(_ctx, _cache) {
  const _component_var_icon = _resolveComponent("var-icon");
  const _component_var_button = _resolveComponent("var-button");
  return _openBlock(), _createBlock(_Teleport, {
    to: "body",
    disabled: _ctx.disabled
  }, [
    _createElementVNode(
      "div",
      _mergeProps({
        ref: "backTopEl",
        class: _ctx.classes(_ctx.n(), [_ctx.show, _ctx.n("--active")]),
        style: {
          right: _ctx.toSizeUnit(_ctx.right),
          bottom: _ctx.toSizeUnit(_ctx.bottom)
        }
      }, _ctx.$attrs, {
        onClick: _cache[0] || (_cache[0] = _withModifiers((...args) => _ctx.handleClick && _ctx.handleClick(...args), ["stop"]))
      }),
      [
        _renderSlot(_ctx.$slots, "default", {}, () => [
          _createVNode(_component_var_button, {
            elevation: _ctx.elevation,
            type: "primary",
            "var-back-top-cover": ""
          }, {
            default: _withCtx(() => [
              _createVNode(_component_var_icon, { name: "chevron-up" })
            ]),
            _: 1
            /* STABLE */
          }, 8, ["elevation"])
        ])
      ],
      16
      /* FULL_PROPS */
    )
  ], 8, ["disabled"]);
}
const __sfc__ = defineComponent({
  name,
  components: {
    VarButton,
    VarIcon
  },
  inheritAttrs: false,
  props,
  setup(props2) {
    const show = ref(false);
    const backTopEl = ref(null);
    const disabled = ref(true);
    let scroller;
    const handleScroll = throttle(() => {
      setBackTopVisibility();
    }, 200);
    onMounted(() => {
      setScroller();
      addScrollerEventListener();
      setBackTopVisibility();
      disabled.value = false;
    });
    onActivated(addScrollerEventListener);
    onSmartUnmounted(removeScrollerEventListener);
    function setBackTopVisibility() {
      show.value = getScrollTop(scroller) >= toPxNum(props2.visibilityHeight);
    }
    function handleClick(event) {
      call(props2.onClick, event);
      const left = getScrollLeft(scroller);
      scrollTo(scroller, {
        left,
        duration: props2.duration,
        animation: easeInOutCubic
      });
    }
    function setScroller() {
      scroller = props2.target ? getTarget(props2.target, "BackTop") : getParentScroller(backTopEl.value);
    }
    function addScrollerEventListener() {
      scroller.addEventListener("scroll", handleScroll);
    }
    function removeScrollerEventListener() {
      if (!scroller) {
        return;
      }
      scroller.removeEventListener("scroll", handleScroll);
    }
    return {
      disabled,
      show,
      backTopEl,
      toSizeUnit,
      n,
      classes,
      handleClick
    };
  }
});
__sfc__.render = __render__;
var stdin_default = __sfc__;
export {
  stdin_default as default
};
