:root { --back-top-right: 40px; --back-top-bottom: 40px; --back-top-button-size: 40px; --back-top-button-border-radius: 50%;}.var-back-top { position: fixed; z-index: 100; right: var(--back-top-right); bottom: var(--back-top-bottom); transform: scale(0); transition: 0.3s var(--cubic-bezier); -webkit-tap-highlight-color: transparent;}.var-back-top .var-button[var-back-top-cover] { width: var(--back-top-button-size); height: var(--back-top-button-size); border-radius: var(--back-top-button-border-radius);}.var-back-top--active { transform: scale(1);}