import { defineListenerProp } from "../utils/components.mjs";
const props = {
  visibilityHeight: {
    type: [Number, String],
    default: 200
  },
  duration: {
    type: Number,
    default: 300
  },
  right: [Number, String],
  elevation: {
    type: [Boolean, Number, String],
    default: true
  },
  bottom: [Number, String],
  target: [String, Object],
  onClick: defineListenerProp()
};
export {
  props
};
