import { computed, defineComponent } from "vue";
import { call, isPlainObject, toNumber } from "@varlet/shared";
import { createNamespace } from "../utils/components.mjs";
import { padStartFlex, toSizeUnit } from "../utils/elements.mjs";
import { props } from "./props.mjs";
import { useRow } from "./provide.mjs";
const { name, n, classes } = createNamespace("col");
import { renderSlot as _renderSlot, normalizeClass as _normalizeClass, normalizeStyle as _normalizeStyle, openBlock as _openBlock, createElementBlock as _createElementBlock } from "vue";
function __render__(_ctx, _cache) {
  return _openBlock(), _createElementBlock(
    "div",
    {
      class: _normalizeClass(
        _ctx.classes(
          _ctx.n(),
          _ctx.n("$--box"),
          [_ctx.span >= 0, _ctx.n(`--span-${_ctx.span}`)],
          [_ctx.offset, _ctx.n(`--offset-${_ctx.offset}`)],
          ..._ctx.getSize("xs", _ctx.xs),
          ..._ctx.getSize("sm", _ctx.sm),
          ..._ctx.getSize("md", _ctx.md),
          ..._ctx.getSize("lg", _ctx.lg),
          ..._ctx.getSize("xl", _ctx.xl)
        )
      ),
      style: _normalizeStyle({
        flexDirection: _ctx.direction,
        justifyContent: _ctx.padStartFlex(_ctx.justify),
        alignItems: _ctx.padStartFlex(_ctx.align),
        paddingLeft: _ctx.toSizeUnit(_ctx.padding.left),
        paddingRight: _ctx.toSizeUnit(_ctx.padding.right),
        paddingTop: _ctx.toSizeUnit(_ctx.padding.top),
        paddingBottom: _ctx.toSizeUnit(_ctx.padding.bottom)
      }),
      onClick: _cache[0] || (_cache[0] = (...args) => _ctx.handleClick && _ctx.handleClick(...args))
    },
    [
      _renderSlot(_ctx.$slots, "default")
    ],
    6
    /* CLASS, STYLE */
  );
}
const __sfc__ = defineComponent({
  name,
  props,
  setup(props2) {
    const span = computed(() => toNumber(props2.span));
    const offset = computed(() => toNumber(props2.offset));
    const padding = computed(() => {
      var _a;
      const [y = 0, x = 0] = (_a = row == null ? void 0 : row.average.value) != null ? _a : [];
      return { left: x, right: x, top: y, bottom: y };
    });
    const { row, bindRow } = useRow();
    call(bindRow, null);
    function getSize(mode, size) {
      const classes2 = [];
      if (size == null) {
        return classes2;
      }
      if (isPlainObject(size)) {
        const { offset: offset2, span: span2 } = size;
        if (Number(span2) >= 0) {
          classes2.push(n(`--span-${mode}-${span2}`));
        }
        if (offset2) {
          classes2.push(n(`--offset-${mode}-${offset2}`));
        }
        return classes2;
      }
      if (Number(size) >= 0) {
        classes2.push(n(`--span-${mode}-${size}`));
      }
      return classes2;
    }
    function handleClick(e) {
      call(props2.onClick, e);
    }
    return {
      span,
      offset,
      padding,
      n,
      classes,
      toNumber,
      toSizeUnit,
      getSize,
      handleClick,
      padStartFlex
    };
  }
});
__sfc__.render = __render__;
var stdin_default = __sfc__;
export {
  stdin_default as default
};
