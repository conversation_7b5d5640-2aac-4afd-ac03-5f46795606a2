var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
import { computed, defineComponent, nextTick, onUpdated, ref, watch } from "vue";
import { call, doubleRaf, getStyle, isEmpty } from "@varlet/shared";
import { onSmartMounted, onWindowResize } from "@varlet/use";
import VarIcon from "../icon/index.mjs";
import { usePopup } from "../popup/provide.mjs";
import { useSwipeResizeDispatcher } from "../swipe/provide.mjs";
import { createNamespace } from "../utils/components.mjs";
import { props } from "./props.mjs";
const { name, n, classes } = createNamespace("field-decorator");
import { renderSlot as _renderSlot, normalizeClass as _normalizeClass, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, normalizeStyle as _normalizeStyle, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, Teleport as _Teleport, createBlock as _createBlock, Fragment as _Fragment } from "vue";
const _hoisted_1 = ["for"];
function __render__(_ctx, _cache) {
  const _component_var_icon = _resolveComponent("var-icon");
  return _openBlock(), _createElementBlock(
    "div",
    {
      class: _normalizeClass(_ctx.classes(_ctx.n(), _ctx.n("$--box"), _ctx.n(`--${_ctx.variant}`), [_ctx.size === "small", _ctx.n("--small")], [_ctx.disabled, _ctx.n("--disabled")])),
      onClick: _cache[0] || (_cache[0] = (...args) => _ctx.handleClick && _ctx.handleClick(...args))
    },
    [
      _createElementVNode(
        "div",
        {
          class: _normalizeClass(
            _ctx.classes(
              _ctx.n("controller"),
              [_ctx.isFocusing, _ctx.n("--focus")],
              [_ctx.isError, _ctx.n("--error")],
              [_ctx.formDisabled || _ctx.disabled, _ctx.n("--disabled")]
            )
          ),
          style: _normalizeStyle({
            color: _ctx.color,
            cursor: _ctx.cursor,
            overflow: _ctx.isFloating ? "visible" : "hidden",
            "--field-decorator-middle-offset-left": _ctx.middleOffsetLeft,
            "--field-decorator-middle-offset-width": _ctx.middleOffsetWidth,
            "--field-decorator-middle-offset-height": _ctx.middleOffsetHeight
          })
        },
        [
          _createElementVNode(
            "div",
            {
              class: _normalizeClass(_ctx.classes(_ctx.n("icon"), [!_ctx.hint, _ctx.n("--icon-non-hint")]))
            },
            [
              _renderSlot(_ctx.$slots, "prepend-icon")
            ],
            2
            /* CLASS */
          ),
          _createElementVNode(
            "div",
            {
              ref: "middleEl",
              class: _normalizeClass(_ctx.classes(_ctx.n("middle"), [!_ctx.hint, _ctx.n("--middle-non-hint")]))
            },
            [
              _renderSlot(_ctx.$slots, "default")
            ],
            2
            /* CLASS */
          ),
          _ctx.placeholder && _ctx.hint ? (_openBlock(), _createElementBlock("label", {
            key: 0,
            class: _normalizeClass(
              _ctx.classes(
                _ctx.n("placeholder"),
                _ctx.n("$--ellipsis"),
                [_ctx.isFocusing, _ctx.n("--focus")],
                [_ctx.hintCenter, _ctx.n("--hint-center")],
                [_ctx.formDisabled || _ctx.disabled, _ctx.n("--disabled")],
                [_ctx.isError, _ctx.n("--error")],
                [_ctx.transitionDisabled, _ctx.n("--transition-disabled")],
                _ctx.computePlaceholderState()
              )
            ),
            style: _normalizeStyle({
              color: _ctx.color
            }),
            for: _ctx.id
          }, [
            _createElementVNode(
              "span",
              null,
              _toDisplayString(_ctx.placeholder),
              1
              /* TEXT */
            )
          ], 14, _hoisted_1)) : _createCommentVNode("v-if", true),
          _createElementVNode(
            "div",
            {
              class: _normalizeClass(_ctx.classes(_ctx.n("icon"), [!_ctx.hint, _ctx.n("--icon-non-hint")]))
            },
            [
              _ctx.clearable && !_ctx.isEmpty(_ctx.value) ? _renderSlot(_ctx.$slots, "clear-icon", {
                key: 0,
                clear: _ctx.handleClear
              }, () => [
                _createVNode(_component_var_icon, {
                  class: _normalizeClass(_ctx.n("clear-icon")),
                  "var-field-decorator-cover": "",
                  name: "close-circle",
                  onClick: _ctx.handleClear
                }, null, 8, ["class", "onClick"])
              ]) : _createCommentVNode("v-if", true),
              _renderSlot(_ctx.$slots, "append-icon")
            ],
            2
            /* CLASS */
          )
        ],
        6
        /* CLASS, STYLE */
      ),
      _ctx.line ? (_openBlock(), _createElementBlock(
        _Fragment,
        { key: 0 },
        [
          _ctx.variant === "outlined" ? (_openBlock(), _createElementBlock(
            "fieldset",
            {
              key: 0,
              class: _normalizeClass(
                _ctx.classes(
                  _ctx.n("line"),
                  [_ctx.isFocusing, _ctx.n("--line-focus")],
                  [_ctx.isError, _ctx.n("--line-error")],
                  [_ctx.formDisabled || _ctx.disabled, _ctx.n("--line-disabled")]
                )
              ),
              style: _normalizeStyle({ borderColor: _ctx.color })
            },
            [
              _createElementVNode(
                "legend",
                {
                  class: _normalizeClass(_ctx.classes(_ctx.n("line-legend"), [_ctx.isFloating, _ctx.n("line-legend--hint")])),
                  style: _normalizeStyle({ width: _ctx.legendWidth })
                },
                [
                  _ctx.placeholder && _ctx.hint ? (_openBlock(), _createBlock(_Teleport, {
                    key: 0,
                    to: "body"
                  }, [
                    _createElementVNode(
                      "span",
                      {
                        ref: "placeholderTextEl",
                        class: _normalizeClass(
                          _ctx.classes(_ctx.n("placeholder-text"), _ctx.n("$--ellipsis"), [_ctx.size === "small", _ctx.n("placeholder-text--small")])
                        )
                      },
                      _toDisplayString(_ctx.placeholder),
                      3
                      /* TEXT, CLASS */
                    )
                  ])) : _createCommentVNode("v-if", true)
                ],
                6
                /* CLASS, STYLE */
              )
            ],
            6
            /* CLASS, STYLE */
          )) : (_openBlock(), _createElementBlock(
            "div",
            {
              key: 1,
              class: _normalizeClass(_ctx.classes(_ctx.n("line"), [_ctx.formDisabled || _ctx.disabled, _ctx.n("--line-disabled")], [_ctx.isError, _ctx.n("--line-error")])),
              style: _normalizeStyle({ background: !_ctx.isError ? _ctx.blurColor : void 0 })
            },
            [
              _createElementVNode(
                "div",
                {
                  class: _normalizeClass(
                    _ctx.classes(
                      _ctx.n("dot"),
                      [_ctx.isFocusing, _ctx.n("--line-focus")],
                      [_ctx.formDisabled || _ctx.disabled, _ctx.n("--line-disabled")],
                      [_ctx.isError, _ctx.n("--line-error")]
                    )
                  ),
                  style: _normalizeStyle({ background: !_ctx.isError ? _ctx.focusColor : void 0 })
                },
                null,
                6
                /* CLASS, STYLE */
              )
            ],
            6
            /* CLASS, STYLE */
          ))
        ],
        64
        /* STABLE_FRAGMENT */
      )) : _createCommentVNode("v-if", true)
    ],
    2
    /* CLASS */
  );
}
const __sfc__ = defineComponent({
  name,
  components: { VarIcon },
  props,
  setup(props2) {
    const placeholderTextEl = ref(null);
    const middleEl = ref(null);
    const legendWidth = ref("");
    const middleOffsetLeft = ref("0px");
    const middleOffsetWidth = ref("0px");
    const middleOffsetHeight = ref("0px");
    const transitionDisabled = ref(true);
    const isFloating = computed(() => props2.hint && (!isEmpty(props2.value) || props2.isFocusing));
    const { popup, bindPopup } = usePopup();
    const { bindSwipeResizeDispatcher } = useSwipeResizeDispatcher();
    const color = computed(
      () => !props2.isError ? props2.isFocusing ? props2.focusColor : props2.blurColor : void 0
    );
    onWindowResize(resize);
    onSmartMounted(() => {
      resize();
      nextTick().then(() => {
        transitionDisabled.value = false;
      });
    });
    onUpdated(resize);
    call(bindPopup, null);
    call(bindSwipeResizeDispatcher, {
      onResize() {
        nextTick().then(resize);
      }
    });
    if (popup) {
      watch(
        () => popup.show.value,
        (show) => __async(this, null, function* () {
          if (show) {
            yield doubleRaf();
            resize();
          }
        })
      );
    }
    function computePlaceholderState() {
      const { hint, value, composing } = props2;
      if (!hint && (!isEmpty(value) || composing)) {
        return n("--placeholder-hidden");
      }
      if (isFloating.value) {
        return n("--placeholder-hint");
      }
    }
    function handleClear(e) {
      call(props2.onClear, e);
    }
    function handleClick(e) {
      call(props2.onClick, e);
    }
    function resize() {
      middleOffsetLeft.value = `${middleEl.value.offsetLeft}px`;
      middleOffsetWidth.value = `${middleEl.value.offsetWidth}px`;
      middleOffsetHeight.value = `${middleEl.value.offsetHeight}px`;
      if (props2.variant === "outlined" && placeholderTextEl.value) {
        const placeholderTextStyle = getStyle(placeholderTextEl.value);
        const placeholderSpace = `var(--field-decorator-outlined-${props2.size}-placeholder-space)`;
        legendWidth.value = `calc(${placeholderTextStyle.width} * 0.75 + ${placeholderSpace} * 2)`;
      }
    }
    return {
      placeholderTextEl,
      middleEl,
      middleOffsetLeft,
      middleOffsetWidth,
      middleOffsetHeight,
      color,
      legendWidth,
      isFloating,
      transitionDisabled,
      resize,
      computePlaceholderState,
      n,
      classes,
      isEmpty,
      handleClear,
      handleClick
    };
  }
});
__sfc__.render = __render__;
var stdin_default = __sfc__;
export {
  stdin_default as default
};
