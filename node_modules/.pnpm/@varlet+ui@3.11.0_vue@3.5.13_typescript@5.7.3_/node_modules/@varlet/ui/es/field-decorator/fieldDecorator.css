:root { --field-decorator-text-color: #555; --field-decorator-error-color: var(--color-danger); --field-decorator-blur-color: #888; --field-decorator-focus-color: var(--color-primary); --field-decorator-placeholder-size: 16px; --field-decorator-icon-size: 20px; --field-decorator-line-size: 1px; --field-decorator-line-focus-size: 2px; --field-decorator-line-border-radius: 4px; --field-decorator-disabled-color: var(--color-text-disabled); --field-decorator-standard-normal-margin-top: 22px; --field-decorator-standard-normal-margin-bottom: 4px; --field-decorator-standard-normal-icon-margin-top: 22px; --field-decorator-standard-normal-icon-margin-bottom: 4px; --field-decorator-standard-normal-non-hint-margin-top: 4px; --field-decorator-standard-small-margin-top: 18px; --field-decorator-standard-small-margin-bottom: 4px; --field-decorator-standard-small-icon-margin-top: 18px; --field-decorator-standard-small-icon-margin-bottom: 4px; --field-decorator-standard-small-non-hint-margin-top: 2px; --field-decorator-outlined-normal-margin-top: 16px; --field-decorator-outlined-normal-margin-bottom: 16px; --field-decorator-outlined-normal-padding-left: 16px; --field-decorator-outlined-normal-padding-right: 16px; --field-decorator-outlined-normal-placeholder-space: 4px; --field-decorator-outlined-normal-icon-margin-top: 16px; --field-decorator-outlined-normal-icon-margin-bottom: 16px; --field-decorator-outlined-small-margin-top: 8px; --field-decorator-outlined-small-margin-bottom: 8px; --field-decorator-outlined-small-padding-left: 12px; --field-decorator-outlined-small-padding-right: 12px; --field-decorator-outlined-small-placeholder-space: 2px; --field-decorator-outlined-small-icon-margin-top: 8px; --field-decorator-outlined-small-icon-margin-bottom: 8px;}.var-field-decorator { position: relative; width: 100%; color: var(--field-decorator-text-color);}.var-field-decorator__controller { width: 100%; display: flex; align-items: stretch; position: relative;}.var-field-decorator__middle { position: relative; flex-grow: 1; display: flex; justify-content: center; align-items: center;}.var-field-decorator__icon { display: flex; align-items: center; font-size: var(--field-decorator-icon-size);}.var-field-decorator__icon .var-icon { font-size: var(--field-decorator-icon-size);}.var-field-decorator__placeholder { position: absolute; top: 0; left: 0; max-width: var(--field-decorator-middle-offset-width); font-size: var(--field-decorator-placeholder-size); line-height: 1.5em; color: var(--field-decorator-placeholder-color, var(--field-decorator-blur-color)); pointer-events: none; cursor: inherit; transform-origin: left; transition: transform 0.28s var(--cubic-bezier), color 0.25s, max-width 0.2s;}.var-field-decorator__clear-icon[var-field-decorator-cover] { font-size: var(--field-decorator-icon-size); margin-left: 6px; cursor: pointer;}.var-field-decorator__placeholder-text { max-width: calc(133% - var(--field-decorator-outlined-normal-padding-left) - var(--field-decorator-outlined-normal-padding-right)); position: absolute; z-index: -1000; top: -1000px; display: inline-block; pointer-events: none; opacity: 0; font-size: var(--field-decorator-placeholder-size);}.var-field-decorator__placeholder-text--small { max-width: calc(133% - var(--field-decorator-outlined-small-padding-left) - var(--field-decorator-outlined-small-padding-right));}.var-field-decorator--placeholder-hidden { visibility: hidden;}.var-field-decorator--focus { color: var(--field-decorator-focus-color);}.var-field-decorator--disabled { color: var(--field-decorator-disabled-color); cursor: not-allowed;}.var-field-decorator--error { color: var(--field-decorator-error-color);}.var-field-decorator--standard .var-field-decorator__middle { margin-top: var(--field-decorator-standard-normal-margin-top); margin-bottom: var(--field-decorator-standard-normal-margin-bottom);}.var-field-decorator--standard .var-field-decorator__icon { margin-top: var(--field-decorator-standard-normal-icon-margin-top); margin-bottom: var(--field-decorator-standard-normal-icon-margin-bottom);}.var-field-decorator--standard .var-field-decorator__placeholder { transform: translate(var(--field-decorator-middle-offset-left), var(--field-decorator-standard-normal-margin-top));}.var-field-decorator--standard .var-field-decorator--placeholder-hint { max-width: 133%; transform: translate(0, 0) scale(0.75) !important;}.var-field-decorator--standard .var-field-decorator__line { width: 100%; height: var(--field-decorator-line-size); background: var(--field-decorator-blur-color); transition: background-color 0.25s;}.var-field-decorator--standard .var-field-decorator--line-disabled { background: var(--field-decorator-disabled-color);}.var-field-decorator--standard .var-field-decorator__dot { width: 100%; height: var(--field-decorator-line-focus-size); background: var(--field-decorator-focus-color); transform: scaleX(0); transform-origin: center; transition: transform 0.3s var(--cubic-bezier), background-color 0.25s;}.var-field-decorator--standard .var-field-decorator--middle-non-hint { margin-top: var(--field-decorator-standard-normal-non-hint-margin-top);}.var-field-decorator--standard .var-field-decorator--icon-non-hint { margin-top: var(--field-decorator-standard-normal-non-hint-margin-top);}.var-field-decorator--standard .var-field-decorator--hint-center { transform: translate(var(--field-decorator-middle-offset-left), calc(var(--field-decorator-standard-normal-margin-top) + var(--field-decorator-middle-offset-height) / 2 - 50%));}.var-field-decorator--standard .var-field-decorator--line-focus { transform: scaleX(1);}.var-field-decorator--standard .var-field-decorator--line-error { background: var(--field-decorator-error-color);}.var-field-decorator--outlined .var-field-decorator__controller { padding: 0 var(--field-decorator-outlined-normal-padding-right) 0 var(--field-decorator-outlined-normal-padding-left);}.var-field-decorator--outlined .var-field-decorator__middle { margin-top: var(--field-decorator-outlined-normal-margin-top); margin-bottom: var(--field-decorator-outlined-normal-margin-bottom);}.var-field-decorator--outlined .var-field-decorator__icon { margin-top: var(--field-decorator-outlined-normal-icon-margin-top); margin-bottom: var(--field-decorator-outlined-normal-icon-margin-bottom);}.var-field-decorator--outlined .var-field-decorator__placeholder { transform: translate(var(--field-decorator-middle-offset-left), var(--field-decorator-outlined-normal-margin-top));}.var-field-decorator--outlined .var-field-decorator--hint-center { transform: translate(var(--field-decorator-middle-offset-left), calc(var(--field-decorator-outlined-normal-margin-top) + var(--field-decorator-middle-offset-height) / 2 - 50%));}.var-field-decorator--outlined .var-field-decorator--placeholder-hint { max-width: calc(133% - var(--field-decorator-outlined-normal-padding-left) - var(--field-decorator-outlined-normal-padding-right)); transform: translate(calc(var(--field-decorator-outlined-normal-padding-left)), -50%) scale(0.75);}.var-field-decorator--outlined .var-field-decorator__line { min-width: 0; width: 100%; height: calc(100% + (var(--field-decorator-placeholder-size) * 0.75 / 2)); position: absolute; top: calc(var(--field-decorator-placeholder-size) * 0.75 / 2 * -1); left: 0; pointer-events: none; border-radius: var(--field-decorator-line-border-radius); border: var(--field-decorator-line-size) solid var(--field-decorator-blur-color); overflow: hidden; padding: 0 calc(var(--field-decorator-outlined-normal-padding-right) - var(--field-decorator-outlined-normal-placeholder-space) - var(--field-decorator-line-size)) 0 calc(var(--field-decorator-outlined-normal-padding-left) - var(--field-decorator-outlined-normal-placeholder-space) - var(--field-decorator-line-size)); margin: 0;}.var-field-decorator--outlined .var-field-decorator__line-legend { max-width: 0; height: calc(var(--field-decorator-placeholder-size) * 0.75); visibility: hidden; padding: 0; overflow: hidden; display: block; white-space: nowrap;}.var-field-decorator--outlined .var-field-decorator__line-legend--hint { max-width: 100%;}.var-field-decorator--outlined .var-field-decorator--line-focus { border-width: var(--field-decorator-line-focus-size); border-color: var(--field-decorator-focus-color); padding: 0 calc(var(--field-decorator-outlined-normal-padding-right) - var(--field-decorator-outlined-normal-placeholder-space) - var(--field-decorator-line-focus-size)) 0 calc(var(--field-decorator-outlined-normal-padding-left) - var(--field-decorator-outlined-normal-placeholder-space) - var(--field-decorator-line-focus-size));}.var-field-decorator--outlined .var-field-decorator--line-disabled { border-radius: var(--field-decorator-line-border-radius); border: var(--field-decorator-line-size) solid var(--field-decorator-disabled-color);}.var-field-decorator--outlined .var-field-decorator--line-error { border-color: var(--field-decorator-error-color) !important;}.var-field-decorator--standard.var-field-decorator--small .var-field-decorator__middle { margin-top: var(--field-decorator-standard-small-margin-top); margin-bottom: var(--field-decorator-standard-small-margin-bottom);}.var-field-decorator--standard.var-field-decorator--small .var-field-decorator__placeholder { transform: translate(var(--field-decorator-middle-offset-left), var(--field-decorator-standard-small-margin-top));}.var-field-decorator--standard.var-field-decorator--small .var-field-decorator--middle-non-hint { margin-top: var(--field-decorator-standard-small-non-hint-margin-top);}.var-field-decorator--standard.var-field-decorator--small .var-field-decorator--icon-non-hint { margin-top: var(--field-decorator-standard-small-non-hint-margin-top);}.var-field-decorator--standard.var-field-decorator--small .var-field-decorator--hint-center { transform: translate(var(--field-decorator-middle-offset-left), calc(var(--field-decorator-standard-small-margin-top) + var(--field-decorator-middle-offset-height) / 2 - 50%));}.var-field-decorator--outlined.var-field-decorator--small .var-field-decorator__controller { padding: 0 var(--field-decorator-outlined-small-padding-right) 0 var(--field-decorator-outlined-small-padding-left);}.var-field-decorator--outlined.var-field-decorator--small .var-field-decorator__middle { margin-top: var(--field-decorator-outlined-small-margin-top); margin-bottom: var(--field-decorator-outlined-small-margin-bottom);}.var-field-decorator--outlined.var-field-decorator--small .var-field-decorator__icon { margin-top: var(--field-decorator-outlined-small-icon-margin-top); margin-bottom: var(--field-decorator-outlined-small-icon-margin-bottom);}.var-field-decorator--outlined.var-field-decorator--small .var-field-decorator__placeholder { transform: translate(var(--field-decorator-middle-offset-left), var(--field-decorator-outlined-small-margin-top));}.var-field-decorator--outlined.var-field-decorator--small .var-field-decorator--hint-center { transform: translate(var(--field-decorator-middle-offset-left), calc(var(--field-decorator-outlined-small-margin-top) + var(--field-decorator-middle-offset-height) / 2 - 50%));}.var-field-decorator--outlined.var-field-decorator--small .var-field-decorator--placeholder-hint { max-width: calc(133% - var(--field-decorator-outlined-small-padding-left) - var(--field-decorator-outlined-small-padding-right)); transform: translate(calc(var(--field-decorator-outlined-small-padding-left)), -50%) scale(0.75);}.var-field-decorator--outlined.var-field-decorator--small .var-field-decorator__line { padding: 0 calc(var(--field-decorator-outlined-small-padding-right) - var(--field-decorator-outlined-small-placeholder-space) - var(--field-decorator-line-size)) 0 calc(var(--field-decorator-outlined-small-padding-left) - var(--field-decorator-outlined-small-placeholder-space) - var(--field-decorator-line-size));}.var-field-decorator--outlined.var-field-decorator--small .var-field-decorator--line-focus { padding: 0 calc(var(--field-decorator-outlined-small-padding-right) - var(--field-decorator-outlined-small-placeholder-space) - var(--field-decorator-line-focus-size)) 0 calc(var(--field-decorator-outlined-small-padding-left) - var(--field-decorator-outlined-small-placeholder-space) - var(--field-decorator-line-focus-size));}.var-field-decorator--transition-disabled { transition: none;}