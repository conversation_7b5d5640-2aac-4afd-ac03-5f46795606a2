var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
import { computed, defineComponent, ref, watch } from "vue";
import { injectHighlighterProvider } from "../highlighter-provider/provide.mjs";
import { createNamespace } from "../utils/components.mjs";
import { props } from "./props.mjs";
const { name, n, classes } = createNamespace("code");
import { normalizeClass as _normalizeClass, openBlock as _openBlock, createElementBlock as _createElementBlock } from "vue";
const _hoisted_1 = ["innerHTML"];
function __render__(_ctx, _cache) {
  return _openBlock(), _createElementBlock("div", {
    class: _normalizeClass(_ctx.classes(_ctx.n(), [_ctx.wordWrap, _ctx.n("--word-wrap")])),
    innerHTML: _ctx.highlightedCode
  }, null, 10, _hoisted_1);
}
const __sfc__ = defineComponent({
  name,
  props,
  setup(props2) {
    const { highlighter, theme } = injectHighlighterProvider();
    const highlightedCode = ref("");
    const normalizedTheme = computed(() => {
      var _a;
      return (_a = props2.theme) != null ? _a : theme == null ? void 0 : theme.value;
    });
    if (highlighter) {
      watch(
        () => [highlighter.value, props2.code, props2.language, normalizedTheme.value, props2.trim],
        (_0) => __async(this, [_0], function* ([highlighter2, code, lang = "", theme2 = "", trim]) {
          if (!highlighter2) {
            return;
          }
          highlightedCode.value = yield highlighter2.codeToHtml(trim ? code.trim() : code, { lang, theme: theme2 });
        }),
        { immediate: true }
      );
    }
    return {
      n,
      classes,
      highlightedCode
    };
  }
});
__sfc__.render = __render__;
var stdin_default = __sfc__;
export {
  stdin_default as default
};
