import { defineComponent } from "vue";
import { inMobile } from "@varlet/shared";
import { createNamespace } from "../utils/components.mjs";
import { props } from "./props.mjs";
const { name, n, classes } = createNamespace("hover-overlay");
import { normalizeClass as _normalizeClass, openBlock as _openBlock, createElementBlock as _createElementBlock } from "vue";
function __render__(_ctx, _cache) {
  return _openBlock(), _createElementBlock(
    "div",
    {
      class: _normalizeClass(_ctx.classes(_ctx.n(), [_ctx.hovering, _ctx.n("--hovering")], [_ctx.focusing && !_ctx.inMobile(), _ctx.n("--focusing")]))
    },
    null,
    2
    /* CLASS */
  );
}
const __sfc__ = defineComponent({
  name,
  props,
  setup: () => ({
    n,
    classes,
    inMobile
  })
});
__sfc__.render = __render__;
var stdin_default = __sfc__;
export {
  stdin_default as default
};
