import { ref } from "vue";
import { withInstall, withPropsDefaultsSetter } from "../utils/components.mjs";
import HoverOverlay from "./HoverOverlay.mjs";
import { props as hoverOverlayProps } from "./props.mjs";
withInstall(HoverOverlay);
withPropsDefaultsSetter(HoverOverlay, hoverOverlayProps);
function useHoverOverlay() {
  const hovering = ref(false);
  const handleHovering = (value) => {
    hovering.value = value;
  };
  return {
    hovering,
    handleHovering
  };
}
const _HoverOverlayComponent = HoverOverlay;
var stdin_default = HoverOverlay;
export {
  _HoverOverlayComponent,
  stdin_default as default,
  hoverOverlayProps,
  useHoverOverlay
};
