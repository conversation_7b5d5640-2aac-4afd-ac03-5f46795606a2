import { computed, defineComponent } from "vue";
import { call } from "@varlet/shared";
import VarBadge from "../badge/index.mjs";
import VarIcon from "../icon/index.mjs";
import Ripple from "../ripple/index.mjs";
import { createNamespace } from "../utils/components.mjs";
import { props } from "./props.mjs";
import { useBottomNavigation } from "./provide.mjs";
const { name, n, classes } = createNamespace("bottom-navigation-item");
const defaultBadgeProps = {
  type: "danger",
  dot: true
};
import { renderSlot as _renderSlot, resolveComponent as _resolveComponent, normalizeClass as _normalizeClass, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, mergeProps as _mergeProps, withCtx as _withCtx, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, normalizeStyle as _normalizeStyle, resolveDirective as _resolveDirective, createElementBlock as _createElementBlock, withDirectives as _withDirectives } from "vue";
function __render__(_ctx, _cache) {
  const _component_var_icon = _resolveComponent("var-icon");
  const _component_var_badge = _resolveComponent("var-badge");
  const _directive_ripple = _resolveDirective("ripple");
  return _withDirectives((_openBlock(), _createElementBlock(
    "button",
    {
      class: _normalizeClass(_ctx.classes(_ctx.n(), _ctx.n("$--box"), [_ctx.variant, _ctx.n("--variant-padding")], [_ctx.isActive && !_ctx.variant, _ctx.n("--active")])),
      style: _normalizeStyle({
        color: _ctx.isActive ? _ctx.activeColor : _ctx.inactiveColor
      }),
      onClick: _cache[0] || (_cache[0] = (...args) => _ctx.handleClick && _ctx.handleClick(...args))
    },
    [
      _createElementVNode(
        "div",
        {
          class: _normalizeClass(
            _ctx.classes(
              _ctx.n("icon-container"),
              [_ctx.variant, _ctx.n("--variant-icon-container")],
              [_ctx.isActive && _ctx.variant, _ctx.n("--variant-active")]
            )
          )
        },
        [
          _ctx.badge ? (_openBlock(), _createBlock(_component_var_badge, _mergeProps({ key: 0 }, _ctx.badgeProps, {
            class: _ctx.n("badge"),
            "var-bottom-navigation-item-cover": ""
          }), {
            default: _withCtx(() => [
              _renderSlot(_ctx.$slots, "icon", { active: _ctx.isActive }, () => [
                _ctx.icon ? (_openBlock(), _createBlock(_component_var_icon, {
                  key: 0,
                  name: _ctx.icon,
                  namespace: _ctx.namespace,
                  class: _normalizeClass(_ctx.n("icon")),
                  "var-bottom-navigation-item-cover": ""
                }, null, 8, ["name", "namespace", "class"])) : _createCommentVNode("v-if", true)
              ])
            ]),
            _: 3
            /* FORWARDED */
          }, 16, ["class"])) : _renderSlot(_ctx.$slots, "icon", {
            key: 1,
            active: _ctx.isActive
          }, () => [
            _ctx.icon ? (_openBlock(), _createBlock(_component_var_icon, {
              key: 0,
              name: _ctx.icon,
              namespace: _ctx.namespace,
              class: _normalizeClass(_ctx.n("icon")),
              "var-bottom-navigation-item-cover": ""
            }, null, 8, ["name", "namespace", "class"])) : _createCommentVNode("v-if", true)
          ])
        ],
        2
        /* CLASS */
      ),
      _createElementVNode(
        "span",
        {
          class: _normalizeClass(_ctx.n("label"))
        },
        [
          _renderSlot(_ctx.$slots, "default", {}, () => [
            _createTextVNode(
              _toDisplayString(_ctx.label),
              1
              /* TEXT */
            )
          ])
        ],
        2
        /* CLASS */
      )
    ],
    6
    /* CLASS, STYLE */
  )), [
    [_directive_ripple]
  ]);
}
const __sfc__ = defineComponent({
  name,
  components: {
    VarBadge,
    VarIcon
  },
  directives: { Ripple },
  props,
  setup(props2) {
    const name2 = computed(() => props2.name);
    const isActive = computed(() => [name2.value, index.value].includes(active.value));
    const badgeProps = computed(() => props2.badge === true ? defaultBadgeProps : props2.badge);
    const { index, bottomNavigation, bindBottomNavigation } = useBottomNavigation();
    const { active, activeColor, inactiveColor, variant } = bottomNavigation;
    const bottomNavigationItemProvider = {
      name: name2,
      index
    };
    bindBottomNavigation(bottomNavigationItemProvider);
    function handleClick() {
      var _a;
      const active2 = (_a = name2.value) != null ? _a : index.value;
      call(props2.onClick, active2);
      call(bottomNavigation.onToggle, active2);
    }
    return {
      activeColor,
      inactiveColor,
      badgeProps,
      isActive,
      variant,
      n,
      classes,
      handleClick
    };
  }
});
__sfc__.render = __render__;
var stdin_default = __sfc__;
export {
  stdin_default as default
};
