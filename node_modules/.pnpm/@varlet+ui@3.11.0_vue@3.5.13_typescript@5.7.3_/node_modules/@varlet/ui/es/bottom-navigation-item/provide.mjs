import { assert } from "@varlet/shared";
import { useParent } from "@varlet/use";
import {
  BOTTOM_NAVIGATION_BIND_BOTTOM_NAVIGATION_ITEM_KEY
} from "../bottom-navigation/provide.mjs";
function useBottomNavigation() {
  const { parentProvider, index, bindParent } = useParent(
    BOTTOM_NAVIGATION_BIND_BOTTOM_NAVIGATION_ITEM_KEY
  );
  assert(!!bindParent, "BottomNavigationItem", "<var-bottom-navigation-item/> must in <var-bottom-navigation/>");
  return {
    index,
    bottomNavigation: parentProvider,
    bindBottomNavigation: bindParent
  };
}
export {
  useBottomNavigation
};
