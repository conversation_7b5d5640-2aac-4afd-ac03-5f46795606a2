import { withInstall, withPropsDefaultsSetter } from "../utils/components.mjs";
import BottomNavigationItem from "./BottomNavigationItem.mjs";
import { props as bottomNavigationItemProps } from "./props.mjs";
withInstall(BottomNavigationItem);
withPropsDefaultsSetter(BottomNavigationItem, bottomNavigationItemProps);
const _BottomNavigationItemComponent = BottomNavigationItem;
var stdin_default = BottomNavigationItem;
export {
  _BottomNavigationItemComponent,
  bottomNavigationItemProps,
  stdin_default as default
};
