import { computed, defineComponent, ref, watch } from "vue";
import { toNumber } from "@varlet/shared";
import VarButton from "../../button/index.mjs";
import VarIcon from "../../icon/index.mjs";
import { t } from "../../locale/index.mjs";
import { injectLocaleProvider } from "../../locale-provider/provide.mjs";
import { createNamespace } from "../../utils/components.mjs";
const { n } = createNamespace("date-picker-header");
import { resolveComponent as _resolveComponent, createVNode as _createVNode, normalizeClass as _normalizeClass, withCtx as _withCtx, toDisplayString as _toDisplayString, openBlock as _openBlock, createElementBlock as _createElementBlock, Transition as _Transition, createElementVNode as _createElementVNode } from "vue";
function __render__(_ctx, _cache) {
  const _component_var_icon = _resolveComponent("var-icon");
  const _component_var_button = _resolveComponent("var-button");
  return _openBlock(), _createElementBlock(
    "div",
    {
      class: _normalizeClass(_ctx.n())
    },
    [
      _createVNode(_component_var_button, {
        class: _normalizeClass(_ctx.n("arrow")),
        "var-date-picker-header-cover": "",
        round: "",
        text: "",
        disabled: _ctx.disabled.left,
        onClick: _cache[0] || (_cache[0] = ($event) => _ctx.checkDate("prev"))
      }, {
        default: _withCtx(() => [
          _createVNode(_component_var_icon, { name: "chevron-left" })
        ]),
        _: 1
        /* STABLE */
      }, 8, ["class", "disabled"]),
      _createElementVNode(
        "div",
        {
          class: _normalizeClass(_ctx.n("value")),
          onClick: _cache[1] || (_cache[1] = ($event) => _ctx.$emit("check-panel"))
        },
        [
          _createVNode(_Transition, {
            name: `var-date-picker${_ctx.reverse ? "-reverse" : ""}-translatex`
          }, {
            default: _withCtx(() => [
              (_openBlock(), _createElementBlock(
                "div",
                { key: _ctx.showDate },
                _toDisplayString(_ctx.showDate),
                1
                /* TEXT */
              ))
            ]),
            _: 1
            /* STABLE */
          }, 8, ["name"])
        ],
        2
        /* CLASS */
      ),
      _createVNode(_component_var_button, {
        class: _normalizeClass(_ctx.n("arrow")),
        "var-date-picker-header-cover": "",
        round: "",
        text: "",
        disabled: _ctx.disabled.right,
        onClick: _cache[2] || (_cache[2] = ($event) => _ctx.checkDate("next"))
      }, {
        default: _withCtx(() => [
          _createVNode(_component_var_icon, { name: "chevron-right" })
        ]),
        _: 1
        /* STABLE */
      }, 8, ["class", "disabled"])
    ],
    2
    /* CLASS */
  );
}
const __sfc__ = defineComponent({
  name: "PanelHeader",
  components: {
    VarButton,
    VarIcon
  },
  props: {
    date: {
      type: Object,
      required: true
    },
    type: {
      type: String,
      default: "date"
    },
    disabled: {
      type: Object,
      required: true
    }
  },
  emits: ["check-panel", "check-date"],
  setup(props, { emit }) {
    const reverse = ref(false);
    const forwardOrBackNum = ref(0);
    const { t: pt } = injectLocaleProvider();
    const showDate = computed(() => {
      var _a;
      const { date, type } = props;
      const { previewMonth, previewYear } = date;
      if (type === "year") {
        return previewYear;
      }
      if (type === "month") {
        return toNumber(previewYear) + forwardOrBackNum.value;
      }
      const monthName = (_a = (pt || t)("datePickerMonthDict")) == null ? void 0 : _a[previewMonth].name;
      return (pt || t)("lang") === "zh-CN" ? `${previewYear} ${monthName}` : `${monthName} ${previewYear}`;
    });
    const checkDate = (checkType) => {
      if (checkType === "prev" && props.disabled.left || checkType === "next" && props.disabled.right) {
        return;
      }
      emit("check-date", checkType);
      reverse.value = checkType === "prev";
      forwardOrBackNum.value += checkType === "prev" ? -1 : 1;
    };
    watch(
      () => props.date,
      () => {
        forwardOrBackNum.value = 0;
      }
    );
    return {
      n,
      reverse,
      showDate,
      checkDate
    };
  }
});
__sfc__.render = __render__;
var stdin_default = __sfc__;
export {
  stdin_default as default
};
