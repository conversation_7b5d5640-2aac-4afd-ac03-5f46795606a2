var __defProp = Object.defineProperty;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
import {
  computed,
  defineComponent,
  reactive,
  ref,
  watch
} from "vue";
import { toNumber } from "@varlet/shared";
import { onSmartMounted } from "@varlet/use";
import dayjs from "dayjs/esm/index.js";
import VarButton from "../../button/index.mjs";
import VarSticky from "../../sticky/index.mjs";
import { createNamespace } from "../../utils/components.mjs";
import PanelHeader from "./panel-header.mjs";
const { n, classes } = createNamespace("year-picker");
const { n: nDate } = createNamespace("date-picker");
import { resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, mergeProps as _mergeProps, normalizeClass as _normalizeClass, Transition as _Transition } from "vue";
function __render__(_ctx, _cache) {
  const _component_panel_header = _resolveComponent("panel-header");
  const _component_var_sticky = _resolveComponent("var-sticky");
  const _component_var_button = _resolveComponent("var-button");
  return _openBlock(), _createElementBlock("div", null, [
    _createVNode(_component_var_sticky, { "css-mode": "" }, {
      default: _withCtx(() => [
        _createVNode(_component_panel_header, {
          ref: "headerEl",
          type: "year",
          date: {
            previewYear: `${_ctx.yearList[0]} ~ ${_ctx.yearList[_ctx.yearList.length - 1]}`
          },
          disabled: _ctx.panelBtnDisabled,
          onCheckDate: _ctx.checkDate
        }, null, 8, ["date", "disabled", "onCheckDate"])
      ]),
      _: 1
      /* STABLE */
    }),
    _createVNode(_Transition, {
      name: `${_ctx.nDate()}${_ctx.reverse ? "-reverse" : ""}-translatex`
    }, {
      default: _withCtx(() => [
        (_openBlock(), _createElementBlock(
          "ul",
          {
            ref: "panel",
            key: _ctx.panelKey,
            class: _normalizeClass(_ctx.n())
          },
          [
            (_openBlock(true), _createElementBlock(
              _Fragment,
              null,
              _renderList(_ctx.yearList, (year) => {
                return _openBlock(), _createElementBlock("li", { key: year }, [
                  _createVNode(_component_var_button, _mergeProps({
                    type: "primary",
                    "var-year-picker-cover": "",
                    ripple: false,
                    elevation: _ctx.componentProps.buttonElevation,
                    ref_for: true
                  }, __spreadValues({}, _ctx.buttonProps(`${year}`)), {
                    onClick: (event) => _ctx.chooseYear(year, event)
                  }), {
                    default: _withCtx(() => [
                      _createTextVNode(
                        _toDisplayString(year),
                        1
                        /* TEXT */
                      )
                    ]),
                    _: 2
                    /* DYNAMIC */
                  }, 1040, ["elevation", "onClick"])
                ]);
              }),
              128
              /* KEYED_FRAGMENT */
            ))
          ],
          2
          /* CLASS */
        ))
      ]),
      _: 1
      /* STABLE */
    }, 8, ["name"])
  ]);
}
const __sfc__ = defineComponent({
  name: "YearPickerPanel",
  components: {
    VarButton,
    VarSticky,
    PanelHeader
  },
  props: {
    choose: {
      type: Object,
      required: true
    },
    current: {
      type: String,
      required: true
    },
    preview: {
      type: String
    },
    componentProps: {
      type: Object,
      required: true
    }
  },
  emits: ["choose-year"],
  setup(props, { emit }) {
    const [currentYear] = props.current.split("-");
    const panel = ref();
    const headerEl = ref(null);
    const reverse = ref(false);
    const panelKey = ref(0);
    const page = ref(0);
    const panelBtnDisabled = reactive({
      left: false,
      right: false
    });
    const yearList = computed(() => {
      if (!props.preview) {
        return [];
      }
      const startYear = Math.floor(toNumber(props.preview) / 100 + page.value) * 100;
      return Array.from(Array(100), (_v, k) => Math.max(0, startYear) + k);
    });
    const shouldChoose = (val) => {
      const {
        choose: { chooseMonths, chooseDays, chooseYears, chooseRangeYear },
        componentProps: { type, range }
      } = props;
      if (range) {
        if (!chooseRangeYear.length) {
          return false;
        }
        const isBeforeMax = dayjs(val).isSameOrBefore(dayjs(chooseRangeYear[1]), "year");
        const isAfterMin = dayjs(val).isSameOrAfter(dayjs(chooseRangeYear[0]), "year");
        return isBeforeMax && isAfterMin;
      }
      if (type === "year") {
        return chooseYears.includes(val);
      }
      if (type === "month") {
        return chooseMonths.some((value) => value.includes(val));
      }
      return chooseDays.some((value) => value.includes(val));
    };
    const inRange = (year) => {
      const {
        componentProps: { min, max }
      } = props;
      const isBeforeMax = max ? dayjs(year).isSameOrBefore(dayjs(max), "year") : true;
      const isAfterMin = min ? dayjs(year).isSameOrAfter(dayjs(min), "year") : true;
      return isBeforeMax && isAfterMin;
    };
    const buttonProps = (year) => {
      const {
        choose: { chooseYear: chooseYear2 },
        componentProps: { allowedDates, color, multiple, range }
      } = props;
      const yearExist = () => {
        if (range || multiple) {
          return shouldChoose(year);
        }
        return chooseYear2 === year;
      };
      const computeDisabled = () => {
        if (!inRange(year)) {
          return true;
        }
        if (!allowedDates) {
          return false;
        }
        return !allowedDates(year);
      };
      const disabled = computeDisabled();
      const computeText = () => {
        if (disabled) {
          return true;
        }
        if (range || multiple) {
          return !shouldChoose(year);
        }
        return chooseYear2 !== year;
      };
      const computeOutline = () => {
        if (!(currentYear === year && props.componentProps.showCurrent)) {
          return false;
        }
        if ((range || multiple) && disabled) {
          return true;
        }
        if (range || multiple) {
          return !shouldChoose(year);
        }
        return chooseYear2 !== currentYear;
      };
      const textColorOrCover = () => {
        if (disabled) {
          return "";
        }
        if (computeOutline()) {
          return color != null ? color : "";
        }
        if (yearExist()) {
          return "";
        }
        return `${nDate()}-color-cover`;
      };
      const isCover = textColorOrCover().startsWith(nDate());
      return {
        outline: computeOutline(),
        text: computeText(),
        color: !computeText() ? color : "",
        textColor: isCover ? "" : textColorOrCover(),
        [`${nDate()}-color-cover`]: isCover,
        class: classes(n("button"), [disabled, n("button--disabled")]),
        disabled
      };
    };
    const chooseYear = (year, event) => {
      const buttonEl = event.currentTarget;
      if (buttonEl.classList.contains(n("button--disabled"))) {
        return;
      }
      emit("choose-year", year);
    };
    const scrollToView = () => {
      var _a;
      const activeEl = (_a = panel.value.querySelector(".var-button--primary")) != null ? _a : panel.value.querySelector(".var-button--outline");
      activeEl == null ? void 0 : activeEl.scrollIntoView({
        block: "center"
      });
    };
    const checkDate = (checkType) => {
      const isPrevType = checkType === "prev";
      reverse.value = isPrevType;
      panelKey.value += isPrevType ? -1 : 1;
      page.value += isPrevType ? -1 : 1;
    };
    const forwardRef = (checkType) => {
      headerEl.value.checkDate(checkType);
    };
    onSmartMounted(scrollToView);
    watch(
      () => props.preview,
      () => {
        page.value = 0;
      }
    );
    watch(
      () => [yearList.value, props.componentProps.max, props.componentProps.min],
      (newVal) => {
        const [list, max, min] = newVal;
        if (max) {
          panelBtnDisabled.right = !dayjs(`${toNumber(list[list.length - 1])}`).isSameOrBefore(dayjs(max), "year");
        }
        if (min) {
          panelBtnDisabled.left = !dayjs(`${toNumber(list[0])}`).isSameOrAfter(dayjs(min), "year");
        }
        if (toNumber(list[0] <= 0)) {
          panelBtnDisabled.left = false;
        }
      },
      {
        immediate: true
      }
    );
    return {
      n,
      classes,
      buttonProps,
      panel,
      headerEl,
      yearList,
      reverse,
      panelKey,
      panelBtnDisabled,
      nDate,
      checkDate,
      chooseYear,
      forwardRef,
      toNumber
    };
  }
});
__sfc__.render = __render__;
var stdin_default = __sfc__;
export {
  stdin_default as default
};
