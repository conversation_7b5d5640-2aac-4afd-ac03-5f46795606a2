import { withInstall, withPropsDefaultsSetter } from "../utils/components.mjs";
import HighlighterProvider from "./HighlighterProvider.mjs";
import { props as highlighterProviderProps } from "./props.mjs";
withInstall(HighlighterProvider);
withPropsDefaultsSetter(HighlighterProvider, highlighterProviderProps);
const _HighlighterProviderComponent = HighlighterProvider;
var stdin_default = HighlighterProvider;
export {
  _HighlighterProviderComponent,
  stdin_default as default
};
