import { computed, defineComponent, h } from "vue";
import { call } from "@varlet/shared";
import { createNamespace } from "../utils/components.mjs";
import { props } from "./props.mjs";
import { provideHighlighterProvider } from "./provide.mjs";
const { name, n } = createNamespace("highlighter-provider");
const __sfc__ = defineComponent({
  name,
  props,
  setup(props2, { slots }) {
    const highlighter = computed(() => props2.highlighter);
    const theme = computed(() => props2.theme);
    provideHighlighterProvider({ highlighter, theme });
    return () => h(
      props2.tag,
      {
        class: n()
      },
      call(slots.default)
    );
  }
});
var stdin_default = __sfc__;
export {
  stdin_default as default
};
