import { computed, defineComponent, ref, watch } from "vue";
import { call, isNumber, toNumber } from "@varlet/shared";
import VarIcon from "../icon/index.mjs";
import VarInput from "../input/index.mjs";
import { t } from "../locale/index.mjs";
import { injectLocaleProvider } from "../locale-provider/provide.mjs";
import VarMenuOption from "../menu-option/index.mjs";
import VarMenuSelect from "../menu-select/index.mjs";
import Ripple from "../ripple/index.mjs";
import { createNamespace, formatElevation } from "../utils/components.mjs";
import { props } from "./props.mjs";
const { name, n, classes } = createNamespace("pagination");
import { renderSlot as _renderSlot, resolveComponent as _resolveComponent, createVNode as _createVNode, normalizeClass as _normalizeClass, resolveDirective as _resolveDirective, openBlock as _openBlock, createElementBlock as _createElementBlock, withDirectives as _withDirectives, withKeys as _withKeys, toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, createCommentVNode as _createCommentVNode, renderList as _renderList, Fragment as _Fragment, withCtx as _withCtx, createBlock as _createBlock } from "vue";
const _hoisted_1 = ["item-mode", "onClick"];
function __render__(_ctx, _cache) {
  const _component_var_icon = _resolveComponent("var-icon");
  const _component_var_input = _resolveComponent("var-input");
  const _component_var_menu_option = _resolveComponent("var-menu-option");
  const _component_var_menu_select = _resolveComponent("var-menu-select");
  const _directive_ripple = _resolveDirective("ripple");
  return _openBlock(), _createElementBlock(
    "ul",
    {
      class: _normalizeClass(_ctx.n())
    },
    [
      _withDirectives((_openBlock(), _createElementBlock(
        "li",
        {
          class: _normalizeClass(
            _ctx.classes(
              _ctx.n("item"),
              _ctx.n("prev"),
              [_ctx.current <= 1 || _ctx.disabled, _ctx.n("item--disabled")],
              [_ctx.simple, _ctx.n("item--simple"), _ctx.formatElevation(_ctx.elevation, 2)]
            )
          ),
          onClick: _cache[0] || (_cache[0] = ($event) => _ctx.clickItem("prev"))
        },
        [
          _renderSlot(_ctx.$slots, "prev", {}, () => [
            _createVNode(_component_var_icon, { name: "chevron-left" })
          ])
        ],
        2
        /* CLASS */
      )), [
        [_directive_ripple, { disabled: _ctx.current <= 1 || _ctx.disabled }]
      ]),
      _ctx.simple ? (_openBlock(), _createElementBlock(
        "li",
        {
          key: 0,
          class: _normalizeClass(_ctx.classes(_ctx.n("simple"), [_ctx.disabled, _ctx.n("item--disabled")]))
        },
        [
          _createVNode(_component_var_input, {
            modelValue: _ctx.simpleCurrentValue,
            "onUpdate:modelValue": _cache[1] || (_cache[1] = ($event) => _ctx.simpleCurrentValue = $event),
            "var-pagination-cover": "",
            variant: "standard",
            hint: false,
            disabled: _ctx.disabled,
            onBlur: _cache[2] || (_cache[2] = ($event) => _ctx.setPage("simple", _ctx.simpleCurrentValue, $event)),
            onKeydown: _cache[3] || (_cache[3] = _withKeys(($event) => _ctx.setPage("simple", _ctx.simpleCurrentValue, $event), ["enter"]))
          }, null, 8, ["modelValue", "disabled"]),
          _createElementVNode("span", null, [
            _createTextVNode(
              " / " + _toDisplayString(_ctx.pageCount) + " ",
              1
              /* TEXT */
            ),
            _createElementVNode(
              "div",
              {
                class: _normalizeClass(_ctx.n("simple-line"))
              },
              null,
              2
              /* CLASS */
            )
          ])
        ],
        2
        /* CLASS */
      )) : (_openBlock(true), _createElementBlock(
        _Fragment,
        { key: 1 },
        _renderList(_ctx.pageList, (item, index) => {
          return _withDirectives((_openBlock(), _createElementBlock("li", {
            key: index,
            "item-mode": _ctx.getMode(item, index),
            class: _normalizeClass(
              _ctx.classes(
                _ctx.n("item"),
                _ctx.formatElevation(_ctx.elevation, 2),
                [item === _ctx.current && !_ctx.disabled, _ctx.n("item--active")],
                [_ctx.isHideEllipsis(item, index), _ctx.n("item--hide")],
                [_ctx.disabled, _ctx.n("item--disabled")],
                [item === _ctx.current && _ctx.disabled, _ctx.n("item--disabled--active")]
              )
            ),
            onClick: ($event) => _ctx.clickItem(item, index)
          }, [
            _createTextVNode(
              _toDisplayString(item),
              1
              /* TEXT */
            )
          ], 10, _hoisted_1)), [
            [_directive_ripple, { disabled: _ctx.disabled }]
          ]);
        }),
        128
        /* KEYED_FRAGMENT */
      )),
      _withDirectives((_openBlock(), _createElementBlock(
        "li",
        {
          class: _normalizeClass(
            _ctx.classes(
              _ctx.n("item"),
              _ctx.n("next"),
              [_ctx.current >= _ctx.pageCount || _ctx.disabled, _ctx.n("item--disabled")],
              [_ctx.simple, _ctx.n("item--simple"), _ctx.formatElevation(_ctx.elevation, 2)]
            )
          ),
          onClick: _cache[4] || (_cache[4] = ($event) => _ctx.clickItem("next"))
        },
        [
          _renderSlot(_ctx.$slots, "next", {}, () => [
            _createVNode(_component_var_icon, { name: "chevron-right" })
          ])
        ],
        2
        /* CLASS */
      )), [
        [_directive_ripple, { disabled: _ctx.current >= _ctx.pageCount || _ctx.disabled }]
      ]),
      _ctx.showSizeChanger ? (_openBlock(), _createBlock(_component_var_menu_select, {
        key: 2,
        modelValue: _ctx.size,
        "onUpdate:modelValue": _cache[5] || (_cache[5] = ($event) => _ctx.size = $event),
        placement: "cover-top",
        disabled: _ctx.disabled
      }, {
        options: _withCtx(() => [
          (_openBlock(true), _createElementBlock(
            _Fragment,
            null,
            _renderList(_ctx.sizeOption, (option, index) => {
              return _openBlock(), _createBlock(_component_var_menu_option, {
                key: index,
                value: option,
                onClick: _ctx.clickSize
              }, {
                default: _withCtx(() => [
                  _createTextVNode(
                    _toDisplayString(option) + _toDisplayString((_ctx.pt ? _ctx.pt : _ctx.t)("paginationItem")) + " / " + _toDisplayString((_ctx.pt ? _ctx.pt : _ctx.t)("paginationPage")),
                    1
                    /* TEXT */
                  )
                ]),
                _: 2
                /* DYNAMIC */
              }, 1032, ["value", "onClick"]);
            }),
            128
            /* KEYED_FRAGMENT */
          ))
        ]),
        default: _withCtx(() => [
          _createElementVNode(
            "li",
            {
              class: _normalizeClass(_ctx.classes(_ctx.n("size"), [_ctx.disabled, _ctx.n("item--disabled")]))
            },
            [
              _createElementVNode(
                "div",
                {
                  class: _normalizeClass(_ctx.classes(_ctx.n("size--open"), [_ctx.current <= 1 || _ctx.disabled, _ctx.n("size--open--disabled")]))
                },
                [
                  _createElementVNode(
                    "span",
                    null,
                    _toDisplayString(_ctx.size) + _toDisplayString((_ctx.pt ? _ctx.pt : _ctx.t)("paginationItem")) + " / " + _toDisplayString((_ctx.pt ? _ctx.pt : _ctx.t)("paginationPage")),
                    1
                    /* TEXT */
                  ),
                  _createVNode(_component_var_icon, {
                    class: _normalizeClass(_ctx.n("size--open-icon")),
                    "var-pagination-cover": "",
                    name: "menu-down"
                  }, null, 8, ["class"])
                ],
                2
                /* CLASS */
              )
            ],
            2
            /* CLASS */
          )
        ]),
        _: 1
        /* STABLE */
      }, 8, ["modelValue", "disabled"])) : _createCommentVNode("v-if", true),
      _ctx.showQuickJumper && !_ctx.simple ? (_openBlock(), _createElementBlock(
        "li",
        {
          key: 3,
          class: _normalizeClass(_ctx.classes(_ctx.n("quickly"), [_ctx.disabled, _ctx.n("item--disabled")]))
        },
        [
          _createTextVNode(
            _toDisplayString((_ctx.pt ? _ctx.pt : _ctx.t)("paginationJump")) + " ",
            1
            /* TEXT */
          ),
          _createVNode(_component_var_input, {
            modelValue: _ctx.quickJumperValue,
            "onUpdate:modelValue": _cache[6] || (_cache[6] = ($event) => _ctx.quickJumperValue = $event),
            disabled: _ctx.disabled,
            hint: false,
            variant: "standard",
            "var-pagination-cover": "",
            onBlur: _cache[7] || (_cache[7] = ($event) => _ctx.setPage("quick", _ctx.quickJumperValue, $event)),
            onKeydown: _cache[8] || (_cache[8] = _withKeys(($event) => _ctx.setPage("quick", _ctx.quickJumperValue, $event), ["enter"]))
          }, null, 8, ["modelValue", "disabled"])
        ],
        2
        /* CLASS */
      )) : _createCommentVNode("v-if", true),
      _ctx.totalText ? (_openBlock(), _createElementBlock(
        "li",
        {
          key: 4,
          class: _normalizeClass(_ctx.classes(_ctx.n("total"), [_ctx.disabled, _ctx.n("item--disabled")]))
        },
        _toDisplayString(_ctx.totalText),
        3
        /* TEXT, CLASS */
      )) : _createCommentVNode("v-if", true)
    ],
    2
    /* CLASS */
  );
}
const __sfc__ = defineComponent({
  name,
  components: {
    VarMenuSelect,
    VarMenuOption,
    VarIcon,
    VarInput
  },
  directives: { Ripple },
  props,
  setup(props2) {
    const quickJumperValue = ref("");
    const simpleCurrentValue = ref("1");
    const isHideEllipsisHead = ref(false);
    const isHideEllipsisTail = ref(false);
    const current = ref(toNumber(props2.current) || 1);
    const size = ref(toNumber(props2.size) || 10);
    const pageList = ref([]);
    const activePosition = computed(() => Math.ceil(props2.maxPagerCount / 2));
    const pageCount = computed(() => Math.ceil(toNumber(props2.total) / toNumber(size.value)));
    const range = computed(() => {
      const start = size.value * (current.value - 1) + 1;
      const end = Math.min(size.value * current.value, toNumber(props2.total));
      return [start, end];
    });
    const totalText = computed(() => {
      if (!props2.showTotal) {
        return "";
      }
      return props2.showTotal(toNumber(props2.total), range.value);
    });
    const { t: pt } = injectLocaleProvider();
    watch([() => props2.current, () => props2.size], ([newCurrent, newSize]) => {
      current.value = toNumber(newCurrent) || 1;
      size.value = toNumber(newSize || 10);
    });
    watch(
      [current, size, pageCount],
      ([newCurrent, newSize, newCount], [oldCurrent, oldSize]) => {
        let list = [];
        const { maxPagerCount, total, onChange } = props2;
        const oldCount = Math.ceil(toNumber(total) / toNumber(oldSize));
        const rEllipseSign = newCount - (maxPagerCount - activePosition.value) - 1;
        simpleCurrentValue.value = `${newCurrent}`;
        if (newCount - 2 > maxPagerCount) {
          if (oldCurrent === void 0 || newCount !== oldCount) {
            for (let i = 2; i < maxPagerCount + 2; i++) {
              list.push(i);
            }
          }
          if (newCurrent <= maxPagerCount && newCurrent < rEllipseSign) {
            list = [];
            for (let i = 1; i < maxPagerCount + 1; i++) {
              list.push(i + 1);
            }
            isHideEllipsisHead.value = true;
            isHideEllipsisTail.value = false;
          }
          if (newCurrent > maxPagerCount && newCurrent < rEllipseSign) {
            list = [];
            for (let i = 1; i < maxPagerCount + 1; i++) {
              list.push(newCurrent + i - activePosition.value);
            }
            isHideEllipsisHead.value = newCurrent === 2 && maxPagerCount === 1;
            isHideEllipsisTail.value = false;
          }
          if (newCurrent >= rEllipseSign) {
            list = [];
            for (let i = 1; i < maxPagerCount + 1; i++) {
              list.push(newCount - (maxPagerCount - i) - 1);
            }
            isHideEllipsisHead.value = false;
            isHideEllipsisTail.value = true;
          }
          list = [1, "...", ...list, "...", newCount];
        } else {
          for (let i = 1; i <= newCount; i++) {
            list.push(i);
          }
        }
        pageList.value = list;
        if (oldCurrent != null && newCount > 0) {
          call(onChange, newCurrent, newSize);
        }
        call(props2["onUpdate:current"], newCurrent);
        call(props2["onUpdate:size"], newSize);
      },
      {
        immediate: true
      }
    );
    function isHideEllipsis(item, index) {
      if (isNumber(item)) {
        return false;
      }
      return index === 1 ? isHideEllipsisHead.value : isHideEllipsisTail.value;
    }
    function getMode(item, index) {
      if (isNumber(item)) {
        return "basic";
      }
      return index === 1 ? "head" : "tail";
    }
    function clickItem(item, index) {
      if (item === current.value || props2.disabled) {
        return;
      }
      if (item === "...") {
        current.value = index === 1 ? Math.max(current.value - props2.maxPagerCount, 1) : Math.min(current.value + props2.maxPagerCount, pageCount.value);
        return;
      }
      if (item === "prev") {
        current.value = ensureCurrentBoundary(current.value - 1);
        return;
      }
      if (item === "next") {
        current.value = ensureCurrentBoundary(current.value + 1);
        return;
      }
      if (isNumber(item)) {
        current.value = item;
      }
    }
    function clickSize() {
      const targetCurrent = ensureCurrentBoundary(current.value);
      simpleCurrentValue.value = String(targetCurrent);
      current.value = targetCurrent;
    }
    function ensureCurrentBoundary(targetCurrent) {
      if (targetCurrent > pageCount.value) {
        return pageCount.value;
      }
      if (targetCurrent < 1) {
        return 1;
      }
      return targetCurrent;
    }
    function setPage(type, page, event) {
      ;
      event.target.blur();
      const targetCurrent = ensureCurrentBoundary(toNumber(page));
      simpleCurrentValue.value = String(targetCurrent);
      current.value = targetCurrent;
      if (type === "quick") {
        quickJumperValue.value = "";
      }
    }
    return {
      current,
      size,
      pageCount,
      pageList,
      quickJumperValue,
      simpleCurrentValue,
      totalText,
      pt,
      t,
      n,
      classes,
      getMode,
      isHideEllipsis,
      clickItem,
      clickSize,
      setPage,
      toNumber,
      formatElevation
    };
  }
});
__sfc__.render = __render__;
var stdin_default = __sfc__;
export {
  stdin_default as default
};
