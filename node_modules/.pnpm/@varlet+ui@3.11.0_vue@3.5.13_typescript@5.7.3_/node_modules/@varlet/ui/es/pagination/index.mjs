import { withInstall, withPropsDefaultsSetter } from "../utils/components.mjs";
import Pagination from "./Pagination.mjs";
import { props as paginationProps } from "./props.mjs";
withInstall(Pagination);
withPropsDefaultsSetter(Pagination, paginationProps);
const _PaginationComponent = Pagination;
var stdin_default = Pagination;
export {
  _PaginationComponent,
  stdin_default as default,
  paginationProps
};
