:root { --pagination-text-color: #555; --pagination-font-size: var(--font-size-md); --pagination-active-color: var(--color-on-primary); --pagination-active-bg-color: var(--color-primary); --pagination-hover-bg-color: rgba(85, 85, 85, 0.15); --pagination-total-margin: 0 10px; --pagination-total-line-height: 24px; --pagination-item-width: 32px; --pagination-item-height: 32px; --pagination-item-margin: 0 6px; --pagination-item-background: #fff; --pagination-item-border-radius: 4px; --pagination-item-simple-border-radius: 50%; --pagination-input-width: 32px; --pagination-disabled-color: var(--color-text-disabled); --pagination-bg-disabled-color: var(--color-disabled); --pagination-size-line-height: 24px; --pagination-size-padding: 0 4px; --pagination-quick-jumper-margin: 0 10px;}.var-pagination-flex-nowrap { display: flex; white-space: nowrap; align-items: center;}.var-pagination { display: flex; align-items: center; list-style: none; margin: 0; font-size: var(--pagination-font-size); padding: 0; color: var(--pagination-text-color);}.var-pagination__item { display: inline-flex; min-width: var(--pagination-item-width); align-items: center; justify-content: center; margin: var(--pagination-item-margin); height: var(--pagination-item-height); cursor: pointer; border-radius: var(--pagination-item-border-radius); outline: none; transition: all 0.25s; user-select: none; background-color: var(--pagination-item-background);}.var-pagination__item:hover { background-color: var(--pagination-hover-bg-color);}.var-pagination__item--active { color: var(--pagination-active-color); background-color: var(--pagination-active-bg-color) !important;}.var-pagination__item--hide { display: none;}.var-pagination__item--simple { background: transparent; border-radius: var(--pagination-item-simple-border-radius);}.var-pagination__item--disabled { cursor: not-allowed !important; color: var(--pagination-disabled-color); background-color: unset; box-shadow: none !important;}.var-pagination__item--disabled:hover { background-color: unset;}.var-pagination__item--disabled--active { background-color: var(--pagination-bg-disabled-color);}.var-pagination__item--disabled--active:hover { background-color: var(--pagination-bg-disabled-color);}.var-pagination__prev { margin-left: 0;}.var-pagination__total { display: flex; white-space: nowrap; align-items: center; line-height: var(--pagination-total-line-height); margin: var(--pagination-total-margin);}.var-pagination__size { display: flex; white-space: nowrap; align-items: center; height: var(--pagination-item-height); line-height: var(--pagination-size-line-height); margin: var(--pagination-item-margin); padding: var(--pagination-size-padding); cursor: pointer;}.var-pagination__size--open { display: flex; align-items: center; cursor: pointer;}.var-pagination__size--open--disabled { cursor: inherit;}.var-pagination__quickly,.var-pagination__simple { display: flex; white-space: nowrap; align-items: center;}.var-pagination__quickly [var-pagination-cover],.var-pagination__simple [var-pagination-cover] { width: var(--pagination-input-width);}.var-pagination__quickly [var-pagination-cover] .var-field-decorator__middle,.var-pagination__simple [var-pagination-cover] .var-field-decorator__middle { padding: 0;}.var-pagination__quickly [var-pagination-cover] .var-input__input,.var-pagination__simple [var-pagination-cover] .var-input__input { height: auto; text-align: center; font-weight: normal; font-size: var(--pagination-font-size); padding-top: 1px;}.var-pagination__quickly-line,.var-pagination__simple-line { height: 1px; background: transparent;}.var-pagination__quickly { margin: var(--pagination-quick-jumper-margin);}.var-pagination__quickly [var-pagination-cover] { margin-left: 6px;}.var-pagination__size--open-icon[var-pagination-cover] { font-size: inherit;}