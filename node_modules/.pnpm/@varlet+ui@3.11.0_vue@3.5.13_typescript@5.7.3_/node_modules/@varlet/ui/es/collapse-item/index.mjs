import { withInstall, withPropsDefaultsSetter } from "../utils/components.mjs";
import CollapseItem from "./CollapseItem.mjs";
import { props as collapseItemProps } from "./props.mjs";
withInstall(CollapseItem);
withPropsDefaultsSetter(CollapseItem, collapseItemProps);
const _CollapseItemComponent = CollapseItem;
var stdin_default = CollapseItem;
export {
  _CollapseItemComponent,
  collapseItemProps,
  stdin_default as default
};
