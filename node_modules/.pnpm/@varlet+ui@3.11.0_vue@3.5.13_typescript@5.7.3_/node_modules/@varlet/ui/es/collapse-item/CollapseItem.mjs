import { computed, defineComponent, ref } from "vue";
import { useCollapseTransition } from "../collapse-transition/useCollapseTransition.mjs";
import VarIcon from "../icon/index.mjs";
import { createNamespace, formatElevation } from "../utils/components.mjs";
import { props } from "./props.mjs";
import { useCollapse } from "./provide.mjs";
const { name, n, classes } = createNamespace("collapse-item");
import { normalizeClass as _normalizeClass, createElementVNode as _createElementVNode, renderSlot as _renderSlot, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, vShow as _vShow, withDirectives as _withDirectives, normalizeStyle as _normalizeStyle } from "vue";
const _hoisted_1 = ["aria-expanded", "aria-disabled", "role"];
function __render__(_ctx, _cache) {
  const _component_var_icon = _resolveComponent("var-icon");
  return _openBlock(), _createElementBlock(
    "div",
    {
      class: _normalizeClass(_ctx.classes(_ctx.n(), [_ctx.offset && _ctx.isShow, _ctx.n("--active")], [_ctx.disabled, _ctx.n("--disable")])),
      style: _normalizeStyle(`--collapse-divider-top: ${_ctx.divider ? "var(--collapse-border-top)" : "none"}`)
    },
    [
      _createElementVNode(
        "div",
        {
          class: _normalizeClass(_ctx.classes(_ctx.n("shadow"), _ctx.formatElevation(_ctx.elevation, 2)))
        },
        null,
        2
        /* CLASS */
      ),
      _createElementVNode("div", {
        class: _normalizeClass(_ctx.classes(_ctx.n("header"), [!_ctx.disabled, _ctx.n("header--cursor-pointer")])),
        "aria-expanded": _ctx.isShow,
        "aria-disabled": _ctx.disabled,
        role: _ctx.accordion ? "tab" : "button",
        onClick: _cache[0] || (_cache[0] = (...args) => _ctx.toggle && _ctx.toggle(...args))
      }, [
        _ctx.$slots.title || _ctx.title ? (_openBlock(), _createElementBlock(
          "div",
          {
            key: 0,
            class: _normalizeClass(_ctx.n("header-title"))
          },
          [
            _renderSlot(_ctx.$slots, "title", {}, () => [
              _createTextVNode(
                _toDisplayString(_ctx.title),
                1
                /* TEXT */
              )
            ])
          ],
          2
          /* CLASS */
        )) : _createCommentVNode("v-if", true),
        _createElementVNode(
          "div",
          {
            class: _normalizeClass(_ctx.n("header-icon"))
          },
          [
            _renderSlot(_ctx.$slots, "icon", {}, () => [
              _createVNode(_component_var_icon, {
                name: _ctx.icon,
                transition: 250,
                class: _normalizeClass(
                  _ctx.classes(
                    _ctx.n("header-icon"),
                    [_ctx.isShow && _ctx.icon === "chevron-down", _ctx.n("header-open")],
                    [_ctx.disabled, _ctx.n("header--disable")]
                  )
                )
              }, null, 8, ["name", "class"])
            ])
          ],
          2
          /* CLASS */
        )
      ], 10, _hoisted_1),
      _withDirectives(_createElementVNode(
        "div",
        {
          ref: "contentEl",
          class: _normalizeClass(_ctx.n("content")),
          onTransitionend: _cache[1] || (_cache[1] = (...args) => _ctx.handleTransitionEnd && _ctx.handleTransitionEnd(...args)),
          onTransitionstart: _cache[2] || (_cache[2] = (...args) => _ctx.handleTransitionStart && _ctx.handleTransitionStart(...args))
        },
        [
          _createElementVNode(
            "div",
            {
              class: _normalizeClass(_ctx.n("content-wrap"))
            },
            [
              _renderSlot(_ctx.$slots, "default")
            ],
            2
            /* CLASS */
          )
        ],
        34
        /* CLASS, NEED_HYDRATION */
      ), [
        [_vShow, _ctx.showContent]
      ])
    ],
    6
    /* CLASS, STYLE */
  );
}
const __sfc__ = defineComponent({
  name,
  components: {
    VarIcon
  },
  props,
  setup(props2) {
    const isShow = ref(false);
    const showContent = ref(false);
    const contentEl = ref(null);
    const name2 = computed(() => props2.name);
    const disabled = computed(() => props2.disabled);
    const { index, collapse, bindCollapse } = useCollapse();
    const { offset, divider, elevation, accordion, updateItem } = collapse;
    const collapseItemProvider = {
      index,
      name: name2,
      disabled,
      init
    };
    bindCollapse(collapseItemProvider);
    const { handleTransitionEnd, handleTransitionStart } = useCollapseTransition({
      contentEl,
      showContent,
      expand: isShow
    });
    function init(show) {
      isShow.value = show;
    }
    function toggle() {
      var _a;
      if (props2.disabled) {
        return;
      }
      updateItem((_a = props2.name) != null ? _a : index.value, !isShow.value);
    }
    return {
      isShow,
      showContent,
      offset,
      divider,
      elevation,
      contentEl,
      accordion,
      n,
      classes,
      toggle,
      formatElevation,
      handleTransitionEnd,
      handleTransitionStart
    };
  }
});
__sfc__.render = __render__;
var stdin_default = __sfc__;
export {
  stdin_default as default
};
