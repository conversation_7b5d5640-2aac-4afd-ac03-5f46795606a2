import { assert } from "@varlet/shared";
import { useParent } from "@varlet/use";
import { COLLAPSE_BIND_COLLAPSE_ITEM_KEY } from "../collapse/provide.mjs";
function useCollapse() {
  const { parentProvider, index, bindParent } = useParent(
    COLLAPSE_BIND_COLLAPSE_ITEM_KEY
  );
  assert(!!bindParent, "Collapse", "<var-collapse-item/> must in <var-collapse>");
  return {
    index,
    collapse: parentProvider,
    bindCollapse: bindParent
  };
}
export {
  useCollapse
};
