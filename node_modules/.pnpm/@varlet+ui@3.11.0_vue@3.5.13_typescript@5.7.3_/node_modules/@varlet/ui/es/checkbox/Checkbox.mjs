import { computed, defineComponent, nextTick, ref } from "vue";
import { call, preventDefault } from "@varlet/shared";
import { useEventListener, useVModel } from "@varlet/use";
import VarFormDetails from "../form-details/index.mjs";
import { useForm } from "../form/provide.mjs";
import Hover from "../hover/index.mjs";
import VarHoverOverlay, { useHoverOverlay } from "../hover-overlay/index.mjs";
import VarIcon from "../icon/index.mjs";
import Ripple from "../ripple/index.mjs";
import { createNamespace, useValidation } from "../utils/components.mjs";
import { props } from "./props.mjs";
import { useCheckboxGroup } from "./provide.mjs";
const { name, n, classes } = createNamespace("checkbox");
import { renderSlot as _renderSlot, resolveComponent as _resolveComponent, normalizeClass as _normalizeClass, createVNode as _createVNode, createCommentVNode as _createCommentVNode, normalizeStyle as _normalizeStyle, resolveDirective as _resolveDirective, openBlock as _openBlock, createElementBlock as _createElementBlock, withDirectives as _withDirectives, createElementVNode as _createElementVNode } from "vue";
const _hoisted_1 = ["tabindex"];
function __render__(_ctx, _cache) {
  const _component_var_icon = _resolveComponent("var-icon");
  const _component_var_hover_overlay = _resolveComponent("var-hover-overlay");
  const _component_var_form_details = _resolveComponent("var-form-details");
  const _directive_hover = _resolveDirective("hover");
  const _directive_ripple = _resolveDirective("ripple");
  return _openBlock(), _createElementBlock(
    "div",
    {
      class: _normalizeClass(_ctx.n("wrap"))
    },
    [
      _createElementVNode(
        "div",
        {
          class: _normalizeClass(_ctx.n()),
          onClick: _cache[3] || (_cache[3] = (...args) => _ctx.handleClick && _ctx.handleClick(...args))
        },
        [
          _withDirectives((_openBlock(), _createElementBlock("div", {
            ref: "action",
            class: _normalizeClass(
              _ctx.classes(
                _ctx.n("action"),
                [_ctx.checked || _ctx.isIndeterminate, _ctx.n("--checked"), _ctx.n("--unchecked")],
                [_ctx.errorMessage || _ctx.checkboxGroupErrorMessage, _ctx.n("--error")],
                [_ctx.formDisabled || _ctx.disabled, _ctx.n("--disabled")]
              )
            ),
            style: _normalizeStyle({ color: _ctx.checked || _ctx.isIndeterminate ? _ctx.checkedColor : _ctx.uncheckedColor }),
            tabindex: _ctx.disabled || _ctx.formDisabled ? void 0 : "0",
            onFocus: _cache[0] || (_cache[0] = ($event) => _ctx.isFocusing = true),
            onBlur: _cache[1] || (_cache[1] = ($event) => _ctx.isFocusing = false)
          }, [
            _ctx.isIndeterminate ? _renderSlot(_ctx.$slots, "indeterminate-icon", { key: 0 }, () => [
              _createVNode(_component_var_icon, {
                class: _normalizeClass(_ctx.n("icon")),
                name: "minus-box",
                size: _ctx.iconSize,
                "var-checkbox-cover": ""
              }, null, 8, ["class", "size"])
            ]) : _createCommentVNode("v-if", true),
            _ctx.checked && !_ctx.isIndeterminate ? _renderSlot(_ctx.$slots, "checked-icon", { key: 1 }, () => [
              _createVNode(_component_var_icon, {
                class: _normalizeClass(_ctx.n("icon")),
                name: "checkbox-marked",
                size: _ctx.iconSize,
                "var-checkbox-cover": ""
              }, null, 8, ["class", "size"])
            ]) : _createCommentVNode("v-if", true),
            !_ctx.checked && !_ctx.isIndeterminate ? _renderSlot(_ctx.$slots, "unchecked-icon", { key: 2 }, () => [
              _createVNode(_component_var_icon, {
                class: _normalizeClass(_ctx.n("icon")),
                name: "checkbox-blank-outline",
                size: _ctx.iconSize,
                "var-checkbox-cover": ""
              }, null, 8, ["class", "size"])
            ]) : _createCommentVNode("v-if", true),
            _createVNode(_component_var_hover_overlay, {
              hovering: !_ctx.disabled && !_ctx.formDisabled && _ctx.hovering,
              focusing: !_ctx.disabled && !_ctx.formDisabled && _ctx.isFocusing
            }, null, 8, ["hovering", "focusing"])
          ], 46, _hoisted_1)), [
            [_directive_hover, _ctx.handleHovering, "desktop"],
            [_directive_ripple, { disabled: _ctx.formReadonly || _ctx.readonly || _ctx.formDisabled || _ctx.disabled || !_ctx.ripple }]
          ]),
          _ctx.$slots.default ? (_openBlock(), _createElementBlock(
            "div",
            {
              key: 0,
              class: _normalizeClass(
                _ctx.classes(
                  _ctx.n("text"),
                  [_ctx.errorMessage || _ctx.checkboxGroupErrorMessage, _ctx.n("--error")],
                  [_ctx.formDisabled || _ctx.disabled, _ctx.n("--disabled")]
                )
              ),
              onClick: _cache[2] || (_cache[2] = (...args) => _ctx.handleTextClick && _ctx.handleTextClick(...args))
            },
            [
              _renderSlot(_ctx.$slots, "default", { checked: _ctx.checked })
            ],
            2
            /* CLASS */
          )) : _createCommentVNode("v-if", true)
        ],
        2
        /* CLASS */
      ),
      _createVNode(_component_var_form_details, { "error-message": _ctx.errorMessage }, null, 8, ["error-message"])
    ],
    2
    /* CLASS */
  );
}
const __sfc__ = defineComponent({
  name,
  directives: { Ripple, Hover },
  components: {
    VarIcon,
    VarFormDetails,
    VarHoverOverlay
  },
  props,
  setup(props2) {
    const action = ref(null);
    const isFocusing = ref(false);
    const value = useVModel(props2, "modelValue");
    const isIndeterminate = useVModel(props2, "indeterminate");
    const checked = computed(() => value.value === props2.checkedValue);
    const checkedValue = computed(() => props2.checkedValue);
    const { checkboxGroup, bindCheckboxGroup } = useCheckboxGroup();
    const { hovering, handleHovering } = useHoverOverlay();
    const { form, bindForm } = useForm();
    const {
      errorMessage,
      validateWithTrigger: vt,
      validate: v,
      // expose
      resetValidation
    } = useValidation();
    const checkboxProvider = {
      checkedValue,
      checked,
      sync,
      validate,
      resetValidation,
      reset
    };
    call(bindCheckboxGroup, checkboxProvider);
    call(bindForm, checkboxProvider);
    useEventListener(() => window, "keydown", handleKeydown);
    useEventListener(() => window, "keyup", handleKeyup);
    function validateWithTrigger(trigger) {
      nextTick(() => {
        const { validateTrigger, rules, modelValue } = props2;
        vt(validateTrigger, trigger, rules, modelValue);
      });
    }
    function change(changedValue) {
      const { checkedValue: checkedValue2, onChange } = props2;
      value.value = changedValue;
      call(onChange, value.value, isIndeterminate.value);
      validateWithTrigger("onChange");
      changedValue === checkedValue2 ? checkboxGroup == null ? void 0 : checkboxGroup.onChecked(checkedValue2) : checkboxGroup == null ? void 0 : checkboxGroup.onUnchecked(checkedValue2);
    }
    function handleClick(e) {
      const { disabled, readonly, checkedValue: checkedValue2, uncheckedValue, onClick } = props2;
      if ((form == null ? void 0 : form.disabled.value) || disabled) {
        return;
      }
      call(onClick, e);
      if ((form == null ? void 0 : form.readonly.value) || readonly) {
        return;
      }
      if (isIndeterminate.value === true) {
        isIndeterminate.value = false;
        call(props2.onChange, value.value, isIndeterminate.value);
        validateWithTrigger("onChange");
        return;
      }
      const maximum = checkboxGroup ? checkboxGroup.checkedCount.value >= Number(checkboxGroup.max.value) : false;
      if (!checked.value && maximum) {
        return;
      }
      change(checked.value ? uncheckedValue : checkedValue2);
    }
    function handleTextClick() {
      action.value.focus();
    }
    function sync(values) {
      const { checkedValue: checkedValue2, uncheckedValue } = props2;
      value.value = values.includes(checkedValue2) ? checkedValue2 : uncheckedValue;
    }
    function reset() {
      value.value = props2.uncheckedValue;
      resetValidation();
    }
    function toggle(changedValue) {
      const { checkedValue: checkedValue2, uncheckedValue } = props2;
      const shouldReverse = ![checkedValue2, uncheckedValue].includes(changedValue);
      if (shouldReverse) {
        changedValue = checked.value ? uncheckedValue : checkedValue2;
      }
      change(changedValue);
    }
    function handleKeydown(event) {
      if (!isFocusing.value) {
        return;
      }
      const { key } = event;
      if (key === "Enter" || key === " ") {
        preventDefault(event);
      }
      if (key === "Enter") {
        action.value.click();
      }
    }
    function handleKeyup(event) {
      if (!isFocusing.value) {
        return;
      }
      if (event.key === " ") {
        preventDefault(event);
        action.value.click();
      }
    }
    function validate() {
      return v(props2.rules, props2.modelValue);
    }
    return {
      action,
      isFocusing,
      isIndeterminate,
      checked,
      errorMessage,
      checkboxGroupErrorMessage: checkboxGroup == null ? void 0 : checkboxGroup.errorMessage,
      formDisabled: form == null ? void 0 : form.disabled,
      formReadonly: form == null ? void 0 : form.readonly,
      hovering,
      n,
      classes,
      handleHovering,
      handleClick,
      handleTextClick,
      toggle,
      reset,
      validate,
      resetValidation
    };
  }
});
__sfc__.render = __render__;
var stdin_default = __sfc__;
export {
  stdin_default as default
};
