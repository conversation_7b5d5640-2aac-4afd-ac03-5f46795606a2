import { withInstall, withPropsDefaultsSetter } from "../utils/components.mjs";
import AvatarGroup from "./AvatarGroup.mjs";
import { props as avatarGroupProps } from "./props.mjs";
withInstall(AvatarGroup);
withPropsDefaultsSetter(AvatarGroup, avatarGroupProps);
const _AvatarGroupComponent = AvatarGroup;
var stdin_default = AvatarGroup;
export {
  _AvatarGroupComponent,
  avatarGroupProps,
  stdin_default as default
};
