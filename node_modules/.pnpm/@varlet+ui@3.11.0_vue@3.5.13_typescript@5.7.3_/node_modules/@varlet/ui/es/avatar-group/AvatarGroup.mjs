import { computed, defineComponent } from "vue";
import { createNamespace } from "../utils/components.mjs";
import { toSizeUnit } from "../utils/elements.mjs";
import { props } from "./props.mjs";
const { name, n, classes } = createNamespace("avatar-group");
import { renderSlot as _renderSlot, normalizeClass as _normalizeClass, normalizeStyle as _normalizeStyle, openBlock as _openBlock, createElementBlock as _createElementBlock } from "vue";
function __render__(_ctx, _cache) {
  return _openBlock(), _createElementBlock(
    "div",
    {
      class: _normalizeClass(_ctx.classes(_ctx.n(), [_ctx.vertical, _ctx.n("--column"), _ctx.n("--row")])),
      style: _normalizeStyle(_ctx.rootStyles)
    },
    [
      _renderSlot(_ctx.$slots, "default")
    ],
    6
    /* CLASS, STYLE */
  );
}
const __sfc__ = defineComponent({
  name,
  props,
  setup(props2) {
    const rootStyles = computed(() => {
      if (props2.offset == null) {
        return {};
      }
      return {
        "--avatar-group-offset": toSizeUnit(props2.offset)
      };
    });
    return {
      rootStyles,
      n,
      classes
    };
  }
});
__sfc__.render = __render__;
var stdin_default = __sfc__;
export {
  stdin_default as default
};
