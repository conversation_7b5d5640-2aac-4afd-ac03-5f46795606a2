:root { --avatar-group-offset: -10px;}.var-avatar-group { display: inline-flex; flex-wrap: wrap;}.var-avatar-group--row { margin-left: calc(var(--avatar-group-offset) * -1);}.var-avatar-group--row .var-avatar { margin-left: var(--avatar-group-offset);}.var-avatar-group--column { flex-direction: column; margin-top: calc(var(--avatar-group-offset) * -1);}.var-avatar-group--column .var-avatar { margin-top: var(--avatar-group-offset);}