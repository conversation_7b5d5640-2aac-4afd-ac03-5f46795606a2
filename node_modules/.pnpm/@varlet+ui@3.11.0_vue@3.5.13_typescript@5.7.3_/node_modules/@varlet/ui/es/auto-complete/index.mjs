import { withInstall, withPropsDefaultsSetter } from "../utils/components.mjs";
import AutoComplete from "./AutoComplete.mjs";
import { props as autoCompleteProps } from "./props.mjs";
withInstall(AutoComplete);
withPropsDefaultsSetter(AutoComplete, autoCompleteProps);
const _AutoCompleteComponent = AutoComplete;
var stdin_default = AutoComplete;
export {
  _AutoCompleteComponent,
  stdin_default as default
};
