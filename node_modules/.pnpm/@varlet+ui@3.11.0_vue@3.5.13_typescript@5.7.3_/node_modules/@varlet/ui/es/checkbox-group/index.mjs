import { withI<PERSON><PERSON>, withPropsDefaultsSetter } from "../utils/components.mjs";
import CheckboxGroup from "./CheckboxGroup.mjs";
import { props as checkboxGroupProps } from "./props.mjs";
withInstall(CheckboxGroup);
withPropsDefaultsSetter(CheckboxGroup, checkboxGroupProps);
const _CheckboxGroupComponent = CheckboxGroup;
var stdin_default = CheckboxGroup;
export {
  _CheckboxGroupComponent,
  checkboxGroupProps,
  stdin_default as default
};
