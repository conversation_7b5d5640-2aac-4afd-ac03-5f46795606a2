import { computed, defineComponent, nextTick, watch } from "vue";
import { call, isFunction, uniq } from "@varlet/shared";
import VarCheckbox from "../checkbox/index.mjs";
import VarFormDetails from "../form-details/index.mjs";
import { useForm } from "../form/provide.mjs";
import { createNamespace, MaybeVNode, useValidation } from "../utils/components.mjs";
import { props } from "./props.mjs";
import { useCheckboxes } from "./provide.mjs";
const { name, n, classes } = createNamespace("checkbox-group");
import { renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createBlock as _createBlock, createCommentVNode as _createCommentVNode, renderSlot as _renderSlot, normalizeClass as _normalizeClass, createElementVNode as _createElementVNode } from "vue";
function __render__(_ctx, _cache) {
  const _component_maybe_v_node = _resolveComponent("maybe-v-node");
  const _component_var_checkbox = _resolveComponent("var-checkbox");
  const _component_var_form_details = _resolveComponent("var-form-details");
  return _openBlock(), _createElementBlock(
    "div",
    {
      class: _normalizeClass(_ctx.n("wrap"))
    },
    [
      _createElementVNode(
        "div",
        {
          class: _normalizeClass(_ctx.classes(_ctx.n(), _ctx.n(`--${_ctx.direction}`)))
        },
        [
          _ctx.options.length ? (_openBlock(true), _createElementBlock(
            _Fragment,
            { key: 0 },
            _renderList(_ctx.options, (option) => {
              return _openBlock(), _createBlock(_component_var_checkbox, {
                key: option[_ctx.valueKey],
                "checked-value": option[_ctx.valueKey],
                disabled: option.disabled
              }, {
                default: _withCtx(({ checked }) => [
                  _createVNode(_component_maybe_v_node, {
                    is: _ctx.isFunction(option[_ctx.labelKey]) ? option[_ctx.labelKey](option, checked) : option[_ctx.labelKey]
                  }, null, 8, ["is"])
                ]),
                _: 2
                /* DYNAMIC */
              }, 1032, ["checked-value", "disabled"]);
            }),
            128
            /* KEYED_FRAGMENT */
          )) : _createCommentVNode("v-if", true),
          _renderSlot(_ctx.$slots, "default")
        ],
        2
        /* CLASS */
      ),
      _createVNode(_component_var_form_details, { "error-message": _ctx.errorMessage }, null, 8, ["error-message"])
    ],
    2
    /* CLASS */
  );
}
const __sfc__ = defineComponent({
  name,
  components: { VarFormDetails, VarCheckbox, MaybeVNode },
  props,
  setup(props2) {
    const max = computed(() => props2.max);
    const checkedCount = computed(() => props2.modelValue.length);
    const { length, checkboxes, bindCheckboxes } = useCheckboxes();
    const { bindForm } = useForm();
    const {
      errorMessage,
      validateWithTrigger: vt,
      validate: v,
      // expose
      resetValidation
    } = useValidation();
    const checkboxGroupErrorMessage = computed(() => errorMessage.value);
    const checkboxGroupProvider = {
      max,
      checkedCount,
      onChecked,
      onUnchecked,
      validate,
      resetValidation,
      reset,
      errorMessage: checkboxGroupErrorMessage
    };
    watch(() => props2.modelValue, syncCheckboxes, { deep: true });
    watch(() => length.value, syncCheckboxes);
    bindCheckboxes(checkboxGroupProvider);
    call(bindForm, checkboxGroupProvider);
    function validateWithTrigger(trigger) {
      nextTick(() => {
        const { validateTrigger, rules, modelValue } = props2;
        vt(validateTrigger, trigger, rules, modelValue);
      });
    }
    function change(changedModelValue) {
      call(props2["onUpdate:modelValue"], changedModelValue);
      call(props2.onChange, changedModelValue);
      validateWithTrigger("onChange");
    }
    function onChecked(changedValue) {
      const { modelValue } = props2;
      if (!modelValue.includes(changedValue)) {
        change([...modelValue, changedValue]);
      }
    }
    function onUnchecked(changedValue) {
      const { modelValue } = props2;
      if (!modelValue.includes(changedValue)) {
        return;
      }
      change(modelValue.filter((value) => value !== changedValue));
    }
    function syncCheckboxes() {
      checkboxes.forEach(({ sync }) => sync(props2.modelValue));
    }
    function checkAll() {
      const checkedValues = checkboxes.map(({ checkedValue }) => checkedValue.value);
      const changedModelValue = uniq(checkedValues);
      call(props2["onUpdate:modelValue"], changedModelValue);
      return changedModelValue;
    }
    function inverseAll() {
      const checkedValues = checkboxes.filter(({ checked }) => !checked.value).map(({ checkedValue }) => checkedValue.value);
      const changedModelValue = uniq(checkedValues);
      call(props2["onUpdate:modelValue"], changedModelValue);
      return changedModelValue;
    }
    function reset() {
      call(props2["onUpdate:modelValue"], []);
      resetValidation();
    }
    function validate() {
      return v(props2.rules, props2.modelValue);
    }
    return {
      errorMessage,
      n,
      classes,
      checkAll,
      inverseAll,
      reset,
      validate,
      resetValidation,
      isFunction
    };
  }
});
__sfc__.render = __render__;
var stdin_default = __sfc__;
export {
  stdin_default as default
};
