import { useChildren } from "@varlet/use";
const CHECKBOX_GROUP_BIND_CHECKBOX_KEY = Symbol("CHECKBOX_GROUP_BIND_CHECKBOX_KEY");
function useCheckboxes() {
  const { bindChildren, childProviders, length } = useChildren(
    CHECKBOX_GROUP_BIND_CHECKBOX_KEY
  );
  return {
    length,
    checkboxes: childProviders,
    bindCheckboxes: bindChildren
  };
}
export {
  CHECKBOX_GROUP_BIND_CHECKBOX_KEY,
  useCheckboxes
};
