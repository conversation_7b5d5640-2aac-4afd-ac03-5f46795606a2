import { withInst<PERSON>, withPropsDefaultsSetter } from "../utils/components.mjs";
import { props as radioGroupProps } from "./props.mjs";
import RadioGroup from "./RadioGroup.mjs";
withInstall(RadioGroup);
withPropsDefaultsSetter(RadioGroup, radioGroupProps);
const _RadioGroupComponent = RadioGroup;
var stdin_default = RadioGroup;
export {
  _RadioGroupComponent,
  stdin_default as default,
  radioGroupProps
};
