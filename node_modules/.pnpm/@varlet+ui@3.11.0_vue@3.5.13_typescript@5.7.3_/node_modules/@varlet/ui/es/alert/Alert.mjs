import { computed, defineComponent } from "vue";
import { call } from "@varlet/shared";
import VarIcon from "../icon/index.mjs";
import { createNamespace, formatElevation } from "../utils/components.mjs";
import { props } from "./props.mjs";
const { name, n, classes } = createNamespace("alert");
const iconTypeMap = {
  success: "checkbox-marked-circle",
  warning: "warning",
  info: "information",
  danger: "error"
};
import { renderSlot as _renderSlot, resolveComponent as _resolveComponent, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, normalizeClass as _normalizeClass, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, createElementVNode as _createElementVNode, createVNode as _createVNode, normalizeStyle as _normalizeStyle } from "vue";
function __render__(_ctx, _cache) {
  const _component_var_icon = _resolveComponent("var-icon");
  return _openBlock(), _createElementBlock(
    "div",
    {
      class: _normalizeClass(_ctx.classes(_ctx.n(), _ctx.n("$--box"), _ctx.n(`--${_ctx.variant}`), _ctx.n(`--${_ctx.type}`), _ctx.formatElevation(_ctx.elevation, 2))),
      style: _normalizeStyle({
        "background-color": _ctx.color
      }),
      role: "alert"
    },
    [
      _ctx.isInternalType || _ctx.$slots["icon"] ? (_openBlock(), _createElementBlock(
        "div",
        {
          key: 0,
          class: _normalizeClass(_ctx.n("icon"))
        },
        [
          _renderSlot(_ctx.$slots, "icon", {}, () => [
            _ctx.isInternalType ? (_openBlock(), _createBlock(_component_var_icon, {
              key: 0,
              name: _ctx.iconTypeMap[_ctx.type]
            }, null, 8, ["name"])) : _createCommentVNode("v-if", true)
          ])
        ],
        2
        /* CLASS */
      )) : _createCommentVNode("v-if", true),
      _renderSlot(_ctx.$slots, "content", {}, () => [
        _createElementVNode(
          "div",
          {
            class: _normalizeClass(_ctx.n("content"))
          },
          [
            _ctx.title || _ctx.$slots["title"] ? (_openBlock(), _createElementBlock(
              "div",
              {
                key: 0,
                class: _normalizeClass(_ctx.n("title"))
              },
              [
                _renderSlot(_ctx.$slots, "title", {}, () => [
                  _createTextVNode(
                    _toDisplayString(_ctx.title),
                    1
                    /* TEXT */
                  )
                ])
              ],
              2
              /* CLASS */
            )) : _createCommentVNode("v-if", true),
            _ctx.message || _ctx.$slots["default"] ? (_openBlock(), _createElementBlock(
              "div",
              {
                key: 1,
                class: _normalizeClass(_ctx.n("message"))
              },
              [
                _renderSlot(_ctx.$slots, "default", {}, () => [
                  _createTextVNode(
                    _toDisplayString(_ctx.message),
                    1
                    /* TEXT */
                  )
                ])
              ],
              2
              /* CLASS */
            )) : _createCommentVNode("v-if", true)
          ],
          2
          /* CLASS */
        )
      ]),
      _ctx.closeable ? (_openBlock(), _createElementBlock(
        "div",
        {
          key: 1,
          class: _normalizeClass(_ctx.n("close-icon")),
          onClick: _cache[0] || (_cache[0] = (...args) => _ctx.handleClose && _ctx.handleClose(...args))
        },
        [
          _renderSlot(_ctx.$slots, "close-icon", {}, () => [
            _createVNode(_component_var_icon, { name: "close-circle" })
          ])
        ],
        2
        /* CLASS */
      )) : _createCommentVNode("v-if", true)
    ],
    6
    /* CLASS, STYLE */
  );
}
const __sfc__ = defineComponent({
  name,
  components: {
    VarIcon
  },
  props,
  setup(props2) {
    const isInternalType = computed(() => ["info", "success", "danger", "warning"].includes(props2.type));
    function handleClose(e) {
      call(props2.onClose, e);
    }
    return {
      n,
      classes,
      iconTypeMap,
      isInternalType,
      formatElevation,
      handleClose
    };
  }
});
__sfc__.render = __render__;
var stdin_default = __sfc__;
export {
  stdin_default as default
};
