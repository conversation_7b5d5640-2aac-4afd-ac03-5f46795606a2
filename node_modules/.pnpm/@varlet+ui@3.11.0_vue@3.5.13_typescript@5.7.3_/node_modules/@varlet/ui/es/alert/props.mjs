import { defineListenerProp } from "../utils/components.mjs";
const props = {
  type: {
    type: String,
    default: "info"
  },
  variant: {
    type: String,
    default: "standard"
  },
  color: String,
  title: String,
  message: String,
  closeable: Boolean,
  elevation: {
    type: [<PERSON><PERSON><PERSON>, String, Number],
    default: false
  },
  onClose: defineListenerProp()
};
export {
  props
};
