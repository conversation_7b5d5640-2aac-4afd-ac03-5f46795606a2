:root { --progress-font-size: var(--font-size-sm); --progress-track-color: #d8d8d8; --progress-label-color: #555; --progress-background: var(--color-primary); --progress-default-color: #f5f5f5; --progress-primary-color: var(--color-primary); --progress-danger-color: var(--color-danger); --progress-success-color: var(--color-success); --progress-warning-color: var(--color-warning); --progress-info-color: var(--color-info); --progress-linear-border-radius: 0px;}.var-progress { position: relative; font-size: var(--progress-font-size);}.var-progress__linear { display: flex; align-items: center;}.var-progress__linear-block { flex: 1; position: relative; overflow: hidden; height: 4px; border-radius: var(--progress-linear-border-radius);}.var-progress__linear-certain { width: 100%; height: 100%; border-radius: var(--progress-linear-border-radius);}.var-progress__linear-background { background-color: var(--progress-track-color);}.var-progress__linear-certain { position: absolute; background-color: var(--progress-background); top: 0; left: 0; transition: all 0.2s, background-color 0.8s;}.var-progress__linear-label { margin-left: 8px; flex: 0; color: var(--progress-label-color);}.var-progress__linear-indeterminate div { bottom: 0; height: inherit; left: 0; position: absolute; right: auto; top: 0; width: auto; will-change: left, right; border-radius: var(--progress-linear-border-radius);}.var-progress__linear-indeterminate div:first-child { animation: progress-linear-long 2.2s infinite;}.var-progress__linear-indeterminate div:last-child { animation: progress-linear-short 2.2s infinite;}.var-progress__linear--success { background-color: var(--progress-success-color);}.var-progress__linear--default { background-color: var(--progress-default-color);}.var-progress__linear--primary { background-color: var(--progress-primary-color);}.var-progress__linear--warning { background-color: var(--progress-warning-color);}.var-progress__linear--danger { background-color: var(--progress-danger-color);}.var-progress__linear--info { background-color: var(--progress-info-color);}@keyframes progress-linear-long { 0% { left: -90%; right: 100%; } 60% { left: -90%; right: 100%; } 100% { left: 100%; right: -35%; }}@keyframes progress-linear-short { 0% { left: -200%; right: 100%; } 60% { left: 107%; right: -8%; } 100% { left: 107%; right: -8%; }}.var-progress__circle { position: relative; width: 40px; height: 40px;}.var-progress__circle-background { stroke: var(--progress-track-color); z-index: 1;}.var-progress__circle-certain { transition: all 0.2s; stroke: var(--progress-background); position: absolute;}.var-progress__circle-label { position: absolute; left: 50%; top: 50%; transform: translate(-50%, -50%); color: var(--progress-label-color);}.var-progress__circle--success { stroke: var(--progress-success-color);}.var-progress__circle--default { stroke: var(--progress-default-color);}.var-progress__circle--primary { stroke: var(--progress-primary-color);}.var-progress__circle--warning { stroke: var(--progress-warning-color);}.var-progress__circle--danger { stroke: var(--progress-danger-color);}.var-progress__circle--info { stroke: var(--progress-info-color);}.var-progress__circle-indeterminate svg { animation: progress-circle-rotate 1.4s linear infinite; transform-origin: center center; transition: all 0.2s ease-in-out; width: 100%; height: 100%; position: absolute; top: 0; bottom: 0; left: 0; right: 0; z-index: 0;}.var-progress__circle-overlay { animation: progress-circle-dash 1.4s ease-in-out infinite, progress-circle-rotate 1.4s linear infinite; stroke-dasharray: 25, 200; stroke-dashoffset: 0; stroke-linecap: round; transform-origin: center center; transform: rotate(-90deg); z-index: 2;}@keyframes progress-circle-dash { 0% { stroke-dasharray: 1, 200; stroke-dashoffset: 0; } 50% { stroke-dasharray: 100, 200; stroke-dashoffset: -15px; } 100% { stroke-dasharray: 100, 200; stroke-dashoffset: -124px; }}@keyframes progress-circle-rotate { 100% { transform: rotate(270deg); }}