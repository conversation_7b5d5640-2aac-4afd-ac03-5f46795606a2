import { computed, defineComponent } from "vue";
import { clamp, isPlainObject, toNumber } from "@varlet/shared";
import { useClientId } from "@varlet/use";
import { createNamespace } from "../utils/components.mjs";
import { toPxNum, toSizeUnit } from "../utils/elements.mjs";
import { props } from "./props.mjs";
const MAX = 100;
const MIN = 0;
const RADIUS = 20;
const CIRCUMFERENCE = 2 * Math.PI * RADIUS;
const { name, n, classes } = createNamespace("progress");
import { normalizeClass as _normalizeClass, normalizeStyle as _normalizeStyle, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, renderSlot as _renderSlot, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, renderList as _renderList, Fragment as _Fragment } from "vue";
const _hoisted_1 = ["aria-valuenow"];
const _hoisted_2 = ["viewBox"];
const _hoisted_3 = { key: 0 };
const _hoisted_4 = ["id"];
const _hoisted_5 = ["offset", "stop-color"];
const _hoisted_6 = ["d", "stroke-width", "stroke-dasharray"];
const _hoisted_7 = ["d", "stroke-width", "stroke-dasharray", "stroke-dashoffset"];
function __render__(_ctx, _cache) {
  return _openBlock(), _createElementBlock("div", {
    class: _normalizeClass(_ctx.n()),
    role: "progressbar",
    "aria-valuemin": "0",
    "aria-valuemax": "100",
    "aria-valuenow": _ctx.indeterminate ? void 0 : _ctx.mode === "linear" ? _ctx.linearProps.value : _ctx.circleProps.value
  }, [
    _ctx.mode === "linear" ? (_openBlock(), _createElementBlock(
      "div",
      {
        key: 0,
        class: _normalizeClass(_ctx.n("linear"))
      },
      [
        _createElementVNode(
          "div",
          {
            class: _normalizeClass(_ctx.classes(_ctx.n("linear-block"), [_ctx.track, _ctx.n("linear-background")])),
            style: _normalizeStyle({ height: _ctx.toSizeUnit(_ctx.lineWidth), background: _ctx.trackColor })
          },
          [
            _ctx.indeterminate ? (_openBlock(), _createElementBlock(
              "div",
              {
                key: 0,
                class: _normalizeClass(_ctx.n("linear-indeterminate"))
              },
              [
                _createElementVNode(
                  "div",
                  {
                    class: _normalizeClass(_ctx.classes(_ctx.n(`linear--${_ctx.type}`))),
                    style: _normalizeStyle({ background: _ctx.progressColor })
                  },
                  null,
                  6
                  /* CLASS, STYLE */
                ),
                _createElementVNode(
                  "div",
                  {
                    class: _normalizeClass(_ctx.classes(_ctx.n(`linear--${_ctx.type}`))),
                    style: _normalizeStyle({ background: _ctx.progressColor })
                  },
                  null,
                  6
                  /* CLASS, STYLE */
                )
              ],
              2
              /* CLASS */
            )) : (_openBlock(), _createElementBlock(
              "div",
              {
                key: 1,
                class: _normalizeClass(_ctx.classes(_ctx.n("linear-certain"), _ctx.n(`linear--${_ctx.type}`))),
                style: _normalizeStyle({ background: _ctx.progressColor, width: _ctx.linearProps.width })
              },
              null,
              6
              /* CLASS, STYLE */
            ))
          ],
          6
          /* CLASS, STYLE */
        ),
        _ctx.label ? (_openBlock(), _createElementBlock(
          "div",
          {
            key: 0,
            class: _normalizeClass(_ctx.classes(_ctx.n("linear-label"), [_ctx.labelClass, _ctx.labelClass]))
          },
          [
            _renderSlot(_ctx.$slots, "default", {}, () => [
              _createTextVNode(
                _toDisplayString(_ctx.linearProps.roundValue),
                1
                /* TEXT */
              )
            ])
          ],
          2
          /* CLASS */
        )) : _createCommentVNode("v-if", true)
      ],
      2
      /* CLASS */
    )) : _createCommentVNode("v-if", true),
    _ctx.mode === "circle" ? (_openBlock(), _createElementBlock(
      "div",
      {
        key: 1,
        class: _normalizeClass(_ctx.classes(_ctx.n("circle"), [_ctx.indeterminate, _ctx.n("circle-indeterminate")])),
        style: _normalizeStyle({ width: _ctx.toSizeUnit(_ctx.size), height: _ctx.toSizeUnit(_ctx.size) })
      },
      [
        (_openBlock(), _createElementBlock("svg", {
          class: _normalizeClass(_ctx.n("circle-svg")),
          viewBox: _ctx.circleProps.viewBox
        }, [
          _ctx.isPlainObject(_ctx.color) ? (_openBlock(), _createElementBlock("defs", _hoisted_3, [
            _createElementVNode("linearGradient", {
              id: _ctx.id,
              x1: "100%",
              y1: "0%",
              x2: "0%",
              y2: "0%"
            }, [
              (_openBlock(true), _createElementBlock(
                _Fragment,
                null,
                _renderList(_ctx.linearGradientProgress, (progress, idx) => {
                  return _openBlock(), _createElementBlock("stop", {
                    key: idx,
                    offset: progress,
                    "stop-color": _ctx.color[progress]
                  }, null, 8, _hoisted_5);
                }),
                128
                /* KEYED_FRAGMENT */
              ))
            ], 8, _hoisted_4)
          ])) : _createCommentVNode("v-if", true),
          _ctx.track ? (_openBlock(), _createElementBlock("path", {
            key: 1,
            class: _normalizeClass(_ctx.n("circle-background")),
            d: _ctx.circleProps.path,
            fill: "transparent",
            "stroke-width": _ctx.circleProps.strokeWidth,
            "stroke-dasharray": _ctx.CIRCUMFERENCE,
            style: _normalizeStyle({
              stroke: _ctx.trackColor
            })
          }, null, 14, _hoisted_6)) : _createCommentVNode("v-if", true),
          _createElementVNode("path", {
            class: _normalizeClass(_ctx.classes(_ctx.n("circle-certain"), _ctx.n(`circle--${_ctx.type}`), [_ctx.indeterminate, _ctx.n("circle-overlay")])),
            d: _ctx.circleProps.path,
            fill: "transparent",
            "stroke-width": _ctx.circleProps.strokeWidth,
            "stroke-dasharray": _ctx.CIRCUMFERENCE,
            "stroke-dashoffset": _ctx.circleProps.strokeOffset,
            style: _normalizeStyle({
              stroke: _ctx.progressColor,
              transform: `rotateZ(${_ctx.rotate}deg)`,
              transformOrigin: "50% 50%"
            })
          }, null, 14, _hoisted_7)
        ], 10, _hoisted_2)),
        _ctx.label ? (_openBlock(), _createElementBlock(
          "div",
          {
            key: 0,
            class: _normalizeClass(_ctx.classes(_ctx.n("circle-label"), _ctx.labelClass))
          },
          [
            _renderSlot(_ctx.$slots, "default", {}, () => [
              _createTextVNode(
                _toDisplayString(_ctx.circleProps.roundValue),
                1
                /* TEXT */
              )
            ])
          ],
          2
          /* CLASS */
        )) : _createCommentVNode("v-if", true)
      ],
      6
      /* CLASS, STYLE */
    )) : _createCommentVNode("v-if", true)
  ], 10, _hoisted_1);
}
const __sfc__ = defineComponent({
  name,
  props,
  setup(props2) {
    const id = useClientId();
    const linearProps = computed(() => {
      const value = toNumber(props2.value);
      const width = clamp(value, MIN, MAX);
      const roundValue = clamp(Math.round(value), MIN, MAX);
      return {
        width: `${width}%`,
        roundValue: `${roundValue}%`,
        value: width
      };
    });
    const circleProps = computed(() => {
      const { size, lineWidth, value } = props2;
      const diameter = RADIUS / (1 - toPxNum(lineWidth) / toPxNum(size)) * 2;
      const viewBox = `0 0 ${diameter} ${diameter}`;
      const roundValue = clamp(Math.round(toNumber(value)), MIN, MAX);
      const strokeOffset = `${(MAX - roundValue) / MAX * CIRCUMFERENCE}`;
      const strokeWidth = toPxNum(lineWidth) / toPxNum(size) * diameter;
      const beginPositionX = 0;
      const beginPositionY = -RADIUS;
      const endPositionX = 0;
      const endPositionY = -2 * RADIUS;
      const path = `M ${diameter / 2} ${diameter / 2} m ${beginPositionX} ${beginPositionY} a ${RADIUS} ${RADIUS} 
        0 1 1 ${endPositionX} ${-endPositionY} a ${RADIUS} ${RADIUS} 0 1 1 ${-endPositionX} ${endPositionY}`;
      return {
        strokeWidth,
        viewBox,
        strokeOffset,
        roundValue: `${roundValue}%`,
        path,
        value: clamp(toNumber(value), MIN, MAX)
      };
    });
    const progressColor = computed(() => {
      if (isPlainObject(props2.color)) {
        return `url(#${id.value})`;
      }
      return props2.color;
    });
    const linearGradientProgress = computed(
      () => Object.keys(props2.color).sort((a, b) => parseFloat(a) - parseFloat(b))
    );
    return {
      id,
      linearProps,
      CIRCUMFERENCE,
      RADIUS,
      circleProps,
      progressColor,
      linearGradientProgress,
      n,
      classes,
      toSizeUnit,
      isPlainObject
    };
  }
});
__sfc__.render = __render__;
var stdin_default = __sfc__;
export {
  stdin_default as default
};
