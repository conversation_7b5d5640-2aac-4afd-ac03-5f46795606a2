import { withInstall, withPropsDefaultsSetter } from "../utils/components.mjs";
import CollapseTransition from "./CollapseTransition.mjs";
import { props as collapseTransitionProps } from "./props.mjs";
withInstall(CollapseTransition);
withPropsDefaultsSetter(CollapseTransition, collapseTransitionProps);
const _CollapseTransitionComponent = CollapseTransition;
var stdin_default = CollapseTransition;
export {
  _CollapseTransitionComponent,
  collapseTransitionProps,
  stdin_default as default
};
