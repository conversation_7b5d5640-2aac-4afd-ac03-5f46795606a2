import { computed, defineComponent, ref } from "vue";
import { createNamespace } from "../utils/components.mjs";
import { props } from "./props.mjs";
import { useCollapseTransition } from "./useCollapseTransition.mjs";
const { name, n } = createNamespace("collapse-transition");
import { renderSlot as _renderSlot, vShow as _vShow, normalizeClass as _normalizeClass, withDirectives as _withDirectives, openBlock as _openBlock, createElementBlock as _createElementBlock } from "vue";
function __render__(_ctx, _cache) {
  return _withDirectives((_openBlock(), _createElementBlock(
    "div",
    {
      ref: "contentEl",
      class: _normalizeClass(_ctx.n("content")),
      onTransitionend: _cache[0] || (_cache[0] = (...args) => _ctx.handleTransitionEnd && _ctx.handleTransitionEnd(...args)),
      onTransitionstart: _cache[1] || (_cache[1] = (...args) => _ctx.handleTransitionStart && _ctx.handleTransitionStart(...args))
    },
    [
      _renderSlot(_ctx.$slots, "default")
    ],
    34
    /* CLASS, NEED_HYDRATION */
  )), [
    [_vShow, _ctx.showContent]
  ]);
}
const __sfc__ = defineComponent({
  name,
  props,
  setup(props2) {
    const showContent = ref(false);
    const contentEl = ref(null);
    const expand = computed(() => props2.expand);
    const { handleTransitionEnd, handleTransitionStart } = useCollapseTransition({
      contentEl,
      showContent,
      expand
    });
    return {
      showContent,
      contentEl,
      n,
      handleTransitionEnd,
      handleTransitionStart
    };
  }
});
__sfc__.render = __render__;
var stdin_default = __sfc__;
export {
  stdin_default as default
};
