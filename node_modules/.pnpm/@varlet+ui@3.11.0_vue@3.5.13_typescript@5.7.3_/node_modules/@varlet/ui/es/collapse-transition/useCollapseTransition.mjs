var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
import { nextTick, watch } from "vue";
import { doubleRaf, raf } from "@varlet/shared";
function useCollapseTransition(options) {
  const { contentEl, showContent, expand } = options;
  let isInitToTrigger = true;
  watch(
    expand,
    (value) => {
      nextTick(() => {
        value ? open() : close();
      });
    },
    { immediate: true }
  );
  function open() {
    return __async(this, null, function* () {
      if (!contentEl.value) {
        return;
      }
      contentEl.value.style.height = "";
      showContent.value = true;
      yield raf();
      if (!contentEl.value) {
        return;
      }
      const { offsetHeight } = contentEl.value;
      contentEl.value.style.height = "0px";
      yield raf();
      if (!contentEl.value) {
        return;
      }
      contentEl.value.style.height = offsetHeight + "px";
      if (!isInitToTrigger) {
        return;
      }
      yield doubleRaf();
      if (isInitToTrigger) {
        handleTransitionEnd();
      }
    });
  }
  const close = () => __async(this, null, function* () {
    if (!contentEl.value) {
      return;
    }
    const { offsetHeight } = contentEl.value;
    contentEl.value.style.height = offsetHeight + "px";
    yield raf();
    contentEl.value.style.height = "0px";
  });
  const handleTransitionEnd = () => {
    if (!expand.value) {
      showContent.value = false;
    }
    contentEl.value.style.height = "";
  };
  const handleTransitionStart = () => {
    isInitToTrigger = false;
  };
  return {
    handleTransitionEnd,
    handleTransitionStart
  };
}
export {
  useCollapseTransition
};
