import { defineComponent, nextTick, ref } from "vue";
import { call, toNumber } from "@varlet/shared";
import VarFormDetails from "../form-details/index.mjs";
import { useForm } from "../form/provide.mjs";
import Hover from "../hover/index.mjs";
import VarHoverOverlay, { useHoverOverlay } from "../hover-overlay/index.mjs";
import VarIcon from "../icon/index.mjs";
import Ripple from "../ripple/index.mjs";
import { createNamespace, useValidation } from "../utils/components.mjs";
import { toSizeUnit } from "../utils/elements.mjs";
import { props } from "./props.mjs";
const { name, n } = createNamespace("rate");
import { renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, resolveComponent as _resolveComponent, normalizeClass as _normalizeClass, normalizeStyle as _normalizeStyle, createVNode as _createVNode, resolveDirective as _resolveDirective, withDirectives as _withDirectives, createElementVNode as _createElementVNode } from "vue";
const _hoisted_1 = ["onClick"];
function __render__(_ctx, _cache) {
  const _component_var_icon = _resolveComponent("var-icon");
  const _component_var_hover_overlay = _resolveComponent("var-hover-overlay");
  const _component_var_form_details = _resolveComponent("var-form-details");
  const _directive_ripple = _resolveDirective("ripple");
  const _directive_hover = _resolveDirective("hover");
  return _openBlock(), _createElementBlock(
    "div",
    {
      class: _normalizeClass(_ctx.n("wrap"))
    },
    [
      _createElementVNode(
        "div",
        {
          class: _normalizeClass(_ctx.n())
        },
        [
          (_openBlock(true), _createElementBlock(
            _Fragment,
            null,
            _renderList(_ctx.toNumber(_ctx.count), (value) => {
              return _withDirectives((_openBlock(), _createElementBlock("div", {
                key: value,
                style: _normalizeStyle(_ctx.getStyle(value)),
                class: _normalizeClass(_ctx.getClass(value)),
                onClick: ($event) => _ctx.handleClick(value, $event)
              }, [
                _createVNode(_component_var_icon, {
                  class: _normalizeClass(_ctx.n("content-icon")),
                  "var-rate-cover": "",
                  transition: 0,
                  namespace: _ctx.getCurrentState(value).namespace,
                  name: _ctx.getCurrentState(value).name,
                  style: _normalizeStyle({ fontSize: _ctx.toSizeUnit(_ctx.size) })
                }, null, 8, ["class", "namespace", "name", "style"]),
                _createVNode(_component_var_hover_overlay, {
                  hovering: _ctx.hovering && value === _ctx.currentHoveringValue && !_ctx.disabled && !_ctx.formDisabled
                }, null, 8, ["hovering"])
              ], 14, _hoisted_1)), [
                [_directive_ripple, { disabled: _ctx.formReadonly || _ctx.readonly || _ctx.formDisabled || _ctx.disabled || !_ctx.ripple }],
                [_directive_hover, _ctx.createHoverHandler(value), "desktop"]
              ]);
            }),
            128
            /* KEYED_FRAGMENT */
          ))
        ],
        2
        /* CLASS */
      ),
      _createVNode(_component_var_form_details, { "error-message": _ctx.errorMessage }, null, 8, ["error-message"])
    ],
    2
    /* CLASS */
  );
}
const __sfc__ = defineComponent({
  name,
  components: {
    VarIcon,
    VarFormDetails,
    VarHoverOverlay
  },
  directives: { Ripple, Hover },
  props,
  setup(props2) {
    const currentHoveringValue = ref(-1);
    const { form, bindForm } = useForm();
    const { errorMessage, validateWithTrigger: vt, validate: v, resetValidation } = useValidation();
    const { hovering } = useHoverOverlay();
    let lastScore = toNumber(props2.modelValue);
    const rateProvider = {
      reset,
      validate,
      resetValidation
    };
    call(bindForm, rateProvider);
    function getStyle(val) {
      const { count, gap } = props2;
      return {
        color: getCurrentState(val).color,
        marginRight: val !== toNumber(count) ? toSizeUnit(gap) : 0
      };
    }
    function getClass(val) {
      const { name: name2, color } = getCurrentState(val);
      return {
        [n("content")]: true,
        [n("--disabled")]: (form == null ? void 0 : form.disabled.value) || props2.disabled,
        [n("--error")]: errorMessage.value,
        [n("--primary")]: name2 !== props2.emptyIcon && !color
      };
    }
    function getCurrentState(index) {
      const {
        modelValue,
        disabled,
        disabledColor,
        color,
        half,
        emptyColor,
        icon,
        halfIcon,
        emptyIcon,
        namespace,
        halfIconNamespace,
        emptyIconNamespace
      } = props2;
      let iconColor = color;
      if (disabled || (form == null ? void 0 : form.disabled.value)) {
        iconColor = disabledColor;
      }
      if (index <= modelValue) {
        return { color: iconColor, name: icon, namespace };
      }
      if (half && index <= modelValue + 0.5) {
        return { color: iconColor, name: halfIcon, namespace: halfIconNamespace };
      }
      return {
        color: disabled || (form == null ? void 0 : form.disabled.value) ? disabledColor : emptyColor,
        name: emptyIcon,
        namespace: emptyIconNamespace
      };
    }
    function changeValue(score, event) {
      const { half, clearable } = props2;
      const { offsetWidth } = event.target;
      if (half && event.offsetX <= Math.floor(offsetWidth / 2)) {
        score -= 0.5;
      }
      if (lastScore === score && clearable) {
        score = 0;
      }
      if (lastScore !== score) {
        call(props2["onUpdate:modelValue"], score);
        call(props2.onChange, score);
      }
      lastScore = score;
    }
    function validate() {
      return v(props2.rules, props2.modelValue);
    }
    function validateWithTrigger() {
      nextTick(() => vt(["onChange"], "onChange", props2.rules, props2.modelValue));
    }
    function handleClick(score, event) {
      const { readonly, disabled } = props2;
      if (readonly || disabled || (form == null ? void 0 : form.disabled.value) || (form == null ? void 0 : form.readonly.value)) {
        return;
      }
      changeValue(score, event);
      validateWithTrigger();
    }
    function createHoverHandler(value) {
      return (isHover) => {
        currentHoveringValue.value = value;
        hovering.value = isHover;
      };
    }
    function reset() {
      call(props2["onUpdate:modelValue"], 0);
      resetValidation();
    }
    return {
      errorMessage,
      formDisabled: form == null ? void 0 : form.disabled,
      formReadonly: form == null ? void 0 : form.readonly,
      hovering,
      currentHoveringValue,
      getStyle,
      getClass,
      getCurrentState,
      handleClick,
      createHoverHandler,
      reset,
      validate,
      resetValidation,
      toSizeUnit,
      toNumber,
      n
    };
  }
});
__sfc__.render = __render__;
var stdin_default = __sfc__;
export {
  stdin_default as default
};
