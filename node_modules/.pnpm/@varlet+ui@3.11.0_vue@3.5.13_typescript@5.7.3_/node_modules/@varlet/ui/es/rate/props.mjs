import { iconProps } from "../icon/index.mjs";
import { defineListenerProp, pickProps } from "../utils/components.mjs";
const props = {
  modelValue: {
    type: Number,
    default: 0
  },
  count: {
    type: [String, Number],
    default: 5
  },
  color: String,
  icon: {
    type: String,
    default: "star"
  },
  emptyIcon: {
    type: String,
    default: "star-outline"
  },
  halfIcon: {
    type: String,
    default: "star-half-full"
  },
  namespace: pickProps(iconProps, "namespace"),
  emptyIconNamespace: pickProps(iconProps, "namespace"),
  halfIconNamespace: pickProps(iconProps, "namespace"),
  emptyColor: String,
  size: [String, Number],
  gap: [String, Number],
  half: <PERSON><PERSON>an,
  disabled: Boolean,
  disabledColor: String,
  readonly: Boolean,
  ripple: {
    type: Boolean,
    default: true
  },
  clearable: <PERSON>olean,
  rules: Array,
  onChange: defineListenerProp(),
  "onUpdate:modelValue": defineListenerProp()
};
export {
  props
};
