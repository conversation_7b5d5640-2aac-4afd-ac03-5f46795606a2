:root { --rate-color: var(--color-text); --rate-size: 24px; --rate-disabled-color: var(--color-text-disabled); --rate-error-color: var(--color-danger); --rate-action-padding: 4px; --rate-primary-color: var(--color-primary);}.var-rate { display: flex; transform: translateX(calc(-1 * var(--rate-action-padding)));}.var-rate__wrap { width: 100%;}.var-rate__content { position: relative; display: flex; padding: var(--rate-action-padding); cursor: pointer; border-radius: 50%; color: var(--rate-color); transition: color 0.25s; -webkit-tap-highlight-color: transparent;}.var-rate__content-icon[var-rate-cover] { font-size: var(--rate-size);}.var-rate--primary { color: var(--rate-primary-color);}.var-rate--disabled { color: var(--rate-disabled-color); cursor: not-allowed;}.var-rate--error { color: var(--rate-error-color);}