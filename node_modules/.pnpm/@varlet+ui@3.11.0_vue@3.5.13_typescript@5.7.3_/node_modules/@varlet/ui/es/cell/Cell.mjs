import { computed, defineComponent } from "vue";
import { call } from "@varlet/shared";
import VarIcon from "../icon/index.mjs";
import Ripple from "../ripple/index.mjs";
import { createNamespace } from "../utils/components.mjs";
import { toSizeUnit } from "../utils/elements.mjs";
import { props } from "./props.mjs";
const { name, n, classes } = createNamespace("cell");
import { renderSlot as _renderSlot, resolveComponent as _resolveComponent, createVNode as _createVNode, normalizeClass as _normalizeClass, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, normalizeStyle as _normalizeStyle, resolveDirective as _resolveDirective, withDirectives as _withDirectives } from "vue";
function __render__(_ctx, _cache) {
  const _component_var_icon = _resolveComponent("var-icon");
  const _directive_ripple = _resolveDirective("ripple");
  return _withDirectives((_openBlock(), _createElementBlock(
    "div",
    {
      class: _normalizeClass(_ctx.classes(_ctx.n(), [_ctx.border, _ctx.n("--border")], [_ctx.onClick, _ctx.n("--cursor")])),
      style: _normalizeStyle(_ctx.borderOffsetStyles),
      onClick: _cache[0] || (_cache[0] = (...args) => _ctx.handleClick && _ctx.handleClick(...args))
    },
    [
      _renderSlot(_ctx.$slots, "icon", {}, () => [
        _ctx.icon ? (_openBlock(), _createElementBlock(
          "div",
          {
            key: 0,
            class: _normalizeClass(_ctx.classes(_ctx.n("icon"), _ctx.iconClass))
          },
          [
            _createVNode(_component_var_icon, {
              name: _ctx.icon,
              namespace: _ctx.namespace
            }, null, 8, ["name", "namespace"])
          ],
          2
          /* CLASS */
        )) : _createCommentVNode("v-if", true)
      ]),
      _createElementVNode(
        "div",
        {
          class: _normalizeClass(_ctx.n("content"))
        },
        [
          _renderSlot(_ctx.$slots, "default", {}, () => [
            _ctx.title ? (_openBlock(), _createElementBlock(
              "div",
              {
                key: 0,
                class: _normalizeClass(_ctx.classes(_ctx.n("title"), _ctx.titleClass))
              },
              _toDisplayString(_ctx.title),
              3
              /* TEXT, CLASS */
            )) : _createCommentVNode("v-if", true)
          ]),
          _renderSlot(_ctx.$slots, "description", {}, () => [
            _ctx.description ? (_openBlock(), _createElementBlock(
              "div",
              {
                key: 0,
                class: _normalizeClass(_ctx.classes(_ctx.n("description"), _ctx.descriptionClass))
              },
              _toDisplayString(_ctx.description),
              3
              /* TEXT, CLASS */
            )) : _createCommentVNode("v-if", true)
          ])
        ],
        2
        /* CLASS */
      ),
      _ctx.$slots.extra ? (_openBlock(), _createElementBlock(
        "div",
        {
          key: 0,
          class: _normalizeClass(_ctx.classes(_ctx.n("extra"), _ctx.extraClass))
        },
        [
          _renderSlot(_ctx.$slots, "extra")
        ],
        2
        /* CLASS */
      )) : _createCommentVNode("v-if", true)
    ],
    6
    /* CLASS, STYLE */
  )), [
    [_directive_ripple, { disabled: !_ctx.ripple }]
  ]);
}
const __sfc__ = defineComponent({
  name,
  components: { VarIcon },
  directives: { Ripple },
  props,
  setup(props2) {
    const borderOffsetStyles = computed(() => {
      if (props2.borderOffset == null) {
        return {};
      }
      return {
        "--cell-border-left": toSizeUnit(props2.borderOffset),
        "--cell-border-right": toSizeUnit(props2.borderOffset)
      };
    });
    function handleClick(e) {
      call(props2.onClick, e);
    }
    return {
      borderOffsetStyles,
      n,
      classes,
      toSizeUnit,
      handleClick
    };
  }
});
__sfc__.render = __render__;
var stdin_default = __sfc__;
export {
  stdin_default as default
};
