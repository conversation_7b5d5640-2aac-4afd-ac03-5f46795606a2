:root { --cell-color: var(--color-text); --cell-font-size: var(--font-size-md); --cell-description-font-size: var(--font-size-sm); --cell-description-color: rgba(0, 0, 0, 0.6); --cell-description-margin-top: 4px; --cell-padding: 10px 12px; --cell-min-height: 40px; --cell-border-color: var(--color-outline); --cell-border-left: 12px; --cell-border-right: 12px; --cell-icon-right: 8px; --cell-extra-left: 8px;}.var-cell { align-items: center; display: flex; min-height: var(--cell-min-height); outline: none; width: 100%; padding: var(--cell-padding); position: relative; box-sizing: border-box; font-size: var(--cell-font-size); color: var(--cell-color); -webkit-tap-highlight-color: transparent;}.var-cell--border::after { position: absolute; box-sizing: border-box; content: ' '; pointer-events: none; bottom: 0; right: var(--cell-border-right); left: var(--cell-border-left); border-bottom: 1px solid var(--cell-border-color); transform: scaleY(0.5); transition: border 0.25s;}.var-cell__icon { margin-right: var(--cell-icon-right); flex: 0;}.var-cell__content { flex: 1; min-width: 0;}.var-cell__title { overflow: hidden; text-overflow: ellipsis; white-space: nowrap;}.var-cell__description { font-size: var(--cell-description-font-size); color: var(--cell-description-color); margin-top: var(--cell-description-margin-top);}.var-cell__extra { flex: 0; margin-left: var(--cell-extra-left);}.var-cell--cursor { cursor: pointer;}