import { iconProps } from "../icon/index.mjs";
import { defineListenerProp, pickProps } from "../utils/components.mjs";
const props = {
  title: String,
  icon: pickProps(iconProps, "name"),
  namespace: pickProps(iconProps, "namespace"),
  description: String,
  border: Boolean,
  borderOffset: [Number, String],
  iconClass: String,
  titleClass: String,
  descriptionClass: String,
  extraClass: String,
  ripple: Boolean,
  onClick: defineListenerProp()
};
export {
  props
};
