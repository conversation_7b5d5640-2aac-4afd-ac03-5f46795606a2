import { computed, defineComponent, onUpdated, ref } from "vue";
import { getRect } from "@varlet/shared";
import { onSmartMounted, onWindowResize } from "@varlet/use";
import { createNamespace, formatElevation } from "../utils/components.mjs";
import { toSizeUnit } from "../utils/elements.mjs";
import { props } from "./props.mjs";
const { name, n, classes } = createNamespace("app-bar");
import { renderSlot as _renderSlot, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, normalizeClass as _normalizeClass, normalizeStyle as _normalizeStyle, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, mergeProps as _mergeProps, Fragment as _Fragment } from "vue";
function __render__(_ctx, _cache) {
  return _openBlock(), _createElementBlock(
    _Fragment,
    null,
    [
      _createElementVNode(
        "div",
        _mergeProps({
          ref: "appBar",
          class: _ctx.classes(
            _ctx.n(),
            _ctx.n("$--box"),
            [_ctx.safeAreaTop, _ctx.n("--safe-area-top")],
            [_ctx.round, _ctx.n("--round")],
            [_ctx.fixed, _ctx.n("--fixed")],
            [_ctx.border, _ctx.n("--border")],
            _ctx.formatElevation(_ctx.elevation, 3)
          ),
          style: _ctx.rootStyles
        }, _ctx.$attrs),
        [
          _createElementVNode(
            "div",
            {
              class: _normalizeClass(_ctx.n("toolbar"))
            },
            [
              _createElementVNode(
                "div",
                {
                  class: _normalizeClass(_ctx.n("left"))
                },
                [
                  _renderSlot(_ctx.$slots, "left"),
                  _ctx.titlePosition === "left" ? (_openBlock(), _createElementBlock(
                    "div",
                    {
                      key: 0,
                      class: _normalizeClass(_ctx.n("title")),
                      style: _normalizeStyle({ paddingLeft: _ctx.paddingLeft })
                    },
                    [
                      _renderSlot(_ctx.$slots, "default", {}, () => [
                        _createTextVNode(
                          _toDisplayString(_ctx.title),
                          1
                          /* TEXT */
                        )
                      ])
                    ],
                    6
                    /* CLASS, STYLE */
                  )) : _createCommentVNode("v-if", true)
                ],
                2
                /* CLASS */
              ),
              _ctx.titlePosition === "center" ? (_openBlock(), _createElementBlock(
                "div",
                {
                  key: 0,
                  class: _normalizeClass(_ctx.n("title"))
                },
                [
                  _renderSlot(_ctx.$slots, "default", {}, () => [
                    _createTextVNode(
                      _toDisplayString(_ctx.title),
                      1
                      /* TEXT */
                    )
                  ])
                ],
                2
                /* CLASS */
              )) : _createCommentVNode("v-if", true),
              _createElementVNode(
                "div",
                {
                  class: _normalizeClass(_ctx.n("right"))
                },
                [
                  _ctx.titlePosition === "right" ? (_openBlock(), _createElementBlock(
                    "div",
                    {
                      key: 0,
                      class: _normalizeClass(_ctx.n("title")),
                      style: _normalizeStyle({ paddingRight: _ctx.paddingRight })
                    },
                    [
                      _renderSlot(_ctx.$slots, "default", {}, () => [
                        _createTextVNode(
                          _toDisplayString(_ctx.title),
                          1
                          /* TEXT */
                        )
                      ])
                    ],
                    6
                    /* CLASS, STYLE */
                  )) : _createCommentVNode("v-if", true),
                  _renderSlot(_ctx.$slots, "right")
                ],
                2
                /* CLASS */
              )
            ],
            2
            /* CLASS */
          ),
          _renderSlot(_ctx.$slots, "content")
        ],
        16
        /* FULL_PROPS */
      ),
      _ctx.fixed && _ctx.placeholder ? (_openBlock(), _createElementBlock(
        "div",
        {
          key: 0,
          class: _normalizeClass(_ctx.n("placeholder")),
          style: _normalizeStyle({ height: _ctx.placeholderHeight })
        },
        null,
        6
        /* CLASS, STYLE */
      )) : _createCommentVNode("v-if", true)
    ],
    64
    /* STABLE_FRAGMENT */
  );
}
const __sfc__ = defineComponent({
  name,
  props,
  setup(props2, { slots }) {
    const appBar = ref(null);
    const paddingLeft = ref();
    const paddingRight = ref();
    const placeholderHeight = ref();
    const rootStyles = computed(() => {
      const { image, color, textColor, imageLinearGradient, zIndex } = props2;
      if (image != null) {
        const gradient = imageLinearGradient ? `linear-gradient(${imageLinearGradient}), ` : "";
        return {
          "background-image": `${gradient}url(${image})`,
          "background-position": "center center",
          "background-size": "cover",
          "z-index": zIndex
        };
      }
      return {
        background: color,
        color: textColor,
        "z-index": zIndex
      };
    });
    onWindowResize(resizePlaceholder);
    onSmartMounted(() => {
      computePadding();
      resizePlaceholder();
    });
    onUpdated(computePadding);
    function computePadding() {
      paddingLeft.value = slots.left ? 0 : void 0;
      paddingRight.value = slots.right ? 0 : void 0;
    }
    function resizePlaceholder() {
      if (!props2.fixed || !props2.placeholder) {
        return;
      }
      const { height } = getRect(appBar.value);
      placeholderHeight.value = toSizeUnit(height);
    }
    return {
      rootStyles,
      paddingLeft,
      paddingRight,
      n,
      classes,
      formatElevation,
      appBar,
      placeholderHeight
    };
  }
});
__sfc__.render = __render__;
var stdin_default = __sfc__;
export {
  stdin_default as default
};
