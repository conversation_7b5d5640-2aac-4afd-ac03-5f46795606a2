import { useChildren } from "@varlet/use";
const BREADCRUMBS_BIND_BREADCRUMB_ITEM_KEY = Symbol("BREADCRUMBS_BIND_BREADCRUMB_KEY");
function useBreadcrumbsList() {
  const { childProviders, bindChildren, length } = useChildren(
    BREADCRUMBS_BIND_BREADCRUMB_ITEM_KEY
  );
  return {
    length,
    breadcrumbList: childProviders,
    bindBreadcrumbList: bindChildren
  };
}
export {
  BREADCRUMBS_BIND_BREADCRUMB_ITEM_KEY,
  useBreadcrumbsList
};
