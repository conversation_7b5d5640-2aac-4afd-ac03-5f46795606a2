import { withInstall, withPropsDefaultsSetter } from "../utils/components.mjs";
import Breadcrumbs from "./Breadcrumbs.mjs";
import { props as breadcrumbsProps } from "./props.mjs";
withInstall(Breadcrumbs);
withPropsDefaultsSetter(Breadcrumbs, breadcrumbsProps);
const _BreadcrumbsComponent = Breadcrumbs;
var stdin_default = Breadcrumbs;
export {
  _BreadcrumbsComponent,
  breadcrumbsProps,
  stdin_default as default
};
