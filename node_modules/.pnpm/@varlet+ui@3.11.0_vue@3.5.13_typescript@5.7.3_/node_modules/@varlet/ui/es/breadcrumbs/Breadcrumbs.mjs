import { computed, defineComponent } from "vue";
import { createNamespace } from "../utils/components.mjs";
import { props } from "./props.mjs";
import { useBreadcrumbsList } from "./provide.mjs";
const { name, n } = createNamespace("breadcrumbs");
import { renderSlot as _renderSlot, normalizeClass as _normalizeClass, openBlock as _openBlock, createElementBlock as _createElementBlock } from "vue";
function __render__(_ctx, _cache) {
  return _openBlock(), _createElementBlock(
    "div",
    {
      class: _normalizeClass(_ctx.n()),
      role: "navigation",
      "aria-label": "Breadcrumbs"
    },
    [
      _renderSlot(_ctx.$slots, "default")
    ],
    2
    /* CLASS */
  );
}
const __sfc__ = defineComponent({
  name,
  props,
  setup(props2) {
    const separator = computed(() => props2.separator);
    const { bindBreadcrumbList, length } = useBreadcrumbsList();
    const breadcrumbsProvider = {
      length,
      separator
    };
    bindBreadcrumbList(breadcrumbsProvider);
    return { n };
  }
});
__sfc__.render = __render__;
var stdin_default = __sfc__;
export {
  stdin_default as default
};
