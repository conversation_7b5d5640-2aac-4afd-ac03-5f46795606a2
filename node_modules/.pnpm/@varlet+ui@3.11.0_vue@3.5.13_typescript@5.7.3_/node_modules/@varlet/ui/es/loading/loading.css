:root { --loading-color: var(--color-primary); --loading-opacity: 0.38; --loading-desc-margin: 8px 0 0; --loading-desc-color: var(--color-primary);}.var-loading { --loading-normal-width: 50px; --loading-normal-height: calc(var(--font-size-md) + 2px); --loading-large-width: 64px; --loading-large-height: calc(var(--font-size-lg) + 2px); --loading-small-width: 36px; --loading-small-height: calc(var(--font-size-sm) + 2px); --loading-mini-width: 22px; --loading-mini-height: calc(var(--font-size-xs) + 2px); position: relative;}.var-loading__content { position: relative; transition: opacity 0.3s; opacity: 1;}.var-loading__content--active { opacity: var(--loading-opacity);}.var-loading__content-mask { position: absolute; left: 0; right: 0; top: 0; bottom: 0;}.var-loading__body { display: flex; justify-content: center; align-items: center; height: 100%; flex-direction: column;}.var-loading__inside { position: absolute; left: 50%; top: 0; transform: translateX(-50%); max-height: 50vh; z-index: 1;}.var-loading__description { color: var(--loading-desc-color); margin: var(--loading-desc-margin);}.var-loading__description--large { font-size: var(--font-size-lg);}.var-loading__description--normal { font-size: var(--font-size-md);}.var-loading__description--small { font-size: var(--font-size-sm);}.var-loading__description--mini { font-size: var(--font-size-xs);}.var-loading__circle { display: flex;}.var-loading__circle-block { display: inline-block; color: var(--loading-color); animation: circle 1.8s linear infinite;}.var-loading__circle-block--large { width: 36px; height: 36px;}.var-loading__circle-block--normal { width: 30px; height: 30px;}.var-loading__circle-block--small { width: 24px; height: 24px;}.var-loading__circle-block--mini { width: 18px; height: 18px;}.var-loading__circle-block svg { display: block; width: 100%; height: 100%;}.var-loading__circle-block svg circle { animation: circular 1.5s ease-in-out infinite; stroke: currentColor; stroke-width: 3; stroke-linecap: round;}@keyframes circle { from { transform: rotate(0deg); } to { transform: rotate(360deg); }}@keyframes circular { 0% { stroke-dasharray: 1, 200; stroke-dashoffset: 0; } 50% { stroke-dasharray: 90, 150; stroke-dashoffset: -40; } 100% { stroke-dasharray: 90, 150; stroke-dashoffset: -120; }}.var-loading__wave { --loading-wave-size-item-width: 5px; --loading-wave-size-item-margin: 5px; display: flex; align-items: center; justify-content: center; flex-shrink: 0;}.var-loading__wave--large { width: var(--loading-large-width); height: var(--loading-large-height);}.var-loading__wave--normal { width: var(--loading-normal-width); height: var(--loading-normal-height);}.var-loading__wave--small { width: var(--loading-small-width); height: var(--loading-small-height);}.var-loading__wave--mini { width: var(--loading-mini-width); height: var(--loading-mini-height);}.var-loading__wave-item { height: 100%; display: inline-block; animation: 1.2s ease-in-out infinite wave; background-color: var(--loading-color);}.var-loading__wave-item:nth-child(1) { animation-delay: -1.2s; margin-left: 0;}.var-loading__wave-item:nth-child(2) { animation-delay: -1.1s;}.var-loading__wave-item:nth-child(3) { animation-delay: -1s;}.var-loading__wave-item:nth-child(4) { animation-delay: -0.9s;}.var-loading__wave-item:nth-child(5) { animation-delay: -0.8s;}.var-loading__wave-item--large { width: var(--loading-wave-size-item-width); margin-left: var(--loading-wave-size-item-margin);}.var-loading__wave-item--normal { width: calc(var(--loading-wave-size-item-width) - 1px); margin-left: calc(var(--loading-wave-size-item-margin) - 1px);}.var-loading__wave-item--small { width: calc(var(--loading-wave-size-item-width) - 2px); margin-left: calc(var(--loading-wave-size-item-margin) - 2px);}.var-loading__wave-item--mini { width: calc(var(--loading-wave-size-item-width) - 3px); margin-left: calc(var(--loading-wave-size-item-margin) - 3px);}@keyframes wave { 0%, 40%, 100% { transform: scaleY(0.4); } 20% { transform: scaleY(1); }}.var-loading__cube { --loading-cube-size-item-width: 10px; --loading-cube-size-item-height: 10px; --loading-cube-size-item-margin: 5px; display: flex; align-items: center; flex-shrink: 0;}.var-loading__cube--large { width: var(--loading-large-width); height: var(--loading-large-height);}.var-loading__cube--normal { width: var(--loading-normal-width); height: var(--loading-normal-height);}.var-loading__cube--small { width: var(--loading-small-width); height: var(--loading-small-height);}.var-loading__cube--mini { width: var(--loading-mini-width); height: var(--loading-mini-height);}.var-loading__cube-item { display: inline-block; transform-origin: right bottom; animation: 1.5s ease infinite cube; background-color: var(--loading-color);}.var-loading__cube-item:nth-child(1) { animation-delay: 0.2s; margin-left: 0;}.var-loading__cube-item:nth-child(2) { animation-delay: 0.4s;}.var-loading__cube-item:nth-child(3) { animation-delay: 0.6s;}.var-loading__cube-item:nth-child(4) { animation-delay: 0.8s;}.var-loading__cube-item--large { height: var(--loading-cube-size-item-height); width: var(--loading-cube-size-item-width); margin-left: var(--loading-cube-size-item-margin);}.var-loading__cube-item--normal { height: calc(var(--loading-cube-size-item-height) - 2px); width: calc(var(--loading-cube-size-item-width) - 2px); margin-left: calc(var(--loading-cube-size-item-margin) - 1px);}.var-loading__cube-item--small { height: calc(var(--loading-cube-size-item-height) - 4px); width: calc(var(--loading-cube-size-item-width) - 4px); margin-left: calc(var(--loading-cube-size-item-margin) - 2px);}.var-loading__cube-item--mini { height: calc(var(--loading-cube-size-item-height) - 6px); width: calc(var(--loading-cube-size-item-width) - 6px); margin-left: calc(var(--loading-cube-size-item-margin) - 3px);}@keyframes cube { 0% { opacity: 1; transform: scale(1); } 100% { opacity: 0; transform: rotate(90deg) scale(0.3); }}.var-loading__rect { --loading-rect-size-item-width: 8px; --loading-rect-size-item-height: 100%; display: flex; flex-wrap: nowrap; justify-content: center; align-items: center; flex-shrink: 0;}.var-loading__rect--large { width: var(--loading-large-width); height: var(--loading-large-height);}.var-loading__rect--normal { width: var(--loading-normal-width); height: var(--loading-normal-height);}.var-loading__rect--small { width: var(--loading-small-width); height: var(--loading-small-height);}.var-loading__rect--mini { width: var(--loading-mini-width); height: var(--loading-mini-height);}.var-loading__rect-item { animation: 2s ease-in-out infinite rect; background-color: var(--loading-color);}.var-loading__rect-item:nth-child(1) { animation-delay: 1.75s;}.var-loading__rect-item:nth-child(2) { animation-delay: 1.5s;}.var-loading__rect-item:nth-child(3) { animation-delay: 1.25s;}.var-loading__rect-item:nth-child(4) { animation-delay: 1s;}.var-loading__rect-item:nth-child(5) { animation-delay: 0.75s;}.var-loading__rect-item:nth-child(6) { animation-delay: 0.5s;}.var-loading__rect-item:nth-child(7) { animation-delay: 0.25s;}.var-loading__rect-item:nth-child(8) { animation-delay: 0s;}.var-loading__rect-item--large { height: var(--loading-rect-size-item-height); width: var(--loading-rect-size-item-width);}.var-loading__rect-item--normal { height: calc(var(--loading-rect-size-item-height) * 0.9); width: calc(var(--loading-rect-size-item-width) * 0.8);}.var-loading__rect-item--small { height: calc(var(--loading-rect-size-item-height) * 0.8); width: calc(var(--loading-rect-size-item-width) * 0.6);}.var-loading__rect-item--mini { height: calc(var(--loading-rect-size-item-height) * 0.7); width: calc(var(--loading-rect-size-item-width) * 0.4);}@keyframes rect { 0% { opacity: 0.3; } 25% { opacity: 1; } 50% { opacity: 0.3; } 65% { opacity: 1; } 100% { opacity: 0.3; }}.var-loading__disappear { --loading-disappear-size-item-width: 15px; --loading-disappear-size-item-height: 15px; display: flex; justify-content: space-around; align-items: center; flex-flow: nowrap; flex-shrink: 0;}.var-loading__disappear--large { width: var(--loading-large-width); height: var(--loading-large-height);}.var-loading__disappear--normal { width: var(--loading-normal-width); height: var(--loading-normal-height);}.var-loading__disappear--small { width: var(--loading-small-width); height: var(--loading-small-height);}.var-loading__disappear--mini { width: var(--loading-mini-width); height: var(--loading-mini-height);}.var-loading__disappear-item { border-radius: 50%; animation: 0.5s ease-in-out infinite alternate disappear; background-color: var(--loading-color);}.var-loading__disappear-item:nth-child(1) { animation-delay: -0.4s;}.var-loading__disappear-item:nth-child(2) { animation-delay: -0.2s;}.var-loading__disappear-item:nth-child(3) { animation-delay: 0s;}.var-loading__disappear-item--large { height: var(--loading-disappear-size-item-height); width: var(--loading-disappear-size-item-width);}.var-loading__disappear-item--normal { height: calc(var(--loading-disappear-size-item-height) * 0.8); width: calc(var(--loading-disappear-size-item-width) * 0.8);}.var-loading__disappear-item--small { height: calc(var(--loading-disappear-size-item-height) * 0.6); width: calc(var(--loading-disappear-size-item-width) * 0.6);}.var-loading__disappear-item--mini { height: calc(var(--loading-disappear-size-item-height) * 0.4); width: calc(var(--loading-disappear-size-item-width) * 0.4);}@keyframes disappear { 0% { opacity: 1; } 100% { opacity: 0; }}