import { computed, defineComponent, ref, watch } from "vue";
import { isFunction, preventDefault } from "@varlet/shared";
import { useEventListener } from "@varlet/use";
import VarCheckbox from "../checkbox/index.mjs";
import Hover from "../hover/index.mjs";
import VarHoverOverlay, { useHoverOverlay } from "../hover-overlay/index.mjs";
import Ripple from "../ripple/index.mjs";
import { createNamespace, MaybeVNode } from "../utils/components.mjs";
import { props } from "./props.mjs";
import { useSelect } from "./provide.mjs";
const { name, n, classes } = createNamespace("option");
import { normalizeClass as _normalizeClass, normalizeStyle as _normalizeStyle, createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, withModifiers as _withModifiers, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, renderSlot as _renderSlot, createVNode as _createVNode, resolveDirective as _resolveDirective, createElement<PERSON>lock as _createElementBlock, withDirectives as _withDirectives } from "vue";
const _hoisted_1 = ["tabindex"];
function __render__(_ctx, _cache) {
  const _component_var_checkbox = _resolveComponent("var-checkbox");
  const _component_maybe_v_node = _resolveComponent("maybe-v-node");
  const _component_var_hover_overlay = _resolveComponent("var-hover-overlay");
  const _directive_ripple = _resolveDirective("ripple");
  const _directive_hover = _resolveDirective("hover");
  return _withDirectives((_openBlock(), _createElementBlock("div", {
    ref: "root",
    class: _normalizeClass(_ctx.classes(_ctx.n(), _ctx.n("$--box"), [_ctx.optionSelected, _ctx.n("--selected-color")], [_ctx.disabled, _ctx.n("--disabled")])),
    style: _normalizeStyle({
      color: _ctx.optionSelected ? _ctx.focusColor : void 0
    }),
    tabindex: _ctx.disabled ? void 0 : "-1",
    onFocus: _cache[2] || (_cache[2] = ($event) => _ctx.isFocusing = true),
    onBlur: _cache[3] || (_cache[3] = ($event) => _ctx.isFocusing = false),
    onClick: _cache[4] || (_cache[4] = (...args) => _ctx.handleClick && _ctx.handleClick(...args))
  }, [
    _createElementVNode(
      "div",
      {
        class: _normalizeClass(_ctx.classes(_ctx.n("cover"), [_ctx.optionSelected, _ctx.n("--selected-background")])),
        style: _normalizeStyle({
          background: _ctx.optionSelected ? _ctx.focusColor : void 0
        })
      },
      null,
      6
      /* CLASS, STYLE */
    ),
    _ctx.multiple ? (_openBlock(), _createBlock(_component_var_checkbox, {
      key: 0,
      ref: "checkbox",
      modelValue: _ctx.optionSelected,
      "onUpdate:modelValue": _cache[0] || (_cache[0] = ($event) => _ctx.optionSelected = $event),
      "checked-color": _ctx.focusColor,
      disabled: _ctx.disabled,
      onClick: _cache[1] || (_cache[1] = _withModifiers(() => {
      }, ["stop"])),
      onChange: _ctx.handleSelect
    }, null, 8, ["modelValue", "checked-color", "disabled", "onChange"])) : _createCommentVNode("v-if", true),
    _renderSlot(_ctx.$slots, "default", { selected: _ctx.optionSelected }, () => [
      _createElementVNode(
        "div",
        {
          class: _normalizeClass(_ctx.classes(_ctx.n("text"), _ctx.n("$--ellipsis")))
        },
        [
          _createVNode(_component_maybe_v_node, { is: _ctx.labelVNode }, null, 8, ["is"])
        ],
        2
        /* CLASS */
      )
    ]),
    _createVNode(_component_var_hover_overlay, {
      hovering: _ctx.hovering && !_ctx.disabled,
      focusing: _ctx.isFocusing && !_ctx.disabled
    }, null, 8, ["hovering", "focusing"])
  ], 46, _hoisted_1)), [
    [_directive_ripple, { disabled: _ctx.disabled || !_ctx.ripple }],
    [_directive_hover, _ctx.handleHovering, "desktop"]
  ]);
}
const __sfc__ = defineComponent({
  name,
  directives: { Ripple, Hover },
  components: {
    VarCheckbox,
    VarHoverOverlay,
    MaybeVNode
  },
  props,
  setup(props2) {
    const root = ref();
    const isFocusing = ref(false);
    const optionSelected = ref(false);
    const selected = computed(() => optionSelected.value);
    const value = computed(() => props2.value);
    const disabled = computed(() => props2.disabled);
    const ripple = computed(() => props2.ripple);
    const { select, bindSelect } = useSelect();
    const { multiple, focusColor, onSelect, computeLabel } = select;
    const { hovering, handleHovering } = useHoverOverlay();
    const labelVNode = computed(
      () => {
        var _a;
        return isFunction(props2.label) ? props2.label(
          (_a = props2.option) != null ? _a : {
            label: props2.label,
            value: props2.value,
            disabled: props2.disabled
          },
          optionSelected.value
        ) : props2.label;
      }
    );
    const optionProvider = {
      label: labelVNode,
      value,
      disabled,
      ripple,
      selected,
      sync
    };
    watch([() => props2.label, () => props2.value], computeLabel);
    bindSelect(optionProvider);
    useEventListener(() => window, "keydown", handleKeydown);
    useEventListener(() => window, "keyup", handleKeyup);
    function handleClick() {
      if (props2.disabled) {
        return;
      }
      handleSelect();
    }
    function handleKeydown(event) {
      if (!isFocusing.value) {
        return;
      }
      if (event.key === " " || event.key === "Enter") {
        preventDefault(event);
      }
      if (event.key === "Enter") {
        root.value.click();
      }
    }
    function handleKeyup(event) {
      if (!isFocusing.value) {
        return;
      }
      if (event.key === " ") {
        preventDefault(event);
        root.value.click();
      }
    }
    function handleSelect() {
      if (multiple.value) {
        optionSelected.value = !optionSelected.value;
      }
      onSelect(optionProvider);
    }
    function sync(checked) {
      optionSelected.value = checked;
    }
    return {
      root,
      optionSelected,
      multiple,
      focusColor,
      hovering,
      isFocusing,
      labelVNode,
      n,
      classes,
      handleHovering,
      handleClick,
      handleSelect
    };
  }
});
__sfc__.render = __render__;
var stdin_default = __sfc__;
export {
  stdin_default as default
};
