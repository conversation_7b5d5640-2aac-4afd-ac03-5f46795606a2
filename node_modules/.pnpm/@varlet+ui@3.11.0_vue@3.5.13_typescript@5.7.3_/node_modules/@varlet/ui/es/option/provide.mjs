import { assert } from "@varlet/shared";
import { useParent } from "@varlet/use";
import { SELECT_BIND_OPTION_KEY } from "../select/provide.mjs";
function useSelect() {
  const { index, parentProvider, bindParent } = useParent(SELECT_BIND_OPTION_KEY);
  assert(!!bindParent, "Option", "<var-option/> must in <var-select/>");
  return {
    index,
    select: parentProvider,
    bindSelect: bindParent
  };
}
export {
  useSelect
};
