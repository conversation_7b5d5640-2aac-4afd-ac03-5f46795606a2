import { defineListenerProp } from "../utils/components.mjs";
const props = {
  from: {
    type: [String, Number],
    default: 0
  },
  to: {
    type: [String, Number],
    default: 0
  },
  duration: {
    type: [String, Number],
    default: 2e3
  },
  precision: {
    type: [String, Number],
    default: 0
  },
  autoStart: {
    type: Boolean,
    default: true
  },
  timingFunction: {
    type: Function
  },
  onEnd: defineListenerProp()
};
export {
  props
};
