import { computed, defineComponent, onMounted, watch } from "vue";
import { call, floor, toNumber } from "@varlet/shared";
import { useMotion } from "@varlet/use";
import { createNamespace } from "../utils/components.mjs";
import { props } from "./props.mjs";
const { name, n } = createNamespace("count-to");
import { renderSlot as _renderSlot, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, normalizeClass as _normalizeClass, openBlock as _openBlock, createElementBlock as _createElementBlock } from "vue";
function __render__(_ctx, _cache) {
  return _openBlock(), _createElementBlock(
    "div",
    {
      class: _normalizeClass(_ctx.n())
    },
    [
      _renderSlot(_ctx.$slots, "default", { value: _ctx.value }, () => [
        _createTextVNode(
          _toDisplayString(_ctx.value),
          1
          /* TEXT */
        )
      ])
    ],
    2
    /* CLASS */
  );
}
const __sfc__ = defineComponent({
  name,
  props,
  setup(props2) {
    const {
      value: _value,
      reset: _reset,
      // expose
      start,
      // expose
      pause
    } = useMotion({
      from: () => toNumber(props2.from),
      to: () => toNumber(props2.to),
      duration: () => toNumber(props2.duration),
      timingFunction: props2.timingFunction,
      onFinished() {
        call(props2.onEnd);
      }
    });
    const value = computed(() => floor(_value.value, toNumber(props2.precision)));
    watch(() => [props2.from, props2.to, props2.duration], reset);
    onMounted(reset);
    function reset() {
      _reset();
      if (props2.autoStart) {
        start();
      }
    }
    return {
      value,
      n,
      start,
      pause,
      reset,
      toNumber,
      floor
    };
  }
});
__sfc__.render = __render__;
var stdin_default = __sfc__;
export {
  stdin_default as default
};
