:root { --chip-default-text-color: #555; --chip-primary-text-color: var(--color-on-primary-container); --chip-danger-text-color: var(--color-on-danger-container); --chip-success-text-color: var(--color-on-success-container); --chip-warning-text-color: var(--color-on-warning-container); --chip-info-text-color: var(--color-on-info-container); --chip-default-color: #e0e0e0; --chip-primary-color: var(--color-primary-container); --chip-danger-color: var(--color-danger-container); --chip-success-color: var(--color-success-container); --chip-warning-color: var(--color-warning-container); --chip-info-color: var(--color-info-container); --chip-primary-plain-color: var(--color-primary); --chip-danger-plain-color: var(--color-danger); --chip-success-plain-color: var(--color-success); --chip-warning-plain-color: var(--color-warning); --chip-info-plain-color: var(--color-info); --chip-border-radius: 2px; --chip-normal-height: 32px; --chip-large-height: 40px; --chip-small-height: 24px; --chip-mini-height: 16px; --chip-round-radius: 100px; --chip-normal-padding: 0 10px; --chip-large-padding: 0 17px; --chip-small-padding: 0 6px; --chip-mini-padding: 0 4px; --chip-text-normal-margin: 0 5px; --chip-text-large-margin: 0 5px; --chip-text-small-margin: 0 3px; --chip-text-mini-margin: 0 2px; --chip-mini-font-size: var(--font-size-xs); --chip-small-font-size: var(--font-size-sm); --chip-normal-font-size: var(--font-size-md); --chip-large-font-size: var(--font-size-lg);}.var-fade-leave-to { opacity: 0;}.var-fade-leave-active { transition: opacity 0.3s;}.var-chip { justify-content: center; align-items: center; font-family: Roboto, sans-serif; border-radius: var(--chip-border-radius); cursor: default; border: thin solid transparent; vertical-align: middle; transition: background-color 0.25s, border-radius 0.25s;}.var-chip--default { color: var(--chip-default-text-color); background: var(--chip-default-color);}.var-chip--primary { color: var(--chip-primary-text-color); background-color: var(--chip-primary-color);}.var-chip--info { color: var(--chip-info-text-color); background-color: var(--chip-info-color);}.var-chip--success { color: var(--chip-success-text-color); background-color: var(--chip-success-color);}.var-chip--warning { color: var(--chip-warning-text-color); background-color: var(--chip-warning-color);}.var-chip--danger { color: var(--chip-danger-text-color); background-color: var(--chip-danger-color);}.var-chip__plain { background-color: transparent;}.var-chip__plain:active { box-shadow: none;}.var-chip__plain-default { color: inherit; border-color: currentColor;}.var-chip__plain-primary { color: var(--chip-primary-plain-color); border-color: currentColor;}.var-chip__plain-info { color: var(--chip-info-plain-color); border-color: currentColor;}.var-chip__plain-success { color: var(--chip-success-plain-color); border-color: currentColor;}.var-chip__plain-warning { color: var(--chip-warning-plain-color); border-color: currentColor;}.var-chip__plain-danger { color: var(--chip-danger-plain-color); border-color: currentColor;}.var-chip--round { border-radius: var(--chip-round-radius);}.var-chip--normal { font-size: var(--chip-normal-font-size); padding: var(--chip-normal-padding); height: var(--chip-normal-height);}.var-chip--large { padding: var(--chip-large-padding); font-size: var(--chip-large-font-size); height: var(--chip-large-height);}.var-chip--small { padding: var(--chip-small-padding); font-size: var(--chip-small-font-size); height: var(--chip-small-height);}.var-chip--mini { padding: var(--chip-mini-padding); font-size: var(--chip-mini-font-size); height: var(--chip-mini-height);}.var-chip--close { cursor: pointer; -webkit-tap-highlight-color: transparent;}.var-chip__text-large { margin: var(--chip-text-large-margin);}.var-chip__text-normal { margin: var(--chip-text-normal-margin);}.var-chip__text-small { margin: var(--chip-text-small-margin);}.var-chip__text-mini { margin: var(--chip-text-mini-margin);}