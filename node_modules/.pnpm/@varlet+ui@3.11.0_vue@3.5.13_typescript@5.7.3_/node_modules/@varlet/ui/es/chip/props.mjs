import { iconProps } from "../icon/index.mjs";
import { defineListenerProp, pickProps } from "../utils/components.mjs";
const props = {
  type: {
    type: String,
    default: "default"
  },
  size: {
    type: String,
    default: "normal"
  },
  color: String,
  textColor: String,
  iconName: pickProps(iconProps, "name"),
  namespace: pickProps(iconProps, "namespace"),
  plain: Boolean,
  round: {
    type: Boolean,
    default: true
  },
  elevation: {
    type: [Boolean, Number, String],
    default: false
  },
  block: Boolean,
  closeable: Boolean,
  onClose: defineListenerProp()
};
export {
  props
};
