import { computed, defineComponent } from "vue";
import { call } from "@varlet/shared";
import VarIcon from "../icon/index.mjs";
import { createNamespace, formatElevation } from "../utils/components.mjs";
import { props } from "./props.mjs";
const { name, n, classes } = createNamespace("chip");
import { createCommentVNode as _createCommentVNode, renderSlot as _renderSlot, normalizeClass as _normalizeClass, createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, mergeProps as _mergeProps, Transition as _Transition, withCtx as _withCtx, createBlock as _createBlock } from "vue";
function __render__(_ctx, _cache) {
  const _component_var_icon = _resolveComponent("var-icon");
  return _openBlock(), _createBlock(_Transition, {
    name: _ctx.n("$-fade")
  }, {
    default: _withCtx(() => [
      _createElementVNode(
        "span",
        _mergeProps({
          class: _ctx.classes(_ctx.n(), _ctx.n("$--box"), _ctx.formatElevation(_ctx.elevation, 1), ..._ctx.contentClass),
          style: _ctx.chipStyle
        }, _ctx.$attrs),
        [
          _renderSlot(_ctx.$slots, "left"),
          _createElementVNode(
            "span",
            {
              class: _normalizeClass(_ctx.n(`text-${_ctx.size}`))
            },
            [
              _renderSlot(_ctx.$slots, "default")
            ],
            2
            /* CLASS */
          ),
          _renderSlot(_ctx.$slots, "right"),
          _ctx.closeable ? (_openBlock(), _createElementBlock(
            "span",
            {
              key: 0,
              class: _normalizeClass(_ctx.n("--close")),
              onClick: _cache[0] || (_cache[0] = (...args) => _ctx.handleClose && _ctx.handleClose(...args))
            },
            [
              _createVNode(_component_var_icon, {
                name: `${_ctx.iconName ? _ctx.iconName : "close-circle"}`,
                namespace: _ctx.namespace
              }, null, 8, ["name", "namespace"])
            ],
            2
            /* CLASS */
          )) : _createCommentVNode("v-if", true)
        ],
        16
        /* FULL_PROPS */
      )
    ]),
    _: 3
    /* FORWARDED */
  }, 8, ["name"]);
}
const __sfc__ = defineComponent({
  name,
  components: {
    VarIcon
  },
  inheritAttrs: false,
  props,
  setup(props2) {
    const chipStyle = computed(() => {
      const { plain, textColor, color } = props2;
      if (plain) {
        return {
          color: textColor || color,
          borderColor: color
        };
      }
      return {
        color: textColor,
        background: color
      };
    });
    const contentClass = computed(() => {
      const { size, block, type, plain, round } = props2;
      const blockClass = block ? n("$--flex") : n("$--inline-flex");
      const plainTypeClass = plain ? `${n("plain")} ${n(`plain-${type}`)}` : n(`--${type}`);
      const roundClass = round ? n("--round") : null;
      return [n(`--${size}`), blockClass, plainTypeClass, roundClass];
    });
    function handleClose(e) {
      call(props2.onClose, e);
    }
    return {
      chipStyle,
      contentClass,
      n,
      classes,
      formatElevation,
      handleClose
    };
  }
});
__sfc__.render = __render__;
var stdin_default = __sfc__;
export {
  stdin_default as default
};
