import { withInstall, withPropsDefaultsSetter } from "../utils/components.mjs";
import { props as pullRefreshProps } from "./props.mjs";
import PullRefresh from "./PullRefresh.mjs";
withInstall(PullRefresh);
withPropsDefaultsSetter(PullRefresh, pullRefreshProps);
const _PullRefreshComponent = PullRefresh;
var stdin_default = PullRefresh;
export {
  _PullRefreshComponent,
  stdin_default as default,
  pullRefreshProps
};
