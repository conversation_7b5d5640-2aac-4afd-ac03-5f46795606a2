import { defineListenerProp } from "../utils/components.mjs";
const props = {
  modelValue: Bo<PERSON>an,
  disabled: Boolean,
  animationDuration: {
    type: [Number, String],
    default: 300
  },
  successDuration: {
    type: [Number, String],
    default: 2e3
  },
  bgColor: String,
  successBgColor: String,
  color: String,
  successColor: String,
  target: [String, Object],
  onRefresh: defineListenerProp(),
  "onUpdate:modelValue": defineListenerProp()
};
export {
  props
};
