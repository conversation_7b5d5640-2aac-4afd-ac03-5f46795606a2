var __defProp = Object.defineProperty;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
import { defineComponent, ref, watch } from "vue";
import { call, raf } from "@varlet/shared";
import VarMenuOption from "../menu-option/MenuOption.mjs";
import VarMenu from "../menu/Menu.mjs";
import { createNamespace, defineListenerProp, pickProps } from "../utils/components.mjs";
import { focusChildElementByKey } from "../utils/elements.mjs";
import { props as menuSelectProps } from "./props.mjs";
const { name, n } = createNamespace("menu-children");
import { resolveComponent as _resolveComponent, createVNode as _createVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, createBlock as _createBlock, createElementVNode as _createElementVNode, normalizeClass as _normalizeClass, withCtx as _withCtx, createSlots as _createSlots } from "vue";
const _hoisted_1 = { ref: "menuOptions" };
function __render__(_ctx, _cache) {
  const _component_var_menu_option = _resolveComponent("var-menu-option");
  const _component_var_menu_children = _resolveComponent("var-menu-children");
  const _component_var_menu = _resolveComponent("var-menu");
  return _openBlock(), _createBlock(_component_var_menu, {
    ref: "menu",
    show: _ctx.show,
    "onUpdate:show": _cache[1] || (_cache[1] = ($event) => _ctx.show = $event),
    trigger: "hover",
    placement: "right-start",
    "cascade-optimization": "",
    "var-menu-children-cover": "",
    class: _normalizeClass(_ctx.n()),
    disabled: _ctx.disabled,
    teleport: false,
    "close-on-click-reference": false
  }, _createSlots({
    default: _withCtx(() => [
      _createVNode(_component_var_menu_option, {
        ref: "trigger",
        "children-trigger": "",
        label: _ctx.option[_ctx.labelKey],
        value: _ctx.option[_ctx.valueKey],
        option: _ctx.option,
        ripple: _ctx.option.ripple,
        disabled: _ctx.option.disabled,
        highlight: _ctx.highlightOptions.some((_option) => _option.value === _ctx.option.value),
        onKeyArrowX: _ctx.handleArrowRight,
        onMouseenter: _ctx.handleMouseenter
      }, null, 8, ["label", "value", "option", "ripple", "disabled", "highlight", "onKeyArrowX", "onMouseenter"])
    ]),
    _: 2
    /* DYNAMIC */
  }, [
    _ctx.options.length ? {
      name: "menu",
      fn: _withCtx(() => [
        _createElementVNode(
          "div",
          _hoisted_1,
          [
            (_openBlock(true), _createElementBlock(
              _Fragment,
              null,
              _renderList(_ctx.options, (menuOption) => {
                return _openBlock(), _createElementBlock(
                  _Fragment,
                  {
                    key: menuOption[_ctx.valueKey]
                  },
                  [
                    menuOption[_ctx.childrenKey] ? (_openBlock(), _createBlock(_component_var_menu_children, {
                      key: 0,
                      ref_for: true,
                      ref: "menuChildren",
                      "parent-show": _ctx.show,
                      option: menuOption,
                      options: menuOption[_ctx.childrenKey],
                      "highlight-options": _ctx.highlightOptions,
                      disabled: menuOption.disabled,
                      onKeyArrowX: _ctx.handleArrowLeft,
                      onKeyArrowRightOpen: ($event) => _ctx.allowChildrenClose(menuOption),
                      onMouseenter: ($event) => _ctx.allowChildrenClose(menuOption)
                    }, null, 8, ["parent-show", "option", "options", "highlight-options", "disabled", "onKeyArrowX", "onKeyArrowRightOpen", "onMouseenter"])) : (_openBlock(), _createBlock(_component_var_menu_option, {
                      key: 1,
                      label: menuOption[_ctx.labelKey],
                      value: menuOption[_ctx.valueKey],
                      option: menuOption,
                      ripple: menuOption.ripple,
                      disabled: menuOption.disabled,
                      onKeyArrowX: _ctx.handleArrowLeft,
                      onMouseenter: _cache[0] || (_cache[0] = ($event) => _ctx.allowChildrenClose())
                    }, null, 8, ["label", "value", "option", "ripple", "disabled", "onKeyArrowX"]))
                  ],
                  64
                  /* STABLE_FRAGMENT */
                );
              }),
              128
              /* KEYED_FRAGMENT */
            ))
          ],
          512
          /* NEED_PATCH */
        )
      ]),
      key: "0"
    } : void 0
  ]), 1032, ["show", "class", "disabled"]);
}
const __sfc__ = defineComponent({
  name,
  components: {
    VarMenu,
    VarMenuOption
  },
  props: __spreadValues({
    parentShow: Boolean,
    disabled: Boolean,
    option: {
      type: Object,
      required: true
    },
    highlightOptions: pickProps(menuSelectProps, "options"),
    onKeyArrowX: defineListenerProp(),
    onKeyArrowRightOpen: defineListenerProp(),
    onMouseenter: defineListenerProp()
  }, pickProps(menuSelectProps, ["options", "valueKey", "labelKey", "childrenKey"])),
  setup(props) {
    const show = ref(false);
    const menu = ref();
    const trigger = ref();
    const menuOptions = ref();
    const menuChildren = ref();
    watch(
      () => props.parentShow,
      (value) => {
        if (!value) {
          show.value = false;
        }
      },
      { immediate: true }
    );
    function handleArrowRight(key) {
      return __async(this, null, function* () {
        var _a;
        call(props.onKeyArrowX, key);
        if (key !== "ArrowRight") {
          return;
        }
        call(props.onKeyArrowRightOpen);
        (_a = menu.value) == null ? void 0 : _a.open();
        yield raf();
        focusChildElementByKey(menu.value.$el, menuOptions.value, "ArrowDown");
      });
    }
    function handleArrowLeft(key) {
      var _a;
      if (key !== "ArrowLeft") {
        return;
      }
      allowClose();
      close();
      (_a = trigger.value) == null ? void 0 : _a.$el.focus();
    }
    function close() {
      var _a;
      (_a = menu.value) == null ? void 0 : _a.close();
    }
    function allowClose() {
      var _a;
      (_a = menu.value) == null ? void 0 : _a.allowClose();
    }
    function allowChildrenClose(option) {
      var _a;
      (_a = menuChildren.value) == null ? void 0 : _a.forEach((child) => {
        child.allowClose();
        if (option == null) {
          child.close();
          return;
        }
        if (child.option.value === option.value) {
          return;
        }
        child.close();
      });
    }
    function handleMouseenter() {
      call(props.onMouseenter);
    }
    return {
      show,
      menu,
      trigger,
      menuOptions,
      menuChildren,
      n,
      close,
      handleArrowLeft,
      handleArrowRight,
      handleMouseenter,
      allowClose,
      allowChildrenClose
    };
  }
});
__sfc__.render = __render__;
var stdin_default = __sfc__;
export {
  stdin_default as default
};
