import { useChildren } from "@varlet/use";
const MENU_SELECT_BIND_MENU_OPTION_KEY = Symbol("MENU_SELECT_BIND_MENU_OPTION_KEY");
function useMenuOptions() {
  const { length, childProviders, bindChildren } = useChildren(
    MENU_SELECT_BIND_MENU_OPTION_KEY
  );
  return {
    length,
    menuOptions: childProviders,
    bindMenuOptions: bindChildren
  };
}
export {
  MENU_SELECT_BIND_MENU_OPTION_KEY,
  useMenuOptions
};
