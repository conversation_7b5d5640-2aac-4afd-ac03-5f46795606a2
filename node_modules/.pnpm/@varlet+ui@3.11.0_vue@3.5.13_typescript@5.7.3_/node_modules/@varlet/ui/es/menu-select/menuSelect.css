:root { --menu-select-menu-max-height: 278px; --menu-select-menu-padding: 0; --menu-select-menu-border-radius: 2px; --menu-select-menu-background-color: var(--color-surface-container-high);}.var-menu-select__menu { padding: var(--menu-select-menu-padding); border-radius: var(--menu-select-menu-border-radius); background-color: var(--menu-select-menu-background-color);}.var-menu-select--scrollable { overflow-y: auto; max-height: var(--menu-select-menu-max-height);}.var-menu-children[var-menu-children-cover] { width: 100%; display: block;}