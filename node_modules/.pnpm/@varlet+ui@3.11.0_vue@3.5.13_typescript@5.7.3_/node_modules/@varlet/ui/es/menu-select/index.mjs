import { withInstall, withPropsDefaultsSetter } from "../utils/components.mjs";
import MenuSelect from "./MenuSelect.mjs";
import { props as menuSelectProps } from "./props.mjs";
withInstall(MenuSelect);
withPropsDefaultsSetter(MenuSelect, menuSelectProps);
const _MenuSelectComponent = MenuSelect;
var stdin_default = MenuSelect;
export {
  _MenuSelectComponent,
  stdin_default as default,
  menuSelectProps
};
