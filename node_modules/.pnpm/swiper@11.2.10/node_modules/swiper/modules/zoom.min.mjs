import{a as getWindow}from"../shared/ssr-window.esm.min.mjs";import{e as elementChildren,b as elementParents,d as elementOffset,k as getTranslate}from"../shared/utils.min.mjs";function Zoom(e){let{swiper:t,extendParams:i,on:a,emit:r}=e;const s=getWindow();i({zoom:{enabled:!1,limitToOriginalSize:!1,maxRatio:3,minRatio:1,panOnMouseMove:!1,toggle:!0,containerClass:"swiper-zoom-container",zoomedSlideClass:"swiper-slide-zoomed"}}),t.zoom={enabled:!1};let o=1,n=!1,l=!1,m={x:0,y:0};const c=-3;let d,u;const p=[],g={originX:0,originY:0,slideEl:void 0,slideWidth:void 0,slideHeight:void 0,imageEl:void 0,imageWrapEl:void 0,maxRatio:3},h={isTouched:void 0,isMoved:void 0,currentX:void 0,currentY:void 0,minX:void 0,minY:void 0,maxX:void 0,maxY:void 0,width:void 0,height:void 0,startX:void 0,startY:void 0,touchesStart:{},touchesCurrent:{}},E={x:void 0,y:void 0,prevPositionX:void 0,prevPositionY:void 0,prevTime:void 0};let v,f=1;function x(){if(p.length<2)return 1;const e=p[0].pageX,t=p[0].pageY,i=p[1].pageX,a=p[1].pageY;return Math.sqrt((i-e)**2+(a-t)**2)}function X(){const e=t.params.zoom,i=g.imageWrapEl.getAttribute("data-swiper-zoom")||e.maxRatio;if(e.limitToOriginalSize&&g.imageEl&&g.imageEl.naturalWidth){const e=g.imageEl.naturalWidth/g.imageEl.offsetWidth;return Math.min(e,i)}return i}function Y(e){const i=t.isElement?"swiper-slide":`.${t.params.slideClass}`;return!!e.target.matches(i)||t.slides.filter((t=>t.contains(e.target))).length>0}function y(e){const i=`.${t.params.zoom.containerClass}`;return!!e.target.matches(i)||[...t.hostEl.querySelectorAll(i)].filter((t=>t.contains(e.target))).length>0}function M(e){if("mouse"===e.pointerType&&p.splice(0,p.length),!Y(e))return;const i=t.params.zoom;if(d=!1,u=!1,p.push(e),!(p.length<2)){if(d=!0,g.scaleStart=x(),!g.slideEl){g.slideEl=e.target.closest(`.${t.params.slideClass}, swiper-slide`),g.slideEl||(g.slideEl=t.slides[t.activeIndex]);let a=g.slideEl.querySelector(`.${i.containerClass}`);if(a&&(a=a.querySelectorAll("picture, img, svg, canvas, .swiper-zoom-target")[0]),g.imageEl=a,g.imageWrapEl=a?elementParents(g.imageEl,`.${i.containerClass}`)[0]:void 0,!g.imageWrapEl)return void(g.imageEl=void 0);g.maxRatio=X()}if(g.imageEl){const[e,t]=function(){if(p.length<2)return{x:null,y:null};const e=g.imageEl.getBoundingClientRect();return[(p[0].pageX+(p[1].pageX-p[0].pageX)/2-e.x-s.scrollX)/o,(p[0].pageY+(p[1].pageY-p[0].pageY)/2-e.y-s.scrollY)/o]}();g.originX=e,g.originY=t,g.imageEl.style.transitionDuration="0ms"}n=!0}}function z(e){if(!Y(e))return;const i=t.params.zoom,a=t.zoom,r=p.findIndex((t=>t.pointerId===e.pointerId));r>=0&&(p[r]=e),p.length<2||(u=!0,g.scaleMove=x(),g.imageEl&&(a.scale=g.scaleMove/g.scaleStart*o,a.scale>g.maxRatio&&(a.scale=g.maxRatio-1+(a.scale-g.maxRatio+1)**.5),a.scale<i.minRatio&&(a.scale=i.minRatio+1-(i.minRatio-a.scale+1)**.5),g.imageEl.style.transform=`translate3d(0,0,0) scale(${a.scale})`))}function C(e){if(!Y(e))return;if("mouse"===e.pointerType&&"pointerout"===e.type)return;const i=t.params.zoom,a=t.zoom,r=p.findIndex((t=>t.pointerId===e.pointerId));r>=0&&p.splice(r,1),d&&u&&(d=!1,u=!1,g.imageEl&&(a.scale=Math.max(Math.min(a.scale,g.maxRatio),i.minRatio),g.imageEl.style.transitionDuration=`${t.params.speed}ms`,g.imageEl.style.transform=`translate3d(0,0,0) scale(${a.scale})`,o=a.scale,n=!1,a.scale>1&&g.slideEl?g.slideEl.classList.add(`${i.zoomedSlideClass}`):a.scale<=1&&g.slideEl&&g.slideEl.classList.remove(`${i.zoomedSlideClass}`),1===a.scale&&(g.originX=0,g.originY=0,g.slideEl=void 0)))}function W(){t.touchEventsData.preventTouchMoveFromPointerMove=!1}function w(e){const i="mouse"===e.pointerType&&t.params.zoom.panOnMouseMove;if(!Y(e)||!y(e))return;const a=t.zoom;if(!g.imageEl)return;if(!h.isTouched||!g.slideEl)return void(i&&b(e));if(i)return void b(e);h.isMoved||(h.width=g.imageEl.offsetWidth||g.imageEl.clientWidth,h.height=g.imageEl.offsetHeight||g.imageEl.clientHeight,h.startX=getTranslate(g.imageWrapEl,"x")||0,h.startY=getTranslate(g.imageWrapEl,"y")||0,g.slideWidth=g.slideEl.offsetWidth,g.slideHeight=g.slideEl.offsetHeight,g.imageWrapEl.style.transitionDuration="0ms");const r=h.width*a.scale,s=h.height*a.scale;h.minX=Math.min(g.slideWidth/2-r/2,0),h.maxX=-h.minX,h.minY=Math.min(g.slideHeight/2-s/2,0),h.maxY=-h.minY,h.touchesCurrent.x=p.length>0?p[0].pageX:e.pageX,h.touchesCurrent.y=p.length>0?p[0].pageY:e.pageY;if(Math.max(Math.abs(h.touchesCurrent.x-h.touchesStart.x),Math.abs(h.touchesCurrent.y-h.touchesStart.y))>5&&(t.allowClick=!1),!h.isMoved&&!n){if(t.isHorizontal()&&(Math.floor(h.minX)===Math.floor(h.startX)&&h.touchesCurrent.x<h.touchesStart.x||Math.floor(h.maxX)===Math.floor(h.startX)&&h.touchesCurrent.x>h.touchesStart.x))return h.isTouched=!1,void W();if(!t.isHorizontal()&&(Math.floor(h.minY)===Math.floor(h.startY)&&h.touchesCurrent.y<h.touchesStart.y||Math.floor(h.maxY)===Math.floor(h.startY)&&h.touchesCurrent.y>h.touchesStart.y))return h.isTouched=!1,void W()}e.cancelable&&e.preventDefault(),e.stopPropagation(),clearTimeout(v),t.touchEventsData.preventTouchMoveFromPointerMove=!0,v=setTimeout((()=>{t.destroyed||W()})),h.isMoved=!0;const l=(a.scale-o)/(g.maxRatio-t.params.zoom.minRatio),{originX:m,originY:c}=g;h.currentX=h.touchesCurrent.x-h.touchesStart.x+h.startX+l*(h.width-2*m),h.currentY=h.touchesCurrent.y-h.touchesStart.y+h.startY+l*(h.height-2*c),h.currentX<h.minX&&(h.currentX=h.minX+1-(h.minX-h.currentX+1)**.8),h.currentX>h.maxX&&(h.currentX=h.maxX-1+(h.currentX-h.maxX+1)**.8),h.currentY<h.minY&&(h.currentY=h.minY+1-(h.minY-h.currentY+1)**.8),h.currentY>h.maxY&&(h.currentY=h.maxY-1+(h.currentY-h.maxY+1)**.8),E.prevPositionX||(E.prevPositionX=h.touchesCurrent.x),E.prevPositionY||(E.prevPositionY=h.touchesCurrent.y),E.prevTime||(E.prevTime=Date.now()),E.x=(h.touchesCurrent.x-E.prevPositionX)/(Date.now()-E.prevTime)/2,E.y=(h.touchesCurrent.y-E.prevPositionY)/(Date.now()-E.prevTime)/2,Math.abs(h.touchesCurrent.x-E.prevPositionX)<2&&(E.x=0),Math.abs(h.touchesCurrent.y-E.prevPositionY)<2&&(E.y=0),E.prevPositionX=h.touchesCurrent.x,E.prevPositionY=h.touchesCurrent.y,E.prevTime=Date.now(),g.imageWrapEl.style.transform=`translate3d(${h.currentX}px, ${h.currentY}px,0)`}function S(){const e=t.zoom;g.slideEl&&t.activeIndex!==t.slides.indexOf(g.slideEl)&&(g.imageEl&&(g.imageEl.style.transform="translate3d(0,0,0) scale(1)"),g.imageWrapEl&&(g.imageWrapEl.style.transform="translate3d(0,0,0)"),g.slideEl.classList.remove(`${t.params.zoom.zoomedSlideClass}`),e.scale=1,o=1,g.slideEl=void 0,g.imageEl=void 0,g.imageWrapEl=void 0,g.originX=0,g.originY=0)}function b(e){if(o<=1||!g.imageWrapEl)return;if(!Y(e)||!y(e))return;const t=s.getComputedStyle(g.imageWrapEl).transform,i=new s.DOMMatrix(t);if(!l)return l=!0,m.x=e.clientX,m.y=e.clientY,h.startX=i.e,h.startY=i.f,h.width=g.imageEl.offsetWidth||g.imageEl.clientWidth,h.height=g.imageEl.offsetHeight||g.imageEl.clientHeight,g.slideWidth=g.slideEl.offsetWidth,void(g.slideHeight=g.slideEl.offsetHeight);const a=(e.clientX-m.x)*c,r=(e.clientY-m.y)*c,n=h.width*o,d=h.height*o,u=g.slideWidth,p=g.slideHeight,E=Math.min(u/2-n/2,0),v=-E,f=Math.min(p/2-d/2,0),x=-f,X=Math.max(Math.min(h.startX+a,v),E),M=Math.max(Math.min(h.startY+r,x),f);g.imageWrapEl.style.transitionDuration="0ms",g.imageWrapEl.style.transform=`translate3d(${X}px, ${M}px, 0)`,m.x=e.clientX,m.y=e.clientY,h.startX=X,h.startY=M,h.currentX=X,h.currentY=M}function $(e){const i=t.zoom,a=t.params.zoom;if(!g.slideEl){e&&e.target&&(g.slideEl=e.target.closest(`.${t.params.slideClass}, swiper-slide`)),g.slideEl||(t.params.virtual&&t.params.virtual.enabled&&t.virtual?g.slideEl=elementChildren(t.slidesEl,`.${t.params.slideActiveClass}`)[0]:g.slideEl=t.slides[t.activeIndex]);let i=g.slideEl.querySelector(`.${a.containerClass}`);i&&(i=i.querySelectorAll("picture, img, svg, canvas, .swiper-zoom-target")[0]),g.imageEl=i,g.imageWrapEl=i?elementParents(g.imageEl,`.${a.containerClass}`)[0]:void 0}if(!g.imageEl||!g.imageWrapEl)return;let r,n,l,m,c,d,u,p,E,v,f,x,Y,y,M,z,C,W;t.params.cssMode&&(t.wrapperEl.style.overflow="hidden",t.wrapperEl.style.touchAction="none"),g.slideEl.classList.add(`${a.zoomedSlideClass}`),void 0===h.touchesStart.x&&e?(r=e.pageX,n=e.pageY):(r=h.touchesStart.x,n=h.touchesStart.y);const w=o,S="number"==typeof e?e:null;1===o&&S&&(r=void 0,n=void 0,h.touchesStart.x=void 0,h.touchesStart.y=void 0);const b=X();i.scale=S||b,o=S||b,!e||1===o&&S?(u=0,p=0):(C=g.slideEl.offsetWidth,W=g.slideEl.offsetHeight,l=elementOffset(g.slideEl).left+s.scrollX,m=elementOffset(g.slideEl).top+s.scrollY,c=l+C/2-r,d=m+W/2-n,E=g.imageEl.offsetWidth||g.imageEl.clientWidth,v=g.imageEl.offsetHeight||g.imageEl.clientHeight,f=E*i.scale,x=v*i.scale,Y=Math.min(C/2-f/2,0),y=Math.min(W/2-x/2,0),M=-Y,z=-y,w>0&&S&&"number"==typeof h.currentX&&"number"==typeof h.currentY?(u=h.currentX*i.scale/w,p=h.currentY*i.scale/w):(u=c*i.scale,p=d*i.scale),u<Y&&(u=Y),u>M&&(u=M),p<y&&(p=y),p>z&&(p=z)),S&&1===i.scale&&(g.originX=0,g.originY=0),h.currentX=u,h.currentY=p,g.imageWrapEl.style.transitionDuration="300ms",g.imageWrapEl.style.transform=`translate3d(${u}px, ${p}px,0)`,g.imageEl.style.transitionDuration="300ms",g.imageEl.style.transform=`translate3d(0,0,0) scale(${i.scale})`}function T(){const e=t.zoom,i=t.params.zoom;if(!g.slideEl){t.params.virtual&&t.params.virtual.enabled&&t.virtual?g.slideEl=elementChildren(t.slidesEl,`.${t.params.slideActiveClass}`)[0]:g.slideEl=t.slides[t.activeIndex];let e=g.slideEl.querySelector(`.${i.containerClass}`);e&&(e=e.querySelectorAll("picture, img, svg, canvas, .swiper-zoom-target")[0]),g.imageEl=e,g.imageWrapEl=e?elementParents(g.imageEl,`.${i.containerClass}`)[0]:void 0}g.imageEl&&g.imageWrapEl&&(t.params.cssMode&&(t.wrapperEl.style.overflow="",t.wrapperEl.style.touchAction=""),e.scale=1,o=1,h.currentX=void 0,h.currentY=void 0,h.touchesStart.x=void 0,h.touchesStart.y=void 0,g.imageWrapEl.style.transitionDuration="300ms",g.imageWrapEl.style.transform="translate3d(0,0,0)",g.imageEl.style.transitionDuration="300ms",g.imageEl.style.transform="translate3d(0,0,0) scale(1)",g.slideEl.classList.remove(`${i.zoomedSlideClass}`),g.slideEl=void 0,g.originX=0,g.originY=0,t.params.zoom.panOnMouseMove&&(m={x:0,y:0},l&&(l=!1,h.startX=0,h.startY=0)))}function L(e){const i=t.zoom;i.scale&&1!==i.scale?T():$(e)}function P(){return{passiveListener:!!t.params.passiveListeners&&{passive:!0,capture:!1},activeListenerWithCapture:!t.params.passiveListeners||{passive:!1,capture:!0}}}function D(){const e=t.zoom;if(e.enabled)return;e.enabled=!0;const{passiveListener:i,activeListenerWithCapture:a}=P();t.wrapperEl.addEventListener("pointerdown",M,i),t.wrapperEl.addEventListener("pointermove",z,a),["pointerup","pointercancel","pointerout"].forEach((e=>{t.wrapperEl.addEventListener(e,C,i)})),t.wrapperEl.addEventListener("pointermove",w,a)}function H(){const e=t.zoom;if(!e.enabled)return;e.enabled=!1;const{passiveListener:i,activeListenerWithCapture:a}=P();t.wrapperEl.removeEventListener("pointerdown",M,i),t.wrapperEl.removeEventListener("pointermove",z,a),["pointerup","pointercancel","pointerout"].forEach((e=>{t.wrapperEl.removeEventListener(e,C,i)})),t.wrapperEl.removeEventListener("pointermove",w,a)}Object.defineProperty(t.zoom,"scale",{get:()=>f,set(e){if(f!==e){const t=g.imageEl,i=g.slideEl;r("zoomChange",e,t,i)}f=e}}),a("init",(()=>{t.params.zoom.enabled&&D()})),a("destroy",(()=>{H()})),a("touchStart",((e,i)=>{t.zoom.enabled&&function(e){const i=t.device;if(!g.imageEl)return;if(h.isTouched)return;i.android&&e.cancelable&&e.preventDefault(),h.isTouched=!0;const a=p.length>0?p[0]:e;h.touchesStart.x=a.pageX,h.touchesStart.y=a.pageY}(i)})),a("touchEnd",((e,i)=>{t.zoom.enabled&&function(){const e=t.zoom;if(p.length=0,!g.imageEl)return;if(!h.isTouched||!h.isMoved)return h.isTouched=!1,void(h.isMoved=!1);h.isTouched=!1,h.isMoved=!1;let i=300,a=300;const r=E.x*i,s=h.currentX+r,o=E.y*a,n=h.currentY+o;0!==E.x&&(i=Math.abs((s-h.currentX)/E.x)),0!==E.y&&(a=Math.abs((n-h.currentY)/E.y));const l=Math.max(i,a);h.currentX=s,h.currentY=n;const m=h.width*e.scale,c=h.height*e.scale;h.minX=Math.min(g.slideWidth/2-m/2,0),h.maxX=-h.minX,h.minY=Math.min(g.slideHeight/2-c/2,0),h.maxY=-h.minY,h.currentX=Math.max(Math.min(h.currentX,h.maxX),h.minX),h.currentY=Math.max(Math.min(h.currentY,h.maxY),h.minY),g.imageWrapEl.style.transitionDuration=`${l}ms`,g.imageWrapEl.style.transform=`translate3d(${h.currentX}px, ${h.currentY}px,0)`}()})),a("doubleTap",((e,i)=>{!t.animating&&t.params.zoom.enabled&&t.zoom.enabled&&t.params.zoom.toggle&&L(i)})),a("transitionEnd",(()=>{t.zoom.enabled&&t.params.zoom.enabled&&S()})),a("slideChange",(()=>{t.zoom.enabled&&t.params.zoom.enabled&&t.params.cssMode&&S()})),Object.assign(t.zoom,{enable:D,disable:H,in:$,out:T,toggle:L})}export{Zoom as default};
//# sourceMappingURL=zoom.min.mjs.map