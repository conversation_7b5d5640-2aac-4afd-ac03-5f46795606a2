import{c as createShadow}from"../shared/create-shadow.min.mjs";import{e as effectInit}from"../shared/effect-init.min.mjs";import{e as effectTarget}from"../shared/effect-target.min.mjs";import{e as effectVirtualTransitionEnd}from"../shared/effect-virtual-transition-end.min.mjs";import{g as getSlideTransformEl}from"../shared/utils.min.mjs";function EffectCards(e){let{swiper:t,extendParams:a,on:s}=e;a({cardsEffect:{slideShadows:!0,rotate:!0,perSlideRotate:2,perSlideOffset:8}});effectInit({effect:"cards",swiper:t,on:s,setTranslate:()=>{const{slides:e,activeIndex:a,rtlTranslate:s}=t,r=t.params.cardsEffect,{startTranslate:i,isTouched:o}=t.touchEventsData,n=s?-t.translate:t.translate;for(let l=0;l<e.length;l+=1){const d=e[l],c=d.progress,f=Math.min(Math.max(c,-4),4);let m=d.swiperSlideOffset;t.params.centeredSlides&&!t.params.cssMode&&(t.wrapperEl.style.transform=`translateX(${t.minTranslate()}px)`),t.params.centeredSlides&&t.params.cssMode&&(m-=e[0].swiperSlideOffset);let p=t.params.cssMode?-m-t.translate:-m,h=0;const M=-100*Math.abs(f);let S=1,u=-r.perSlideRotate*f,w=r.perSlideOffset-.75*Math.abs(f);const $=t.virtual&&t.params.virtual.enabled?t.virtual.from+l:l,E=($===a||$===a-1)&&f>0&&f<1&&(o||t.params.cssMode)&&n<i,T=($===a||$===a+1)&&f<0&&f>-1&&(o||t.params.cssMode)&&n>i;if(E||T){const e=(1-Math.abs((Math.abs(f)-.5)/.5))**.5;u+=-28*f*e,S+=-.5*e,w+=96*e,h=-25*e*Math.abs(f)+"%"}if(p=f<0?`calc(${p}px ${s?"-":"+"} (${w*Math.abs(f)}%))`:f>0?`calc(${p}px ${s?"-":"+"} (-${w*Math.abs(f)}%))`:`${p}px`,!t.isHorizontal()){const e=h;h=p,p=e}const g=f<0?""+(1+(1-S)*f):""+(1-(1-S)*f),x=`\n        translate3d(${p}, ${h}, ${M}px)\n        rotateZ(${r.rotate?s?-u:u:0}deg)\n        scale(${g})\n      `;if(r.slideShadows){let e=d.querySelector(".swiper-slide-shadow");e||(e=createShadow("cards",d)),e&&(e.style.opacity=Math.min(Math.max((Math.abs(f)-.5)/.5,0),1))}d.style.zIndex=-Math.abs(Math.round(c))+e.length;effectTarget(r,d).style.transform=x}},setTransition:e=>{const a=t.slides.map((e=>getSlideTransformEl(e)));a.forEach((t=>{t.style.transitionDuration=`${e}ms`,t.querySelectorAll(".swiper-slide-shadow").forEach((t=>{t.style.transitionDuration=`${e}ms`}))})),effectVirtualTransitionEnd({swiper:t,duration:e,transformElements:a})},perspective:()=>!0,overwriteParams:()=>({_loopSwapReset:!1,watchSlidesProgress:!0,loopAdditionalSlides:t.params.cardsEffect.rotate?3:2,centeredSlides:!0,virtualTranslate:!t.params.cssMode})})}export{EffectCards as default};
//# sourceMappingURL=effect-cards.min.mjs.map