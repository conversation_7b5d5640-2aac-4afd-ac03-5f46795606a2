import{s as setInnerHTML}from"../shared/utils.min.mjs";function appendSlide(e){const l=this,{params:o,slidesEl:t}=l;o.loop&&l.loopDestroy();const i=e=>{if("string"==typeof e){const l=document.createElement("div");setInnerHTML(l,e),t.append(l.children[0]),setInnerHTML(l,"")}else t.append(e)};if("object"==typeof e&&"length"in e)for(let l=0;l<e.length;l+=1)e[l]&&i(e[l]);else i(e);l.recalcSlides(),o.loop&&l.loopCreate(),o.observer&&!l.isElement||l.update()}function prependSlide(e){const l=this,{params:o,activeIndex:t,slidesEl:i}=l;o.loop&&l.loopDestroy();let n=t+1;const s=e=>{if("string"==typeof e){const l=document.createElement("div");setInnerHTML(l,e),i.prepend(l.children[0]),setInnerHTML(l,"")}else i.prepend(e)};if("object"==typeof e&&"length"in e){for(let l=0;l<e.length;l+=1)e[l]&&s(e[l]);n=t+e.length}else s(e);l.recalcSlides(),o.loop&&l.loopCreate(),o.observer&&!l.isElement||l.update(),l.slideTo(n,0,!1)}function addSlide(e,l){const o=this,{params:t,activeIndex:i,slidesEl:n}=o;let s=i;t.loop&&(s-=o.loopedSlides,o.loopDestroy(),o.recalcSlides());const d=o.slides.length;if(e<=0)return void o.prependSlide(l);if(e>=d)return void o.appendSlide(l);let p=s>e?s+1:s;const r=[];for(let l=d-1;l>=e;l-=1){const e=o.slides[l];e.remove(),r.unshift(e)}if("object"==typeof l&&"length"in l){for(let e=0;e<l.length;e+=1)l[e]&&n.append(l[e]);p=s>e?s+l.length:s}else n.append(l);for(let e=0;e<r.length;e+=1)n.append(r[e]);o.recalcSlides(),t.loop&&o.loopCreate(),t.observer&&!o.isElement||o.update(),t.loop?o.slideTo(p+o.loopedSlides,0,!1):o.slideTo(p,0,!1)}function removeSlide(e){const l=this,{params:o,activeIndex:t}=l;let i=t;o.loop&&(i-=l.loopedSlides,l.loopDestroy());let n,s=i;if("object"==typeof e&&"length"in e){for(let o=0;o<e.length;o+=1)n=e[o],l.slides[n]&&l.slides[n].remove(),n<s&&(s-=1);s=Math.max(s,0)}else n=e,l.slides[n]&&l.slides[n].remove(),n<s&&(s-=1),s=Math.max(s,0);l.recalcSlides(),o.loop&&l.loopCreate(),o.observer&&!l.isElement||l.update(),o.loop?l.slideTo(s+l.loopedSlides,0,!1):l.slideTo(s,0,!1)}function removeAllSlides(){const e=this,l=[];for(let o=0;o<e.slides.length;o+=1)l.push(o);e.removeSlide(l)}function Manipulation(e){let{swiper:l}=e;Object.assign(l,{appendSlide:appendSlide.bind(l),prependSlide:prependSlide.bind(l),addSlide:addSlide.bind(l),removeSlide:removeSlide.bind(l),removeAllSlides:removeAllSlides.bind(l)})}export{Manipulation as default};
//# sourceMappingURL=manipulation.min.mjs.map