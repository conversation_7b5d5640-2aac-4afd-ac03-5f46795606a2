{"name": "swiper", "version": "11.2.10", "description": "Most modern mobile touch slider and framework with hardware accelerated transitions", "typings": "swiper.d.ts", "type": "module", "main": "./swiper.mjs", "module": "./swiper.mjs", "exports": {".": {"types": "./swiper.d.ts", "default": "./swiper.mjs"}, "./effect-utils": {"types": "./swiper-effect-utils.d.ts", "default": "./swiper-effect-utils.mjs"}, "./core": {"types": "./swiper.d.ts", "default": "./swiper.mjs"}, "./bundle": {"types": "./swiper.d.ts", "default": "./swiper-bundle.mjs"}, "./css": "./swiper.css", "./css/bundle": "./swiper-bundle.css", "./swiper-bundle.css": "./swiper-bundle.css", "./css/a11y": "./modules/a11y.css", "./css/autoplay": "./modules/autoplay.css", "./css/controller": "./modules/controller.css", "./css/effect-coverflow": "./modules/effect-coverflow.css", "./css/effect-cube": "./modules/effect-cube.css", "./css/effect-fade": "./modules/effect-fade.css", "./css/effect-flip": "./modules/effect-flip.css", "./css/effect-creative": "./modules/effect-creative.css", "./css/effect-cards": "./modules/effect-cards.css", "./css/free-mode": "./modules/free-mode.css", "./css/grid": "./modules/grid.css", "./css/hash-navigation": "./modules/hash-navigation.css", "./css/history": "./modules/history.css", "./css/keyboard": "./modules/keyboard.css", "./css/manipulation": "./modules/manipulation.css", "./css/mousewheel": "./modules/mousewheel.css", "./css/navigation": "./modules/navigation.css", "./css/pagination": "./modules/pagination.css", "./css/parallax": "./modules/parallax.css", "./css/scrollbar": "./modules/scrollbar.css", "./css/thumbs": "./modules/thumbs.css", "./css/virtual": "./modules/virtual.css", "./css/zoom": "./modules/zoom.css", "./less": "./swiper.less", "./less/a11y": "./modules/a11y.less", "./less/autoplay": "./modules/autoplay.less", "./less/controller": "./modules/controller.less", "./less/effect-coverflow": "./modules/effect-coverflow.less", "./less/effect-cube": "./modules/effect-cube.less", "./less/effect-fade": "./modules/effect-fade.less", "./less/effect-flip": "./modules/effect-flip.less", "./less/effect-creative": "./modules/effect-creative.less", "./less/effect-cards": "./modules/effect-cards.less", "./less/free-mode": "./modules/free-mode.less", "./less/grid": "./modules/grid.less", "./less/hash-navigation": "./modules/hash-navigation.less", "./less/history": "./modules/history.less", "./less/keyboard": "./modules/keyboard.less", "./less/manipulation": "./modules/manipulation.less", "./less/mousewheel": "./modules/mousewheel.less", "./less/navigation": "./modules/navigation.less", "./less/pagination": "./modules/pagination.less", "./less/parallax": "./modules/parallax.less", "./less/scrollbar": "./modules/scrollbar.less", "./less/thumbs": "./modules/thumbs.less", "./less/virtual": "./modules/virtual.less", "./less/zoom": "./modules/zoom.less", "./scss": "./swiper.scss", "./scss/a11y": "./modules/a11y.scss", "./scss/autoplay": "./modules/autoplay.scss", "./scss/controller": "./modules/controller.scss", "./scss/effect-coverflow": "./modules/effect-coverflow.scss", "./scss/effect-cube": "./modules/effect-cube.scss", "./scss/effect-fade": "./modules/effect-fade.scss", "./scss/effect-flip": "./modules/effect-flip.scss", "./scss/effect-creative": "./modules/effect-creative.scss", "./scss/effect-cards": "./modules/effect-cards.scss", "./scss/free-mode": "./modules/free-mode.scss", "./scss/grid": "./modules/grid.scss", "./scss/hash-navigation": "./modules/hash-navigation.scss", "./scss/history": "./modules/history.scss", "./scss/keyboard": "./modules/keyboard.scss", "./scss/manipulation": "./modules/manipulation.scss", "./scss/mousewheel": "./modules/mousewheel.scss", "./scss/navigation": "./modules/navigation.scss", "./scss/pagination": "./modules/pagination.scss", "./scss/parallax": "./modules/parallax.scss", "./scss/scrollbar": "./modules/scrollbar.scss", "./scss/thumbs": "./modules/thumbs.scss", "./scss/vars": "./swiper-vars.scss", "./scss/virtual": "./modules/virtual.scss", "./scss/zoom": "./modules/zoom.scss", "./element": {"types": "./swiper-element.d.ts", "default": "./swiper-element.mjs"}, "./element/bundle": {"types": "./swiper-element.d.ts", "default": "./swiper-element-bundle.mjs"}, "./element-bundle": {"types": "./swiper-element.d.ts", "default": "./swiper-element-bundle.mjs"}, "./element/css/a11y": "./modules/a11y-element.css", "./element/css/autoplay": "./modules/autoplay-element.css", "./element/css/controller": "./modules/controller-element.css", "./element/css/effect-coverflow": "./modules/effect-coverflow-element.css", "./element/css/effect-cube": "./modules/effect-cube-element.css", "./element/css/effect-fade": "./modules/effect-fade-element.css", "./element/css/effect-flip": "./modules/effect-flip-element.css", "./element/css/effect-creative": "./modules/effect-creative-element.css", "./element/css/effect-cards": "./modules/effect-cards-element.css", "./element/css/free-mode": "./modules/free-mode-element.css", "./element/css/grid": "./modules/grid-element.css", "./element/css/hash-navigation": "./modules/hash-navigation-element.css", "./element/css/history": "./modules/history-element.css", "./element/css/keyboard": "./modules/keyboard-element.css", "./element/css/manipulation": "./modules/manipulation-element.css", "./element/css/mousewheel": "./modules/mousewheel-element.css", "./element/css/navigation": "./modules/navigation-element.css", "./element/css/pagination": "./modules/pagination-element.css", "./element/css/parallax": "./modules/parallax-element.css", "./element/css/scrollbar": "./modules/scrollbar-element.css", "./element/css/thumbs": "./modules/thumbs-element.css", "./element/css/virtual": "./modules/virtual-element.css", "./element/css/zoom": "./modules/zoom-element.css", "./react": {"types": "./swiper-react.d.ts", "default": "./swiper-react.mjs"}, "./vue": {"types": "./swiper-vue.d.ts", "default": "./swiper-vue.mjs"}, "./modules": {"types": "./types/modules/index.d.ts", "default": "./modules/index.mjs"}, "./types": "./types/index.d.ts", "./package.json": "./package.json"}, "typesVersions": {"*": {"modules": ["./types/modules/index.d.ts"], "element": ["./swiper-element.d.ts"], "element/bundle": ["./swiper-element.d.ts"], "react": ["./swiper-react.d.ts"], "vue": ["./swiper-vue.d.ts"]}}, "repository": {"type": "git", "url": "https://github.com/nolimits4web/Swiper.git"}, "keywords": ["swiper", "swipe", "slider", "touch", "ios", "mobile", "<PERSON><PERSON>", "phonegap", "app", "framework", "framework7", "carousel", "gallery", "plugin", "react", "vue", "slideshow"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/nolimits4web/swiper/issues"}, "homepage": "https://swiperjs.com", "funding": [{"type": "patreon", "url": "https://www.patreon.com/swiperjs"}, {"type": "open_collective", "url": "http://opencollective.com/swiper"}], "engines": {"node": ">= 4.7.0"}, "releaseDate": "June 28, 2025"}