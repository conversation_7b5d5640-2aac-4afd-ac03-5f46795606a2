{"version": 3, "file": "get-element-params.mjs.mjs", "names": ["extend", "isObject", "attrToProp", "paramsList", "defaults", "formatValue", "val", "parseFloat", "Number", "includes", "v", "JSON", "parse", "err", "modulesParamsList", "getParams", "element", "propName", "propValue", "params", "passedParams", "localParamsList", "allowedParams", "map", "key", "replace", "for<PERSON>ach", "paramName", "attrsList", "attributes", "push", "name", "value", "attr", "moduleParam", "find", "mParam", "startsWith", "parentObjName", "subObjName", "split", "enabled", "constructor", "Object", "navigation", "prevEl", "nextEl", "scrollbar", "el", "pagination"], "sources": ["0"], "mappings": "YAAcA,YAAaC,cAAeC,gBAAiBC,eAAkB,sCAC/DC,aAAgB,wBAE9B,MAAMC,YAAcC,IAClB,GAAIC,WAAWD,KAASE,OAAOF,GAAM,OAAOE,OAAOF,GACnD,GAAY,SAARA,EAAgB,OAAO,EAC3B,GAAY,KAARA,EAAY,OAAO,EACvB,GAAY,UAARA,EAAiB,OAAO,EAC5B,GAAY,SAARA,EAAgB,OAAO,KAC3B,GAAY,cAARA,EAAJ,CACA,GAAmB,iBAARA,GAAoBA,EAAIG,SAAS,MAAQH,EAAIG,SAAS,MAAQH,EAAIG,SAAS,KAAM,CAC1F,IAAIC,EACJ,IACEA,EAAIC,KAAKC,MAAMN,EACjB,CAAE,MAAOO,GACPH,EAAIJ,CACN,CACA,OAAOI,CACT,CACA,OAAOJ,CAVkC,CAU/B,EAENQ,kBAAoB,CAAC,OAAQ,WAAY,aAAc,eAAgB,mBAAoB,kBAAmB,cAAe,cAAe,cAAe,YAAa,OAAQ,kBAAmB,UAAW,WAAY,aAAc,aAAc,aAAc,WAAY,YAAa,SAAU,UAAW,QACxT,SAASC,UAAUC,EAASC,EAAUC,GACpC,MAAMC,EAAS,CAAC,EACVC,EAAe,CAAC,EACtBpB,OAAOmB,EAAQf,UACf,MAAMiB,EAAkB,IAAIlB,WAAY,MAClCmB,EAAgBD,EAAgBE,KAAIC,GAAOA,EAAIC,QAAQ,IAAK,MAGlEJ,EAAgBK,SAAQC,IACtBA,EAAYA,EAAUF,QAAQ,IAAK,SACD,IAAvBT,EAAQW,KACjBP,EAAaO,GAAaX,EAAQW,GACpC,IAIF,MAAMC,EAAY,IAAIZ,EAAQa,YAoE9B,MAnEwB,iBAAbZ,QAA8C,IAAdC,GACzCU,EAAUE,KAAK,CACbC,KAAMd,EACNe,MAAO/B,SAASiB,GAAa,IACxBA,GACDA,IAGRU,EAAUF,SAAQO,IAChB,MAAMC,EAAcpB,kBAAkBqB,MAAKC,GAAUH,EAAKF,KAAKM,WAAW,GAAGD,QAC7E,GAAIF,EAAa,CACf,MAAMI,EAAgBpC,WAAWgC,GAC3BK,EAAarC,WAAW+B,EAAKF,KAAKS,MAAM,GAAGN,MAAgB,SACtB,IAAhCd,EAAakB,KACtBlB,EAAakB,GAAiB,CAAC,IAEG,IAAhClB,EAAakB,KACflB,EAAakB,GAAiB,CAC5BG,SAAS,KAGuB,IAAhCrB,EAAakB,KACflB,EAAakB,GAAiB,CAC5BG,SAAS,IAGbrB,EAAakB,GAAeC,GAAclC,YAAY4B,EAAKD,MAC7D,KAAO,CACL,MAAMD,EAAO7B,WAAW+B,EAAKF,MAC7B,IAAKT,EAAcb,SAASsB,GAAO,OACnC,MAAMC,EAAQ3B,YAAY4B,EAAKD,OAC3BZ,EAAaW,IAASjB,kBAAkBL,SAASwB,EAAKF,QAAU9B,SAAS+B,IACvEZ,EAAaW,GAAMW,cAAgBC,SACrCvB,EAAaW,GAAQ,CAAC,GAExBX,EAAaW,GAAMU,UAAYT,GAE/BZ,EAAaW,GAAQC,CAEzB,KAEFhC,OAAOmB,EAAQC,GACXD,EAAOyB,WACTzB,EAAOyB,WAAa,CAClBC,OAAQ,sBACRC,OAAQ,0BACkB,IAAtB3B,EAAOyB,WAAsBzB,EAAOyB,WAAa,CAAC,IAEzB,IAAtBzB,EAAOyB,mBACTzB,EAAOyB,WAEZzB,EAAO4B,UACT5B,EAAO4B,UAAY,CACjBC,GAAI,wBACqB,IAArB7B,EAAO4B,UAAqB5B,EAAO4B,UAAY,CAAC,IAExB,IAArB5B,EAAO4B,kBACT5B,EAAO4B,UAEZ5B,EAAO8B,WACT9B,EAAO8B,WAAa,CAClBD,GAAI,yBACsB,IAAtB7B,EAAO8B,WAAsB9B,EAAO8B,WAAa,CAAC,IAEzB,IAAtB9B,EAAO8B,mBACT9B,EAAO8B,WAET,CACL9B,SACAC,eAEJ,QAESL"}