{"name": "rattail", "version": "1.0.17", "type": "module", "main": "lib/index.cjs", "module": "lib/index.js", "unpkg": "lib/index.global.js", "jsdelivr": "lib/index.global.js", "types": "lib/index.d.ts", "sideEffects": false, "exports": {".": {"import": "./lib/index.js", "require": "./lib/index.cjs"}}, "description": "A utilities library for front-end developers, lightweight and ts-friendly", "keywords": ["utilities library", "front-end developers", "lightweight", "ts-friendly"], "author": "haoziqaq <<EMAIL>>", "license": "MIT", "files": ["lib"], "repository": {"type": "git", "url": "git+https://github.com/varletjs/rattail.git"}, "bugs": {"url": "https://github.com/varletjs/rattail/issues"}, "simple-git-hooks": {"pre-commit": "pnpm exec nano-staged --allow-empty", "commit-msg": "pnpm exec vr commit-lint -p $1"}, "nano-staged": {"*.{md}": "prettier --write", "*.{ts}": ["prettier --write", "eslint --fix"]}, "devDependencies": {"@types/node": "^22.8.1", "@varlet/eslint-config": "^3.6.5", "@varlet/release": "^0.3.1", "@vitest/coverage-istanbul": "^2.1.3", "eslint": "^8.53.0", "jsdom": "^25.0.1", "nano-staged": "0.8.0", "prettier": "^3.1.0", "rimraf": "^6.0.1", "simple-git-hooks": "^2.11.1", "tsup": "8.3.5", "typescript": "5.3.3", "vitepress": "^1.4.1", "vitest": "^2.1.3"}, "engines": {"pnpm": ">=9.0"}, "dependencies": {"mitt": "^3.0.1"}, "scripts": {"dev": "vitest --coverage", "build": "tsup src/index.ts --format esm,cjs,iife --out-dir=lib --global-name=Rattail --dts --clean", "lint": "eslint . --fix --ext .ts,.js", "format": "prettier --write .", "clean": "rimraf node_modules lib", "test": "vitest run --coverage", "release": "pnpm build && vr release", "docs:dev": "vitepress dev docs", "docs:build": "vitepress build docs", "docs:preview": "vitepress preview docs"}}