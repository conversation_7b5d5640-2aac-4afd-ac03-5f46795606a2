export * from 'mitt';
export { default as mitt } from 'mitt';

declare function at<T>(arr: T[], index: number): T | undefined;

declare function chunk<T>(arr: T[], size?: number): T[][];

declare function removeItem<T>(arr: T[], item: T): T[] | undefined;

declare function toggleItem<T>(arr: T[], item: T): T[];

declare function uniq<T>(arr: T[]): T[];

declare function uniqBy<T>(arr: T[], fn: (a: T, b: T) => any): T[];

declare function find<T>(arr: T[], fn: (item: T, index: number, array: T[]) => any, from?: 'start' | 'end'): [T, number] | [null, -1];

declare function shuffle<T>(arr: T[]): T[];

declare function removeArrayBlank<T>(arr: (T | null | undefined)[]): T[];

declare function removeArrayEmpty<T>(arr: (T | null | undefined | '')[]): T[];

declare function normalizeToArray<T>(value: T | T[]): T[];

declare function difference<T>(arr: T[], ...values: T[][]): T[];

type Fn$4<T> = (a: T, b: T) => any;
declare function differenceWith<T>(arr: T[], ...values: [...T[][], fn: Fn$4<T>]): T[];

declare function intersection<T>(...values: T[][]): T[];

type Fn$3<T> = (a: T, b: T) => any;
declare function intersectionWith<T>(...values: [...T[][], fn: Fn$3<T>]): T[];

type Fn$2<T, K> = (val: T) => K;
declare function groupBy<T, K extends PropertyKey>(arr: T[], fn: Fn$2<T, K>): Record<K, T[]>;

declare function xor<T>(...values: T[][]): T[];

type Fn$1<T> = (a: T, b: T) => any;
declare function xorWith<T>(...values: [...T[][], fn: Fn$1<T>]): T[];

declare function pick<T, K extends keyof T>(object: T, keys: K[]): Pick<T, K>;

declare function pickBy<T extends object>(object: T, fn: (value: any, key: keyof T) => any): Partial<T>;

declare function omit<T extends object, K extends keyof T>(object: T, keys: K[]): Omit<T, K>;

declare function omitBy<T extends object>(object: T, fn: (value: any, key: keyof T) => any): Partial<T>;

declare function mapObject<K extends PropertyKey, V, NK extends PropertyKey = K, NV = V>(object: Record<K, V>, fn: (key: K, value: V) => [PropertyKey, NV] | undefined): Record<NK, NV>;

type PromiseWithResolvers<T> = {
    promise: Promise<T>;
    resolve: (value: T | PromiseLike<T>) => void;
    reject: (reason?: any) => void;
};
declare function promiseWithResolvers<T>(): PromiseWithResolvers<T>;

declare function cancelAnimationFrame(handle: number): void;

type ClassName = string | undefined | null;
type Classes = (ClassName | [any, ClassName, ClassName?])[];
declare function classes(...classes: Classes): any[];

declare function copyText(value: string): void;

type BEM<S extends string | undefined, N extends string, NC extends string> = S extends undefined ? NC : S extends `$--${infer CM}` ? `${N}--${CM}` : S extends `--${infer M}` ? `${NC}--${M}` : `${NC}__${S}`;
declare function createNamespaceFn<N extends string>(namespace: N): <C extends string>(name: C) => {
    name: string;
    n: <S extends string | undefined = undefined>(suffix?: S | undefined) => BEM<S, N, `${N}-${C}`>;
    classes: typeof classes;
};

declare function doubleRaf(): Promise<unknown>;

declare function getAllParentScroller(el: HTMLElement): (HTMLElement | Window)[];

declare function getParentScroller(el: HTMLElement): HTMLElement | Window;

declare function getRect(element: Element | Window): DOMRect;

declare function getScrollLeft(element: Element | Window): number;

declare function getScrollTop(element: Element | Window): number;

declare function getStyle(element: Element): CSSStyleDeclaration;

declare function inViewport(element: HTMLElement): boolean;

declare function prettyJSONObject(jsonObject: Record<string, any>): string;

declare function preventDefault(event: Event): void;

declare function raf(): Promise<unknown>;

declare function requestAnimationFrame(fn: FrameRequestCallback): number;

interface Storage extends globalThis.Storage {
    set(key: string, value: any): void;
    get(key: string): any;
    remove(key: string): void;
}
declare function createStorage(storage: globalThis.Storage): Storage;
declare const sessionStorage: Storage;
declare const localStorage: Storage;

declare function tryParseJSON<T>(json: string): T | undefined;

declare function download(val: string | Blob | File, filename?: string): void;

interface MotionOptions {
    from: number;
    to: number;
    duration?: number;
    frame?: ({ value, done }: {
        value: number;
        done: boolean;
    }) => void;
    timingFunction?: (v: number) => number;
    onStateChange?: (state: MotionState) => void;
}
type MotionState = 'running' | 'paused' | 'pending' | 'finished';
interface Motion {
    start: () => void;
    pause: () => void;
    reset: () => void;
    getState: () => MotionState;
}
declare function motion(options: MotionOptions): Motion;

declare function call<P extends any[], R>(fn?: ((...arg: P) => R) | ((...arg: P) => R)[] | null, ...args: P): R | R[] | undefined;

declare function once<F extends (...args: any[]) => any>(fn: F): (this: unknown, ...args: Parameters<F>) => ReturnType<F>;

declare function debounce<F extends (...args: any[]) => any>(fn: F, delay?: number): (this: unknown, ...args: Parameters<F>) => void;

declare function throttle<F extends (...args: any[]) => any>(fn: F, delay?: number): (this: unknown, ...args: Parameters<F>) => void;

declare function NOOP(): void;

declare function getGlobalThis(): typeof globalThis;

declare function hasOwn<T extends object>(val: T, key: PropertyKey): key is keyof T;

declare function inBrowser(): boolean;

declare function inMobile(): boolean;

declare function isArray(val: unknown): val is any[];

declare function isArrayBuffer(val: unknown): val is ArrayBuffer;

declare function isBoolean(val: unknown): val is boolean;

declare function isDataView(val: unknown): val is DataView;

declare function isDate(val: unknown): val is Date;

declare function isDOMException(val: unknown): val is DOMException;

declare function isEmpty(val: unknown): boolean;

declare function isEqual(value: any, other: any): boolean;

declare function isEqualWith(value: any, other: any, fn: (value: any, other: any) => any): boolean;

declare function isError(val: unknown): val is Error;

declare function isFunction(val: unknown): val is Function;

declare function isMap(val: unknown): val is Map<any, any>;

declare function isNonEmptyArray(val: unknown): val is any[];

declare function isNullish<T>(val: T | null | undefined): val is NonNullable<T>;

declare function isNumber(val: unknown): val is number;

declare function isNumeric(val: unknown): val is number | string;

declare function isObject(val: unknown): val is Record<string, any>;

declare function isPlainObject(val: unknown): val is Record<string, any>;

declare function isPromise<T = any>(val: unknown): val is Promise<T>;

declare function isRegExp(val: unknown): val is RegExp;

declare function isSet(val: unknown): val is Set<any>;

declare function isString(val: unknown): val is string;

declare function isSymbol(val: unknown): val is symbol;

declare function isTruthy<T>(v: T): v is NonNullable<T>;

declare function isTypedArray(val: unknown): val is Int8Array | Uint8Array | Uint8ClampedArray | Int16Array | Uint16Array | Int32Array | Uint32Array | Float32Array | Float64Array | BigInt64Array | BigUint64Array;

declare function isWeakMap(val: unknown): val is WeakMap<any, any>;

declare function isWeakSet(val: unknown): val is WeakSet<any>;

declare function isWindow(val: unknown): val is Window;

declare function supportTouch(): boolean;

declare function toRawType(value: unknown): string;

declare const objectToString: typeof Object.prototype.toString;
declare function toTypeString(value: unknown): string;

declare function isFile(val: unknown): val is File;

declare function isBlob(val: unknown): val is Blob;

declare function isPrimitive(val: unknown): boolean;

declare function isEmptyPlainObject(val: unknown): val is Record<string, any>;

declare function assert(condition: boolean, message: string): asserts condition;

declare function clamp(num: number, min: number, max: number): number;

declare function clampArrayRange(index: number, arr: unknown[]): number;

declare function delay(time: number): Promise<void>;

declare function randomNumber(min?: number, max?: number): number;

declare function times<T>(num: number, fn: (index: number) => T): T[];

declare function toNumber(val: number | string | boolean | undefined | null): number;

declare function genNumberKey(): number;

declare function camelize(s: string): string;

declare function ensurePrefix(s: string, prefix: string): string;

declare function ensureSuffix(s: string, suffix: string): string;

declare function genStringKey(): string;

declare function kebabCase(s: string): string;

declare function pascalCase(s: string): string;

declare function lowerFirst(s: string): string;

declare function upperFirst(s: string): string;

declare function randomColor(): string;

declare function randomString(length?: number): string;

declare function slash(path: string): string;

declare function cloneDeep<T>(value: T): T;

declare function cloneDeepWith<T>(value: T, fn: (value: any) => any): T;

declare function merge<T extends Record<string, any>, K extends Record<string, any>>(object: T, ...sources: K[]): T & K;

type Fn = (objValue: any, srcValue: any, key: string | number | symbol, object?: any, source?: any) => any;
declare function mergeWith<T extends Record<string, any>, K extends Record<string, any>>(object: T, ...sources: [...K[], fn: Fn]): T & K;

declare function toArrayBuffer(file: File): Promise<ArrayBuffer>;

declare function toDataURL(file: File): Promise<string>;

declare function toText(file: File): Promise<string>;

declare function maxBy<T>(arr: T[], fn: (val: T) => number): T | undefined;

declare function minBy<T>(arr: T[], fn: (val: T) => number): T | undefined;

declare function sum(arr: number[]): number;

declare function sumBy<T>(arr: T[], fn: (val: T) => number): number;

declare function sumHash(value: any): string;

declare function mean(arr: number[]): number;

declare function meanBy<T>(arr: T[], fn: (val: T) => number): number;

declare function sample<T>(arr: T[]): T | undefined;

declare function round(val: number, precision?: number): number;
declare function baseRound(val: number, precision: number | undefined, fn: (val: number) => number): number;

declare function floor(val: number, precision?: number): number;

declare function ceil(val: number, precision?: number): number;

export { type BEM, type ClassName, type Classes, type Motion, type MotionOptions, type MotionState, NOOP, type PromiseWithResolvers, type Storage, assert, at, baseRound, call, camelize, cancelAnimationFrame, ceil, chunk, clamp, clampArrayRange, classes, cloneDeep, cloneDeepWith, copyText, createNamespaceFn, createStorage, debounce, delay, difference, differenceWith, doubleRaf, download, ensurePrefix, ensureSuffix, find, floor, genNumberKey, genStringKey, getAllParentScroller, getGlobalThis, getParentScroller, getRect, getScrollLeft, getScrollTop, getStyle, groupBy, hasOwn, inBrowser, inMobile, inViewport, intersection, intersectionWith, isArray, isArrayBuffer, isBlob, isBoolean, isDOMException, isDataView, isDate, isEmpty, isEmptyPlainObject, isEqual, isEqualWith, isError, isFile, isFunction, isMap, isNonEmptyArray, isNullish, isNumber, isNumeric, isObject, isPlainObject, isPrimitive, isPromise, isRegExp, isSet, isString, isSymbol, isTruthy, isTypedArray, isWeakMap, isWeakSet, isWindow, kebabCase, localStorage, lowerFirst, mapObject, maxBy, mean, meanBy, merge, mergeWith, minBy, motion, normalizeToArray, objectToString, omit, omitBy, once, pascalCase, pick, pickBy, prettyJSONObject, preventDefault, promiseWithResolvers, raf, randomColor, randomNumber, randomString, removeArrayBlank, removeArrayEmpty, removeItem, requestAnimationFrame, round, sample, sessionStorage, shuffle, slash, sum, sumBy, sumHash, supportTouch, throttle, times, toArrayBuffer, toDataURL, toNumber, toRawType, toText, toTypeString, toggleItem, tryParseJSON, uniq, uniqBy, upperFirst, xor, xorWith };
