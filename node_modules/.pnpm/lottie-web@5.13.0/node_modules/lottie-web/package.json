{"name": "lottie-web", "version": "5.13.0", "description": "After Effects plugin for exporting animations to SVG + JavaScript or canvas + JavaScript", "main": "./build/player/lottie.js", "repository": {"type": "git", "url": "https://github.com/airbnb/lottie-web.git"}, "sideEffects": false, "scripts": {"build": "npx eslint ./player/js/**/* && rollup -c && node tasks/build_worker", "build_test": "node tasks/build.js", "lint": "npx eslint ./player/js/**/*", "lint:fix": "npx eslint ./player/js/**/* --fix", "test:create": "node test/index.js", "test:compare": "node test/index.js -s compare"}, "keywords": ["animation", "canvas", "svg", "after effects", "plugin", "export"], "devDependencies": {"@babel/core": "^7.16.0", "@babel/plugin-transform-runtime": "^7.17.0", "@babel/preset-env": "^7.16.4", "@rollup/plugin-babel": "^5.3.0", "@rollup/plugin-node-resolve": "^13.0.6", "cheerio": "^1.0.0-rc.2", "command-line-args": "^5.2.1", "eslint": "^7.16.0", "eslint-config-airbnb-base": "^14.2.1", "eslint-plugin-import": "^2.22.1", "express": "^4.18.2", "pixelmatch": "^5.3.0", "pngjs": "^7.0.0", "puppeteer": "^20.5.0", "rollup": "^2.61.0", "rollup-plugin-terser": "^7.0.2", "uglify-js": "^3.4.9", "watch": "^1.0.2"}, "types": "./index.d.ts", "license": "MIT"}