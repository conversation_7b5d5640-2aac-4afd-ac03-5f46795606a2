{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"mn": {"title": "Match Name", "description": "After Effect's Match Name. Used for expressions.", "type": "string"}, "nm": {"title": "Name", "description": "After Effect's Name. Used for expressions.", "type": "string"}, "ty": {"title": "Type", "description": "Shape content type. THIS FEATURE IS NOT SUPPORTED. It's exported because if you export it, they will come.", "type": "string", "const": "mm"}, "mm": {"title": "Merge <PERSON>", "description": "Merge <PERSON>", "type": "number"}}}