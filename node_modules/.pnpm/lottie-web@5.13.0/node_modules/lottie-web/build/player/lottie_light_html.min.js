"undefined"!=typeof document&&"undefined"!=typeof navigator&&function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t="undefined"!=typeof globalThis?globalThis:t||self).lottie=e()}(this,(function(){"use strict";var t="",e=!1,i=-999999,s=function(){return t};function a(t){return document.createElement(t)}function r(t,e){var i,s,a=t.length;for(i=0;i<a;i+=1)for(var r in s=t[i].prototype)Object.prototype.hasOwnProperty.call(s,r)&&(e.prototype[r]=s[r])}function n(t){function e(){}return e.prototype=t,e}var h=function(){function t(t){this.audios=[],this.audioFactory=t,this._volume=1,this._isMuted=!1}return t.prototype={addAudio:function(t){this.audios.push(t)},pause:function(){var t,e=this.audios.length;for(t=0;t<e;t+=1)this.audios[t].pause()},resume:function(){var t,e=this.audios.length;for(t=0;t<e;t+=1)this.audios[t].resume()},setRate:function(t){var e,i=this.audios.length;for(e=0;e<i;e+=1)this.audios[e].setRate(t)},createAudio:function(t){return this.audioFactory?this.audioFactory(t):window.Howl?new window.Howl({src:[t]}):{isPlaying:!1,play:function(){this.isPlaying=!0},seek:function(){this.isPlaying=!1},playing:function(){},rate:function(){},setVolume:function(){}}},setAudioFactory:function(t){this.audioFactory=t},setVolume:function(t){this._volume=t,this._updateVolume()},mute:function(){this._isMuted=!0,this._updateVolume()},unmute:function(){this._isMuted=!1,this._updateVolume()},getVolume:function(){return this._volume},_updateVolume:function(){var t,e=this.audios.length;for(t=0;t<e;t+=1)this.audios[t].volume(this._volume*(this._isMuted?0:1))}},function(){return new t}}(),o=function(){function t(t,e){var i,s=0,a=[];switch(t){case"int16":case"uint8c":i=1;break;default:i=1.1}for(s=0;s<e;s+=1)a.push(i);return a}return"function"==typeof Uint8ClampedArray&&"function"==typeof Float32Array?function(e,i){return"float32"===e?new Float32Array(i):"int16"===e?new Int16Array(i):"uint8c"===e?new Uint8ClampedArray(i):t(e,i)}:t}();function l(t){return Array.apply(null,{length:t})}function p(t){return p="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},p(t)}var f=!0,m=null,d=null,c="",u=/^((?!chrome|android).)*safari/i.test(navigator.userAgent),g=Math.pow,y=Math.sqrt,v=Math.floor,b=Math.max,_=Math.min,x={};!function(){var t,e=["abs","acos","acosh","asin","asinh","atan","atanh","atan2","ceil","cbrt","expm1","clz32","cos","cosh","exp","floor","fround","hypot","imul","log","log1p","log2","log10","max","min","pow","random","round","sign","sin","sinh","sqrt","tan","tanh","trunc","E","LN10","LN2","LOG10E","LOG2E","PI","SQRT1_2","SQRT2"],i=e.length;for(t=0;t<i;t+=1)x[e[t]]=Math[e[t]]}(),x.random=Math.random,x.abs=function(t){if("object"===p(t)&&t.length){var e,i=l(t.length),s=t.length;for(e=0;e<s;e+=1)i[e]=Math.abs(t[e]);return i}return Math.abs(t)};var k=150,w=Math.PI/180,P=.5519;function S(t){0}function A(t){t.style.position="absolute",t.style.top=0,t.style.left=0,t.style.display="block",t.style.transformOrigin="0 0",t.style.webkitTransformOrigin="0 0",t.style.backfaceVisibility="visible",t.style.webkitBackfaceVisibility="visible",t.style.transformStyle="preserve-3d",t.style.webkitTransformStyle="preserve-3d",t.style.mozTransformStyle="preserve-3d"}function D(t,e,i,s){this.type=t,this.currentTime=e,this.totalTime=i,this.direction=s<0?-1:1}function C(t,e){this.type=t,this.direction=e<0?-1:1}function E(t,e,i,s){this.type=t,this.currentLoop=i,this.totalLoops=e,this.direction=s<0?-1:1}function M(t,e,i){this.type=t,this.firstFrame=e,this.totalFrames=i}function T(t,e){this.type=t,this.target=e}function F(t,e){this.type="renderFrameError",this.nativeError=t,this.currentTime=e}function I(t){this.type="configError",this.nativeError=t}var L,B=(L=0,function(){return c+"__lottie_element_"+(L+=1)});function z(t,e,i){var s,a,r,n,h,o,l,p;switch(o=i*(1-e),l=i*(1-(h=6*t-(n=Math.floor(6*t)))*e),p=i*(1-(1-h)*e),n%6){case 0:s=i,a=p,r=o;break;case 1:s=l,a=i,r=o;break;case 2:s=o,a=i,r=p;break;case 3:s=o,a=l,r=i;break;case 4:s=p,a=o,r=i;break;case 5:s=i,a=o,r=l}return[s,a,r]}function V(t,e,i){var s,a=Math.max(t,e,i),r=Math.min(t,e,i),n=a-r,h=0===a?0:n/a,o=a/255;switch(a){case r:s=0;break;case t:s=e-i+n*(e<i?6:0),s/=6*n;break;case e:s=i-t+2*n,s/=6*n;break;case i:s=t-e+4*n,s/=6*n}return[s,h,o]}function R(t,e){var i=V(255*t[0],255*t[1],255*t[2]);return i[1]+=e,i[1]>1?i[1]=1:i[1]<=0&&(i[1]=0),z(i[0],i[1],i[2])}function O(t,e){var i=V(255*t[0],255*t[1],255*t[2]);return i[2]+=e,i[2]>1?i[2]=1:i[2]<0&&(i[2]=0),z(i[0],i[1],i[2])}function N(t,e){var i=V(255*t[0],255*t[1],255*t[2]);return i[0]+=e/360,i[0]>1?i[0]-=1:i[0]<0&&(i[0]+=1),z(i[0],i[1],i[2])}!function(){var t,e,i=[];for(t=0;t<256;t+=1)e=t.toString(16),i[t]=1===e.length?"0"+e:e}();var q=function(){return m},j=function(){return d},W=function(t){k=t},H=function(){return k};function X(t){return document.createElementNS("http://www.w3.org/2000/svg",t)}function Y(t){return Y="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Y(t)}var G=function(){var t,i,s=1,a=[],r={onmessage:function(){},postMessage:function(e){t({data:e})}},n={postMessage:function(t){r.onmessage({data:t})}};function h(i){if(window.Worker&&window.Blob&&e){var s=new Blob(["var _workerSelf = self; self.onmessage = ",i.toString()],{type:"text/javascript"}),a=URL.createObjectURL(s);return new Worker(a)}return t=i,r}function o(){i||(i=h((function(t){if(n.dataManager||(n.dataManager=function(){function t(a,r){var n,h,o,l,p,m,d=a.length;for(h=0;h<d;h+=1)if("ks"in(n=a[h])&&!n.completed){if(n.completed=!0,n.hasMask){var c=n.masksProperties;for(l=c.length,o=0;o<l;o+=1)if(c[o].pt.k.i)s(c[o].pt.k);else for(m=c[o].pt.k.length,p=0;p<m;p+=1)c[o].pt.k[p].s&&s(c[o].pt.k[p].s[0]),c[o].pt.k[p].e&&s(c[o].pt.k[p].e[0])}0===n.ty?(n.layers=e(n.refId,r),t(n.layers,r)):4===n.ty?i(n.shapes):5===n.ty&&f(n)}}function e(t,e){var i=function(t,e){for(var i=0,s=e.length;i<s;){if(e[i].id===t)return e[i];i+=1}return null}(t,e);return i?i.layers.__used?JSON.parse(JSON.stringify(i.layers)):(i.layers.__used=!0,i.layers):null}function i(t){var e,a,r;for(e=t.length-1;e>=0;e-=1)if("sh"===t[e].ty)if(t[e].ks.k.i)s(t[e].ks.k);else for(r=t[e].ks.k.length,a=0;a<r;a+=1)t[e].ks.k[a].s&&s(t[e].ks.k[a].s[0]),t[e].ks.k[a].e&&s(t[e].ks.k[a].e[0]);else"gr"===t[e].ty&&i(t[e].it)}function s(t){var e,i=t.i.length;for(e=0;e<i;e+=1)t.i[e][0]+=t.v[e][0],t.i[e][1]+=t.v[e][1],t.o[e][0]+=t.v[e][0],t.o[e][1]+=t.v[e][1]}function a(t,e){var i=e?e.split("."):[100,100,100];return t[0]>i[0]||!(i[0]>t[0])&&(t[1]>i[1]||!(i[1]>t[1])&&(t[2]>i[2]||!(i[2]>t[2])&&null))}var r,n=function(){var t=[4,4,14];function e(t){var e,i,s,a=t.length;for(e=0;e<a;e+=1)5===t[e].ty&&(s=void 0,s=(i=t[e]).t.d,i.t.d={k:[{s:s,t:0}]})}return function(i){if(a(t,i.v)&&(e(i.layers),i.assets)){var s,r=i.assets.length;for(s=0;s<r;s+=1)i.assets[s].layers&&e(i.assets[s].layers)}}}(),h=(r=[4,7,99],function(t){if(t.chars&&!a(r,t.v)){var e,s=t.chars.length;for(e=0;e<s;e+=1){var n=t.chars[e];n.data&&n.data.shapes&&(i(n.data.shapes),n.data.ip=0,n.data.op=99999,n.data.st=0,n.data.sr=1,n.data.ks={p:{k:[0,0],a:0},s:{k:[100,100],a:0},a:{k:[0,0],a:0},r:{k:0,a:0},o:{k:100,a:0}},t.chars[e].t||(n.data.shapes.push({ty:"no"}),n.data.shapes[0].it.push({p:{k:[0,0],a:0},s:{k:[100,100],a:0},a:{k:[0,0],a:0},r:{k:0,a:0},o:{k:100,a:0},sk:{k:0,a:0},sa:{k:0,a:0},ty:"tr"})))}}}),o=function(){var t=[5,7,15];function e(t){var e,i,s=t.length;for(e=0;e<s;e+=1)5===t[e].ty&&(i=void 0,"number"==typeof(i=t[e].t.p).a&&(i.a={a:0,k:i.a}),"number"==typeof i.p&&(i.p={a:0,k:i.p}),"number"==typeof i.r&&(i.r={a:0,k:i.r}))}return function(i){if(a(t,i.v)&&(e(i.layers),i.assets)){var s,r=i.assets.length;for(s=0;s<r;s+=1)i.assets[s].layers&&e(i.assets[s].layers)}}}(),l=function(){var t=[4,1,9];function e(t){var i,s,a,r=t.length;for(i=0;i<r;i+=1)if("gr"===t[i].ty)e(t[i].it);else if("fl"===t[i].ty||"st"===t[i].ty)if(t[i].c.k&&t[i].c.k[0].i)for(a=t[i].c.k.length,s=0;s<a;s+=1)t[i].c.k[s].s&&(t[i].c.k[s].s[0]/=255,t[i].c.k[s].s[1]/=255,t[i].c.k[s].s[2]/=255,t[i].c.k[s].s[3]/=255),t[i].c.k[s].e&&(t[i].c.k[s].e[0]/=255,t[i].c.k[s].e[1]/=255,t[i].c.k[s].e[2]/=255,t[i].c.k[s].e[3]/=255);else t[i].c.k[0]/=255,t[i].c.k[1]/=255,t[i].c.k[2]/=255,t[i].c.k[3]/=255}function i(t){var i,s=t.length;for(i=0;i<s;i+=1)4===t[i].ty&&e(t[i].shapes)}return function(e){if(a(t,e.v)&&(i(e.layers),e.assets)){var s,r=e.assets.length;for(s=0;s<r;s+=1)e.assets[s].layers&&i(e.assets[s].layers)}}}(),p=function(){var t=[4,4,18];function e(t){var i,s,a;for(i=t.length-1;i>=0;i-=1)if("sh"===t[i].ty)if(t[i].ks.k.i)t[i].ks.k.c=t[i].closed;else for(a=t[i].ks.k.length,s=0;s<a;s+=1)t[i].ks.k[s].s&&(t[i].ks.k[s].s[0].c=t[i].closed),t[i].ks.k[s].e&&(t[i].ks.k[s].e[0].c=t[i].closed);else"gr"===t[i].ty&&e(t[i].it)}function i(t){var i,s,a,r,n,h,o=t.length;for(s=0;s<o;s+=1){if((i=t[s]).hasMask){var l=i.masksProperties;for(r=l.length,a=0;a<r;a+=1)if(l[a].pt.k.i)l[a].pt.k.c=l[a].cl;else for(h=l[a].pt.k.length,n=0;n<h;n+=1)l[a].pt.k[n].s&&(l[a].pt.k[n].s[0].c=l[a].cl),l[a].pt.k[n].e&&(l[a].pt.k[n].e[0].c=l[a].cl)}4===i.ty&&e(i.shapes)}}return function(e){if(a(t,e.v)&&(i(e.layers),e.assets)){var s,r=e.assets.length;for(s=0;s<r;s+=1)e.assets[s].layers&&i(e.assets[s].layers)}}}();function f(t){0===t.t.a.length&&t.t.p}var m={completeData:function(i){i.__complete||(l(i),n(i),h(i),o(i),p(i),t(i.layers,i.assets),function(i,s){if(i){var a=0,r=i.length;for(a=0;a<r;a+=1)1===i[a].t&&(i[a].data.layers=e(i[a].data.refId,s),t(i[a].data.layers,s))}}(i.chars,i.assets),i.__complete=!0)}};return m.checkColors=l,m.checkChars=h,m.checkPathProperties=o,m.checkShapes=p,m.completeLayers=t,m}()),n.assetLoader||(n.assetLoader=function(){function t(t){var e=t.getResponseHeader("content-type");return e&&"json"===t.responseType&&-1!==e.indexOf("json")||t.response&&"object"===Y(t.response)?t.response:t.response&&"string"==typeof t.response?JSON.parse(t.response):t.responseText?JSON.parse(t.responseText):null}return{load:function(e,i,s,a){var r,n=new XMLHttpRequest;try{n.responseType="json"}catch(t){}n.onreadystatechange=function(){if(4===n.readyState)if(200===n.status)r=t(n),s(r);else try{r=t(n),s(r)}catch(t){a&&a(t)}};try{n.open(["G","E","T"].join(""),e,!0)}catch(t){n.open(["G","E","T"].join(""),i+"/"+e,!0)}n.send()}}}()),"loadAnimation"===t.data.type)n.assetLoader.load(t.data.path,t.data.fullPath,(function(e){n.dataManager.completeData(e),n.postMessage({id:t.data.id,payload:e,status:"success"})}),(function(){n.postMessage({id:t.data.id,status:"error"})}));else if("complete"===t.data.type){var e=t.data.animation;n.dataManager.completeData(e),n.postMessage({id:t.data.id,payload:e,status:"success"})}else"loadData"===t.data.type&&n.assetLoader.load(t.data.path,t.data.fullPath,(function(e){n.postMessage({id:t.data.id,payload:e,status:"success"})}),(function(){n.postMessage({id:t.data.id,status:"error"})}))})),i.onmessage=function(t){var e=t.data,i=e.id,s=a[i];a[i]=null,"success"===e.status?s.onComplete(e.payload):s.onError&&s.onError()})}function l(t,e){var i="processId_"+(s+=1);return a[i]={onComplete:t,onError:e},i}return{loadAnimation:function(t,e,s){o();var a=l(e,s);i.postMessage({type:"loadAnimation",path:t,fullPath:window.location.origin+window.location.pathname,id:a})},loadData:function(t,e,s){o();var a=l(e,s);i.postMessage({type:"loadData",path:t,fullPath:window.location.origin+window.location.pathname,id:a})},completeAnimation:function(t,e,s){o();var a=l(e,s);i.postMessage({type:"complete",animation:t,id:a})}}}(),K=function(){var t=function(){var t=a("canvas");t.width=1,t.height=1;var e=t.getContext("2d");return e.fillStyle="rgba(0,0,0,0)",e.fillRect(0,0,1,1),t}();function e(){this.loadedAssets+=1,this.loadedAssets===this.totalImages&&this.loadedFootagesCount===this.totalFootages&&this.imagesLoadedCb&&this.imagesLoadedCb(null)}function i(){this.loadedFootagesCount+=1,this.loadedAssets===this.totalImages&&this.loadedFootagesCount===this.totalFootages&&this.imagesLoadedCb&&this.imagesLoadedCb(null)}function s(t,e,i){var s="";if(t.e)s=t.p;else if(e){var a=t.p;-1!==a.indexOf("images/")&&(a=a.split("/")[1]),s=e+a}else s=i,s+=t.u?t.u:"",s+=t.p;return s}function r(t){var e=0,i=setInterval(function(){(t.getBBox().width||e>500)&&(this._imageLoaded(),clearInterval(i)),e+=1}.bind(this),50)}function n(t){var e={assetData:t},i=s(t,this.assetsPath,this.path);return G.loadData(i,function(t){e.img=t,this._footageLoaded()}.bind(this),function(){e.img={},this._footageLoaded()}.bind(this)),e}function h(){this._imageLoaded=e.bind(this),this._footageLoaded=i.bind(this),this.testImageLoaded=r.bind(this),this.createFootageData=n.bind(this),this.assetsPath="",this.path="",this.totalImages=0,this.totalFootages=0,this.loadedAssets=0,this.loadedFootagesCount=0,this.imagesLoadedCb=null,this.images=[]}return h.prototype={loadAssets:function(t,e){var i;this.imagesLoadedCb=e;var s=t.length;for(i=0;i<s;i+=1)t[i].layers||(t[i].t&&"seq"!==t[i].t?3===t[i].t&&(this.totalFootages+=1,this.images.push(this.createFootageData(t[i]))):(this.totalImages+=1,this.images.push(this._createImageData(t[i]))))},setAssetsPath:function(t){this.assetsPath=t||""},setPath:function(t){this.path=t||""},loadedImages:function(){return this.totalImages===this.loadedAssets},loadedFootages:function(){return this.totalFootages===this.loadedFootagesCount},destroy:function(){this.imagesLoadedCb=null,this.images.length=0},getAsset:function(t){for(var e=0,i=this.images.length;e<i;){if(this.images[e].assetData===t)return this.images[e].img;e+=1}return null},createImgData:function(e){var i=s(e,this.assetsPath,this.path),r=a("img");r.crossOrigin="anonymous",r.addEventListener("load",this._imageLoaded,!1),r.addEventListener("error",function(){n.img=t,this._imageLoaded()}.bind(this),!1),r.src=i;var n={img:r,assetData:e};return n},createImageData:function(e){var i=s(e,this.assetsPath,this.path),a=X("image");u?this.testImageLoaded(a):a.addEventListener("load",this._imageLoaded,!1),a.addEventListener("error",function(){r.img=t,this._imageLoaded()}.bind(this),!1),a.setAttributeNS("http://www.w3.org/1999/xlink","href",i),this._elementHelper.append?this._elementHelper.append(a):this._elementHelper.appendChild(a);var r={img:a,assetData:e};return r},imageLoaded:e,footageLoaded:i,setCacheType:function(t,e){"svg"===t?(this._elementHelper=e,this._createImageData=this.createImageData.bind(this)):this._createImageData=this.createImgData.bind(this)}},h}();function J(){}J.prototype={triggerEvent:function(t,e){if(this._cbs[t])for(var i=this._cbs[t],s=0;s<i.length;s+=1)i[s](e)},addEventListener:function(t,e){return this._cbs[t]||(this._cbs[t]=[]),this._cbs[t].push(e),function(){this.removeEventListener(t,e)}.bind(this)},removeEventListener:function(t,e){if(e){if(this._cbs[t]){for(var i=0,s=this._cbs[t].length;i<s;)this._cbs[t][i]===e&&(this._cbs[t].splice(i,1),i-=1,s-=1),i+=1;this._cbs[t].length||(this._cbs[t]=null)}}else this._cbs[t]=null}};var Z=function(){function t(t){for(var e,i=t.split("\r\n"),s={},a=0,r=0;r<i.length;r+=1)2===(e=i[r].split(":")).length&&(s[e[0]]=e[1].trim(),a+=1);if(0===a)throw new Error;return s}return function(e){for(var i=[],s=0;s<e.length;s+=1){var a=e[s],r={time:a.tm,duration:a.dr};try{r.payload=JSON.parse(e[s].cm)}catch(i){try{r.payload=t(e[s].cm)}catch(t){r.payload={name:e[s].cm}}}i.push(r)}return i}}(),U=function(){function t(t){this.compositions.push(t)}return function(){function e(t){for(var e=0,i=this.compositions.length;e<i;){if(this.compositions[e].data&&this.compositions[e].data.nm===t)return this.compositions[e].prepareFrame&&this.compositions[e].data.xt&&this.compositions[e].prepareFrame(this.currentFrame),this.compositions[e].compInterface;e+=1}return null}return e.compositions=[],e.currentFrame=0,e.registerComposition=t,e}}(),Q={};function $(t){return $="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},$(t)}var tt=function(){this._cbs=[],this.name="",this.path="",this.isLoaded=!1,this.currentFrame=0,this.currentRawFrame=0,this.firstFrame=0,this.totalFrames=0,this.frameRate=0,this.frameMult=0,this.playSpeed=1,this.playDirection=1,this.playCount=0,this.animationData={},this.assets=[],this.isPaused=!0,this.autoplay=!1,this.loop=!0,this.renderer=null,this.animationID=B(),this.assetsPath="",this.timeCompleted=0,this.segmentPos=0,this.isSubframeEnabled=f,this.segments=[],this._idle=!0,this._completedLoop=!1,this.projectInterface=U(),this.imagePreloader=new K,this.audioController=h(),this.markers=[],this.configAnimation=this.configAnimation.bind(this),this.onSetupError=this.onSetupError.bind(this),this.onSegmentComplete=this.onSegmentComplete.bind(this),this.drawnFrameEvent=new D("drawnFrame",0,0,0),this.expressionsPlugin=q()};r([J],tt),tt.prototype.setParams=function(t){(t.wrapper||t.container)&&(this.wrapper=t.wrapper||t.container);var e="svg";t.animType?e=t.animType:t.renderer&&(e=t.renderer);var i=Q[e];this.renderer=new i(this,t.rendererSettings),this.imagePreloader.setCacheType(e,this.renderer.globalData.defs),this.renderer.setProjectInterface(this.projectInterface),this.animType=e,""===t.loop||null===t.loop||void 0===t.loop||!0===t.loop?this.loop=!0:!1===t.loop?this.loop=!1:this.loop=parseInt(t.loop,10),this.autoplay=!("autoplay"in t)||t.autoplay,this.name=t.name?t.name:"",this.autoloadSegments=!Object.prototype.hasOwnProperty.call(t,"autoloadSegments")||t.autoloadSegments,this.assetsPath=t.assetsPath,this.initialSegment=t.initialSegment,t.audioFactory&&this.audioController.setAudioFactory(t.audioFactory),t.animationData?this.setupAnimation(t.animationData):t.path&&(-1!==t.path.lastIndexOf("\\")?this.path=t.path.substr(0,t.path.lastIndexOf("\\")+1):this.path=t.path.substr(0,t.path.lastIndexOf("/")+1),this.fileName=t.path.substr(t.path.lastIndexOf("/")+1),this.fileName=this.fileName.substr(0,this.fileName.lastIndexOf(".json")),G.loadAnimation(t.path,this.configAnimation,this.onSetupError))},tt.prototype.onSetupError=function(){this.trigger("data_failed")},tt.prototype.setupAnimation=function(t){G.completeAnimation(t,this.configAnimation)},tt.prototype.setData=function(t,e){e&&"object"!==$(e)&&(e=JSON.parse(e));var i={wrapper:t,animationData:e},s=t.attributes;i.path=s.getNamedItem("data-animation-path")?s.getNamedItem("data-animation-path").value:s.getNamedItem("data-bm-path")?s.getNamedItem("data-bm-path").value:s.getNamedItem("bm-path")?s.getNamedItem("bm-path").value:"",i.animType=s.getNamedItem("data-anim-type")?s.getNamedItem("data-anim-type").value:s.getNamedItem("data-bm-type")?s.getNamedItem("data-bm-type").value:s.getNamedItem("bm-type")?s.getNamedItem("bm-type").value:s.getNamedItem("data-bm-renderer")?s.getNamedItem("data-bm-renderer").value:s.getNamedItem("bm-renderer")?s.getNamedItem("bm-renderer").value:function(){if(Q.canvas)return"canvas";for(var t in Q)if(Q[t])return t;return""}()||"canvas";var a=s.getNamedItem("data-anim-loop")?s.getNamedItem("data-anim-loop").value:s.getNamedItem("data-bm-loop")?s.getNamedItem("data-bm-loop").value:s.getNamedItem("bm-loop")?s.getNamedItem("bm-loop").value:"";"false"===a?i.loop=!1:"true"===a?i.loop=!0:""!==a&&(i.loop=parseInt(a,10));var r=s.getNamedItem("data-anim-autoplay")?s.getNamedItem("data-anim-autoplay").value:s.getNamedItem("data-bm-autoplay")?s.getNamedItem("data-bm-autoplay").value:!s.getNamedItem("bm-autoplay")||s.getNamedItem("bm-autoplay").value;i.autoplay="false"!==r,i.name=s.getNamedItem("data-name")?s.getNamedItem("data-name").value:s.getNamedItem("data-bm-name")?s.getNamedItem("data-bm-name").value:s.getNamedItem("bm-name")?s.getNamedItem("bm-name").value:"","false"===(s.getNamedItem("data-anim-prerender")?s.getNamedItem("data-anim-prerender").value:s.getNamedItem("data-bm-prerender")?s.getNamedItem("data-bm-prerender").value:s.getNamedItem("bm-prerender")?s.getNamedItem("bm-prerender").value:"")&&(i.prerender=!1),i.path?this.setParams(i):this.trigger("destroy")},tt.prototype.includeLayers=function(t){t.op>this.animationData.op&&(this.animationData.op=t.op,this.totalFrames=Math.floor(t.op-this.animationData.ip));var e,i,s=this.animationData.layers,a=s.length,r=t.layers,n=r.length;for(i=0;i<n;i+=1)for(e=0;e<a;){if(s[e].id===r[i].id){s[e]=r[i];break}e+=1}if((t.chars||t.fonts)&&(this.renderer.globalData.fontManager.addChars(t.chars),this.renderer.globalData.fontManager.addFonts(t.fonts,this.renderer.globalData.defs)),t.assets)for(a=t.assets.length,e=0;e<a;e+=1)this.animationData.assets.push(t.assets[e]);this.animationData.__complete=!1,G.completeAnimation(this.animationData,this.onSegmentComplete)},tt.prototype.onSegmentComplete=function(t){this.animationData=t;var e=q();e&&e.initExpressions(this),this.loadNextSegment()},tt.prototype.loadNextSegment=function(){var t=this.animationData.segments;if(!t||0===t.length||!this.autoloadSegments)return this.trigger("data_ready"),void(this.timeCompleted=this.totalFrames);var e=t.shift();this.timeCompleted=e.time*this.frameRate;var i=this.path+this.fileName+"_"+this.segmentPos+".json";this.segmentPos+=1,G.loadData(i,this.includeLayers.bind(this),function(){this.trigger("data_failed")}.bind(this))},tt.prototype.loadSegments=function(){this.animationData.segments||(this.timeCompleted=this.totalFrames),this.loadNextSegment()},tt.prototype.imagesLoaded=function(){this.trigger("loaded_images"),this.checkLoaded()},tt.prototype.preloadImages=function(){this.imagePreloader.setAssetsPath(this.assetsPath),this.imagePreloader.setPath(this.path),this.imagePreloader.loadAssets(this.animationData.assets,this.imagesLoaded.bind(this))},tt.prototype.configAnimation=function(t){if(this.renderer)try{this.animationData=t,this.initialSegment?(this.totalFrames=Math.floor(this.initialSegment[1]-this.initialSegment[0]),this.firstFrame=Math.round(this.initialSegment[0])):(this.totalFrames=Math.floor(this.animationData.op-this.animationData.ip),this.firstFrame=Math.round(this.animationData.ip)),this.renderer.configAnimation(t),t.assets||(t.assets=[]),this.assets=this.animationData.assets,this.frameRate=this.animationData.fr,this.frameMult=this.animationData.fr/1e3,this.renderer.searchExtraCompositions(t.assets),this.markers=Z(t.markers||[]),this.trigger("config_ready"),this.preloadImages(),this.loadSegments(),this.updaFrameModifier(),this.waitForFontsLoaded(),this.isPaused&&this.audioController.pause()}catch(t){this.triggerConfigError(t)}},tt.prototype.waitForFontsLoaded=function(){this.renderer&&(this.renderer.globalData.fontManager.isLoaded?this.checkLoaded():setTimeout(this.waitForFontsLoaded.bind(this),20))},tt.prototype.checkLoaded=function(){if(!this.isLoaded&&this.renderer.globalData.fontManager.isLoaded&&(this.imagePreloader.loadedImages()||"canvas"!==this.renderer.rendererType)&&this.imagePreloader.loadedFootages()){this.isLoaded=!0;var t=q();t&&t.initExpressions(this),this.renderer.initItems(),setTimeout(function(){this.trigger("DOMLoaded")}.bind(this),0),this.gotoFrame(),this.autoplay&&this.play()}},tt.prototype.resize=function(t,e){var i="number"==typeof t?t:void 0,s="number"==typeof e?e:void 0;this.renderer.updateContainerSize(i,s)},tt.prototype.setSubframe=function(t){this.isSubframeEnabled=!!t},tt.prototype.gotoFrame=function(){this.currentFrame=this.isSubframeEnabled?this.currentRawFrame:~~this.currentRawFrame,this.timeCompleted!==this.totalFrames&&this.currentFrame>this.timeCompleted&&(this.currentFrame=this.timeCompleted),this.trigger("enterFrame"),this.renderFrame(),this.trigger("drawnFrame")},tt.prototype.renderFrame=function(){if(!1!==this.isLoaded&&this.renderer)try{this.expressionsPlugin&&this.expressionsPlugin.resetFrame(),this.renderer.renderFrame(this.currentFrame+this.firstFrame)}catch(t){this.triggerRenderFrameError(t)}},tt.prototype.play=function(t){t&&this.name!==t||!0===this.isPaused&&(this.isPaused=!1,this.trigger("_play"),this.audioController.resume(),this._idle&&(this._idle=!1,this.trigger("_active")))},tt.prototype.pause=function(t){t&&this.name!==t||!1===this.isPaused&&(this.isPaused=!0,this.trigger("_pause"),this._idle=!0,this.trigger("_idle"),this.audioController.pause())},tt.prototype.togglePause=function(t){t&&this.name!==t||(!0===this.isPaused?this.play():this.pause())},tt.prototype.stop=function(t){t&&this.name!==t||(this.pause(),this.playCount=0,this._completedLoop=!1,this.setCurrentRawFrameValue(0))},tt.prototype.getMarkerData=function(t){for(var e,i=0;i<this.markers.length;i+=1)if((e=this.markers[i]).payload&&e.payload.name===t)return e;return null},tt.prototype.goToAndStop=function(t,e,i){if(!i||this.name===i){var s=Number(t);if(isNaN(s)){var a=this.getMarkerData(t);a&&this.goToAndStop(a.time,!0)}else e?this.setCurrentRawFrameValue(t):this.setCurrentRawFrameValue(t*this.frameModifier);this.pause()}},tt.prototype.goToAndPlay=function(t,e,i){if(!i||this.name===i){var s=Number(t);if(isNaN(s)){var a=this.getMarkerData(t);a&&(a.duration?this.playSegments([a.time,a.time+a.duration],!0):this.goToAndStop(a.time,!0))}else this.goToAndStop(s,e,i);this.play()}},tt.prototype.advanceTime=function(t){if(!0!==this.isPaused&&!1!==this.isLoaded){var e=this.currentRawFrame+t*this.frameModifier,i=!1;e>=this.totalFrames-1&&this.frameModifier>0?this.loop&&this.playCount!==this.loop?e>=this.totalFrames?(this.playCount+=1,this.checkSegments(e%this.totalFrames)||(this.setCurrentRawFrameValue(e%this.totalFrames),this._completedLoop=!0,this.trigger("loopComplete"))):this.setCurrentRawFrameValue(e):this.checkSegments(e>this.totalFrames?e%this.totalFrames:0)||(i=!0,e=this.totalFrames-1):e<0?this.checkSegments(e%this.totalFrames)||(!this.loop||this.playCount--<=0&&!0!==this.loop?(i=!0,e=0):(this.setCurrentRawFrameValue(this.totalFrames+e%this.totalFrames),this._completedLoop?this.trigger("loopComplete"):this._completedLoop=!0)):this.setCurrentRawFrameValue(e),i&&(this.setCurrentRawFrameValue(e),this.pause(),this.trigger("complete"))}},tt.prototype.adjustSegment=function(t,e){this.playCount=0,t[1]<t[0]?(this.frameModifier>0&&(this.playSpeed<0?this.setSpeed(-this.playSpeed):this.setDirection(-1)),this.totalFrames=t[0]-t[1],this.timeCompleted=this.totalFrames,this.firstFrame=t[1],this.setCurrentRawFrameValue(this.totalFrames-.001-e)):t[1]>t[0]&&(this.frameModifier<0&&(this.playSpeed<0?this.setSpeed(-this.playSpeed):this.setDirection(1)),this.totalFrames=t[1]-t[0],this.timeCompleted=this.totalFrames,this.firstFrame=t[0],this.setCurrentRawFrameValue(.001+e)),this.trigger("segmentStart")},tt.prototype.setSegment=function(t,e){var i=-1;this.isPaused&&(this.currentRawFrame+this.firstFrame<t?i=t:this.currentRawFrame+this.firstFrame>e&&(i=e-t)),this.firstFrame=t,this.totalFrames=e-t,this.timeCompleted=this.totalFrames,-1!==i&&this.goToAndStop(i,!0)},tt.prototype.playSegments=function(t,e){if(e&&(this.segments.length=0),"object"===$(t[0])){var i,s=t.length;for(i=0;i<s;i+=1)this.segments.push(t[i])}else this.segments.push(t);this.segments.length&&e&&this.adjustSegment(this.segments.shift(),0),this.isPaused&&this.play()},tt.prototype.resetSegments=function(t){this.segments.length=0,this.segments.push([this.animationData.ip,this.animationData.op]),t&&this.checkSegments(0)},tt.prototype.checkSegments=function(t){return!!this.segments.length&&(this.adjustSegment(this.segments.shift(),t),!0)},tt.prototype.destroy=function(t){t&&this.name!==t||!this.renderer||(this.renderer.destroy(),this.imagePreloader.destroy(),this.trigger("destroy"),this._cbs=null,this.onEnterFrame=null,this.onLoopComplete=null,this.onComplete=null,this.onSegmentStart=null,this.onDestroy=null,this.renderer=null,this.expressionsPlugin=null,this.imagePreloader=null,this.projectInterface=null)},tt.prototype.setCurrentRawFrameValue=function(t){this.currentRawFrame=t,this.gotoFrame()},tt.prototype.setSpeed=function(t){this.playSpeed=t,this.updaFrameModifier()},tt.prototype.setDirection=function(t){this.playDirection=t<0?-1:1,this.updaFrameModifier()},tt.prototype.setLoop=function(t){this.loop=t},tt.prototype.setVolume=function(t,e){e&&this.name!==e||this.audioController.setVolume(t)},tt.prototype.getVolume=function(){return this.audioController.getVolume()},tt.prototype.mute=function(t){t&&this.name!==t||this.audioController.mute()},tt.prototype.unmute=function(t){t&&this.name!==t||this.audioController.unmute()},tt.prototype.updaFrameModifier=function(){this.frameModifier=this.frameMult*this.playSpeed*this.playDirection,this.audioController.setRate(this.playSpeed*this.playDirection)},tt.prototype.getPath=function(){return this.path},tt.prototype.getAssetsPath=function(t){var e="";if(t.e)e=t.p;else if(this.assetsPath){var i=t.p;-1!==i.indexOf("images/")&&(i=i.split("/")[1]),e=this.assetsPath+i}else e=this.path,e+=t.u?t.u:"",e+=t.p;return e},tt.prototype.getAssetData=function(t){for(var e=0,i=this.assets.length;e<i;){if(t===this.assets[e].id)return this.assets[e];e+=1}return null},tt.prototype.hide=function(){this.renderer.hide()},tt.prototype.show=function(){this.renderer.show()},tt.prototype.getDuration=function(t){return t?this.totalFrames:this.totalFrames/this.frameRate},tt.prototype.updateDocumentData=function(t,e,i){try{this.renderer.getElementByPath(t).updateDocumentData(e,i)}catch(t){}},tt.prototype.trigger=function(t){if(this._cbs&&this._cbs[t])switch(t){case"enterFrame":this.triggerEvent(t,new D(t,this.currentFrame,this.totalFrames,this.frameModifier));break;case"drawnFrame":this.drawnFrameEvent.currentTime=this.currentFrame,this.drawnFrameEvent.totalTime=this.totalFrames,this.drawnFrameEvent.direction=this.frameModifier,this.triggerEvent(t,this.drawnFrameEvent);break;case"loopComplete":this.triggerEvent(t,new E(t,this.loop,this.playCount,this.frameMult));break;case"complete":this.triggerEvent(t,new C(t,this.frameMult));break;case"segmentStart":this.triggerEvent(t,new M(t,this.firstFrame,this.totalFrames));break;case"destroy":this.triggerEvent(t,new T(t,this));break;default:this.triggerEvent(t)}"enterFrame"===t&&this.onEnterFrame&&this.onEnterFrame.call(this,new D(t,this.currentFrame,this.totalFrames,this.frameMult)),"loopComplete"===t&&this.onLoopComplete&&this.onLoopComplete.call(this,new E(t,this.loop,this.playCount,this.frameMult)),"complete"===t&&this.onComplete&&this.onComplete.call(this,new C(t,this.frameMult)),"segmentStart"===t&&this.onSegmentStart&&this.onSegmentStart.call(this,new M(t,this.firstFrame,this.totalFrames)),"destroy"===t&&this.onDestroy&&this.onDestroy.call(this,new T(t,this))},tt.prototype.triggerRenderFrameError=function(t){var e=new F(t,this.currentFrame);this.triggerEvent("error",e),this.onError&&this.onError.call(this,e)},tt.prototype.triggerConfigError=function(t){var e=new I(t,this.currentFrame);this.triggerEvent("error",e),this.onError&&this.onError.call(this,e)};var et=function(){var t={},e=[],i=0,s=0,r=0,n=!0,h=!1;function o(t){for(var i=0,a=t.target;i<s;)e[i].animation===a&&(e.splice(i,1),i-=1,s-=1,a.isPaused||f()),i+=1}function l(t,i){if(!t)return null;for(var a=0;a<s;){if(e[a].elem===t&&null!==e[a].elem)return e[a].animation;a+=1}var r=new tt;return m(r,t),r.setData(t,i),r}function p(){r+=1,u()}function f(){r-=1}function m(t,i){t.addEventListener("destroy",o),t.addEventListener("_active",p),t.addEventListener("_idle",f),e.push({elem:i,animation:t}),s+=1}function d(t){var a,o=t-i;for(a=0;a<s;a+=1)e[a].animation.advanceTime(o);i=t,r&&!h?window.requestAnimationFrame(d):n=!0}function c(t){i=t,window.requestAnimationFrame(d)}function u(){!h&&r&&n&&(window.requestAnimationFrame(c),n=!1)}return t.registerAnimation=l,t.loadAnimation=function(t){var e=new tt;return m(e,null),e.setParams(t),e},t.setSpeed=function(t,i){var a;for(a=0;a<s;a+=1)e[a].animation.setSpeed(t,i)},t.setDirection=function(t,i){var a;for(a=0;a<s;a+=1)e[a].animation.setDirection(t,i)},t.play=function(t){var i;for(i=0;i<s;i+=1)e[i].animation.play(t)},t.pause=function(t){var i;for(i=0;i<s;i+=1)e[i].animation.pause(t)},t.stop=function(t){var i;for(i=0;i<s;i+=1)e[i].animation.stop(t)},t.togglePause=function(t){var i;for(i=0;i<s;i+=1)e[i].animation.togglePause(t)},t.searchAnimations=function(t,e,i){var s,r=[].concat([].slice.call(document.getElementsByClassName("lottie")),[].slice.call(document.getElementsByClassName("bodymovin"))),n=r.length;for(s=0;s<n;s+=1)i&&r[s].setAttribute("data-bm-type",i),l(r[s],t);if(e&&0===n){i||(i="svg");var h=document.getElementsByTagName("body")[0];h.innerText="";var o=a("div");o.style.width="100%",o.style.height="100%",o.setAttribute("data-bm-type",i),h.appendChild(o),l(o,t)}},t.resize=function(){var t;for(t=0;t<s;t+=1)e[t].animation.resize()},t.goToAndStop=function(t,i,a){var r;for(r=0;r<s;r+=1)e[r].animation.goToAndStop(t,i,a)},t.destroy=function(t){var i;for(i=s-1;i>=0;i-=1)e[i].animation.destroy(t)},t.freeze=function(){h=!0},t.unfreeze=function(){h=!1,u()},t.setVolume=function(t,i){var a;for(a=0;a<s;a+=1)e[a].animation.setVolume(t,i)},t.mute=function(t){var i;for(i=0;i<s;i+=1)e[i].animation.mute(t)},t.unmute=function(t){var i;for(i=0;i<s;i+=1)e[i].animation.unmute(t)},t.getRegisteredAnimations=function(){var t,i=e.length,s=[];for(t=0;t<i;t+=1)s.push(e[t].animation);return s},t}(),it=function(){var t={getBezierEasing:function(t,i,s,a,r){var n=r||("bez_"+t+"_"+i+"_"+s+"_"+a).replace(/\./g,"p");if(e[n])return e[n];var h=new p([t,i,s,a]);return e[n]=h,h}},e={};var i=11,s=1/(i-1),a="function"==typeof Float32Array;function r(t,e){return 1-3*e+3*t}function n(t,e){return 3*e-6*t}function h(t){return 3*t}function o(t,e,i){return((r(e,i)*t+n(e,i))*t+h(e))*t}function l(t,e,i){return 3*r(e,i)*t*t+2*n(e,i)*t+h(e)}function p(t){this._p=t,this._mSampleValues=a?new Float32Array(i):new Array(i),this._precomputed=!1,this.get=this.get.bind(this)}return p.prototype={get:function(t){var e=this._p[0],i=this._p[1],s=this._p[2],a=this._p[3];return this._precomputed||this._precompute(),e===i&&s===a?t:0===t?0:1===t?1:o(this._getTForX(t),i,a)},_precompute:function(){var t=this._p[0],e=this._p[1],i=this._p[2],s=this._p[3];this._precomputed=!0,t===e&&i===s||this._calcSampleValues()},_calcSampleValues:function(){for(var t=this._p[0],e=this._p[2],a=0;a<i;++a)this._mSampleValues[a]=o(a*s,t,e)},_getTForX:function(t){for(var e=this._p[0],a=this._p[2],r=this._mSampleValues,n=0,h=1,p=i-1;h!==p&&r[h]<=t;++h)n+=s;var f=n+(t-r[--h])/(r[h+1]-r[h])*s,m=l(f,e,a);return m>=.001?function(t,e,i,s){for(var a=0;a<4;++a){var r=l(e,i,s);if(0===r)return e;e-=(o(e,i,s)-t)/r}return e}(t,f,e,a):0===m?f:function(t,e,i,s,a){var r,n,h=0;do{(r=o(n=e+(i-e)/2,s,a)-t)>0?i=n:e=n}while(Math.abs(r)>1e-7&&++h<10);return n}(t,n,n+s,e,a)}},t}(),st={double:function(t){return t.concat(l(t.length))}},at=function(t,e,i){var s=0,a=t,r=l(a);return{newElement:function(){return s?r[s-=1]:e()},release:function(t){s===a&&(r=st.double(r),a*=2),i&&i(t),r[s]=t,s+=1}}},rt=at(8,(function(){return{addedLength:0,percents:o("float32",H()),lengths:o("float32",H())}})),nt=at(8,(function(){return{lengths:[],totalLength:0}}),(function(t){var e,i=t.lengths.length;for(e=0;e<i;e+=1)rt.release(t.lengths[e]);t.lengths.length=0}));var ht=function(){var t=Math;function e(t,e,i,s,a,r){var n=t*s+e*a+i*r-a*s-r*t-i*e;return n>-.001&&n<.001}var i=function(t,e,i,s){var a,r,n,h,o,l,p=H(),f=0,m=[],d=[],c=rt.newElement();for(n=i.length,a=0;a<p;a+=1){for(o=a/(p-1),l=0,r=0;r<n;r+=1)h=g(1-o,3)*t[r]+3*g(1-o,2)*o*i[r]+3*(1-o)*g(o,2)*s[r]+g(o,3)*e[r],m[r]=h,null!==d[r]&&(l+=g(m[r]-d[r],2)),d[r]=m[r];l&&(f+=l=y(l)),c.percents[a]=o,c.lengths[a]=f}return c.addedLength=f,c};function s(t){this.segmentLength=0,this.points=new Array(t)}function a(t,e){this.partialLength=t,this.point=e}var r,n=(r={},function(t,i,n,h){var o=(t[0]+"_"+t[1]+"_"+i[0]+"_"+i[1]+"_"+n[0]+"_"+n[1]+"_"+h[0]+"_"+h[1]).replace(/\./g,"p");if(!r[o]){var p,f,m,d,c,u,v,b=H(),_=0,x=null;2===t.length&&(t[0]!==i[0]||t[1]!==i[1])&&e(t[0],t[1],i[0],i[1],t[0]+n[0],t[1]+n[1])&&e(t[0],t[1],i[0],i[1],i[0]+h[0],i[1]+h[1])&&(b=2);var k=new s(b);for(m=n.length,p=0;p<b;p+=1){for(v=l(m),c=p/(b-1),u=0,f=0;f<m;f+=1)d=g(1-c,3)*t[f]+3*g(1-c,2)*c*(t[f]+n[f])+3*(1-c)*g(c,2)*(i[f]+h[f])+g(c,3)*i[f],v[f]=d,null!==x&&(u+=g(v[f]-x[f],2));_+=u=y(u),k.points[p]=new a(u,v),x=v}k.segmentLength=_,r[o]=k}return r[o]});function h(t,e){var i=e.percents,s=e.lengths,a=i.length,r=v((a-1)*t),n=t*e.addedLength,h=0;if(r===a-1||0===r||n===s[r])return i[r];for(var o=s[r]>n?-1:1,l=!0;l;)if(s[r]<=n&&s[r+1]>n?(h=(n-s[r])/(s[r+1]-s[r]),l=!1):r+=o,r<0||r>=a-1){if(r===a-1)return i[r];l=!1}return i[r]+(i[r+1]-i[r])*h}var p=o("float32",8);return{getSegmentsLength:function(t){var e,s=nt.newElement(),a=t.c,r=t.v,n=t.o,h=t.i,o=t._length,l=s.lengths,p=0;for(e=0;e<o-1;e+=1)l[e]=i(r[e],r[e+1],n[e],h[e+1]),p+=l[e].addedLength;return a&&o&&(l[e]=i(r[e],r[0],n[e],h[0]),p+=l[e].addedLength),s.totalLength=p,s},getNewSegment:function(e,i,s,a,r,n,o){r<0?r=0:r>1&&(r=1);var l,f=h(r,o),m=h(n=n>1?1:n,o),d=e.length,c=1-f,u=1-m,g=c*c*c,y=f*c*c*3,v=f*f*c*3,b=f*f*f,_=c*c*u,x=f*c*u+c*f*u+c*c*m,k=f*f*u+c*f*m+f*c*m,w=f*f*m,P=c*u*u,S=f*u*u+c*m*u+c*u*m,A=f*m*u+c*m*m+f*u*m,D=f*m*m,C=u*u*u,E=m*u*u+u*m*u+u*u*m,M=m*m*u+u*m*m+m*u*m,T=m*m*m;for(l=0;l<d;l+=1)p[4*l]=t.round(1e3*(g*e[l]+y*s[l]+v*a[l]+b*i[l]))/1e3,p[4*l+1]=t.round(1e3*(_*e[l]+x*s[l]+k*a[l]+w*i[l]))/1e3,p[4*l+2]=t.round(1e3*(P*e[l]+S*s[l]+A*a[l]+D*i[l]))/1e3,p[4*l+3]=t.round(1e3*(C*e[l]+E*s[l]+M*a[l]+T*i[l]))/1e3;return p},getPointInSegment:function(e,i,s,a,r,n){var o=h(r,n),l=1-o;return[t.round(1e3*(l*l*l*e[0]+(o*l*l+l*o*l+l*l*o)*s[0]+(o*o*l+l*o*o+o*l*o)*a[0]+o*o*o*i[0]))/1e3,t.round(1e3*(l*l*l*e[1]+(o*l*l+l*o*l+l*l*o)*s[1]+(o*o*l+l*o*o+o*l*o)*a[1]+o*o*o*i[1]))/1e3]},buildBezierData:n,pointOnLine2D:e,pointOnLine3D:function(i,s,a,r,n,h,o,l,p){if(0===a&&0===h&&0===p)return e(i,s,r,n,o,l);var f,m=t.sqrt(t.pow(r-i,2)+t.pow(n-s,2)+t.pow(h-a,2)),d=t.sqrt(t.pow(o-i,2)+t.pow(l-s,2)+t.pow(p-a,2)),c=t.sqrt(t.pow(o-r,2)+t.pow(l-n,2)+t.pow(p-h,2));return(f=m>d?m>c?m-d-c:c-d-m:c>d?c-d-m:d-m-c)>-1e-4&&f<1e-4}}}(),ot=i,lt=Math.abs;function pt(t,e){var i,s=this.offsetTime;"multidimensional"===this.propType&&(i=o("float32",this.pv.length));for(var a,r,n,h,l,p,f,m,d,c=e.lastIndex,u=c,g=this.keyframes.length-1,y=!0;y;){if(a=this.keyframes[u],r=this.keyframes[u+1],u===g-1&&t>=r.t-s){a.h&&(a=r),c=0;break}if(r.t-s>t){c=u;break}u<g-1?u+=1:(c=0,y=!1)}n=this.keyframesMetadata[u]||{};var v,b,_,x,k,P,S,A,D,C,E=r.t-s,M=a.t-s;if(a.to){n.bezierData||(n.bezierData=ht.buildBezierData(a.s,r.s||a.e,a.to,a.ti));var T=n.bezierData;if(t>=E||t<M){var F=t>=E?T.points.length-1:0;for(l=T.points[F].point.length,h=0;h<l;h+=1)i[h]=T.points[F].point[h]}else{n.__fnct?d=n.__fnct:(d=it.getBezierEasing(a.o.x,a.o.y,a.i.x,a.i.y,a.n).get,n.__fnct=d),p=d((t-M)/(E-M));var I,L=T.segmentLength*p,B=e.lastFrame<t&&e._lastKeyframeIndex===u?e._lastAddedLength:0;for(m=e.lastFrame<t&&e._lastKeyframeIndex===u?e._lastPoint:0,y=!0,f=T.points.length;y;){if(B+=T.points[m].partialLength,0===L||0===p||m===T.points.length-1){for(l=T.points[m].point.length,h=0;h<l;h+=1)i[h]=T.points[m].point[h];break}if(L>=B&&L<B+T.points[m+1].partialLength){for(I=(L-B)/T.points[m+1].partialLength,l=T.points[m].point.length,h=0;h<l;h+=1)i[h]=T.points[m].point[h]+(T.points[m+1].point[h]-T.points[m].point[h])*I;break}m<f-1?m+=1:y=!1}e._lastPoint=m,e._lastAddedLength=B-T.points[m].partialLength,e._lastKeyframeIndex=u}}else{var z,V,R,O,N;if(g=a.s.length,v=r.s||a.e,this.sh&&1!==a.h)if(t>=E)i[0]=v[0],i[1]=v[1],i[2]=v[2];else if(t<=M)i[0]=a.s[0],i[1]=a.s[1],i[2]=a.s[2];else{var q=ft(a.s),j=ft(v);b=i,_=function(t,e,i){var s,a,r,n,h,o=[],l=t[0],p=t[1],f=t[2],m=t[3],d=e[0],c=e[1],u=e[2],g=e[3];return(a=l*d+p*c+f*u+m*g)<0&&(a=-a,d=-d,c=-c,u=-u,g=-g),1-a>1e-6?(s=Math.acos(a),r=Math.sin(s),n=Math.sin((1-i)*s)/r,h=Math.sin(i*s)/r):(n=1-i,h=i),o[0]=n*l+h*d,o[1]=n*p+h*c,o[2]=n*f+h*u,o[3]=n*m+h*g,o}(q,j,(t-M)/(E-M)),x=_[0],k=_[1],P=_[2],S=_[3],A=Math.atan2(2*k*S-2*x*P,1-2*k*k-2*P*P),D=Math.asin(2*x*k+2*P*S),C=Math.atan2(2*x*S-2*k*P,1-2*x*x-2*P*P),b[0]=A/w,b[1]=D/w,b[2]=C/w}else for(u=0;u<g;u+=1)1!==a.h&&(t>=E?p=1:t<M?p=0:(a.o.x.constructor===Array?(n.__fnct||(n.__fnct=[]),n.__fnct[u]?d=n.__fnct[u]:(z=void 0===a.o.x[u]?a.o.x[0]:a.o.x[u],V=void 0===a.o.y[u]?a.o.y[0]:a.o.y[u],R=void 0===a.i.x[u]?a.i.x[0]:a.i.x[u],O=void 0===a.i.y[u]?a.i.y[0]:a.i.y[u],d=it.getBezierEasing(z,V,R,O).get,n.__fnct[u]=d)):n.__fnct?d=n.__fnct:(z=a.o.x,V=a.o.y,R=a.i.x,O=a.i.y,d=it.getBezierEasing(z,V,R,O).get,a.keyframeMetadata=d),p=d((t-M)/(E-M)))),v=r.s||a.e,N=1===a.h?a.s[u]:a.s[u]+(v[u]-a.s[u])*p,"multidimensional"===this.propType?i[u]=N:i=N}return e.lastIndex=c,i}function ft(t){var e=t[0]*w,i=t[1]*w,s=t[2]*w,a=Math.cos(e/2),r=Math.cos(i/2),n=Math.cos(s/2),h=Math.sin(e/2),o=Math.sin(i/2),l=Math.sin(s/2);return[h*o*n+a*r*l,h*r*n+a*o*l,a*o*n-h*r*l,a*r*n-h*o*l]}function mt(){var t=this.comp.renderedFrame-this.offsetTime,e=this.keyframes[0].t-this.offsetTime,i=this.keyframes[this.keyframes.length-1].t-this.offsetTime;if(!(t===this._caching.lastFrame||this._caching.lastFrame!==ot&&(this._caching.lastFrame>=i&&t>=i||this._caching.lastFrame<e&&t<e))){this._caching.lastFrame>=t&&(this._caching._lastKeyframeIndex=-1,this._caching.lastIndex=0);var s=this.interpolateValue(t,this._caching);this.pv=s}return this._caching.lastFrame=t,this.pv}function dt(t){var e;if("unidimensional"===this.propType)e=t*this.mult,lt(this.v-e)>1e-5&&(this.v=e,this._mdf=!0);else for(var i=0,s=this.v.length;i<s;)e=t[i]*this.mult,lt(this.v[i]-e)>1e-5&&(this.v[i]=e,this._mdf=!0),i+=1}function ct(){if(this.elem.globalData.frameId!==this.frameId&&this.effectsSequence.length)if(this.lock)this.setVValue(this.pv);else{var t;this.lock=!0,this._mdf=this._isFirstFrame;var e=this.effectsSequence.length,i=this.kf?this.pv:this.data.k;for(t=0;t<e;t+=1)i=this.effectsSequence[t](i);this.setVValue(i),this._isFirstFrame=!1,this.lock=!1,this.frameId=this.elem.globalData.frameId}}function ut(t){this.effectsSequence.push(t),this.container.addDynamicProperty(this)}function gt(t,e,i,s){this.propType="unidimensional",this.mult=i||1,this.data=e,this.v=i?e.k*i:e.k,this.pv=e.k,this._mdf=!1,this.elem=t,this.container=s,this.comp=t.comp,this.k=!1,this.kf=!1,this.vel=0,this.effectsSequence=[],this._isFirstFrame=!0,this.getValue=ct,this.setVValue=dt,this.addEffect=ut}function yt(t,e,i,s){var a;this.propType="multidimensional",this.mult=i||1,this.data=e,this._mdf=!1,this.elem=t,this.container=s,this.comp=t.comp,this.k=!1,this.kf=!1,this.frameId=-1;var r=e.k.length;for(this.v=o("float32",r),this.pv=o("float32",r),this.vel=o("float32",r),a=0;a<r;a+=1)this.v[a]=e.k[a]*this.mult,this.pv[a]=e.k[a];this._isFirstFrame=!0,this.effectsSequence=[],this.getValue=ct,this.setVValue=dt,this.addEffect=ut}function vt(t,e,i,s){this.propType="unidimensional",this.keyframes=e.k,this.keyframesMetadata=[],this.offsetTime=t.data.st,this.frameId=-1,this._caching={lastFrame:ot,lastIndex:0,value:0,_lastKeyframeIndex:-1},this.k=!0,this.kf=!0,this.data=e,this.mult=i||1,this.elem=t,this.container=s,this.comp=t.comp,this.v=ot,this.pv=ot,this._isFirstFrame=!0,this.getValue=ct,this.setVValue=dt,this.interpolateValue=pt,this.effectsSequence=[mt.bind(this)],this.addEffect=ut}function bt(t,e,i,s){var a;this.propType="multidimensional";var r,n,h,l,p=e.k.length;for(a=0;a<p-1;a+=1)e.k[a].to&&e.k[a].s&&e.k[a+1]&&e.k[a+1].s&&(r=e.k[a].s,n=e.k[a+1].s,h=e.k[a].to,l=e.k[a].ti,(2===r.length&&(r[0]!==n[0]||r[1]!==n[1])&&ht.pointOnLine2D(r[0],r[1],n[0],n[1],r[0]+h[0],r[1]+h[1])&&ht.pointOnLine2D(r[0],r[1],n[0],n[1],n[0]+l[0],n[1]+l[1])||3===r.length&&(r[0]!==n[0]||r[1]!==n[1]||r[2]!==n[2])&&ht.pointOnLine3D(r[0],r[1],r[2],n[0],n[1],n[2],r[0]+h[0],r[1]+h[1],r[2]+h[2])&&ht.pointOnLine3D(r[0],r[1],r[2],n[0],n[1],n[2],n[0]+l[0],n[1]+l[1],n[2]+l[2]))&&(e.k[a].to=null,e.k[a].ti=null),r[0]===n[0]&&r[1]===n[1]&&0===h[0]&&0===h[1]&&0===l[0]&&0===l[1]&&(2===r.length||r[2]===n[2]&&0===h[2]&&0===l[2])&&(e.k[a].to=null,e.k[a].ti=null));this.effectsSequence=[mt.bind(this)],this.data=e,this.keyframes=e.k,this.keyframesMetadata=[],this.offsetTime=t.data.st,this.k=!0,this.kf=!0,this._isFirstFrame=!0,this.mult=i||1,this.elem=t,this.container=s,this.comp=t.comp,this.getValue=ct,this.setVValue=dt,this.interpolateValue=pt,this.frameId=-1;var f=e.k[0].s.length;for(this.v=o("float32",f),this.pv=o("float32",f),a=0;a<f;a+=1)this.v[a]=ot,this.pv[a]=ot;this._caching={lastFrame:ot,lastIndex:0,value:o("float32",f)},this.addEffect=ut}var _t={getProp:function(t,e,i,s,a){var r;if(e.sid&&(e=t.globalData.slotManager.getProp(e)),e.k.length)if("number"==typeof e.k[0])r=new yt(t,e,s,a);else switch(i){case 0:r=new vt(t,e,s,a);break;case 1:r=new bt(t,e,s,a)}else r=new gt(t,e,s,a);return r.effectsSequence.length&&a.addDynamicProperty(r),r}};function xt(){}xt.prototype={addDynamicProperty:function(t){-1===this.dynamicProperties.indexOf(t)&&(this.dynamicProperties.push(t),this.container.addDynamicProperty(this),this._isAnimated=!0)},iterateDynamicProperties:function(){var t;this._mdf=!1;var e=this.dynamicProperties.length;for(t=0;t<e;t+=1)this.dynamicProperties[t].getValue(),this.dynamicProperties[t]._mdf&&(this._mdf=!0)},initDynamicPropertyContainer:function(t){this.container=t,this.dynamicProperties=[],this._mdf=!1,this._isAnimated=!1}};var kt=at(8,(function(){return o("float32",2)}));function wt(){this.c=!1,this._length=0,this._maxLength=8,this.v=l(this._maxLength),this.o=l(this._maxLength),this.i=l(this._maxLength)}wt.prototype.setPathData=function(t,e){this.c=t,this.setLength(e);for(var i=0;i<e;)this.v[i]=kt.newElement(),this.o[i]=kt.newElement(),this.i[i]=kt.newElement(),i+=1},wt.prototype.setLength=function(t){for(;this._maxLength<t;)this.doubleArrayLength();this._length=t},wt.prototype.doubleArrayLength=function(){this.v=this.v.concat(l(this._maxLength)),this.i=this.i.concat(l(this._maxLength)),this.o=this.o.concat(l(this._maxLength)),this._maxLength*=2},wt.prototype.setXYAt=function(t,e,i,s,a){var r;switch(this._length=Math.max(this._length,s+1),this._length>=this._maxLength&&this.doubleArrayLength(),i){case"v":r=this.v;break;case"i":r=this.i;break;case"o":r=this.o;break;default:r=[]}(!r[s]||r[s]&&!a)&&(r[s]=kt.newElement()),r[s][0]=t,r[s][1]=e},wt.prototype.setTripleAt=function(t,e,i,s,a,r,n,h){this.setXYAt(t,e,"v",n,h),this.setXYAt(i,s,"o",n,h),this.setXYAt(a,r,"i",n,h)},wt.prototype.reverse=function(){var t=new wt;t.setPathData(this.c,this._length);var e=this.v,i=this.o,s=this.i,a=0;this.c&&(t.setTripleAt(e[0][0],e[0][1],s[0][0],s[0][1],i[0][0],i[0][1],0,!1),a=1);var r,n=this._length-1,h=this._length;for(r=a;r<h;r+=1)t.setTripleAt(e[n][0],e[n][1],s[n][0],s[n][1],i[n][0],i[n][1],r,!1),n-=1;return t},wt.prototype.length=function(){return this._length};var Pt,St=((Pt=at(4,(function(){return new wt}),(function(t){var e,i=t._length;for(e=0;e<i;e+=1)kt.release(t.v[e]),kt.release(t.i[e]),kt.release(t.o[e]),t.v[e]=null,t.i[e]=null,t.o[e]=null;t._length=0,t.c=!1}))).clone=function(t){var e,i=Pt.newElement(),s=void 0===t._length?t.v.length:t._length;for(i.setLength(s),i.c=t.c,e=0;e<s;e+=1)i.setTripleAt(t.v[e][0],t.v[e][1],t.o[e][0],t.o[e][1],t.i[e][0],t.i[e][1],e);return i},Pt);function At(){this._length=0,this._maxLength=4,this.shapes=l(this._maxLength)}At.prototype.addShape=function(t){this._length===this._maxLength&&(this.shapes=this.shapes.concat(l(this._maxLength)),this._maxLength*=2),this.shapes[this._length]=t,this._length+=1},At.prototype.releaseShapes=function(){var t;for(t=0;t<this._length;t+=1)St.release(this.shapes[t]);this._length=0};var Dt,Ct,Et,Mt,Tt=(Dt={newShapeCollection:function(){return Ct?Mt[Ct-=1]:new At},release:function(t){var e,i=t._length;for(e=0;e<i;e+=1)St.release(t.shapes[e]);t._length=0,Ct===Et&&(Mt=st.double(Mt),Et*=2),Mt[Ct]=t,Ct+=1}},Ct=0,Mt=l(Et=4),Dt),Ft=function(){var t=-999999;function e(t,e,i){var s,a,r,n,h,o,l,p,f,m=i.lastIndex,d=this.keyframes;if(t<d[0].t-this.offsetTime)s=d[0].s[0],r=!0,m=0;else if(t>=d[d.length-1].t-this.offsetTime)s=d[d.length-1].s?d[d.length-1].s[0]:d[d.length-2].e[0],r=!0;else{for(var c,u,g,y=m,v=d.length-1,b=!0;b&&(c=d[y],!((u=d[y+1]).t-this.offsetTime>t));)y<v-1?y+=1:b=!1;if(g=this.keyframesMetadata[y]||{},m=y,!(r=1===c.h)){if(t>=u.t-this.offsetTime)p=1;else if(t<c.t-this.offsetTime)p=0;else{var _;g.__fnct?_=g.__fnct:(_=it.getBezierEasing(c.o.x,c.o.y,c.i.x,c.i.y).get,g.__fnct=_),p=_((t-(c.t-this.offsetTime))/(u.t-this.offsetTime-(c.t-this.offsetTime)))}a=u.s?u.s[0]:c.e[0]}s=c.s[0]}for(o=e._length,l=s.i[0].length,i.lastIndex=m,n=0;n<o;n+=1)for(h=0;h<l;h+=1)f=r?s.i[n][h]:s.i[n][h]+(a.i[n][h]-s.i[n][h])*p,e.i[n][h]=f,f=r?s.o[n][h]:s.o[n][h]+(a.o[n][h]-s.o[n][h])*p,e.o[n][h]=f,f=r?s.v[n][h]:s.v[n][h]+(a.v[n][h]-s.v[n][h])*p,e.v[n][h]=f}function i(){var e=this.comp.renderedFrame-this.offsetTime,i=this.keyframes[0].t-this.offsetTime,s=this.keyframes[this.keyframes.length-1].t-this.offsetTime,a=this._caching.lastFrame;return a!==t&&(a<i&&e<i||a>s&&e>s)||(this._caching.lastIndex=a<e?this._caching.lastIndex:0,this.interpolateShape(e,this.pv,this._caching)),this._caching.lastFrame=e,this.pv}function s(){this.paths=this.localShapeCollection}function a(t){(function(t,e){if(t._length!==e._length||t.c!==e.c)return!1;var i,s=t._length;for(i=0;i<s;i+=1)if(t.v[i][0]!==e.v[i][0]||t.v[i][1]!==e.v[i][1]||t.o[i][0]!==e.o[i][0]||t.o[i][1]!==e.o[i][1]||t.i[i][0]!==e.i[i][0]||t.i[i][1]!==e.i[i][1])return!1;return!0})(this.v,t)||(this.v=St.clone(t),this.localShapeCollection.releaseShapes(),this.localShapeCollection.addShape(this.v),this._mdf=!0,this.paths=this.localShapeCollection)}function n(){if(this.elem.globalData.frameId!==this.frameId)if(this.effectsSequence.length)if(this.lock)this.setVValue(this.pv);else{var t,e;this.lock=!0,this._mdf=!1,t=this.kf?this.pv:this.data.ks?this.data.ks.k:this.data.pt.k;var i=this.effectsSequence.length;for(e=0;e<i;e+=1)t=this.effectsSequence[e](t);this.setVValue(t),this.lock=!1,this.frameId=this.elem.globalData.frameId}else this._mdf=!1}function h(t,e,i){this.propType="shape",this.comp=t.comp,this.container=t,this.elem=t,this.data=e,this.k=!1,this.kf=!1,this._mdf=!1;var a=3===i?e.pt.k:e.ks.k;this.v=St.clone(a),this.pv=St.clone(this.v),this.localShapeCollection=Tt.newShapeCollection(),this.paths=this.localShapeCollection,this.paths.addShape(this.v),this.reset=s,this.effectsSequence=[]}function o(t){this.effectsSequence.push(t),this.container.addDynamicProperty(this)}function l(e,a,r){this.propType="shape",this.comp=e.comp,this.elem=e,this.container=e,this.offsetTime=e.data.st,this.keyframes=3===r?a.pt.k:a.ks.k,this.keyframesMetadata=[],this.k=!0,this.kf=!0;var n=this.keyframes[0].s[0].i.length;this.v=St.newElement(),this.v.setPathData(this.keyframes[0].s[0].c,n),this.pv=St.clone(this.v),this.localShapeCollection=Tt.newShapeCollection(),this.paths=this.localShapeCollection,this.paths.addShape(this.v),this.lastFrame=t,this.reset=s,this._caching={lastFrame:t,lastIndex:0},this.effectsSequence=[i.bind(this)]}h.prototype.interpolateShape=e,h.prototype.getValue=n,h.prototype.setVValue=a,h.prototype.addEffect=o,l.prototype.getValue=n,l.prototype.interpolateShape=e,l.prototype.setVValue=a,l.prototype.addEffect=o;var p=function(){var t=P;function e(t,e){this.v=St.newElement(),this.v.setPathData(!0,4),this.localShapeCollection=Tt.newShapeCollection(),this.paths=this.localShapeCollection,this.localShapeCollection.addShape(this.v),this.d=e.d,this.elem=t,this.comp=t.comp,this.frameId=-1,this.initDynamicPropertyContainer(t),this.p=_t.getProp(t,e.p,1,0,this),this.s=_t.getProp(t,e.s,1,0,this),this.dynamicProperties.length?this.k=!0:(this.k=!1,this.convertEllToPath())}return e.prototype={reset:s,getValue:function(){this.elem.globalData.frameId!==this.frameId&&(this.frameId=this.elem.globalData.frameId,this.iterateDynamicProperties(),this._mdf&&this.convertEllToPath())},convertEllToPath:function(){var e=this.p.v[0],i=this.p.v[1],s=this.s.v[0]/2,a=this.s.v[1]/2,r=3!==this.d,n=this.v;n.v[0][0]=e,n.v[0][1]=i-a,n.v[1][0]=r?e+s:e-s,n.v[1][1]=i,n.v[2][0]=e,n.v[2][1]=i+a,n.v[3][0]=r?e-s:e+s,n.v[3][1]=i,n.i[0][0]=r?e-s*t:e+s*t,n.i[0][1]=i-a,n.i[1][0]=r?e+s:e-s,n.i[1][1]=i-a*t,n.i[2][0]=r?e+s*t:e-s*t,n.i[2][1]=i+a,n.i[3][0]=r?e-s:e+s,n.i[3][1]=i+a*t,n.o[0][0]=r?e+s*t:e-s*t,n.o[0][1]=i-a,n.o[1][0]=r?e+s:e-s,n.o[1][1]=i+a*t,n.o[2][0]=r?e-s*t:e+s*t,n.o[2][1]=i+a,n.o[3][0]=r?e-s:e+s,n.o[3][1]=i-a*t}},r([xt],e),e}(),f=function(){function t(t,e){this.v=St.newElement(),this.v.setPathData(!0,0),this.elem=t,this.comp=t.comp,this.data=e,this.frameId=-1,this.d=e.d,this.initDynamicPropertyContainer(t),1===e.sy?(this.ir=_t.getProp(t,e.ir,0,0,this),this.is=_t.getProp(t,e.is,0,.01,this),this.convertToPath=this.convertStarToPath):this.convertToPath=this.convertPolygonToPath,this.pt=_t.getProp(t,e.pt,0,0,this),this.p=_t.getProp(t,e.p,1,0,this),this.r=_t.getProp(t,e.r,0,w,this),this.or=_t.getProp(t,e.or,0,0,this),this.os=_t.getProp(t,e.os,0,.01,this),this.localShapeCollection=Tt.newShapeCollection(),this.localShapeCollection.addShape(this.v),this.paths=this.localShapeCollection,this.dynamicProperties.length?this.k=!0:(this.k=!1,this.convertToPath())}return t.prototype={reset:s,getValue:function(){this.elem.globalData.frameId!==this.frameId&&(this.frameId=this.elem.globalData.frameId,this.iterateDynamicProperties(),this._mdf&&this.convertToPath())},convertStarToPath:function(){var t,e,i,s,a=2*Math.floor(this.pt.v),r=2*Math.PI/a,n=!0,h=this.or.v,o=this.ir.v,l=this.os.v,p=this.is.v,f=2*Math.PI*h/(2*a),m=2*Math.PI*o/(2*a),d=-Math.PI/2;d+=this.r.v;var c=3===this.data.d?-1:1;for(this.v._length=0,t=0;t<a;t+=1){i=n?l:p,s=n?f:m;var u=(e=n?h:o)*Math.cos(d),g=e*Math.sin(d),y=0===u&&0===g?0:g/Math.sqrt(u*u+g*g),v=0===u&&0===g?0:-u/Math.sqrt(u*u+g*g);u+=+this.p.v[0],g+=+this.p.v[1],this.v.setTripleAt(u,g,u-y*s*i*c,g-v*s*i*c,u+y*s*i*c,g+v*s*i*c,t,!0),n=!n,d+=r*c}},convertPolygonToPath:function(){var t,e=Math.floor(this.pt.v),i=2*Math.PI/e,s=this.or.v,a=this.os.v,r=2*Math.PI*s/(4*e),n=.5*-Math.PI,h=3===this.data.d?-1:1;for(n+=this.r.v,this.v._length=0,t=0;t<e;t+=1){var o=s*Math.cos(n),l=s*Math.sin(n),p=0===o&&0===l?0:l/Math.sqrt(o*o+l*l),f=0===o&&0===l?0:-o/Math.sqrt(o*o+l*l);o+=+this.p.v[0],l+=+this.p.v[1],this.v.setTripleAt(o,l,o-p*r*a*h,l-f*r*a*h,o+p*r*a*h,l+f*r*a*h,t,!0),n+=i*h}this.paths.length=0,this.paths[0]=this.v}},r([xt],t),t}(),m=function(){function t(t,e){this.v=St.newElement(),this.v.c=!0,this.localShapeCollection=Tt.newShapeCollection(),this.localShapeCollection.addShape(this.v),this.paths=this.localShapeCollection,this.elem=t,this.comp=t.comp,this.frameId=-1,this.d=e.d,this.initDynamicPropertyContainer(t),this.p=_t.getProp(t,e.p,1,0,this),this.s=_t.getProp(t,e.s,1,0,this),this.r=_t.getProp(t,e.r,0,0,this),this.dynamicProperties.length?this.k=!0:(this.k=!1,this.convertRectToPath())}return t.prototype={convertRectToPath:function(){var t=this.p.v[0],e=this.p.v[1],i=this.s.v[0]/2,s=this.s.v[1]/2,a=_(i,s,this.r.v),r=a*(1-P);this.v._length=0,2===this.d||1===this.d?(this.v.setTripleAt(t+i,e-s+a,t+i,e-s+a,t+i,e-s+r,0,!0),this.v.setTripleAt(t+i,e+s-a,t+i,e+s-r,t+i,e+s-a,1,!0),0!==a?(this.v.setTripleAt(t+i-a,e+s,t+i-a,e+s,t+i-r,e+s,2,!0),this.v.setTripleAt(t-i+a,e+s,t-i+r,e+s,t-i+a,e+s,3,!0),this.v.setTripleAt(t-i,e+s-a,t-i,e+s-a,t-i,e+s-r,4,!0),this.v.setTripleAt(t-i,e-s+a,t-i,e-s+r,t-i,e-s+a,5,!0),this.v.setTripleAt(t-i+a,e-s,t-i+a,e-s,t-i+r,e-s,6,!0),this.v.setTripleAt(t+i-a,e-s,t+i-r,e-s,t+i-a,e-s,7,!0)):(this.v.setTripleAt(t-i,e+s,t-i+r,e+s,t-i,e+s,2),this.v.setTripleAt(t-i,e-s,t-i,e-s+r,t-i,e-s,3))):(this.v.setTripleAt(t+i,e-s+a,t+i,e-s+r,t+i,e-s+a,0,!0),0!==a?(this.v.setTripleAt(t+i-a,e-s,t+i-a,e-s,t+i-r,e-s,1,!0),this.v.setTripleAt(t-i+a,e-s,t-i+r,e-s,t-i+a,e-s,2,!0),this.v.setTripleAt(t-i,e-s+a,t-i,e-s+a,t-i,e-s+r,3,!0),this.v.setTripleAt(t-i,e+s-a,t-i,e+s-r,t-i,e+s-a,4,!0),this.v.setTripleAt(t-i+a,e+s,t-i+a,e+s,t-i+r,e+s,5,!0),this.v.setTripleAt(t+i-a,e+s,t+i-r,e+s,t+i-a,e+s,6,!0),this.v.setTripleAt(t+i,e+s-a,t+i,e+s-a,t+i,e+s-r,7,!0)):(this.v.setTripleAt(t-i,e-s,t-i+r,e-s,t-i,e-s,1,!0),this.v.setTripleAt(t-i,e+s,t-i,e+s-r,t-i,e+s,2,!0),this.v.setTripleAt(t+i,e+s,t+i-r,e+s,t+i,e+s,3,!0)))},getValue:function(){this.elem.globalData.frameId!==this.frameId&&(this.frameId=this.elem.globalData.frameId,this.iterateDynamicProperties(),this._mdf&&this.convertRectToPath())},reset:s},r([xt],t),t}();var d={getShapeProp:function(t,e,i){var s;return 3===i||4===i?s=(3===i?e.pt:e.ks).k.length?new l(t,e,i):new h(t,e,i):5===i?s=new m(t,e):6===i?s=new p(t,e):7===i&&(s=new f(t,e)),s.k&&t.addDynamicProperty(s),s},getConstructorFunction:function(){return h},getKeyframedConstructorFunction:function(){return l}};return d}(),It=function(){var t=Math.cos,e=Math.sin,i=Math.tan,s=Math.round;function a(){return this.props[0]=1,this.props[1]=0,this.props[2]=0,this.props[3]=0,this.props[4]=0,this.props[5]=1,this.props[6]=0,this.props[7]=0,this.props[8]=0,this.props[9]=0,this.props[10]=1,this.props[11]=0,this.props[12]=0,this.props[13]=0,this.props[14]=0,this.props[15]=1,this}function r(i){if(0===i)return this;var s=t(i),a=e(i);return this._t(s,-a,0,0,a,s,0,0,0,0,1,0,0,0,0,1)}function n(i){if(0===i)return this;var s=t(i),a=e(i);return this._t(1,0,0,0,0,s,-a,0,0,a,s,0,0,0,0,1)}function h(i){if(0===i)return this;var s=t(i),a=e(i);return this._t(s,0,a,0,0,1,0,0,-a,0,s,0,0,0,0,1)}function l(i){if(0===i)return this;var s=t(i),a=e(i);return this._t(s,-a,0,0,a,s,0,0,0,0,1,0,0,0,0,1)}function p(t,e){return this._t(1,e,t,1,0,0)}function f(t,e){return this.shear(i(t),i(e))}function m(s,a){var r=t(a),n=e(a);return this._t(r,n,0,0,-n,r,0,0,0,0,1,0,0,0,0,1)._t(1,0,0,0,i(s),1,0,0,0,0,1,0,0,0,0,1)._t(r,-n,0,0,n,r,0,0,0,0,1,0,0,0,0,1)}function d(t,e,i){return i||0===i||(i=1),1===t&&1===e&&1===i?this:this._t(t,0,0,0,0,e,0,0,0,0,i,0,0,0,0,1)}function c(t,e,i,s,a,r,n,h,o,l,p,f,m,d,c,u){return this.props[0]=t,this.props[1]=e,this.props[2]=i,this.props[3]=s,this.props[4]=a,this.props[5]=r,this.props[6]=n,this.props[7]=h,this.props[8]=o,this.props[9]=l,this.props[10]=p,this.props[11]=f,this.props[12]=m,this.props[13]=d,this.props[14]=c,this.props[15]=u,this}function u(t,e,i){return i=i||0,0!==t||0!==e||0!==i?this._t(1,0,0,0,0,1,0,0,0,0,1,0,t,e,i,1):this}function g(t,e,i,s,a,r,n,h,o,l,p,f,m,d,c,u){var g=this.props;if(1===t&&0===e&&0===i&&0===s&&0===a&&1===r&&0===n&&0===h&&0===o&&0===l&&1===p&&0===f)return g[12]=g[12]*t+g[15]*m,g[13]=g[13]*r+g[15]*d,g[14]=g[14]*p+g[15]*c,g[15]*=u,this._identityCalculated=!1,this;var y=g[0],v=g[1],b=g[2],_=g[3],x=g[4],k=g[5],w=g[6],P=g[7],S=g[8],A=g[9],D=g[10],C=g[11],E=g[12],M=g[13],T=g[14],F=g[15];return g[0]=y*t+v*a+b*o+_*m,g[1]=y*e+v*r+b*l+_*d,g[2]=y*i+v*n+b*p+_*c,g[3]=y*s+v*h+b*f+_*u,g[4]=x*t+k*a+w*o+P*m,g[5]=x*e+k*r+w*l+P*d,g[6]=x*i+k*n+w*p+P*c,g[7]=x*s+k*h+w*f+P*u,g[8]=S*t+A*a+D*o+C*m,g[9]=S*e+A*r+D*l+C*d,g[10]=S*i+A*n+D*p+C*c,g[11]=S*s+A*h+D*f+C*u,g[12]=E*t+M*a+T*o+F*m,g[13]=E*e+M*r+T*l+F*d,g[14]=E*i+M*n+T*p+F*c,g[15]=E*s+M*h+T*f+F*u,this._identityCalculated=!1,this}function y(t){var e=t.props;return this.transform(e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7],e[8],e[9],e[10],e[11],e[12],e[13],e[14],e[15])}function v(){return this._identityCalculated||(this._identity=!(1!==this.props[0]||0!==this.props[1]||0!==this.props[2]||0!==this.props[3]||0!==this.props[4]||1!==this.props[5]||0!==this.props[6]||0!==this.props[7]||0!==this.props[8]||0!==this.props[9]||1!==this.props[10]||0!==this.props[11]||0!==this.props[12]||0!==this.props[13]||0!==this.props[14]||1!==this.props[15]),this._identityCalculated=!0),this._identity}function b(t){for(var e=0;e<16;){if(t.props[e]!==this.props[e])return!1;e+=1}return!0}function _(t){var e;for(e=0;e<16;e+=1)t.props[e]=this.props[e];return t}function x(t){var e;for(e=0;e<16;e+=1)this.props[e]=t[e]}function k(t,e,i){return{x:t*this.props[0]+e*this.props[4]+i*this.props[8]+this.props[12],y:t*this.props[1]+e*this.props[5]+i*this.props[9]+this.props[13],z:t*this.props[2]+e*this.props[6]+i*this.props[10]+this.props[14]}}function w(t,e,i){return t*this.props[0]+e*this.props[4]+i*this.props[8]+this.props[12]}function P(t,e,i){return t*this.props[1]+e*this.props[5]+i*this.props[9]+this.props[13]}function S(t,e,i){return t*this.props[2]+e*this.props[6]+i*this.props[10]+this.props[14]}function A(){var t=this.props[0]*this.props[5]-this.props[1]*this.props[4],e=this.props[5]/t,i=-this.props[1]/t,s=-this.props[4]/t,a=this.props[0]/t,r=(this.props[4]*this.props[13]-this.props[5]*this.props[12])/t,n=-(this.props[0]*this.props[13]-this.props[1]*this.props[12])/t,h=new It;return h.props[0]=e,h.props[1]=i,h.props[4]=s,h.props[5]=a,h.props[12]=r,h.props[13]=n,h}function D(t){return this.getInverseMatrix().applyToPointArray(t[0],t[1],t[2]||0)}function C(t){var e,i=t.length,s=[];for(e=0;e<i;e+=1)s[e]=D(t[e]);return s}function E(t,e,i){var s=o("float32",6);if(this.isIdentity())s[0]=t[0],s[1]=t[1],s[2]=e[0],s[3]=e[1],s[4]=i[0],s[5]=i[1];else{var a=this.props[0],r=this.props[1],n=this.props[4],h=this.props[5],l=this.props[12],p=this.props[13];s[0]=t[0]*a+t[1]*n+l,s[1]=t[0]*r+t[1]*h+p,s[2]=e[0]*a+e[1]*n+l,s[3]=e[0]*r+e[1]*h+p,s[4]=i[0]*a+i[1]*n+l,s[5]=i[0]*r+i[1]*h+p}return s}function M(t,e,i){return this.isIdentity()?[t,e,i]:[t*this.props[0]+e*this.props[4]+i*this.props[8]+this.props[12],t*this.props[1]+e*this.props[5]+i*this.props[9]+this.props[13],t*this.props[2]+e*this.props[6]+i*this.props[10]+this.props[14]]}function T(t,e){if(this.isIdentity())return t+","+e;var i=this.props;return Math.round(100*(t*i[0]+e*i[4]+i[12]))/100+","+Math.round(100*(t*i[1]+e*i[5]+i[13]))/100}function F(){for(var t=0,e=this.props,i="matrix3d(";t<16;)i+=s(1e4*e[t])/1e4,i+=15===t?")":",",t+=1;return i}function I(t){return t<1e-6&&t>0||t>-1e-6&&t<0?s(1e4*t)/1e4:t}function L(){var t=this.props;return"matrix("+I(t[0])+","+I(t[1])+","+I(t[4])+","+I(t[5])+","+I(t[12])+","+I(t[13])+")"}return function(){this.reset=a,this.rotate=r,this.rotateX=n,this.rotateY=h,this.rotateZ=l,this.skew=f,this.skewFromAxis=m,this.shear=p,this.scale=d,this.setTransform=c,this.translate=u,this.transform=g,this.multiply=y,this.applyToPoint=k,this.applyToX=w,this.applyToY=P,this.applyToZ=S,this.applyToPointArray=M,this.applyToTriplePoints=E,this.applyToPointStringified=T,this.toCSS=F,this.to2dCSS=L,this.clone=_,this.cloneFromProps=x,this.equals=b,this.inversePoints=C,this.inversePoint=D,this.getInverseMatrix=A,this._t=this.transform,this.isIdentity=v,this._identity=!0,this._identityCalculated=!1,this.props=o("float32",16),this.reset()}}();function Lt(t){return Lt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Lt(t)}var Bt={},zt="__[STANDALONE]__";function Vt(){et.searchAnimations()}Bt.play=et.play,Bt.pause=et.pause,Bt.setLocationHref=function(e){t=e},Bt.togglePause=et.togglePause,Bt.setSpeed=et.setSpeed,Bt.setDirection=et.setDirection,Bt.stop=et.stop,Bt.searchAnimations=Vt,Bt.registerAnimation=et.registerAnimation,Bt.loadAnimation=function(t){return et.loadAnimation(t)},Bt.setSubframeRendering=function(t){!function(t){f=!!t}(t)},Bt.resize=et.resize,Bt.goToAndStop=et.goToAndStop,Bt.destroy=et.destroy,Bt.setQuality=function(t){if("string"==typeof t)switch(t){case"high":W(200);break;default:case"medium":W(50);break;case"low":W(10)}else!isNaN(t)&&t>1&&W(t);H()>=50?S(!1):S(!0)},Bt.inBrowser=function(){return"undefined"!=typeof navigator},Bt.installPlugin=function(t,e){"expressions"===t&&(m=e)},Bt.freeze=et.freeze,Bt.unfreeze=et.unfreeze,Bt.setVolume=et.setVolume,Bt.mute=et.mute,Bt.unmute=et.unmute,Bt.getRegisteredAnimations=et.getRegisteredAnimations,Bt.useWebWorker=function(t){e=!!t},Bt.setIDPrefix=function(t){c=t},Bt.__getFactory=function(t){switch(t){case"propertyFactory":return _t;case"shapePropertyFactory":return Ft;case"matrix":return It;default:return null}},Bt.version="5.13.0";var Rt="";if(zt){var Ot=document.getElementsByTagName("script"),Nt=Ot[Ot.length-1]||{src:""};Rt=Nt.src?Nt.src.replace(/^[^\?]+\??/,""):"",function(t){for(var e=Rt.split("&"),i=0;i<e.length;i+=1){var s=e[i].split("=");if(decodeURIComponent(s[0])==t)return decodeURIComponent(s[1])}return null}("renderer")}var qt=setInterval((function(){"complete"===document.readyState&&(clearInterval(qt),Vt())}),100);try{"object"===("undefined"==typeof exports?"undefined":Lt(exports))&&"undefined"!=typeof module||"function"==typeof define&&define.amd||(window.bodymovin=Bt)}catch(t){}var jt=function(){var t={},e={};return t.registerModifier=function(t,i){e[t]||(e[t]=i)},t.getModifier=function(t,i,s){return new e[t](i,s)},t}();function Wt(){}function Ht(){}function Xt(){}Wt.prototype.initModifierProperties=function(){},Wt.prototype.addShapeToModifier=function(){},Wt.prototype.addShape=function(t){if(!this.closed){t.sh.container.addDynamicProperty(t.sh);var e={shape:t.sh,data:t,localShapeCollection:Tt.newShapeCollection()};this.shapes.push(e),this.addShapeToModifier(e),this._isAnimated&&t.setAsAnimated()}},Wt.prototype.init=function(t,e){this.shapes=[],this.elem=t,this.initDynamicPropertyContainer(t),this.initModifierProperties(t,e),this.frameId=i,this.closed=!1,this.k=!1,this.dynamicProperties.length?this.k=!0:this.getValue(!0)},Wt.prototype.processKeys=function(){this.elem.globalData.frameId!==this.frameId&&(this.frameId=this.elem.globalData.frameId,this.iterateDynamicProperties())},r([xt],Wt),r([Wt],Ht),Ht.prototype.initModifierProperties=function(t,e){this.s=_t.getProp(t,e.s,0,.01,this),this.e=_t.getProp(t,e.e,0,.01,this),this.o=_t.getProp(t,e.o,0,0,this),this.sValue=0,this.eValue=0,this.getValue=this.processKeys,this.m=e.m,this._isAnimated=!!this.s.effectsSequence.length||!!this.e.effectsSequence.length||!!this.o.effectsSequence.length},Ht.prototype.addShapeToModifier=function(t){t.pathsData=[]},Ht.prototype.calculateShapeEdges=function(t,e,i,s,a){var r=[];e<=1?r.push({s:t,e:e}):t>=1?r.push({s:t-1,e:e-1}):(r.push({s:t,e:1}),r.push({s:0,e:e-1}));var n,h,o=[],l=r.length;for(n=0;n<l;n+=1){var p,f;if(!((h=r[n]).e*a<s||h.s*a>s+i))p=h.s*a<=s?0:(h.s*a-s)/i,f=h.e*a>=s+i?1:(h.e*a-s)/i,o.push([p,f])}return o.length||o.push([0,0]),o},Ht.prototype.releasePathsData=function(t){var e,i=t.length;for(e=0;e<i;e+=1)nt.release(t[e]);return t.length=0,t},Ht.prototype.processShapes=function(t){var e,i,s,a;if(this._mdf||t){var r=this.o.v%360/360;if(r<0&&(r+=1),(e=this.s.v>1?1+r:this.s.v<0?0+r:this.s.v+r)>(i=this.e.v>1?1+r:this.e.v<0?0+r:this.e.v+r)){var n=e;e=i,i=n}e=1e-4*Math.round(1e4*e),i=1e-4*Math.round(1e4*i),this.sValue=e,this.eValue=i}else e=this.sValue,i=this.eValue;var h,o,l,p,f,m=this.shapes.length,d=0;if(i===e)for(a=0;a<m;a+=1)this.shapes[a].localShapeCollection.releaseShapes(),this.shapes[a].shape._mdf=!0,this.shapes[a].shape.paths=this.shapes[a].localShapeCollection,this._mdf&&(this.shapes[a].pathsData.length=0);else if(1===i&&0===e||0===i&&1===e){if(this._mdf)for(a=0;a<m;a+=1)this.shapes[a].pathsData.length=0,this.shapes[a].shape._mdf=!0}else{var c,u,g=[];for(a=0;a<m;a+=1)if((c=this.shapes[a]).shape._mdf||this._mdf||t||2===this.m){if(o=(s=c.shape.paths)._length,f=0,!c.shape._mdf&&c.pathsData.length)f=c.totalShapeLength;else{for(l=this.releasePathsData(c.pathsData),h=0;h<o;h+=1)p=ht.getSegmentsLength(s.shapes[h]),l.push(p),f+=p.totalLength;c.totalShapeLength=f,c.pathsData=l}d+=f,c.shape._mdf=!0}else c.shape.paths=c.localShapeCollection;var y,v=e,b=i,_=0;for(a=m-1;a>=0;a-=1)if((c=this.shapes[a]).shape._mdf){for((u=c.localShapeCollection).releaseShapes(),2===this.m&&m>1?(y=this.calculateShapeEdges(e,i,c.totalShapeLength,_,d),_+=c.totalShapeLength):y=[[v,b]],o=y.length,h=0;h<o;h+=1){v=y[h][0],b=y[h][1],g.length=0,b<=1?g.push({s:c.totalShapeLength*v,e:c.totalShapeLength*b}):v>=1?g.push({s:c.totalShapeLength*(v-1),e:c.totalShapeLength*(b-1)}):(g.push({s:c.totalShapeLength*v,e:c.totalShapeLength}),g.push({s:0,e:c.totalShapeLength*(b-1)}));var x=this.addShapes(c,g[0]);if(g[0].s!==g[0].e){if(g.length>1)if(c.shape.paths.shapes[c.shape.paths._length-1].c){var k=x.pop();this.addPaths(x,u),x=this.addShapes(c,g[1],k)}else this.addPaths(x,u),x=this.addShapes(c,g[1]);this.addPaths(x,u)}}c.shape.paths=u}}},Ht.prototype.addPaths=function(t,e){var i,s=t.length;for(i=0;i<s;i+=1)e.addShape(t[i])},Ht.prototype.addSegment=function(t,e,i,s,a,r,n){a.setXYAt(e[0],e[1],"o",r),a.setXYAt(i[0],i[1],"i",r+1),n&&a.setXYAt(t[0],t[1],"v",r),a.setXYAt(s[0],s[1],"v",r+1)},Ht.prototype.addSegmentFromArray=function(t,e,i,s){e.setXYAt(t[1],t[5],"o",i),e.setXYAt(t[2],t[6],"i",i+1),s&&e.setXYAt(t[0],t[4],"v",i),e.setXYAt(t[3],t[7],"v",i+1)},Ht.prototype.addShapes=function(t,e,i){var s,a,r,n,h,o,l,p,f=t.pathsData,m=t.shape.paths.shapes,d=t.shape.paths._length,c=0,u=[],g=!0;for(i?(h=i._length,p=i._length):(i=St.newElement(),h=0,p=0),u.push(i),s=0;s<d;s+=1){for(o=f[s].lengths,i.c=m[s].c,r=m[s].c?o.length:o.length+1,a=1;a<r;a+=1)if(c+(n=o[a-1]).addedLength<e.s)c+=n.addedLength,i.c=!1;else{if(c>e.e){i.c=!1;break}e.s<=c&&e.e>=c+n.addedLength?(this.addSegment(m[s].v[a-1],m[s].o[a-1],m[s].i[a],m[s].v[a],i,h,g),g=!1):(l=ht.getNewSegment(m[s].v[a-1],m[s].v[a],m[s].o[a-1],m[s].i[a],(e.s-c)/n.addedLength,(e.e-c)/n.addedLength,o[a-1]),this.addSegmentFromArray(l,i,h,g),g=!1,i.c=!1),c+=n.addedLength,h+=1}if(m[s].c&&o.length){if(n=o[a-1],c<=e.e){var y=o[a-1].addedLength;e.s<=c&&e.e>=c+y?(this.addSegment(m[s].v[a-1],m[s].o[a-1],m[s].i[0],m[s].v[0],i,h,g),g=!1):(l=ht.getNewSegment(m[s].v[a-1],m[s].v[0],m[s].o[a-1],m[s].i[0],(e.s-c)/y,(e.e-c)/y,o[a-1]),this.addSegmentFromArray(l,i,h,g),g=!1,i.c=!1)}else i.c=!1;c+=n.addedLength,h+=1}if(i._length&&(i.setXYAt(i.v[p][0],i.v[p][1],"i",p),i.setXYAt(i.v[i._length-1][0],i.v[i._length-1][1],"o",i._length-1)),c>e.e)break;s<d-1&&(i=St.newElement(),g=!0,u.push(i),h=0)}return u},r([Wt],Xt),Xt.prototype.initModifierProperties=function(t,e){this.getValue=this.processKeys,this.amount=_t.getProp(t,e.a,0,null,this),this._isAnimated=!!this.amount.effectsSequence.length},Xt.prototype.processPath=function(t,e){var i=e/100,s=[0,0],a=t._length,r=0;for(r=0;r<a;r+=1)s[0]+=t.v[r][0],s[1]+=t.v[r][1];s[0]/=a,s[1]/=a;var n,h,o,l,p,f,m=St.newElement();for(m.c=t.c,r=0;r<a;r+=1)n=t.v[r][0]+(s[0]-t.v[r][0])*i,h=t.v[r][1]+(s[1]-t.v[r][1])*i,o=t.o[r][0]+(s[0]-t.o[r][0])*-i,l=t.o[r][1]+(s[1]-t.o[r][1])*-i,p=t.i[r][0]+(s[0]-t.i[r][0])*-i,f=t.i[r][1]+(s[1]-t.i[r][1])*-i,m.setTripleAt(n,h,o,l,p,f,r);return m},Xt.prototype.processShapes=function(t){var e,i,s,a,r,n,h=this.shapes.length,o=this.amount.v;if(0!==o)for(i=0;i<h;i+=1){if(n=(r=this.shapes[i]).localShapeCollection,r.shape._mdf||this._mdf||t)for(n.releaseShapes(),r.shape._mdf=!0,e=r.shape.paths.shapes,a=r.shape.paths._length,s=0;s<a;s+=1)n.addShape(this.processPath(e[s],o));r.shape.paths=r.localShapeCollection}this.dynamicProperties.length||(this._mdf=!1)};var Yt=function(){var t=[0,0];function e(t,e,i){if(this.elem=t,this.frameId=-1,this.propType="transform",this.data=e,this.v=new It,this.pre=new It,this.appliedTransformations=0,this.initDynamicPropertyContainer(i||t),e.p&&e.p.s?(this.px=_t.getProp(t,e.p.x,0,0,this),this.py=_t.getProp(t,e.p.y,0,0,this),e.p.z&&(this.pz=_t.getProp(t,e.p.z,0,0,this))):this.p=_t.getProp(t,e.p||{k:[0,0,0]},1,0,this),e.rx){if(this.rx=_t.getProp(t,e.rx,0,w,this),this.ry=_t.getProp(t,e.ry,0,w,this),this.rz=_t.getProp(t,e.rz,0,w,this),e.or.k[0].ti){var s,a=e.or.k.length;for(s=0;s<a;s+=1)e.or.k[s].to=null,e.or.k[s].ti=null}this.or=_t.getProp(t,e.or,1,w,this),this.or.sh=!0}else this.r=_t.getProp(t,e.r||{k:0},0,w,this);e.sk&&(this.sk=_t.getProp(t,e.sk,0,w,this),this.sa=_t.getProp(t,e.sa,0,w,this)),this.a=_t.getProp(t,e.a||{k:[0,0,0]},1,0,this),this.s=_t.getProp(t,e.s||{k:[100,100,100]},1,.01,this),e.o?this.o=_t.getProp(t,e.o,0,.01,t):this.o={_mdf:!1,v:1},this._isDirty=!0,this.dynamicProperties.length||this.getValue(!0)}return e.prototype={applyToMatrix:function(t){var e=this._mdf;this.iterateDynamicProperties(),this._mdf=this._mdf||e,this.a&&t.translate(-this.a.v[0],-this.a.v[1],this.a.v[2]),this.s&&t.scale(this.s.v[0],this.s.v[1],this.s.v[2]),this.sk&&t.skewFromAxis(-this.sk.v,this.sa.v),this.r?t.rotate(-this.r.v):t.rotateZ(-this.rz.v).rotateY(this.ry.v).rotateX(this.rx.v).rotateZ(-this.or.v[2]).rotateY(this.or.v[1]).rotateX(this.or.v[0]),this.data.p.s?this.data.p.z?t.translate(this.px.v,this.py.v,-this.pz.v):t.translate(this.px.v,this.py.v,0):t.translate(this.p.v[0],this.p.v[1],-this.p.v[2])},getValue:function(e){if(this.elem.globalData.frameId!==this.frameId){if(this._isDirty&&(this.precalculateMatrix(),this._isDirty=!1),this.iterateDynamicProperties(),this._mdf||e){var i;if(this.v.cloneFromProps(this.pre.props),this.appliedTransformations<1&&this.v.translate(-this.a.v[0],-this.a.v[1],this.a.v[2]),this.appliedTransformations<2&&this.v.scale(this.s.v[0],this.s.v[1],this.s.v[2]),this.sk&&this.appliedTransformations<3&&this.v.skewFromAxis(-this.sk.v,this.sa.v),this.r&&this.appliedTransformations<4?this.v.rotate(-this.r.v):!this.r&&this.appliedTransformations<4&&this.v.rotateZ(-this.rz.v).rotateY(this.ry.v).rotateX(this.rx.v).rotateZ(-this.or.v[2]).rotateY(this.or.v[1]).rotateX(this.or.v[0]),this.autoOriented){var s,a;if(i=this.elem.globalData.frameRate,this.p&&this.p.keyframes&&this.p.getValueAtTime)this.p._caching.lastFrame+this.p.offsetTime<=this.p.keyframes[0].t?(s=this.p.getValueAtTime((this.p.keyframes[0].t+.01)/i,0),a=this.p.getValueAtTime(this.p.keyframes[0].t/i,0)):this.p._caching.lastFrame+this.p.offsetTime>=this.p.keyframes[this.p.keyframes.length-1].t?(s=this.p.getValueAtTime(this.p.keyframes[this.p.keyframes.length-1].t/i,0),a=this.p.getValueAtTime((this.p.keyframes[this.p.keyframes.length-1].t-.05)/i,0)):(s=this.p.pv,a=this.p.getValueAtTime((this.p._caching.lastFrame+this.p.offsetTime-.01)/i,this.p.offsetTime));else if(this.px&&this.px.keyframes&&this.py.keyframes&&this.px.getValueAtTime&&this.py.getValueAtTime){s=[],a=[];var r=this.px,n=this.py;r._caching.lastFrame+r.offsetTime<=r.keyframes[0].t?(s[0]=r.getValueAtTime((r.keyframes[0].t+.01)/i,0),s[1]=n.getValueAtTime((n.keyframes[0].t+.01)/i,0),a[0]=r.getValueAtTime(r.keyframes[0].t/i,0),a[1]=n.getValueAtTime(n.keyframes[0].t/i,0)):r._caching.lastFrame+r.offsetTime>=r.keyframes[r.keyframes.length-1].t?(s[0]=r.getValueAtTime(r.keyframes[r.keyframes.length-1].t/i,0),s[1]=n.getValueAtTime(n.keyframes[n.keyframes.length-1].t/i,0),a[0]=r.getValueAtTime((r.keyframes[r.keyframes.length-1].t-.01)/i,0),a[1]=n.getValueAtTime((n.keyframes[n.keyframes.length-1].t-.01)/i,0)):(s=[r.pv,n.pv],a[0]=r.getValueAtTime((r._caching.lastFrame+r.offsetTime-.01)/i,r.offsetTime),a[1]=n.getValueAtTime((n._caching.lastFrame+n.offsetTime-.01)/i,n.offsetTime))}else s=a=t;this.v.rotate(-Math.atan2(s[1]-a[1],s[0]-a[0]))}this.data.p&&this.data.p.s?this.data.p.z?this.v.translate(this.px.v,this.py.v,-this.pz.v):this.v.translate(this.px.v,this.py.v,0):this.v.translate(this.p.v[0],this.p.v[1],-this.p.v[2])}this.frameId=this.elem.globalData.frameId}},precalculateMatrix:function(){if(this.appliedTransformations=0,this.pre.reset(),!this.a.effectsSequence.length&&(this.pre.translate(-this.a.v[0],-this.a.v[1],this.a.v[2]),this.appliedTransformations=1,!this.s.effectsSequence.length)){if(this.pre.scale(this.s.v[0],this.s.v[1],this.s.v[2]),this.appliedTransformations=2,this.sk){if(this.sk.effectsSequence.length||this.sa.effectsSequence.length)return;this.pre.skewFromAxis(-this.sk.v,this.sa.v),this.appliedTransformations=3}this.r?this.r.effectsSequence.length||(this.pre.rotate(-this.r.v),this.appliedTransformations=4):this.rz.effectsSequence.length||this.ry.effectsSequence.length||this.rx.effectsSequence.length||this.or.effectsSequence.length||(this.pre.rotateZ(-this.rz.v).rotateY(this.ry.v).rotateX(this.rx.v).rotateZ(-this.or.v[2]).rotateY(this.or.v[1]).rotateX(this.or.v[0]),this.appliedTransformations=4)}},autoOrient:function(){}},r([xt],e),e.prototype.addDynamicProperty=function(t){this._addDynamicProperty(t),this.elem.addDynamicProperty(t),this._isDirty=!0},e.prototype._addDynamicProperty=xt.prototype.addDynamicProperty,{getTransformProperty:function(t,i,s){return new e(t,i,s)}}}();function Gt(){}function Kt(){}function Jt(t,e){return 1e5*Math.abs(t-e)<=Math.min(Math.abs(t),Math.abs(e))}function Zt(t){return Math.abs(t)<=1e-5}function Ut(t,e,i){return t*(1-i)+e*i}function Qt(t,e,i){return[Ut(t[0],e[0],i),Ut(t[1],e[1],i)]}function $t(t,e,i,s){return[3*e-t-3*i+s,3*t-6*e+3*i,-3*t+3*e,t]}function te(t){return new ee(t,t,t,t,!1)}function ee(t,e,i,s,a){a&&pe(t,e)&&(e=Qt(t,s,1/3)),a&&pe(i,s)&&(i=Qt(t,s,2/3));var r=$t(t[0],e[0],i[0],s[0]),n=$t(t[1],e[1],i[1],s[1]);this.a=[r[0],n[0]],this.b=[r[1],n[1]],this.c=[r[2],n[2]],this.d=[r[3],n[3]],this.points=[t,e,i,s]}function ie(t,e){var i=t.points[0][e],s=t.points[t.points.length-1][e];if(i>s){var a=s;s=i,i=a}for(var r=function(t,e,i){if(0===t)return[];var s=e*e-4*t*i;if(s<0)return[];var a=-e/(2*t);if(0===s)return[a];var r=Math.sqrt(s)/(2*t);return[a-r,a+r]}(3*t.a[e],2*t.b[e],t.c[e]),n=0;n<r.length;n+=1)if(r[n]>0&&r[n]<1){var h=t.point(r[n])[e];h<i?i=h:h>s&&(s=h)}return{min:i,max:s}}function se(t,e,i){var s=t.boundingBox();return{cx:s.cx,cy:s.cy,width:s.width,height:s.height,bez:t,t:(e+i)/2,t1:e,t2:i}}function ae(t){var e=t.bez.split(.5);return[se(e[0],t.t1,t.t),se(e[1],t.t,t.t2)]}function re(t,e,i,s,a,r){var n,h;if(n=t,h=e,2*Math.abs(n.cx-h.cx)<n.width+h.width&&2*Math.abs(n.cy-h.cy)<n.height+h.height)if(i>=r||t.width<=s&&t.height<=s&&e.width<=s&&e.height<=s)a.push([t.t,e.t]);else{var o=ae(t),l=ae(e);re(o[0],l[0],i+1,s,a,r),re(o[0],l[1],i+1,s,a,r),re(o[1],l[0],i+1,s,a,r),re(o[1],l[1],i+1,s,a,r)}}function ne(t,e){return[t[1]*e[2]-t[2]*e[1],t[2]*e[0]-t[0]*e[2],t[0]*e[1]-t[1]*e[0]]}function he(t,e,i,s){var a=[t[0],t[1],1],r=[e[0],e[1],1],n=[i[0],i[1],1],h=[s[0],s[1],1],o=ne(ne(a,r),ne(n,h));return Zt(o[2])?null:[o[0]/o[2],o[1]/o[2]]}function oe(t,e,i){return[t[0]+Math.cos(e)*i,t[1]-Math.sin(e)*i]}function le(t,e){return Math.hypot(t[0]-e[0],t[1]-e[1])}function pe(t,e){return Jt(t[0],e[0])&&Jt(t[1],e[1])}function fe(){}function me(t,e,i,s,a,r,n){var h=i-Math.PI/2,o=i+Math.PI/2,l=e[0]+Math.cos(i)*s*a,p=e[1]-Math.sin(i)*s*a;t.setTripleAt(l,p,l+Math.cos(h)*r,p-Math.sin(h)*r,l+Math.cos(o)*n,p-Math.sin(o)*n,t.length())}function de(t,e){var i,s,a,r,n=0===e?t.length()-1:e-1,h=(e+1)%t.length(),o=t.v[n],l=t.v[h],p=(i=o,a=[(s=l)[0]-i[0],s[1]-i[1]],r=.5*-Math.PI,[Math.cos(r)*a[0]-Math.sin(r)*a[1],Math.sin(r)*a[0]+Math.cos(r)*a[1]]);return Math.atan2(0,1)-Math.atan2(p[1],p[0])}function ce(t,e,i,s,a,r,n){var h=de(e,i),o=e.v[i%e._length],l=e.v[0===i?e._length-1:i-1],p=e.v[(i+1)%e._length],f=2===r?Math.sqrt(Math.pow(o[0]-l[0],2)+Math.pow(o[1]-l[1],2)):0,m=2===r?Math.sqrt(Math.pow(o[0]-p[0],2)+Math.pow(o[1]-p[1],2)):0;me(t,e.v[i%e._length],h,n,s,m/(2*(a+1)),f/(2*(a+1)))}function ue(t,e,i,s,a,r){for(var n=0;n<s;n+=1){var h=(n+1)/(s+1),o=2===a?Math.sqrt(Math.pow(e.points[3][0]-e.points[0][0],2)+Math.pow(e.points[3][1]-e.points[0][1],2)):0,l=e.normalAngle(h);me(t,e.point(h),l,r,i,o/(2*(s+1)),o/(2*(s+1))),r=-r}return r}function ge(t,e,i){var s=Math.atan2(e[0]-t[0],e[1]-t[1]);return[oe(t,s,i),oe(e,s,i)]}function ye(t,e){var i,s,a,r,n,h,o;i=(o=ge(t.points[0],t.points[1],e))[0],s=o[1],a=(o=ge(t.points[1],t.points[2],e))[0],r=o[1],n=(o=ge(t.points[2],t.points[3],e))[0],h=o[1];var l=he(i,s,a,r);null===l&&(l=s);var p=he(n,h,a,r);return null===p&&(p=n),new ee(i,l,p,h)}function ve(t,e,i,s,a){var r=e.points[3],n=i.points[0];if(3===s)return r;if(pe(r,n))return r;if(2===s){var h=-e.tangentAngle(1),o=-i.tangentAngle(0)+Math.PI,l=he(r,oe(r,h+Math.PI/2,100),n,oe(n,h+Math.PI/2,100)),p=l?le(l,r):le(r,n)/2,f=oe(r,h,2*p*P);return t.setXYAt(f[0],f[1],"o",t.length()-1),f=oe(n,o,2*p*P),t.setTripleAt(n[0],n[1],n[0],n[1],f[0],f[1],t.length()),n}var m=he(pe(r,e.points[2])?e.points[0]:e.points[2],r,n,pe(n,i.points[1])?i.points[3]:i.points[1]);return m&&le(m,r)<a?(t.setTripleAt(m[0],m[1],m[0],m[1],m[0],m[1],t.length()),m):r}function be(t,e){var i=t.intersections(e);return i.length&&Jt(i[0][0],1)&&i.shift(),i.length?i[0]:null}function _e(t,e){var i=t.slice(),s=e.slice(),a=be(t[t.length-1],e[0]);return a&&(i[t.length-1]=t[t.length-1].split(a[0])[0],s[0]=e[0].split(a[1])[1]),t.length>1&&e.length>1&&(a=be(t[0],e[e.length-1]))?[[t[0].split(a[0])[0]],[e[e.length-1].split(a[1])[1]]]:[i,s]}function xe(t,e){var i,s,a,r,n=t.inflectionPoints();if(0===n.length)return[ye(t,e)];if(1===n.length||Jt(n[1],1))return i=(a=t.split(n[0]))[0],s=a[1],[ye(i,e),ye(s,e)];i=(a=t.split(n[0]))[0];var h=(n[1]-n[0])/(1-n[0]);return r=(a=a[1].split(h))[0],s=a[1],[ye(i,e),ye(r,e),ye(s,e)]}function ke(){}function we(t){for(var e=t.fStyle?t.fStyle.split(" "):[],i="normal",s="normal",a=e.length,r=0;r<a;r+=1)switch(e[r].toLowerCase()){case"italic":s="italic";break;case"bold":i="700";break;case"black":i="900";break;case"medium":i="500";break;case"regular":case"normal":i="400";break;case"light":case"thin":i="200"}return{style:s,weight:t.fWeight||i}}r([Wt],Gt),Gt.prototype.initModifierProperties=function(t,e){this.getValue=this.processKeys,this.c=_t.getProp(t,e.c,0,null,this),this.o=_t.getProp(t,e.o,0,null,this),this.tr=Yt.getTransformProperty(t,e.tr,this),this.so=_t.getProp(t,e.tr.so,0,.01,this),this.eo=_t.getProp(t,e.tr.eo,0,.01,this),this.data=e,this.dynamicProperties.length||this.getValue(!0),this._isAnimated=!!this.dynamicProperties.length,this.pMatrix=new It,this.rMatrix=new It,this.sMatrix=new It,this.tMatrix=new It,this.matrix=new It},Gt.prototype.applyTransforms=function(t,e,i,s,a,r){var n=r?-1:1,h=s.s.v[0]+(1-s.s.v[0])*(1-a),o=s.s.v[1]+(1-s.s.v[1])*(1-a);t.translate(s.p.v[0]*n*a,s.p.v[1]*n*a,s.p.v[2]),e.translate(-s.a.v[0],-s.a.v[1],s.a.v[2]),e.rotate(-s.r.v*n*a),e.translate(s.a.v[0],s.a.v[1],s.a.v[2]),i.translate(-s.a.v[0],-s.a.v[1],s.a.v[2]),i.scale(r?1/h:h,r?1/o:o),i.translate(s.a.v[0],s.a.v[1],s.a.v[2])},Gt.prototype.init=function(t,e,i,s){for(this.elem=t,this.arr=e,this.pos=i,this.elemsData=s,this._currentCopies=0,this._elements=[],this._groups=[],this.frameId=-1,this.initDynamicPropertyContainer(t),this.initModifierProperties(t,e[i]);i>0;)i-=1,this._elements.unshift(e[i]);this.dynamicProperties.length?this.k=!0:this.getValue(!0)},Gt.prototype.resetElements=function(t){var e,i=t.length;for(e=0;e<i;e+=1)t[e]._processed=!1,"gr"===t[e].ty&&this.resetElements(t[e].it)},Gt.prototype.cloneElements=function(t){var e=JSON.parse(JSON.stringify(t));return this.resetElements(e),e},Gt.prototype.changeGroupRender=function(t,e){var i,s=t.length;for(i=0;i<s;i+=1)t[i]._render=e,"gr"===t[i].ty&&this.changeGroupRender(t[i].it,e)},Gt.prototype.processShapes=function(t){var e,i,s,a,r,n=!1;if(this._mdf||t){var h,o=Math.ceil(this.c.v);if(this._groups.length<o){for(;this._groups.length<o;){var l={it:this.cloneElements(this._elements),ty:"gr"};l.it.push({a:{a:0,ix:1,k:[0,0]},nm:"Transform",o:{a:0,ix:7,k:100},p:{a:0,ix:2,k:[0,0]},r:{a:1,ix:6,k:[{s:0,e:0,t:0},{s:0,e:0,t:1}]},s:{a:0,ix:3,k:[100,100]},sa:{a:0,ix:5,k:0},sk:{a:0,ix:4,k:0},ty:"tr"}),this.arr.splice(0,0,l),this._groups.splice(0,0,l),this._currentCopies+=1}this.elem.reloadShapes(),n=!0}for(r=0,s=0;s<=this._groups.length-1;s+=1){if(h=r<o,this._groups[s]._render=h,this.changeGroupRender(this._groups[s].it,h),!h){var p=this.elemsData[s].it,f=p[p.length-1];0!==f.transform.op.v?(f.transform.op._mdf=!0,f.transform.op.v=0):f.transform.op._mdf=!1}r+=1}this._currentCopies=o;var m=this.o.v,d=m%1,c=m>0?Math.floor(m):Math.ceil(m),u=this.pMatrix.props,g=this.rMatrix.props,y=this.sMatrix.props;this.pMatrix.reset(),this.rMatrix.reset(),this.sMatrix.reset(),this.tMatrix.reset(),this.matrix.reset();var v,b,_=0;if(m>0){for(;_<c;)this.applyTransforms(this.pMatrix,this.rMatrix,this.sMatrix,this.tr,1,!1),_+=1;d&&(this.applyTransforms(this.pMatrix,this.rMatrix,this.sMatrix,this.tr,d,!1),_+=d)}else if(m<0){for(;_>c;)this.applyTransforms(this.pMatrix,this.rMatrix,this.sMatrix,this.tr,1,!0),_-=1;d&&(this.applyTransforms(this.pMatrix,this.rMatrix,this.sMatrix,this.tr,-d,!0),_-=d)}for(s=1===this.data.m?0:this._currentCopies-1,a=1===this.data.m?1:-1,r=this._currentCopies;r;){if(b=(i=(e=this.elemsData[s].it)[e.length-1].transform.mProps.v.props).length,e[e.length-1].transform.mProps._mdf=!0,e[e.length-1].transform.op._mdf=!0,e[e.length-1].transform.op.v=1===this._currentCopies?this.so.v:this.so.v+(this.eo.v-this.so.v)*(s/(this._currentCopies-1)),0!==_){for((0!==s&&1===a||s!==this._currentCopies-1&&-1===a)&&this.applyTransforms(this.pMatrix,this.rMatrix,this.sMatrix,this.tr,1,!1),this.matrix.transform(g[0],g[1],g[2],g[3],g[4],g[5],g[6],g[7],g[8],g[9],g[10],g[11],g[12],g[13],g[14],g[15]),this.matrix.transform(y[0],y[1],y[2],y[3],y[4],y[5],y[6],y[7],y[8],y[9],y[10],y[11],y[12],y[13],y[14],y[15]),this.matrix.transform(u[0],u[1],u[2],u[3],u[4],u[5],u[6],u[7],u[8],u[9],u[10],u[11],u[12],u[13],u[14],u[15]),v=0;v<b;v+=1)i[v]=this.matrix.props[v];this.matrix.reset()}else for(this.matrix.reset(),v=0;v<b;v+=1)i[v]=this.matrix.props[v];_+=1,r-=1,s+=a}}else for(r=this._currentCopies,s=0,a=1;r;)i=(e=this.elemsData[s].it)[e.length-1].transform.mProps.v.props,e[e.length-1].transform.mProps._mdf=!1,e[e.length-1].transform.op._mdf=!1,r-=1,s+=a;return n},Gt.prototype.addShape=function(){},r([Wt],Kt),Kt.prototype.initModifierProperties=function(t,e){this.getValue=this.processKeys,this.rd=_t.getProp(t,e.r,0,null,this),this._isAnimated=!!this.rd.effectsSequence.length},Kt.prototype.processPath=function(t,e){var i,s=St.newElement();s.c=t.c;var a,r,n,h,o,l,p,f,m,d,c,u,g=t._length,y=0;for(i=0;i<g;i+=1)a=t.v[i],n=t.o[i],r=t.i[i],a[0]===n[0]&&a[1]===n[1]&&a[0]===r[0]&&a[1]===r[1]?0!==i&&i!==g-1||t.c?(h=0===i?t.v[g-1]:t.v[i-1],l=(o=Math.sqrt(Math.pow(a[0]-h[0],2)+Math.pow(a[1]-h[1],2)))?Math.min(o/2,e)/o:0,p=c=a[0]+(h[0]-a[0])*l,f=u=a[1]-(a[1]-h[1])*l,m=p-(p-a[0])*P,d=f-(f-a[1])*P,s.setTripleAt(p,f,m,d,c,u,y),y+=1,h=i===g-1?t.v[0]:t.v[i+1],l=(o=Math.sqrt(Math.pow(a[0]-h[0],2)+Math.pow(a[1]-h[1],2)))?Math.min(o/2,e)/o:0,p=m=a[0]+(h[0]-a[0])*l,f=d=a[1]+(h[1]-a[1])*l,c=p-(p-a[0])*P,u=f-(f-a[1])*P,s.setTripleAt(p,f,m,d,c,u,y),y+=1):(s.setTripleAt(a[0],a[1],n[0],n[1],r[0],r[1],y),y+=1):(s.setTripleAt(t.v[i][0],t.v[i][1],t.o[i][0],t.o[i][1],t.i[i][0],t.i[i][1],y),y+=1);return s},Kt.prototype.processShapes=function(t){var e,i,s,a,r,n,h=this.shapes.length,o=this.rd.v;if(0!==o)for(i=0;i<h;i+=1){if(n=(r=this.shapes[i]).localShapeCollection,r.shape._mdf||this._mdf||t)for(n.releaseShapes(),r.shape._mdf=!0,e=r.shape.paths.shapes,a=r.shape.paths._length,s=0;s<a;s+=1)n.addShape(this.processPath(e[s],o));r.shape.paths=r.localShapeCollection}this.dynamicProperties.length||(this._mdf=!1)},ee.prototype.point=function(t){return[((this.a[0]*t+this.b[0])*t+this.c[0])*t+this.d[0],((this.a[1]*t+this.b[1])*t+this.c[1])*t+this.d[1]]},ee.prototype.derivative=function(t){return[(3*t*this.a[0]+2*this.b[0])*t+this.c[0],(3*t*this.a[1]+2*this.b[1])*t+this.c[1]]},ee.prototype.tangentAngle=function(t){var e=this.derivative(t);return Math.atan2(e[1],e[0])},ee.prototype.normalAngle=function(t){var e=this.derivative(t);return Math.atan2(e[0],e[1])},ee.prototype.inflectionPoints=function(){var t=this.a[1]*this.b[0]-this.a[0]*this.b[1];if(Zt(t))return[];var e=-.5*(this.a[1]*this.c[0]-this.a[0]*this.c[1])/t,i=e*e-1/3*(this.b[1]*this.c[0]-this.b[0]*this.c[1])/t;if(i<0)return[];var s=Math.sqrt(i);return Zt(s)?s>0&&s<1?[e]:[]:[e-s,e+s].filter((function(t){return t>0&&t<1}))},ee.prototype.split=function(t){if(t<=0)return[te(this.points[0]),this];if(t>=1)return[this,te(this.points[this.points.length-1])];var e=Qt(this.points[0],this.points[1],t),i=Qt(this.points[1],this.points[2],t),s=Qt(this.points[2],this.points[3],t),a=Qt(e,i,t),r=Qt(i,s,t),n=Qt(a,r,t);return[new ee(this.points[0],e,a,n,!0),new ee(n,r,s,this.points[3],!0)]},ee.prototype.bounds=function(){return{x:ie(this,0),y:ie(this,1)}},ee.prototype.boundingBox=function(){var t=this.bounds();return{left:t.x.min,right:t.x.max,top:t.y.min,bottom:t.y.max,width:t.x.max-t.x.min,height:t.y.max-t.y.min,cx:(t.x.max+t.x.min)/2,cy:(t.y.max+t.y.min)/2}},ee.prototype.intersections=function(t,e,i){void 0===e&&(e=2),void 0===i&&(i=7);var s=[];return re(se(this,0,1),se(t,0,1),0,e,s,i),s},ee.shapeSegment=function(t,e){var i=(e+1)%t.length();return new ee(t.v[e],t.o[e],t.i[i],t.v[i],!0)},ee.shapeSegmentInverted=function(t,e){var i=(e+1)%t.length();return new ee(t.v[i],t.i[i],t.o[e],t.v[e],!0)},r([Wt],fe),fe.prototype.initModifierProperties=function(t,e){this.getValue=this.processKeys,this.amplitude=_t.getProp(t,e.s,0,null,this),this.frequency=_t.getProp(t,e.r,0,null,this),this.pointsType=_t.getProp(t,e.pt,0,null,this),this._isAnimated=0!==this.amplitude.effectsSequence.length||0!==this.frequency.effectsSequence.length||0!==this.pointsType.effectsSequence.length},fe.prototype.processPath=function(t,e,i,s){var a=t._length,r=St.newElement();if(r.c=t.c,t.c||(a-=1),0===a)return r;var n=-1,h=ee.shapeSegment(t,0);ce(r,t,0,e,i,s,n);for(var o=0;o<a;o+=1)n=ue(r,h,e,i,s,-n),h=o!==a-1||t.c?ee.shapeSegment(t,(o+1)%a):null,ce(r,t,o+1,e,i,s,n);return r},fe.prototype.processShapes=function(t){var e,i,s,a,r,n,h=this.shapes.length,o=this.amplitude.v,l=Math.max(0,Math.round(this.frequency.v)),p=this.pointsType.v;if(0!==o)for(i=0;i<h;i+=1){if(n=(r=this.shapes[i]).localShapeCollection,r.shape._mdf||this._mdf||t)for(n.releaseShapes(),r.shape._mdf=!0,e=r.shape.paths.shapes,a=r.shape.paths._length,s=0;s<a;s+=1)n.addShape(this.processPath(e[s],o,l,p));r.shape.paths=r.localShapeCollection}this.dynamicProperties.length||(this._mdf=!1)},r([Wt],ke),ke.prototype.initModifierProperties=function(t,e){this.getValue=this.processKeys,this.amount=_t.getProp(t,e.a,0,null,this),this.miterLimit=_t.getProp(t,e.ml,0,null,this),this.lineJoin=e.lj,this._isAnimated=0!==this.amount.effectsSequence.length},ke.prototype.processPath=function(t,e,i,s){var a=St.newElement();a.c=t.c;var r,n,h,o=t.length();t.c||(o-=1);var l=[];for(r=0;r<o;r+=1)h=ee.shapeSegment(t,r),l.push(xe(h,e));if(!t.c)for(r=o-1;r>=0;r-=1)h=ee.shapeSegmentInverted(t,r),l.push(xe(h,e));l=function(t){for(var e,i=1;i<t.length;i+=1)e=_e(t[i-1],t[i]),t[i-1]=e[0],t[i]=e[1];return t.length>1&&(e=_e(t[t.length-1],t[0]),t[t.length-1]=e[0],t[0]=e[1]),t}(l);var p=null,f=null;for(r=0;r<l.length;r+=1){var m=l[r];for(f&&(p=ve(a,f,m[0],i,s)),f=m[m.length-1],n=0;n<m.length;n+=1)h=m[n],p&&pe(h.points[0],p)?a.setXYAt(h.points[1][0],h.points[1][1],"o",a.length()-1):a.setTripleAt(h.points[0][0],h.points[0][1],h.points[1][0],h.points[1][1],h.points[0][0],h.points[0][1],a.length()),a.setTripleAt(h.points[3][0],h.points[3][1],h.points[3][0],h.points[3][1],h.points[2][0],h.points[2][1],a.length()),p=h.points[3]}return l.length&&ve(a,f,l[0][0],i,s),a},ke.prototype.processShapes=function(t){var e,i,s,a,r,n,h=this.shapes.length,o=this.amount.v,l=this.miterLimit.v,p=this.lineJoin;if(0!==o)for(i=0;i<h;i+=1){if(n=(r=this.shapes[i]).localShapeCollection,r.shape._mdf||this._mdf||t)for(n.releaseShapes(),r.shape._mdf=!0,e=r.shape.paths.shapes,a=r.shape.paths._length,s=0;s<a;s+=1)n.addShape(this.processPath(e[s],o,p,l));r.shape.paths=r.localShapeCollection}this.dynamicProperties.length||(this._mdf=!1)};var Pe=function(){var t={w:0,size:0,shapes:[],data:{shapes:[]}},e=[];e=e.concat([2304,2305,2306,2307,2362,2363,2364,2364,2366,2367,2368,2369,2370,2371,2372,2373,2374,2375,2376,2377,2378,2379,2380,2381,2382,2383,2387,2388,2389,2390,2391,2402,2403]);var i=127988,s=["d83cdffb","d83cdffc","d83cdffd","d83cdffe","d83cdfff"];function r(t,e){var i=a("span");i.setAttribute("aria-hidden",!0),i.style.fontFamily=e;var s=a("span");s.innerText="giItT1WQy@!-/#",i.style.position="absolute",i.style.left="-10000px",i.style.top="-10000px",i.style.fontSize="300px",i.style.fontVariant="normal",i.style.fontStyle="normal",i.style.fontWeight="normal",i.style.letterSpacing="0",i.appendChild(s),document.body.appendChild(i);var r=s.offsetWidth;return s.style.fontFamily=function(t){var e,i=t.split(","),s=i.length,a=[];for(e=0;e<s;e+=1)"sans-serif"!==i[e]&&"monospace"!==i[e]&&a.push(i[e]);return a.join(",")}(t)+", "+e,{node:s,w:r,parent:i}}function n(t,e){var i,s=document.body&&e?"svg":"canvas",a=we(t);if("svg"===s){var r=X("text");r.style.fontSize="100px",r.setAttribute("font-family",t.fFamily),r.setAttribute("font-style",a.style),r.setAttribute("font-weight",a.weight),r.textContent="1",t.fClass?(r.style.fontFamily="inherit",r.setAttribute("class",t.fClass)):r.style.fontFamily=t.fFamily,e.appendChild(r),i=r}else{var n=new OffscreenCanvas(500,500).getContext("2d");n.font=a.style+" "+a.weight+" 100px "+t.fFamily,i=n}return{measureText:function(t){return"svg"===s?(i.textContent=t,i.getComputedTextLength()):i.measureText(t).width}}}function h(t){var e=0,i=t.charCodeAt(0);if(i>=55296&&i<=56319){var s=t.charCodeAt(1);s>=56320&&s<=57343&&(e=1024*(i-55296)+s-56320+65536)}return e}function o(t){var e=h(t);return e>=127462&&e<=127487}var l=function(){this.fonts=[],this.chars=null,this.typekitLoaded=0,this.isLoaded=!1,this._warned=!1,this.initTime=Date.now(),this.setIsLoadedBinded=this.setIsLoaded.bind(this),this.checkLoadedFontsBinded=this.checkLoadedFonts.bind(this)};l.isModifier=function(t,e){var i=t.toString(16)+e.toString(16);return-1!==s.indexOf(i)},l.isZeroWidthJoiner=function(t){return 8205===t},l.isFlagEmoji=function(t){return o(t.substr(0,2))&&o(t.substr(2,2))},l.isRegionalCode=o,l.isCombinedCharacter=function(t){return-1!==e.indexOf(t)},l.isRegionalFlag=function(t,e){var s=h(t.substr(e,2));if(s!==i)return!1;var a=0;for(e+=2;a<5;){if((s=h(t.substr(e,2)))<917601||s>917626)return!1;a+=1,e+=2}return 917631===h(t.substr(e,2))},l.isVariationSelector=function(t){return 65039===t},l.BLACK_FLAG_CODE_POINT=i;var p={addChars:function(t){if(t){var e;this.chars||(this.chars=[]);var i,s,a=t.length,r=this.chars.length;for(e=0;e<a;e+=1){for(i=0,s=!1;i<r;)this.chars[i].style===t[e].style&&this.chars[i].fFamily===t[e].fFamily&&this.chars[i].ch===t[e].ch&&(s=!0),i+=1;s||(this.chars.push(t[e]),r+=1)}}},addFonts:function(t,e){if(t){if(this.chars)return this.isLoaded=!0,void(this.fonts=t.list);if(!document.body)return this.isLoaded=!0,t.list.forEach((function(t){t.helper=n(t),t.cache={}})),void(this.fonts=t.list);var i,s=t.list,h=s.length,o=h;for(i=0;i<h;i+=1){var l,p,f=!0;if(s[i].loaded=!1,s[i].monoCase=r(s[i].fFamily,"monospace"),s[i].sansCase=r(s[i].fFamily,"sans-serif"),s[i].fPath){if("p"===s[i].fOrigin||3===s[i].origin){if((l=document.querySelectorAll('style[f-forigin="p"][f-family="'+s[i].fFamily+'"], style[f-origin="3"][f-family="'+s[i].fFamily+'"]')).length>0&&(f=!1),f){var m=a("style");m.setAttribute("f-forigin",s[i].fOrigin),m.setAttribute("f-origin",s[i].origin),m.setAttribute("f-family",s[i].fFamily),m.type="text/css",m.innerText="@font-face {font-family: "+s[i].fFamily+"; font-style: normal; src: url('"+s[i].fPath+"');}",e.appendChild(m)}}else if("g"===s[i].fOrigin||1===s[i].origin){for(l=document.querySelectorAll('link[f-forigin="g"], link[f-origin="1"]'),p=0;p<l.length;p+=1)-1!==l[p].href.indexOf(s[i].fPath)&&(f=!1);if(f){var d=a("link");d.setAttribute("f-forigin",s[i].fOrigin),d.setAttribute("f-origin",s[i].origin),d.type="text/css",d.rel="stylesheet",d.href=s[i].fPath,document.body.appendChild(d)}}else if("t"===s[i].fOrigin||2===s[i].origin){for(l=document.querySelectorAll('script[f-forigin="t"], script[f-origin="2"]'),p=0;p<l.length;p+=1)s[i].fPath===l[p].src&&(f=!1);if(f){var c=a("link");c.setAttribute("f-forigin",s[i].fOrigin),c.setAttribute("f-origin",s[i].origin),c.setAttribute("rel","stylesheet"),c.setAttribute("href",s[i].fPath),e.appendChild(c)}}}else s[i].loaded=!0,o-=1;s[i].helper=n(s[i],e),s[i].cache={},this.fonts.push(s[i])}0===o?this.isLoaded=!0:setTimeout(this.checkLoadedFonts.bind(this),100)}else this.isLoaded=!0},getCharData:function(e,i,s){for(var a=0,r=this.chars.length;a<r;){if(this.chars[a].ch===e&&this.chars[a].style===i&&this.chars[a].fFamily===s)return this.chars[a];a+=1}return("string"==typeof e&&13!==e.charCodeAt(0)||!e)&&console&&console.warn&&!this._warned&&(this._warned=!0,console.warn("Missing character from exported characters list: ",e,i,s)),t},getFontByName:function(t){for(var e=0,i=this.fonts.length;e<i;){if(this.fonts[e].fName===t)return this.fonts[e];e+=1}return this.fonts[0]},measureText:function(t,e,i){var s=this.getFontByName(e),a=t;if(!s.cache[a]){var r=s.helper;if(" "===t){var n=r.measureText("|"+t+"|"),h=r.measureText("||");s.cache[a]=(n-h)/100}else s.cache[a]=r.measureText(t)/100}return s.cache[a]*i},checkLoadedFonts:function(){var t,e,i,s=this.fonts.length,a=s;for(t=0;t<s;t+=1)this.fonts[t].loaded?a-=1:"n"===this.fonts[t].fOrigin||0===this.fonts[t].origin?this.fonts[t].loaded=!0:(e=this.fonts[t].monoCase.node,i=this.fonts[t].monoCase.w,e.offsetWidth!==i?(a-=1,this.fonts[t].loaded=!0):(e=this.fonts[t].sansCase.node,i=this.fonts[t].sansCase.w,e.offsetWidth!==i&&(a-=1,this.fonts[t].loaded=!0)),this.fonts[t].loaded&&(this.fonts[t].sansCase.parent.parentNode.removeChild(this.fonts[t].sansCase.parent),this.fonts[t].monoCase.parent.parentNode.removeChild(this.fonts[t].monoCase.parent)));0!==a&&Date.now()-this.initTime<5e3?setTimeout(this.checkLoadedFontsBinded,20):setTimeout(this.setIsLoadedBinded,10)},setIsLoaded:function(){this.isLoaded=!0}};return l.prototype=p,l}();function Se(t){this.animationData=t}function Ae(){}Se.prototype.getProp=function(t){return this.animationData.slots&&this.animationData.slots[t.sid]?Object.assign(t,this.animationData.slots[t.sid].p):t},Ae.prototype={initRenderable:function(){this.isInRange=!1,this.hidden=!1,this.isTransparent=!1,this.renderableComponents=[]},addRenderableComponent:function(t){-1===this.renderableComponents.indexOf(t)&&this.renderableComponents.push(t)},removeRenderableComponent:function(t){-1!==this.renderableComponents.indexOf(t)&&this.renderableComponents.splice(this.renderableComponents.indexOf(t),1)},prepareRenderableFrame:function(t){this.checkLayerLimits(t)},checkTransparency:function(){this.finalTransform.mProp.o.v<=0?!this.isTransparent&&this.globalData.renderConfig.hideOnTransparent&&(this.isTransparent=!0,this.hide()):this.isTransparent&&(this.isTransparent=!1,this.show())},checkLayerLimits:function(t){this.data.ip-this.data.st<=t&&this.data.op-this.data.st>t?!0!==this.isInRange&&(this.globalData._mdf=!0,this._mdf=!0,this.isInRange=!0,this.show()):!1!==this.isInRange&&(this.globalData._mdf=!0,this.isInRange=!1,this.hide())},renderRenderable:function(){var t,e=this.renderableComponents.length;for(t=0;t<e;t+=1)this.renderableComponents[t].renderFrame(this._isFirstFrame)},sourceRectAtTime:function(){return{top:0,left:0,width:100,height:100}},getLayerSize:function(){return 5===this.data.ty?{w:this.data.textData.width,h:this.data.textData.height}:{w:this.data.width,h:this.data.height}}};var De,Ce=(De={0:"source-over",1:"multiply",2:"screen",3:"overlay",4:"darken",5:"lighten",6:"color-dodge",7:"color-burn",8:"hard-light",9:"soft-light",10:"difference",11:"exclusion",12:"hue",13:"saturation",14:"color",15:"luminosity"},function(t){return De[t]||""});function Ee(t,e,i){this.p=_t.getProp(e,t.v,0,0,i)}function Me(t,e,i){this.p=_t.getProp(e,t.v,0,0,i)}function Te(t,e,i){this.p=_t.getProp(e,t.v,1,0,i)}function Fe(t,e,i){this.p=_t.getProp(e,t.v,1,0,i)}function Ie(t,e,i){this.p=_t.getProp(e,t.v,0,0,i)}function Le(t,e,i){this.p=_t.getProp(e,t.v,0,0,i)}function Be(t,e,i){this.p=_t.getProp(e,t.v,0,0,i)}function ze(){this.p={}}function Ve(t,e){var i,s=t.ef||[];this.effectElements=[];var a,r=s.length;for(i=0;i<r;i+=1)a=new Re(s[i],e),this.effectElements.push(a)}function Re(t,e){this.init(t,e)}function Oe(){}function Ne(){}function qe(t,e,i){this.initFrame(),this.initRenderable(),this.assetData=e.getAssetData(t.refId),this.footageData=e.imageLoader.getAsset(this.assetData),this.initBaseData(t,e,i)}function je(t,e,i){this.initFrame(),this.initRenderable(),this.assetData=e.getAssetData(t.refId),this.initBaseData(t,e,i),this._isPlaying=!1,this._canPlay=!1;var s=this.globalData.getAssetsPath(this.assetData);this.audio=this.globalData.audioController.createAudio(s),this._currentTime=0,this.globalData.audioController.addAudio(this),this._volumeMultiplier=1,this._volume=1,this._previousVolume=null,this.tm=t.tm?_t.getProp(this,t.tm,0,e.frameRate,this):{_placeholder:!0},this.lv=_t.getProp(this,t.au&&t.au.lv?t.au.lv:{k:[100]},1,.01,this)}function We(){}r([xt],Re),Re.prototype.getValue=Re.prototype.iterateDynamicProperties,Re.prototype.init=function(t,e){var i;this.data=t,this.effectElements=[],this.initDynamicPropertyContainer(e);var s,a=this.data.ef.length,r=this.data.ef;for(i=0;i<a;i+=1){switch(s=null,r[i].ty){case 0:s=new Ee(r[i],e,this);break;case 1:s=new Me(r[i],e,this);break;case 2:s=new Te(r[i],e,this);break;case 3:s=new Fe(r[i],e,this);break;case 4:case 7:s=new Be(r[i],e,this);break;case 10:s=new Ie(r[i],e,this);break;case 11:s=new Le(r[i],e,this);break;case 5:s=new Ve(r[i],e,this);break;default:s=new ze(r[i],e,this)}s&&this.effectElements.push(s)}},Oe.prototype={checkMasks:function(){if(!this.data.hasMask)return!1;for(var t=0,e=this.data.masksProperties.length;t<e;){if("n"!==this.data.masksProperties[t].mode&&!1!==this.data.masksProperties[t].cl)return!0;t+=1}return!1},initExpressions:function(){var t=j();if(t){var e=t("layer"),i=t("effects"),s=t("shape"),a=t("text"),r=t("comp");this.layerInterface=e(this),this.data.hasMask&&this.maskManager&&this.layerInterface.registerMaskInterface(this.maskManager);var n=i.createEffectsInterface(this,this.layerInterface);this.layerInterface.registerEffectsInterface(n),0===this.data.ty||this.data.xt?this.compInterface=r(this):4===this.data.ty?(this.layerInterface.shapeInterface=s(this.shapesData,this.itemsData,this.layerInterface),this.layerInterface.content=this.layerInterface.shapeInterface):5===this.data.ty&&(this.layerInterface.textInterface=a(this),this.layerInterface.text=this.layerInterface.textInterface)}},setBlendMode:function(){var t=Ce(this.data.bm);(this.baseElement||this.layerElement).style["mix-blend-mode"]=t},initBaseData:function(t,e,i){this.globalData=e,this.comp=i,this.data=t,this.layerId=B(),this.data.sr||(this.data.sr=1),this.effectsManager=new Ve(this.data,this,this.dynamicProperties)},getType:function(){return this.type},sourceRectAtTime:function(){}},Ne.prototype={initFrame:function(){this._isFirstFrame=!1,this.dynamicProperties=[],this._mdf=!1},prepareProperties:function(t,e){var i,s=this.dynamicProperties.length;for(i=0;i<s;i+=1)(e||this._isParent&&"transform"===this.dynamicProperties[i].propType)&&(this.dynamicProperties[i].getValue(),this.dynamicProperties[i]._mdf&&(this.globalData._mdf=!0,this._mdf=!0))},addDynamicProperty:function(t){-1===this.dynamicProperties.indexOf(t)&&this.dynamicProperties.push(t)}},qe.prototype.prepareFrame=function(){},r([Ae,Oe,Ne],qe),qe.prototype.getBaseElement=function(){return null},qe.prototype.renderFrame=function(){},qe.prototype.destroy=function(){},qe.prototype.initExpressions=function(){var t=j();if(t){var e=t("footage");this.layerInterface=e(this)}},qe.prototype.getFootageData=function(){return this.footageData},je.prototype.prepareFrame=function(t){if(this.prepareRenderableFrame(t,!0),this.prepareProperties(t,!0),this.tm._placeholder)this._currentTime=t/this.data.sr;else{var e=this.tm.v;this._currentTime=e}this._volume=this.lv.v[0];var i=this._volume*this._volumeMultiplier;this._previousVolume!==i&&(this._previousVolume=i,this.audio.volume(i))},r([Ae,Oe,Ne],je),je.prototype.renderFrame=function(){this.isInRange&&this._canPlay&&(this._isPlaying?(!this.audio.playing()||Math.abs(this._currentTime/this.globalData.frameRate-this.audio.seek())>.1)&&this.audio.seek(this._currentTime/this.globalData.frameRate):(this.audio.play(),this.audio.seek(this._currentTime/this.globalData.frameRate),this._isPlaying=!0))},je.prototype.show=function(){},je.prototype.hide=function(){this.audio.pause(),this._isPlaying=!1},je.prototype.pause=function(){this.audio.pause(),this._isPlaying=!1,this._canPlay=!1},je.prototype.resume=function(){this._canPlay=!0},je.prototype.setRate=function(t){this.audio.rate(t)},je.prototype.volume=function(t){this._volumeMultiplier=t,this._previousVolume=t*this._volume,this.audio.volume(this._previousVolume)},je.prototype.getBaseElement=function(){return null},je.prototype.destroy=function(){},je.prototype.sourceRectAtTime=function(){},je.prototype.initExpressions=function(){},We.prototype.checkLayers=function(t){var e,i,s=this.layers.length;for(this.completeLayers=!0,e=s-1;e>=0;e-=1)this.elements[e]||(i=this.layers[e]).ip-i.st<=t-this.layers[e].st&&i.op-i.st>t-this.layers[e].st&&this.buildItem(e),this.completeLayers=!!this.elements[e]&&this.completeLayers;this.checkPendingElements()},We.prototype.createItem=function(t){switch(t.ty){case 2:return this.createImage(t);case 0:return this.createComp(t);case 1:return this.createSolid(t);case 3:default:return this.createNull(t);case 4:return this.createShape(t);case 5:return this.createText(t);case 6:return this.createAudio(t);case 13:return this.createCamera(t);case 15:return this.createFootage(t)}},We.prototype.createCamera=function(){throw new Error("You're using a 3d camera. Try the html renderer.")},We.prototype.createAudio=function(t){return new je(t,this.globalData,this)},We.prototype.createFootage=function(t){return new qe(t,this.globalData,this)},We.prototype.buildAllItems=function(){var t,e=this.layers.length;for(t=0;t<e;t+=1)this.buildItem(t);this.checkPendingElements()},We.prototype.includeLayers=function(t){var e;this.completeLayers=!1;var i,s=t.length,a=this.layers.length;for(e=0;e<s;e+=1)for(i=0;i<a;){if(this.layers[i].id===t[e].id){this.layers[i]=t[e];break}i+=1}},We.prototype.setProjectInterface=function(t){this.globalData.projectInterface=t},We.prototype.initItems=function(){this.globalData.progressiveLoad||this.buildAllItems()},We.prototype.buildElementParenting=function(t,e,i){for(var s=this.elements,a=this.layers,r=0,n=a.length;r<n;)a[r].ind==e&&(s[r]&&!0!==s[r]?(i.push(s[r]),s[r].setAsParent(),void 0!==a[r].parent?this.buildElementParenting(t,a[r].parent,i):t.setHierarchy(i)):(this.buildItem(r),this.addPendingElement(t))),r+=1},We.prototype.addPendingElement=function(t){this.pendingElements.push(t)},We.prototype.searchExtraCompositions=function(t){var e,i=t.length;for(e=0;e<i;e+=1)if(t[e].xt){var s=this.createComp(t[e]);s.initExpressions(),this.globalData.projectInterface.registerComposition(s)}},We.prototype.getElementById=function(t){var e,i=this.elements.length;for(e=0;e<i;e+=1)if(this.elements[e].data.ind===t)return this.elements[e];return null},We.prototype.getElementByPath=function(t){var e,i=t.shift();if("number"==typeof i)e=this.elements[i];else{var s,a=this.elements.length;for(s=0;s<a;s+=1)if(this.elements[s].data.nm===i){e=this.elements[s];break}}return 0===t.length?e:e.getElementByPath(t)},We.prototype.setupGlobalData=function(t,e){this.globalData.fontManager=new Pe,this.globalData.slotManager=function(t){return new Se(t)}(t),this.globalData.fontManager.addChars(t.chars),this.globalData.fontManager.addFonts(t.fonts,e),this.globalData.getAssetData=this.animationItem.getAssetData.bind(this.animationItem),this.globalData.getAssetsPath=this.animationItem.getAssetsPath.bind(this.animationItem),this.globalData.imageLoader=this.animationItem.imagePreloader,this.globalData.audioController=this.animationItem.audioController,this.globalData.frameId=0,this.globalData.frameRate=t.fr,this.globalData.nm=t.nm,this.globalData.compSize={w:t.w,h:t.h}};var He="transformEFfect";function Xe(){}function Ye(t,e,i){this.data=t,this.element=e,this.globalData=i,this.storedData=[],this.masksProperties=this.data.masksProperties||[],this.maskElement=null;var a,r,n=this.globalData.defs,h=this.masksProperties?this.masksProperties.length:0;this.viewData=l(h),this.solidPath="";var o,p,f,m,d,c,u=this.masksProperties,g=0,y=[],v=B(),b="clipPath",_="clip-path";for(a=0;a<h;a+=1)if(("a"!==u[a].mode&&"n"!==u[a].mode||u[a].inv||100!==u[a].o.k||u[a].o.x)&&(b="mask",_="mask"),"s"!==u[a].mode&&"i"!==u[a].mode||0!==g?f=null:((f=X("rect")).setAttribute("fill","#ffffff"),f.setAttribute("width",this.element.comp.data.w||0),f.setAttribute("height",this.element.comp.data.h||0),y.push(f)),r=X("path"),"n"===u[a].mode)this.viewData[a]={op:_t.getProp(this.element,u[a].o,0,.01,this.element),prop:Ft.getShapeProp(this.element,u[a],3),elem:r,lastPath:""},n.appendChild(r);else{var x;if(g+=1,r.setAttribute("fill","s"===u[a].mode?"#000000":"#ffffff"),r.setAttribute("clip-rule","nonzero"),0!==u[a].x.k?(b="mask",_="mask",c=_t.getProp(this.element,u[a].x,0,null,this.element),x=B(),(m=X("filter")).setAttribute("id",x),(d=X("feMorphology")).setAttribute("operator","erode"),d.setAttribute("in","SourceGraphic"),d.setAttribute("radius","0"),m.appendChild(d),n.appendChild(m),r.setAttribute("stroke","s"===u[a].mode?"#000000":"#ffffff")):(d=null,c=null),this.storedData[a]={elem:r,x:c,expan:d,lastPath:"",lastOperator:"",filterId:x,lastRadius:0},"i"===u[a].mode){p=y.length;var k=X("g");for(o=0;o<p;o+=1)k.appendChild(y[o]);var w=X("mask");w.setAttribute("mask-type","alpha"),w.setAttribute("id",v+"_"+g),w.appendChild(r),n.appendChild(w),k.setAttribute("mask","url("+s()+"#"+v+"_"+g+")"),y.length=0,y.push(k)}else y.push(r);u[a].inv&&!this.solidPath&&(this.solidPath=this.createLayerSolidPath()),this.viewData[a]={elem:r,lastPath:"",op:_t.getProp(this.element,u[a].o,0,.01,this.element),prop:Ft.getShapeProp(this.element,u[a],3),invRect:f},this.viewData[a].prop.k||this.drawPath(u[a],this.viewData[a].prop.v,this.viewData[a])}for(this.maskElement=X(b),h=y.length,a=0;a<h;a+=1)this.maskElement.appendChild(y[a]);g>0&&(this.maskElement.setAttribute("id",v),this.element.maskedElement.setAttribute(_,"url("+s()+"#"+v+")"),n.appendChild(this.maskElement)),this.viewData.length&&this.element.addRenderableComponent(this)}Xe.prototype={initTransform:function(){var t=new It;this.finalTransform={mProp:this.data.ks?Yt.getTransformProperty(this,this.data.ks,this):{o:0},_matMdf:!1,_localMatMdf:!1,_opMdf:!1,mat:t,localMat:t,localOpacity:1},this.data.ao&&(this.finalTransform.mProp.autoOriented=!0),this.data.ty},renderTransform:function(){if(this.finalTransform._opMdf=this.finalTransform.mProp.o._mdf||this._isFirstFrame,this.finalTransform._matMdf=this.finalTransform.mProp._mdf||this._isFirstFrame,this.hierarchy){var t,e=this.finalTransform.mat,i=0,s=this.hierarchy.length;if(!this.finalTransform._matMdf)for(;i<s;){if(this.hierarchy[i].finalTransform.mProp._mdf){this.finalTransform._matMdf=!0;break}i+=1}if(this.finalTransform._matMdf)for(t=this.finalTransform.mProp.v.props,e.cloneFromProps(t),i=0;i<s;i+=1)e.multiply(this.hierarchy[i].finalTransform.mProp.v)}this.localTransforms&&!this.finalTransform._matMdf||(this.finalTransform._localMatMdf=this.finalTransform._matMdf),this.finalTransform._opMdf&&(this.finalTransform.localOpacity=this.finalTransform.mProp.o.v)},renderLocalTransform:function(){if(this.localTransforms){var t=0,e=this.localTransforms.length;if(this.finalTransform._localMatMdf=this.finalTransform._matMdf,!this.finalTransform._localMatMdf||!this.finalTransform._opMdf)for(;t<e;)this.localTransforms[t]._mdf&&(this.finalTransform._localMatMdf=!0),this.localTransforms[t]._opMdf&&!this.finalTransform._opMdf&&(this.finalTransform.localOpacity=this.finalTransform.mProp.o.v,this.finalTransform._opMdf=!0),t+=1;if(this.finalTransform._localMatMdf){var i=this.finalTransform.localMat;for(this.localTransforms[0].matrix.clone(i),t=1;t<e;t+=1){var s=this.localTransforms[t].matrix;i.multiply(s)}i.multiply(this.finalTransform.mat)}if(this.finalTransform._opMdf){var a=this.finalTransform.localOpacity;for(t=0;t<e;t+=1)a*=.01*this.localTransforms[t].opacity;this.finalTransform.localOpacity=a}}},searchEffectTransforms:function(){if(this.renderableEffectsManager){var t=this.renderableEffectsManager.getEffects(He);if(t.length){this.localTransforms=[],this.finalTransform.localMat=new It;var e=0,i=t.length;for(e=0;e<i;e+=1)this.localTransforms.push(t[e])}}},globalToLocal:function(t){var e=[];e.push(this.finalTransform);for(var i,s=!0,a=this.comp;s;)a.finalTransform?(a.data.hasMask&&e.splice(0,0,a.finalTransform),a=a.comp):s=!1;var r,n=e.length;for(i=0;i<n;i+=1)r=e[i].mat.applyToPointArray(0,0,0),t=[t[0]-r[0],t[1]-r[1],0];return t},mHelper:new It},Ye.prototype.getMaskProperty=function(t){return this.viewData[t].prop},Ye.prototype.renderFrame=function(t){var e,i=this.element.finalTransform.mat,a=this.masksProperties.length;for(e=0;e<a;e+=1)if((this.viewData[e].prop._mdf||t)&&this.drawPath(this.masksProperties[e],this.viewData[e].prop.v,this.viewData[e]),(this.viewData[e].op._mdf||t)&&this.viewData[e].elem.setAttribute("fill-opacity",this.viewData[e].op.v),"n"!==this.masksProperties[e].mode&&(this.viewData[e].invRect&&(this.element.finalTransform.mProp._mdf||t)&&this.viewData[e].invRect.setAttribute("transform",i.getInverseMatrix().to2dCSS()),this.storedData[e].x&&(this.storedData[e].x._mdf||t))){var r=this.storedData[e].expan;this.storedData[e].x.v<0?("erode"!==this.storedData[e].lastOperator&&(this.storedData[e].lastOperator="erode",this.storedData[e].elem.setAttribute("filter","url("+s()+"#"+this.storedData[e].filterId+")")),r.setAttribute("radius",-this.storedData[e].x.v)):("dilate"!==this.storedData[e].lastOperator&&(this.storedData[e].lastOperator="dilate",this.storedData[e].elem.setAttribute("filter",null)),this.storedData[e].elem.setAttribute("stroke-width",2*this.storedData[e].x.v))}},Ye.prototype.getMaskelement=function(){return this.maskElement},Ye.prototype.createLayerSolidPath=function(){var t="M0,0 ";return t+=" h"+this.globalData.compSize.w,t+=" v"+this.globalData.compSize.h,t+=" h-"+this.globalData.compSize.w,t+=" v-"+this.globalData.compSize.h+" "},Ye.prototype.drawPath=function(t,e,i){var s,a,r=" M"+e.v[0][0]+","+e.v[0][1];for(a=e._length,s=1;s<a;s+=1)r+=" C"+e.o[s-1][0]+","+e.o[s-1][1]+" "+e.i[s][0]+","+e.i[s][1]+" "+e.v[s][0]+","+e.v[s][1];if(e.c&&a>1&&(r+=" C"+e.o[s-1][0]+","+e.o[s-1][1]+" "+e.i[0][0]+","+e.i[0][1]+" "+e.v[0][0]+","+e.v[0][1]),i.lastPath!==r){var n="";i.elem&&(e.c&&(n=t.inv?this.solidPath+r:r),i.elem.setAttribute("d",n)),i.lastPath=r}},Ye.prototype.destroy=function(){this.element=null,this.globalData=null,this.maskElement=null,this.data=null,this.masksProperties=null};var Ge=function(){var t={};return t.createFilter=function(t,e){var i=X("filter");i.setAttribute("id",t),!0!==e&&(i.setAttribute("filterUnits","objectBoundingBox"),i.setAttribute("x","0%"),i.setAttribute("y","0%"),i.setAttribute("width","100%"),i.setAttribute("height","100%"));return i},t.createAlphaToLuminanceFilter=function(){var t=X("feColorMatrix");return t.setAttribute("type","matrix"),t.setAttribute("color-interpolation-filters","sRGB"),t.setAttribute("values","0 0 0 1 0  0 0 0 1 0  0 0 0 1 0  0 0 0 1 1"),t},t}(),Ke=function(){var t={maskType:!0,svgLumaHidden:!0,offscreenCanvas:"undefined"!=typeof OffscreenCanvas};return(/MSIE 10/i.test(navigator.userAgent)||/MSIE 9/i.test(navigator.userAgent)||/rv:11.0/i.test(navigator.userAgent)||/Edge\/\d./i.test(navigator.userAgent))&&(t.maskType=!1),/firefox/i.test(navigator.userAgent)&&(t.svgLumaHidden=!1),t}(),Je={},Ze="filter_result_";function Ue(t){var e,i,a="SourceGraphic",r=t.data.ef?t.data.ef.length:0,n=B(),h=Ge.createFilter(n,!0),o=0;for(this.filters=[],e=0;e<r;e+=1){i=null;var l=t.data.ef[e].ty;if(Je[l])i=new(0,Je[l].effect)(h,t.effectsManager.effectElements[e],t,Ze+o,a),a=Ze+o,Je[l].countsAsEffect&&(o+=1);i&&this.filters.push(i)}o&&(t.globalData.defs.appendChild(h),t.layerElement.setAttribute("filter","url("+s()+"#"+n+")")),this.filters.length&&t.addRenderableComponent(this)}function Qe(){}function $e(){}function ti(){}function ei(t,e,i){this.assetData=e.getAssetData(t.refId),this.assetData&&this.assetData.sid&&(this.assetData=e.slotManager.getProp(this.assetData)),this.initElement(t,e,i),this.sourceRect={top:0,left:0,width:this.assetData.w,height:this.assetData.h}}function ii(t,e){this.elem=t,this.pos=e}function si(){}Ue.prototype.renderFrame=function(t){var e,i=this.filters.length;for(e=0;e<i;e+=1)this.filters[e].renderFrame(t)},Ue.prototype.getEffects=function(t){var e,i=this.filters.length,s=[];for(e=0;e<i;e+=1)this.filters[e].type===t&&s.push(this.filters[e]);return s},Qe.prototype={initRendererElement:function(){this.layerElement=X("g")},createContainerElements:function(){this.matteElement=X("g"),this.transformedElement=this.layerElement,this.maskedElement=this.layerElement,this._sizeChanged=!1;var t=null;if(this.data.td){this.matteMasks={};var e=X("g");e.setAttribute("id",this.layerId),e.appendChild(this.layerElement),t=e,this.globalData.defs.appendChild(e)}else this.data.tt?(this.matteElement.appendChild(this.layerElement),t=this.matteElement,this.baseElement=this.matteElement):this.baseElement=this.layerElement;if(this.data.ln&&this.layerElement.setAttribute("id",this.data.ln),this.data.cl&&this.layerElement.setAttribute("class",this.data.cl),0===this.data.ty&&!this.data.hd){var i=X("clipPath"),a=X("path");a.setAttribute("d","M0,0 L"+this.data.w+",0 L"+this.data.w+","+this.data.h+" L0,"+this.data.h+"z");var r=B();if(i.setAttribute("id",r),i.appendChild(a),this.globalData.defs.appendChild(i),this.checkMasks()){var n=X("g");n.setAttribute("clip-path","url("+s()+"#"+r+")"),n.appendChild(this.layerElement),this.transformedElement=n,t?t.appendChild(this.transformedElement):this.baseElement=this.transformedElement}else this.layerElement.setAttribute("clip-path","url("+s()+"#"+r+")")}0!==this.data.bm&&this.setBlendMode()},renderElement:function(){this.finalTransform._localMatMdf&&this.transformedElement.setAttribute("transform",this.finalTransform.localMat.to2dCSS()),this.finalTransform._opMdf&&this.transformedElement.setAttribute("opacity",this.finalTransform.localOpacity)},destroyBaseElement:function(){this.layerElement=null,this.matteElement=null,this.maskManager.destroy()},getBaseElement:function(){return this.data.hd?null:this.baseElement},createRenderableComponents:function(){this.maskManager=new Ye(this.data,this,this.globalData),this.renderableEffectsManager=new Ue(this),this.searchEffectTransforms()},getMatte:function(t){if(this.matteMasks||(this.matteMasks={}),!this.matteMasks[t]){var e,i,a,r,n=this.layerId+"_"+t;if(1===t||3===t){var h=X("mask");h.setAttribute("id",n),h.setAttribute("mask-type",3===t?"luminance":"alpha"),(a=X("use")).setAttributeNS("http://www.w3.org/1999/xlink","href","#"+this.layerId),h.appendChild(a),this.globalData.defs.appendChild(h),Ke.maskType||1!==t||(h.setAttribute("mask-type","luminance"),e=B(),i=Ge.createFilter(e),this.globalData.defs.appendChild(i),i.appendChild(Ge.createAlphaToLuminanceFilter()),(r=X("g")).appendChild(a),h.appendChild(r),r.setAttribute("filter","url("+s()+"#"+e+")"))}else if(2===t){var o=X("mask");o.setAttribute("id",n),o.setAttribute("mask-type","alpha");var l=X("g");o.appendChild(l),e=B(),i=Ge.createFilter(e);var p=X("feComponentTransfer");p.setAttribute("in","SourceGraphic"),i.appendChild(p);var f=X("feFuncA");f.setAttribute("type","table"),f.setAttribute("tableValues","1.0 0.0"),p.appendChild(f),this.globalData.defs.appendChild(i);var m=X("rect");m.setAttribute("width",this.comp.data.w),m.setAttribute("height",this.comp.data.h),m.setAttribute("x","0"),m.setAttribute("y","0"),m.setAttribute("fill","#ffffff"),m.setAttribute("opacity","0"),l.setAttribute("filter","url("+s()+"#"+e+")"),l.appendChild(m),(a=X("use")).setAttributeNS("http://www.w3.org/1999/xlink","href","#"+this.layerId),l.appendChild(a),Ke.maskType||(o.setAttribute("mask-type","luminance"),i.appendChild(Ge.createAlphaToLuminanceFilter()),r=X("g"),l.appendChild(m),r.appendChild(this.layerElement),l.appendChild(r)),this.globalData.defs.appendChild(o)}this.matteMasks[t]=n}return this.matteMasks[t]},setMatte:function(t){this.matteElement&&this.matteElement.setAttribute("mask","url("+s()+"#"+t+")")}},$e.prototype={initHierarchy:function(){this.hierarchy=[],this._isParent=!1,this.checkParenting()},setHierarchy:function(t){this.hierarchy=t},setAsParent:function(){this._isParent=!0},checkParenting:function(){void 0!==this.data.parent&&this.comp.buildElementParenting(this,this.data.parent,[])}},r([Ae,n({initElement:function(t,e,i){this.initFrame(),this.initBaseData(t,e,i),this.initTransform(t,e,i),this.initHierarchy(),this.initRenderable(),this.initRendererElement(),this.createContainerElements(),this.createRenderableComponents(),this.createContent(),this.hide()},hide:function(){this.hidden||this.isInRange&&!this.isTransparent||((this.baseElement||this.layerElement).style.display="none",this.hidden=!0)},show:function(){this.isInRange&&!this.isTransparent&&(this.data.hd||((this.baseElement||this.layerElement).style.display="block"),this.hidden=!1,this._isFirstFrame=!0)},renderFrame:function(){this.data.hd||this.hidden||(this.renderTransform(),this.renderRenderable(),this.renderLocalTransform(),this.renderElement(),this.renderInnerContent(),this._isFirstFrame&&(this._isFirstFrame=!1))},renderInnerContent:function(){},prepareFrame:function(t){this._mdf=!1,this.prepareRenderableFrame(t),this.prepareProperties(t,this.isInRange),this.checkTransparency()},destroy:function(){this.innerElem=null,this.destroyBaseElement()}})],ti),r([Oe,Xe,Qe,$e,Ne,ti],ei),ei.prototype.createContent=function(){var t=this.globalData.getAssetsPath(this.assetData);this.innerElem=X("image"),this.innerElem.setAttribute("width",this.assetData.w+"px"),this.innerElem.setAttribute("height",this.assetData.h+"px"),this.innerElem.setAttribute("preserveAspectRatio",this.assetData.pr||this.globalData.renderConfig.imagePreserveAspectRatio),this.innerElem.setAttributeNS("http://www.w3.org/1999/xlink","href",t),this.layerElement.appendChild(this.innerElem)},ei.prototype.sourceRectAtTime=function(){return this.sourceRect},si.prototype={addShapeToModifiers:function(t){var e,i=this.shapeModifiers.length;for(e=0;e<i;e+=1)this.shapeModifiers[e].addShape(t)},isShapeInAnimatedModifiers:function(t){for(var e=this.shapeModifiers.length;0<e;)if(this.shapeModifiers[0].isAnimatedWithShape(t))return!0;return!1},renderModifiers:function(){if(this.shapeModifiers.length){var t,e=this.shapes.length;for(t=0;t<e;t+=1)this.shapes[t].sh.reset();for(t=(e=this.shapeModifiers.length)-1;t>=0&&!this.shapeModifiers[t].processShapes(this._isFirstFrame);t-=1);}},searchProcessedElement:function(t){for(var e=this.processedElements,i=0,s=e.length;i<s;){if(e[i].elem===t)return e[i].pos;i+=1}return 0},addProcessedElement:function(t,e){for(var i=this.processedElements,s=i.length;s;)if(i[s-=1].elem===t)return void(i[s].pos=e);i.push(new ii(t,e))},prepareFrame:function(t){this.prepareRenderableFrame(t),this.prepareProperties(t,this.isInRange)}};var ai={1:"butt",2:"round",3:"square"},ri={1:"miter",2:"round",3:"bevel"};function ni(t,e,i){this.caches=[],this.styles=[],this.transformers=t,this.lStr="",this.sh=i,this.lvl=e,this._isAnimated=!!i.k;for(var s=0,a=t.length;s<a;){if(t[s].mProps.dynamicProperties.length){this._isAnimated=!0;break}s+=1}}function hi(t,e){this.data=t,this.type=t.ty,this.d="",this.lvl=e,this._mdf=!1,this.closed=!0===t.hd,this.pElem=X("path"),this.msElem=null}function oi(t,e,i,s){var a;this.elem=t,this.frameId=-1,this.dataProps=l(e.length),this.renderer=i,this.k=!1,this.dashStr="",this.dashArray=o("float32",e.length?e.length-1:0),this.dashoffset=o("float32",1),this.initDynamicPropertyContainer(s);var r,n=e.length||0;for(a=0;a<n;a+=1)r=_t.getProp(t,e[a].v,0,0,this),this.k=r.k||this.k,this.dataProps[a]={n:e[a].n,p:r};this.k||this.getValue(!0),this._isAnimated=this.k}function li(t,e,i){this.initDynamicPropertyContainer(t),this.getValue=this.iterateDynamicProperties,this.o=_t.getProp(t,e.o,0,.01,this),this.w=_t.getProp(t,e.w,0,null,this),this.d=new oi(t,e.d||{},"svg",this),this.c=_t.getProp(t,e.c,1,255,this),this.style=i,this._isAnimated=!!this._isAnimated}function pi(t,e,i){this.initDynamicPropertyContainer(t),this.getValue=this.iterateDynamicProperties,this.o=_t.getProp(t,e.o,0,.01,this),this.c=_t.getProp(t,e.c,1,255,this),this.style=i}function fi(t,e,i){this.initDynamicPropertyContainer(t),this.getValue=this.iterateDynamicProperties,this.style=i}function mi(t,e,i){this.data=e,this.c=o("uint8c",4*e.p);var s=e.k.k[0].s?e.k.k[0].s.length-4*e.p:e.k.k.length-4*e.p;this.o=o("float32",s),this._cmdf=!1,this._omdf=!1,this._collapsable=this.checkCollapsable(),this._hasOpacity=s,this.initDynamicPropertyContainer(i),this.prop=_t.getProp(t,e.k,1,null,this),this.k=this.prop.k,this.getValue(!0)}function di(t,e,i){this.initDynamicPropertyContainer(t),this.getValue=this.iterateDynamicProperties,this.initGradientData(t,e,i)}function ci(t,e,i){this.initDynamicPropertyContainer(t),this.getValue=this.iterateDynamicProperties,this.w=_t.getProp(t,e.w,0,null,this),this.d=new oi(t,e.d||{},"svg",this),this.initGradientData(t,e,i),this._isAnimated=!!this._isAnimated}function ui(){this.it=[],this.prevViewData=[],this.gr=X("g")}function gi(t,e,i){this.transform={mProps:t,op:e,container:i},this.elements=[],this._isAnimated=this.transform.mProps.dynamicProperties.length||this.transform.op.effectsSequence.length}ni.prototype.setAsAnimated=function(){this._isAnimated=!0},hi.prototype.reset=function(){this.d="",this._mdf=!1},oi.prototype.getValue=function(t){if((this.elem.globalData.frameId!==this.frameId||t)&&(this.frameId=this.elem.globalData.frameId,this.iterateDynamicProperties(),this._mdf=this._mdf||t,this._mdf)){var e=0,i=this.dataProps.length;for("svg"===this.renderer&&(this.dashStr=""),e=0;e<i;e+=1)"o"!==this.dataProps[e].n?"svg"===this.renderer?this.dashStr+=" "+this.dataProps[e].p.v:this.dashArray[e]=this.dataProps[e].p.v:this.dashoffset[0]=this.dataProps[e].p.v}},r([xt],oi),r([xt],li),r([xt],pi),r([xt],fi),mi.prototype.comparePoints=function(t,e){for(var i=0,s=this.o.length/2;i<s;){if(Math.abs(t[4*i]-t[4*e+2*i])>.01)return!1;i+=1}return!0},mi.prototype.checkCollapsable=function(){if(this.o.length/2!=this.c.length/4)return!1;if(this.data.k.k[0].s)for(var t=0,e=this.data.k.k.length;t<e;){if(!this.comparePoints(this.data.k.k[t].s,this.data.p))return!1;t+=1}else if(!this.comparePoints(this.data.k.k,this.data.p))return!1;return!0},mi.prototype.getValue=function(t){if(this.prop.getValue(),this._mdf=!1,this._cmdf=!1,this._omdf=!1,this.prop._mdf||t){var e,i,s,a=4*this.data.p;for(e=0;e<a;e+=1)i=e%4==0?100:255,s=Math.round(this.prop.v[e]*i),this.c[e]!==s&&(this.c[e]=s,this._cmdf=!t);if(this.o.length)for(a=this.prop.v.length,e=4*this.data.p;e<a;e+=1)i=e%2==0?100:1,s=e%2==0?Math.round(100*this.prop.v[e]):this.prop.v[e],this.o[e-4*this.data.p]!==s&&(this.o[e-4*this.data.p]=s,this._omdf=!t);this._mdf=!t}},r([xt],mi),di.prototype.initGradientData=function(t,e,i){this.o=_t.getProp(t,e.o,0,.01,this),this.s=_t.getProp(t,e.s,1,null,this),this.e=_t.getProp(t,e.e,1,null,this),this.h=_t.getProp(t,e.h||{k:0},0,.01,this),this.a=_t.getProp(t,e.a||{k:0},0,w,this),this.g=new mi(t,e.g,this),this.style=i,this.stops=[],this.setGradientData(i.pElem,e),this.setGradientOpacity(e,i),this._isAnimated=!!this._isAnimated},di.prototype.setGradientData=function(t,e){var i=B(),a=X(1===e.t?"linearGradient":"radialGradient");a.setAttribute("id",i),a.setAttribute("spreadMethod","pad"),a.setAttribute("gradientUnits","userSpaceOnUse");var r,n,h,o=[];for(h=4*e.g.p,n=0;n<h;n+=4)r=X("stop"),a.appendChild(r),o.push(r);t.setAttribute("gf"===e.ty?"fill":"stroke","url("+s()+"#"+i+")"),this.gf=a,this.cst=o},di.prototype.setGradientOpacity=function(t,e){if(this.g._hasOpacity&&!this.g._collapsable){var i,a,r,n=X("mask"),h=X("path");n.appendChild(h);var o=B(),l=B();n.setAttribute("id",l);var p=X(1===t.t?"linearGradient":"radialGradient");p.setAttribute("id",o),p.setAttribute("spreadMethod","pad"),p.setAttribute("gradientUnits","userSpaceOnUse"),r=t.g.k.k[0].s?t.g.k.k[0].s.length:t.g.k.k.length;var f=this.stops;for(a=4*t.g.p;a<r;a+=2)(i=X("stop")).setAttribute("stop-color","rgb(255,255,255)"),p.appendChild(i),f.push(i);h.setAttribute("gf"===t.ty?"fill":"stroke","url("+s()+"#"+o+")"),"gs"===t.ty&&(h.setAttribute("stroke-linecap",ai[t.lc||2]),h.setAttribute("stroke-linejoin",ri[t.lj||2]),1===t.lj&&h.setAttribute("stroke-miterlimit",t.ml)),this.of=p,this.ms=n,this.ost=f,this.maskId=l,e.msElem=h}},r([xt],di),r([di,xt],ci);var yi=function(t,e,i,s){if(0===e)return"";var a,r=t.o,n=t.i,h=t.v,o=" M"+s.applyToPointStringified(h[0][0],h[0][1]);for(a=1;a<e;a+=1)o+=" C"+s.applyToPointStringified(r[a-1][0],r[a-1][1])+" "+s.applyToPointStringified(n[a][0],n[a][1])+" "+s.applyToPointStringified(h[a][0],h[a][1]);return i&&e&&(o+=" C"+s.applyToPointStringified(r[a-1][0],r[a-1][1])+" "+s.applyToPointStringified(n[0][0],n[0][1])+" "+s.applyToPointStringified(h[0][0],h[0][1]),o+="z"),o},vi=function(){var t=new It,e=new It;function i(t,e,i){(i||e.transform.op._mdf)&&e.transform.container.setAttribute("opacity",e.transform.op.v),(i||e.transform.mProps._mdf)&&e.transform.container.setAttribute("transform",e.transform.mProps.v.to2dCSS())}function s(){}function a(i,s,a){var r,n,h,o,l,p,f,m,d,c,u=s.styles.length,g=s.lvl;for(p=0;p<u;p+=1){if(o=s.sh._mdf||a,s.styles[p].lvl<g){for(m=e.reset(),d=g-s.styles[p].lvl,c=s.transformers.length-1;!o&&d>0;)o=s.transformers[c].mProps._mdf||o,d-=1,c-=1;if(o)for(d=g-s.styles[p].lvl,c=s.transformers.length-1;d>0;)m.multiply(s.transformers[c].mProps.v),d-=1,c-=1}else m=t;if(n=(f=s.sh.paths)._length,o){for(h="",r=0;r<n;r+=1)(l=f.shapes[r])&&l._length&&(h+=yi(l,l._length,l.c,m));s.caches[p]=h}else h=s.caches[p];s.styles[p].d+=!0===i.hd?"":h,s.styles[p]._mdf=o||s.styles[p]._mdf}}function r(t,e,i){var s=e.style;(e.c._mdf||i)&&s.pElem.setAttribute("fill","rgb("+v(e.c.v[0])+","+v(e.c.v[1])+","+v(e.c.v[2])+")"),(e.o._mdf||i)&&s.pElem.setAttribute("fill-opacity",e.o.v)}function n(t,e,i){h(t,e,i),o(t,e,i)}function h(t,e,i){var s,a,r,n,h,o=e.gf,l=e.g._hasOpacity,p=e.s.v,f=e.e.v;if(e.o._mdf||i){var m="gf"===t.ty?"fill-opacity":"stroke-opacity";e.style.pElem.setAttribute(m,e.o.v)}if(e.s._mdf||i){var d=1===t.t?"x1":"cx",c="x1"===d?"y1":"cy";o.setAttribute(d,p[0]),o.setAttribute(c,p[1]),l&&!e.g._collapsable&&(e.of.setAttribute(d,p[0]),e.of.setAttribute(c,p[1]))}if(e.g._cmdf||i){s=e.cst;var u=e.g.c;for(r=s.length,a=0;a<r;a+=1)(n=s[a]).setAttribute("offset",u[4*a]+"%"),n.setAttribute("stop-color","rgb("+u[4*a+1]+","+u[4*a+2]+","+u[4*a+3]+")")}if(l&&(e.g._omdf||i)){var g=e.g.o;for(r=(s=e.g._collapsable?e.cst:e.ost).length,a=0;a<r;a+=1)n=s[a],e.g._collapsable||n.setAttribute("offset",g[2*a]+"%"),n.setAttribute("stop-opacity",g[2*a+1])}if(1===t.t)(e.e._mdf||i)&&(o.setAttribute("x2",f[0]),o.setAttribute("y2",f[1]),l&&!e.g._collapsable&&(e.of.setAttribute("x2",f[0]),e.of.setAttribute("y2",f[1])));else if((e.s._mdf||e.e._mdf||i)&&(h=Math.sqrt(Math.pow(p[0]-f[0],2)+Math.pow(p[1]-f[1],2)),o.setAttribute("r",h),l&&!e.g._collapsable&&e.of.setAttribute("r",h)),e.s._mdf||e.e._mdf||e.h._mdf||e.a._mdf||i){h||(h=Math.sqrt(Math.pow(p[0]-f[0],2)+Math.pow(p[1]-f[1],2)));var y=Math.atan2(f[1]-p[1],f[0]-p[0]),v=e.h.v;v>=1?v=.99:v<=-1&&(v=-.99);var b=h*v,_=Math.cos(y+e.a.v)*b+p[0],x=Math.sin(y+e.a.v)*b+p[1];o.setAttribute("fx",_),o.setAttribute("fy",x),l&&!e.g._collapsable&&(e.of.setAttribute("fx",_),e.of.setAttribute("fy",x))}}function o(t,e,i){var s=e.style,a=e.d;a&&(a._mdf||i)&&a.dashStr&&(s.pElem.setAttribute("stroke-dasharray",a.dashStr),s.pElem.setAttribute("stroke-dashoffset",a.dashoffset[0])),e.c&&(e.c._mdf||i)&&s.pElem.setAttribute("stroke","rgb("+v(e.c.v[0])+","+v(e.c.v[1])+","+v(e.c.v[2])+")"),(e.o._mdf||i)&&s.pElem.setAttribute("stroke-opacity",e.o.v),(e.w._mdf||i)&&(s.pElem.setAttribute("stroke-width",e.w.v),s.msElem&&s.msElem.setAttribute("stroke-width",e.w.v))}return{createRenderFunction:function(t){switch(t.ty){case"fl":return r;case"gf":return h;case"gs":return n;case"st":return o;case"sh":case"el":case"rc":case"sr":return a;case"tr":return i;case"no":return s;default:return null}}}}();function bi(t,e,i){this.shapes=[],this.shapesData=t.shapes,this.stylesList=[],this.shapeModifiers=[],this.itemsData=[],this.processedElements=[],this.animatedContents=[],this.initElement(t,e,i),this.prevViewData=[]}function _i(t,e,i,s,a,r){this.o=t,this.sw=e,this.sc=i,this.fc=s,this.m=a,this.p=r,this._mdf={o:!0,sw:!!e,sc:!!i,fc:!!s,m:!0,p:!0}}function xi(t,e){this._frameId=i,this.pv="",this.v="",this.kf=!1,this._isFirstFrame=!0,this._mdf=!1,e.d&&e.d.sid&&(e.d=t.globalData.slotManager.getProp(e.d)),this.data=e,this.elem=t,this.comp=this.elem.comp,this.keysIndex=0,this.canResize=!1,this.minimumFontSize=1,this.effectsSequence=[],this.currentData={ascent:0,boxWidth:this.defaultBoxWidth,f:"",fStyle:"",fWeight:"",fc:"",j:"",justifyOffset:"",l:[],lh:0,lineWidths:[],ls:"",of:"",s:"",sc:"",sw:0,t:0,tr:0,sz:0,ps:null,fillColorAnim:!1,strokeColorAnim:!1,strokeWidthAnim:!1,yOffset:0,finalSize:0,finalText:[],finalLineHeight:0,__complete:!1},this.copyData(this.currentData,this.data.d.k[0].s),this.searchProperty()||this.completeTextData(this.currentData)}r([Oe,Xe,Qe,si,$e,Ne,ti],bi),bi.prototype.initSecondaryElement=function(){},bi.prototype.identityMatrix=new It,bi.prototype.buildExpressionInterface=function(){},bi.prototype.createContent=function(){this.searchShapes(this.shapesData,this.itemsData,this.prevViewData,this.layerElement,0,[],!0),this.filterUniqueShapes()},bi.prototype.filterUniqueShapes=function(){var t,e,i,s,a=this.shapes.length,r=this.stylesList.length,n=[],h=!1;for(i=0;i<r;i+=1){for(s=this.stylesList[i],h=!1,n.length=0,t=0;t<a;t+=1)-1!==(e=this.shapes[t]).styles.indexOf(s)&&(n.push(e),h=e._isAnimated||h);n.length>1&&h&&this.setShapesAsAnimated(n)}},bi.prototype.setShapesAsAnimated=function(t){var e,i=t.length;for(e=0;e<i;e+=1)t[e].setAsAnimated()},bi.prototype.createStyleElement=function(t,e){var i,a=new hi(t,e),r=a.pElem;if("st"===t.ty)i=new li(this,t,a);else if("fl"===t.ty)i=new pi(this,t,a);else if("gf"===t.ty||"gs"===t.ty){i=new("gf"===t.ty?di:ci)(this,t,a),this.globalData.defs.appendChild(i.gf),i.maskId&&(this.globalData.defs.appendChild(i.ms),this.globalData.defs.appendChild(i.of),r.setAttribute("mask","url("+s()+"#"+i.maskId+")"))}else"no"===t.ty&&(i=new fi(this,t,a));return"st"!==t.ty&&"gs"!==t.ty||(r.setAttribute("stroke-linecap",ai[t.lc||2]),r.setAttribute("stroke-linejoin",ri[t.lj||2]),r.setAttribute("fill-opacity","0"),1===t.lj&&r.setAttribute("stroke-miterlimit",t.ml)),2===t.r&&r.setAttribute("fill-rule","evenodd"),t.ln&&r.setAttribute("id",t.ln),t.cl&&r.setAttribute("class",t.cl),t.bm&&(r.style["mix-blend-mode"]=Ce(t.bm)),this.stylesList.push(a),this.addToAnimatedContents(t,i),i},bi.prototype.createGroupElement=function(t){var e=new ui;return t.ln&&e.gr.setAttribute("id",t.ln),t.cl&&e.gr.setAttribute("class",t.cl),t.bm&&(e.gr.style["mix-blend-mode"]=Ce(t.bm)),e},bi.prototype.createTransformElement=function(t,e){var i=Yt.getTransformProperty(this,t,this),s=new gi(i,i.o,e);return this.addToAnimatedContents(t,s),s},bi.prototype.createShapeElement=function(t,e,i){var s=4;"rc"===t.ty?s=5:"el"===t.ty?s=6:"sr"===t.ty&&(s=7);var a=new ni(e,i,Ft.getShapeProp(this,t,s,this));return this.shapes.push(a),this.addShapeToModifiers(a),this.addToAnimatedContents(t,a),a},bi.prototype.addToAnimatedContents=function(t,e){for(var i=0,s=this.animatedContents.length;i<s;){if(this.animatedContents[i].element===e)return;i+=1}this.animatedContents.push({fn:vi.createRenderFunction(t),element:e,data:t})},bi.prototype.setElementStyles=function(t){var e,i=t.styles,s=this.stylesList.length;for(e=0;e<s;e+=1)-1!==i.indexOf(this.stylesList[e])||this.stylesList[e].closed||i.push(this.stylesList[e])},bi.prototype.reloadShapes=function(){var t;this._isFirstFrame=!0;var e=this.itemsData.length;for(t=0;t<e;t+=1)this.prevViewData[t]=this.itemsData[t];for(this.searchShapes(this.shapesData,this.itemsData,this.prevViewData,this.layerElement,0,[],!0),this.filterUniqueShapes(),e=this.dynamicProperties.length,t=0;t<e;t+=1)this.dynamicProperties[t].getValue();this.renderModifiers()},bi.prototype.searchShapes=function(t,e,i,s,a,r,n){var h,o,l,p,f,m,d=[].concat(r),c=t.length-1,u=[],g=[];for(h=c;h>=0;h-=1){if((m=this.searchProcessedElement(t[h]))?e[h]=i[m-1]:t[h]._render=n,"fl"===t[h].ty||"st"===t[h].ty||"gf"===t[h].ty||"gs"===t[h].ty||"no"===t[h].ty)m?e[h].style.closed=t[h].hd:e[h]=this.createStyleElement(t[h],a),t[h]._render&&e[h].style.pElem.parentNode!==s&&s.appendChild(e[h].style.pElem),u.push(e[h].style);else if("gr"===t[h].ty){if(m)for(l=e[h].it.length,o=0;o<l;o+=1)e[h].prevViewData[o]=e[h].it[o];else e[h]=this.createGroupElement(t[h]);this.searchShapes(t[h].it,e[h].it,e[h].prevViewData,e[h].gr,a+1,d,n),t[h]._render&&e[h].gr.parentNode!==s&&s.appendChild(e[h].gr)}else"tr"===t[h].ty?(m||(e[h]=this.createTransformElement(t[h],s)),p=e[h].transform,d.push(p)):"sh"===t[h].ty||"rc"===t[h].ty||"el"===t[h].ty||"sr"===t[h].ty?(m||(e[h]=this.createShapeElement(t[h],d,a)),this.setElementStyles(e[h])):"tm"===t[h].ty||"rd"===t[h].ty||"ms"===t[h].ty||"pb"===t[h].ty||"zz"===t[h].ty||"op"===t[h].ty?(m?(f=e[h]).closed=!1:((f=jt.getModifier(t[h].ty)).init(this,t[h]),e[h]=f,this.shapeModifiers.push(f)),g.push(f)):"rp"===t[h].ty&&(m?(f=e[h]).closed=!0:(f=jt.getModifier(t[h].ty),e[h]=f,f.init(this,t,h,e),this.shapeModifiers.push(f),n=!1),g.push(f));this.addProcessedElement(t[h],h+1)}for(c=u.length,h=0;h<c;h+=1)u[h].closed=!0;for(c=g.length,h=0;h<c;h+=1)g[h].closed=!0},bi.prototype.renderInnerContent=function(){var t;this.renderModifiers();var e=this.stylesList.length;for(t=0;t<e;t+=1)this.stylesList[t].reset();for(this.renderShape(),t=0;t<e;t+=1)(this.stylesList[t]._mdf||this._isFirstFrame)&&(this.stylesList[t].msElem&&(this.stylesList[t].msElem.setAttribute("d",this.stylesList[t].d),this.stylesList[t].d="M0 0"+this.stylesList[t].d),this.stylesList[t].pElem.setAttribute("d",this.stylesList[t].d||"M0 0"))},bi.prototype.renderShape=function(){var t,e,i=this.animatedContents.length;for(t=0;t<i;t+=1)e=this.animatedContents[t],(this._isFirstFrame||e.element._isAnimated)&&!0!==e.data&&e.fn(e.data,e.element,this._isFirstFrame)},bi.prototype.destroy=function(){this.destroyBaseElement(),this.shapesData=null,this.itemsData=null},_i.prototype.update=function(t,e,i,s,a,r){this._mdf.o=!1,this._mdf.sw=!1,this._mdf.sc=!1,this._mdf.fc=!1,this._mdf.m=!1,this._mdf.p=!1;var n=!1;return this.o!==t&&(this.o=t,this._mdf.o=!0,n=!0),this.sw!==e&&(this.sw=e,this._mdf.sw=!0,n=!0),this.sc!==i&&(this.sc=i,this._mdf.sc=!0,n=!0),this.fc!==s&&(this.fc=s,this._mdf.fc=!0,n=!0),this.m!==a&&(this.m=a,this._mdf.m=!0,n=!0),!r.length||this.p[0]===r[0]&&this.p[1]===r[1]&&this.p[4]===r[4]&&this.p[5]===r[5]&&this.p[12]===r[12]&&this.p[13]===r[13]||(this.p=r,this._mdf.p=!0,n=!0),n},xi.prototype.defaultBoxWidth=[0,0],xi.prototype.copyData=function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t},xi.prototype.setCurrentData=function(t){t.__complete||this.completeTextData(t),this.currentData=t,this.currentData.boxWidth=this.currentData.boxWidth||this.defaultBoxWidth,this._mdf=!0},xi.prototype.searchProperty=function(){return this.searchKeyframes()},xi.prototype.searchKeyframes=function(){return this.kf=this.data.d.k.length>1,this.kf&&this.addEffect(this.getKeyframeValue.bind(this)),this.kf},xi.prototype.addEffect=function(t){this.effectsSequence.push(t),this.elem.addDynamicProperty(this)},xi.prototype.getValue=function(t){if(this.elem.globalData.frameId!==this.frameId&&this.effectsSequence.length||t){this.currentData.t=this.data.d.k[this.keysIndex].s.t;var e=this.currentData,i=this.keysIndex;if(this.lock)this.setCurrentData(this.currentData);else{var s;this.lock=!0,this._mdf=!1;var a=this.effectsSequence.length,r=t||this.data.d.k[this.keysIndex].s;for(s=0;s<a;s+=1)r=i!==this.keysIndex?this.effectsSequence[s](r,r.t):this.effectsSequence[s](this.currentData,r.t);e!==r&&this.setCurrentData(r),this.v=this.currentData,this.pv=this.v,this.lock=!1,this.frameId=this.elem.globalData.frameId}}},xi.prototype.getKeyframeValue=function(){for(var t=this.data.d.k,e=this.elem.comp.renderedFrame,i=0,s=t.length;i<=s-1&&!(i===s-1||t[i+1].t>e);)i+=1;return this.keysIndex!==i&&(this.keysIndex=i),this.data.d.k[this.keysIndex].s},xi.prototype.buildFinalText=function(t){for(var e,i,s=[],a=0,r=t.length,n=!1,h=!1,o="";a<r;)n=h,h=!1,e=t.charCodeAt(a),o=t.charAt(a),Pe.isCombinedCharacter(e)?n=!0:e>=55296&&e<=56319?Pe.isRegionalFlag(t,a)?o=t.substr(a,14):(i=t.charCodeAt(a+1))>=56320&&i<=57343&&(Pe.isModifier(e,i)?(o=t.substr(a,2),n=!0):o=Pe.isFlagEmoji(t.substr(a,4))?t.substr(a,4):t.substr(a,2)):e>56319?(i=t.charCodeAt(a+1),Pe.isVariationSelector(e)&&(n=!0)):Pe.isZeroWidthJoiner(e)&&(n=!0,h=!0),n?(s[s.length-1]+=o,n=!1):s.push(o),a+=o.length;return s},xi.prototype.completeTextData=function(t){t.__complete=!0;var e,i,s,a,r,n,h,o=this.elem.globalData.fontManager,l=this.data,p=[],f=0,m=l.m.g,d=0,c=0,u=0,g=[],y=0,v=0,b=o.getFontByName(t.f),_=0,x=we(b);t.fWeight=x.weight,t.fStyle=x.style,t.finalSize=t.s,t.finalText=this.buildFinalText(t.t),i=t.finalText.length,t.finalLineHeight=t.lh;var k,w=t.tr/1e3*t.finalSize;if(t.sz)for(var P,S,A=!0,D=t.sz[0],C=t.sz[1];A;){P=0,y=0,i=(S=this.buildFinalText(t.t)).length,w=t.tr/1e3*t.finalSize;var E=-1;for(e=0;e<i;e+=1)k=S[e].charCodeAt(0),s=!1," "===S[e]?E=e:13!==k&&3!==k||(y=0,s=!0,P+=t.finalLineHeight||1.2*t.finalSize),o.chars?(h=o.getCharData(S[e],b.fStyle,b.fFamily),_=s?0:h.w*t.finalSize/100):_=o.measureText(S[e],t.f,t.finalSize),y+_>D&&" "!==S[e]?(-1===E?i+=1:e=E,P+=t.finalLineHeight||1.2*t.finalSize,S.splice(e,E===e?1:0,"\r"),E=-1,y=0):(y+=_,y+=w);P+=b.ascent*t.finalSize/100,this.canResize&&t.finalSize>this.minimumFontSize&&C<P?(t.finalSize-=1,t.finalLineHeight=t.finalSize*t.lh/t.s):(t.finalText=S,i=t.finalText.length,A=!1)}y=-w,_=0;var M,T=0;for(e=0;e<i;e+=1)if(s=!1,13===(k=(M=t.finalText[e]).charCodeAt(0))||3===k?(T=0,g.push(y),v=y>v?y:v,y=-2*w,a="",s=!0,u+=1):a=M,o.chars?(h=o.getCharData(M,b.fStyle,o.getFontByName(t.f).fFamily),_=s?0:h.w*t.finalSize/100):_=o.measureText(a,t.f,t.finalSize)," "===M?T+=_+w:(y+=_+w+T,T=0),p.push({l:_,an:_,add:d,n:s,anIndexes:[],val:a,line:u,animatorJustifyOffset:0}),2==m){if(d+=_,""===a||" "===a||e===i-1){for(""!==a&&" "!==a||(d-=_);c<=e;)p[c].an=d,p[c].ind=f,p[c].extra=_,c+=1;f+=1,d=0}}else if(3==m){if(d+=_,""===a||e===i-1){for(""===a&&(d-=_);c<=e;)p[c].an=d,p[c].ind=f,p[c].extra=_,c+=1;d=0,f+=1}}else p[f].ind=f,p[f].extra=0,f+=1;if(t.l=p,v=y>v?y:v,g.push(y),t.sz)t.boxWidth=t.sz[0],t.justifyOffset=0;else switch(t.boxWidth=v,t.j){case 1:t.justifyOffset=-t.boxWidth;break;case 2:t.justifyOffset=-t.boxWidth/2;break;default:t.justifyOffset=0}t.lineWidths=g;var F,I,L,B,z=l.a;n=z.length;var V=[];for(r=0;r<n;r+=1){for((F=z[r]).a.sc&&(t.strokeColorAnim=!0),F.a.sw&&(t.strokeWidthAnim=!0),(F.a.fc||F.a.fh||F.a.fs||F.a.fb)&&(t.fillColorAnim=!0),B=0,L=F.s.b,e=0;e<i;e+=1)(I=p[e]).anIndexes[r]=B,(1==L&&""!==I.val||2==L&&""!==I.val&&" "!==I.val||3==L&&(I.n||" "==I.val||e==i-1)||4==L&&(I.n||e==i-1))&&(1===F.s.rn&&V.push(B),B+=1);l.a[r].s.totalChars=B;var R,O=-1;if(1===F.s.rn)for(e=0;e<i;e+=1)O!=(I=p[e]).anIndexes[r]&&(O=I.anIndexes[r],R=V.splice(Math.floor(Math.random()*V.length),1)[0]),I.anIndexes[r]=R}t.yOffset=t.finalLineHeight||1.2*t.finalSize,t.ls=t.ls||0,t.ascent=b.ascent*t.finalSize/100},xi.prototype.updateDocumentData=function(t,e){e=void 0===e?this.keysIndex:e;var i=this.copyData({},this.data.d.k[e].s);i=this.copyData(i,t),this.data.d.k[e].s=i,this.recalculate(e),this.setCurrentData(i),this.elem.addDynamicProperty(this)},xi.prototype.recalculate=function(t){var e=this.data.d.k[t].s;e.__complete=!1,this.keysIndex=0,this._isFirstFrame=!0,this.getValue(e)},xi.prototype.canResizeFont=function(t){this.canResize=t,this.recalculate(this.keysIndex),this.elem.addDynamicProperty(this)},xi.prototype.setMinimumFontSize=function(t){this.minimumFontSize=Math.floor(t)||1,this.recalculate(this.keysIndex),this.elem.addDynamicProperty(this)};var ki=function(){var t=Math.max,e=Math.min,i=Math.floor;function s(t,e){this._currentTextLength=-1,this.k=!1,this.data=e,this.elem=t,this.comp=t.comp,this.finalS=0,this.finalE=0,this.initDynamicPropertyContainer(t),this.s=_t.getProp(t,e.s||{k:0},0,0,this),this.e="e"in e?_t.getProp(t,e.e,0,0,this):{v:100},this.o=_t.getProp(t,e.o||{k:0},0,0,this),this.xe=_t.getProp(t,e.xe||{k:0},0,0,this),this.ne=_t.getProp(t,e.ne||{k:0},0,0,this),this.sm=_t.getProp(t,e.sm||{k:100},0,0,this),this.a=_t.getProp(t,e.a,0,.01,this),this.dynamicProperties.length||this.getValue()}return s.prototype={getMult:function(s){this._currentTextLength!==this.elem.textProperty.currentData.l.length&&this.getValue();var a=0,r=0,n=1,h=1;this.ne.v>0?a=this.ne.v/100:r=-this.ne.v/100,this.xe.v>0?n=1-this.xe.v/100:h=1+this.xe.v/100;var o=it.getBezierEasing(a,r,n,h).get,l=0,p=this.finalS,f=this.finalE,m=this.data.sh;if(2===m)l=o(l=f===p?s>=f?1:0:t(0,e(.5/(f-p)+(s-p)/(f-p),1)));else if(3===m)l=o(l=f===p?s>=f?0:1:1-t(0,e(.5/(f-p)+(s-p)/(f-p),1)));else if(4===m)f===p?l=0:(l=t(0,e(.5/(f-p)+(s-p)/(f-p),1)))<.5?l*=2:l=1-2*(l-.5),l=o(l);else if(5===m){if(f===p)l=0;else{var d=f-p,c=-d/2+(s=e(t(0,s+.5-p),f-p)),u=d/2;l=Math.sqrt(1-c*c/(u*u))}l=o(l)}else 6===m?(f===p?l=0:(s=e(t(0,s+.5-p),f-p),l=(1+Math.cos(Math.PI+2*Math.PI*s/(f-p)))/2),l=o(l)):(s>=i(p)&&(l=t(0,e(s-p<0?e(f,1)-(p-s):f-s,1))),l=o(l));if(100!==this.sm.v){var g=.01*this.sm.v;0===g&&(g=1e-8);var y=.5-.5*g;l<y?l=0:(l=(l-y)/g)>1&&(l=1)}return l*this.a.v},getValue:function(t){this.iterateDynamicProperties(),this._mdf=t||this._mdf,this._currentTextLength=this.elem.textProperty.currentData.l.length||0,t&&2===this.data.r&&(this.e.v=this._currentTextLength);var e=2===this.data.r?1:100/this.data.totalChars,i=this.o.v/e,s=this.s.v/e+i,a=this.e.v/e+i;if(s>a){var r=s;s=a,a=r}this.finalS=s,this.finalE=a}},r([xt],s),{getTextSelectorProp:function(t,e,i){return new s(t,e,i)}}}();function wi(t,e,i){var s={propType:!1},a=_t.getProp,r=e.a;this.a={r:r.r?a(t,r.r,0,w,i):s,rx:r.rx?a(t,r.rx,0,w,i):s,ry:r.ry?a(t,r.ry,0,w,i):s,sk:r.sk?a(t,r.sk,0,w,i):s,sa:r.sa?a(t,r.sa,0,w,i):s,s:r.s?a(t,r.s,1,.01,i):s,a:r.a?a(t,r.a,1,0,i):s,o:r.o?a(t,r.o,0,.01,i):s,p:r.p?a(t,r.p,1,0,i):s,sw:r.sw?a(t,r.sw,0,0,i):s,sc:r.sc?a(t,r.sc,1,0,i):s,fc:r.fc?a(t,r.fc,1,0,i):s,fh:r.fh?a(t,r.fh,0,0,i):s,fs:r.fs?a(t,r.fs,0,.01,i):s,fb:r.fb?a(t,r.fb,0,.01,i):s,t:r.t?a(t,r.t,0,0,i):s},this.s=ki.getTextSelectorProp(t,e.s,i),this.s.t=e.s.t}function Pi(t,e,i){this._isFirstFrame=!0,this._hasMaskedPath=!1,this._frameId=-1,this._textData=t,this._renderType=e,this._elem=i,this._animatorsData=l(this._textData.a.length),this._pathData={},this._moreOptions={alignment:{}},this.renderedLetters=[],this.lettersChangedFlag=!1,this.initDynamicPropertyContainer(i)}function Si(){}Pi.prototype.searchProperties=function(){var t,e,i=this._textData.a.length,s=_t.getProp;for(t=0;t<i;t+=1)e=this._textData.a[t],this._animatorsData[t]=new wi(this._elem,e,this);this._textData.p&&"m"in this._textData.p?(this._pathData={a:s(this._elem,this._textData.p.a,0,0,this),f:s(this._elem,this._textData.p.f,0,0,this),l:s(this._elem,this._textData.p.l,0,0,this),r:s(this._elem,this._textData.p.r,0,0,this),p:s(this._elem,this._textData.p.p,0,0,this),m:this._elem.maskManager.getMaskProperty(this._textData.p.m)},this._hasMaskedPath=!0):this._hasMaskedPath=!1,this._moreOptions.alignment=s(this._elem,this._textData.m.a,1,0,this)},Pi.prototype.getMeasures=function(t,e){if(this.lettersChangedFlag=e,this._mdf||this._isFirstFrame||e||this._hasMaskedPath&&this._pathData.m._mdf){this._isFirstFrame=!1;var i,s,a,r,n,h,o,l,p,f,m,d,c,u,g,y,v,b,_,x=this._moreOptions.alignment.v,k=this._animatorsData,w=this._textData,P=this.mHelper,S=this._renderType,A=this.renderedLetters.length,D=t.l;if(this._hasMaskedPath){if(_=this._pathData.m,!this._pathData.n||this._pathData._mdf){var C,E=_.v;for(this._pathData.r.v&&(E=E.reverse()),n={tLength:0,segments:[]},r=E._length-1,y=0,a=0;a<r;a+=1)C=ht.buildBezierData(E.v[a],E.v[a+1],[E.o[a][0]-E.v[a][0],E.o[a][1]-E.v[a][1]],[E.i[a+1][0]-E.v[a+1][0],E.i[a+1][1]-E.v[a+1][1]]),n.tLength+=C.segmentLength,n.segments.push(C),y+=C.segmentLength;a=r,_.v.c&&(C=ht.buildBezierData(E.v[a],E.v[0],[E.o[a][0]-E.v[a][0],E.o[a][1]-E.v[a][1]],[E.i[0][0]-E.v[0][0],E.i[0][1]-E.v[0][1]]),n.tLength+=C.segmentLength,n.segments.push(C),y+=C.segmentLength),this._pathData.pi=n}if(n=this._pathData.pi,h=this._pathData.f.v,m=0,f=1,l=0,p=!0,u=n.segments,h<0&&_.v.c)for(n.tLength<Math.abs(h)&&(h=-Math.abs(h)%n.tLength),f=(c=u[m=u.length-1].points).length-1;h<0;)h+=c[f].partialLength,(f-=1)<0&&(f=(c=u[m-=1].points).length-1);d=(c=u[m].points)[f-1],g=(o=c[f]).partialLength}r=D.length,i=0,s=0;var M,T,F,I,L,B=1.2*t.finalSize*.714,z=!0;F=k.length;var V,q,j,W,H,X,Y,G,K,J,Z,U,Q=-1,$=h,tt=m,et=f,it=-1,st="",at=this.defaultPropsArray;if(2===t.j||1===t.j){var rt=0,nt=0,ot=2===t.j?-.5:-1,lt=0,pt=!0;for(a=0;a<r;a+=1)if(D[a].n){for(rt&&(rt+=nt);lt<a;)D[lt].animatorJustifyOffset=rt,lt+=1;rt=0,pt=!0}else{for(T=0;T<F;T+=1)(M=k[T].a).t.propType&&(pt&&2===t.j&&(nt+=M.t.v*ot),(L=k[T].s.getMult(D[a].anIndexes[T],w.a[T].s.totalChars)).length?rt+=M.t.v*L[0]*ot:rt+=M.t.v*L*ot);pt=!1}for(rt&&(rt+=nt);lt<a;)D[lt].animatorJustifyOffset=rt,lt+=1}for(a=0;a<r;a+=1){if(P.reset(),W=1,D[a].n)i=0,s+=t.yOffset,s+=z?1:0,h=$,z=!1,this._hasMaskedPath&&(f=et,d=(c=u[m=tt].points)[f-1],g=(o=c[f]).partialLength,l=0),st="",Z="",K="",U="",at=this.defaultPropsArray;else{if(this._hasMaskedPath){if(it!==D[a].line){switch(t.j){case 1:h+=y-t.lineWidths[D[a].line];break;case 2:h+=(y-t.lineWidths[D[a].line])/2}it=D[a].line}Q!==D[a].ind&&(D[Q]&&(h+=D[Q].extra),h+=D[a].an/2,Q=D[a].ind),h+=x[0]*D[a].an*.005;var ft=0;for(T=0;T<F;T+=1)(M=k[T].a).p.propType&&((L=k[T].s.getMult(D[a].anIndexes[T],w.a[T].s.totalChars)).length?ft+=M.p.v[0]*L[0]:ft+=M.p.v[0]*L),M.a.propType&&((L=k[T].s.getMult(D[a].anIndexes[T],w.a[T].s.totalChars)).length?ft+=M.a.v[0]*L[0]:ft+=M.a.v[0]*L);for(p=!0,this._pathData.a.v&&(h=.5*D[0].an+(y-this._pathData.f.v-.5*D[0].an-.5*D[D.length-1].an)*Q/(r-1),h+=this._pathData.f.v);p;)l+g>=h+ft||!c?(v=(h+ft-l)/o.partialLength,q=d.point[0]+(o.point[0]-d.point[0])*v,j=d.point[1]+(o.point[1]-d.point[1])*v,P.translate(-x[0]*D[a].an*.005,-x[1]*B*.01),p=!1):c&&(l+=o.partialLength,(f+=1)>=c.length&&(f=0,u[m+=1]?c=u[m].points:_.v.c?(f=0,c=u[m=0].points):(l-=o.partialLength,c=null)),c&&(d=o,g=(o=c[f]).partialLength));V=D[a].an/2-D[a].add,P.translate(-V,0,0)}else V=D[a].an/2-D[a].add,P.translate(-V,0,0),P.translate(-x[0]*D[a].an*.005,-x[1]*B*.01,0);for(T=0;T<F;T+=1)(M=k[T].a).t.propType&&(L=k[T].s.getMult(D[a].anIndexes[T],w.a[T].s.totalChars),0===i&&0===t.j||(this._hasMaskedPath?L.length?h+=M.t.v*L[0]:h+=M.t.v*L:L.length?i+=M.t.v*L[0]:i+=M.t.v*L));for(t.strokeWidthAnim&&(X=t.sw||0),t.strokeColorAnim&&(H=t.sc?[t.sc[0],t.sc[1],t.sc[2]]:[0,0,0]),t.fillColorAnim&&t.fc&&(Y=[t.fc[0],t.fc[1],t.fc[2]]),T=0;T<F;T+=1)(M=k[T].a).a.propType&&((L=k[T].s.getMult(D[a].anIndexes[T],w.a[T].s.totalChars)).length?P.translate(-M.a.v[0]*L[0],-M.a.v[1]*L[1],M.a.v[2]*L[2]):P.translate(-M.a.v[0]*L,-M.a.v[1]*L,M.a.v[2]*L));for(T=0;T<F;T+=1)(M=k[T].a).s.propType&&((L=k[T].s.getMult(D[a].anIndexes[T],w.a[T].s.totalChars)).length?P.scale(1+(M.s.v[0]-1)*L[0],1+(M.s.v[1]-1)*L[1],1):P.scale(1+(M.s.v[0]-1)*L,1+(M.s.v[1]-1)*L,1));for(T=0;T<F;T+=1){if(M=k[T].a,L=k[T].s.getMult(D[a].anIndexes[T],w.a[T].s.totalChars),M.sk.propType&&(L.length?P.skewFromAxis(-M.sk.v*L[0],M.sa.v*L[1]):P.skewFromAxis(-M.sk.v*L,M.sa.v*L)),M.r.propType&&(L.length?P.rotateZ(-M.r.v*L[2]):P.rotateZ(-M.r.v*L)),M.ry.propType&&(L.length?P.rotateY(M.ry.v*L[1]):P.rotateY(M.ry.v*L)),M.rx.propType&&(L.length?P.rotateX(M.rx.v*L[0]):P.rotateX(M.rx.v*L)),M.o.propType&&(L.length?W+=(M.o.v*L[0]-W)*L[0]:W+=(M.o.v*L-W)*L),t.strokeWidthAnim&&M.sw.propType&&(L.length?X+=M.sw.v*L[0]:X+=M.sw.v*L),t.strokeColorAnim&&M.sc.propType)for(G=0;G<3;G+=1)L.length?H[G]+=(M.sc.v[G]-H[G])*L[0]:H[G]+=(M.sc.v[G]-H[G])*L;if(t.fillColorAnim&&t.fc){if(M.fc.propType)for(G=0;G<3;G+=1)L.length?Y[G]+=(M.fc.v[G]-Y[G])*L[0]:Y[G]+=(M.fc.v[G]-Y[G])*L;M.fh.propType&&(Y=L.length?N(Y,M.fh.v*L[0]):N(Y,M.fh.v*L)),M.fs.propType&&(Y=L.length?R(Y,M.fs.v*L[0]):R(Y,M.fs.v*L)),M.fb.propType&&(Y=L.length?O(Y,M.fb.v*L[0]):O(Y,M.fb.v*L))}}for(T=0;T<F;T+=1)(M=k[T].a).p.propType&&(L=k[T].s.getMult(D[a].anIndexes[T],w.a[T].s.totalChars),this._hasMaskedPath?L.length?P.translate(0,M.p.v[1]*L[0],-M.p.v[2]*L[1]):P.translate(0,M.p.v[1]*L,-M.p.v[2]*L):L.length?P.translate(M.p.v[0]*L[0],M.p.v[1]*L[1],-M.p.v[2]*L[2]):P.translate(M.p.v[0]*L,M.p.v[1]*L,-M.p.v[2]*L));if(t.strokeWidthAnim&&(K=X<0?0:X),t.strokeColorAnim&&(J="rgb("+Math.round(255*H[0])+","+Math.round(255*H[1])+","+Math.round(255*H[2])+")"),t.fillColorAnim&&t.fc&&(Z="rgb("+Math.round(255*Y[0])+","+Math.round(255*Y[1])+","+Math.round(255*Y[2])+")"),this._hasMaskedPath){if(P.translate(0,-t.ls),P.translate(0,x[1]*B*.01+s,0),this._pathData.p.v){b=(o.point[1]-d.point[1])/(o.point[0]-d.point[0]);var mt=180*Math.atan(b)/Math.PI;o.point[0]<d.point[0]&&(mt+=180),P.rotate(-mt*Math.PI/180)}P.translate(q,j,0),h-=x[0]*D[a].an*.005,D[a+1]&&Q!==D[a+1].ind&&(h+=D[a].an/2,h+=.001*t.tr*t.finalSize)}else{switch(P.translate(i,s,0),t.ps&&P.translate(t.ps[0],t.ps[1]+t.ascent,0),t.j){case 1:P.translate(D[a].animatorJustifyOffset+t.justifyOffset+(t.boxWidth-t.lineWidths[D[a].line]),0,0);break;case 2:P.translate(D[a].animatorJustifyOffset+t.justifyOffset+(t.boxWidth-t.lineWidths[D[a].line])/2,0,0)}P.translate(0,-t.ls),P.translate(V,0,0),P.translate(x[0]*D[a].an*.005,x[1]*B*.01,0),i+=D[a].l+.001*t.tr*t.finalSize}"html"===S?st=P.toCSS():"svg"===S?st=P.to2dCSS():at=[P.props[0],P.props[1],P.props[2],P.props[3],P.props[4],P.props[5],P.props[6],P.props[7],P.props[8],P.props[9],P.props[10],P.props[11],P.props[12],P.props[13],P.props[14],P.props[15]],U=W}A<=a?(I=new _i(U,K,J,Z,st,at),this.renderedLetters.push(I),A+=1,this.lettersChangedFlag=!0):(I=this.renderedLetters[a],this.lettersChangedFlag=I.update(U,K,J,Z,st,at)||this.lettersChangedFlag)}}},Pi.prototype.getValue=function(){this._elem.globalData.frameId!==this._frameId&&(this._frameId=this._elem.globalData.frameId,this.iterateDynamicProperties())},Pi.prototype.mHelper=new It,Pi.prototype.defaultPropsArray=[],r([xt],Pi),Si.prototype.initElement=function(t,e,i){this.lettersChangedFlag=!0,this.initFrame(),this.initBaseData(t,e,i),this.textProperty=new xi(this,t.t,this.dynamicProperties),this.textAnimator=new Pi(t.t,this.renderType,this),this.initTransform(t,e,i),this.initHierarchy(),this.initRenderable(),this.initRendererElement(),this.createContainerElements(),this.createRenderableComponents(),this.createContent(),this.hide(),this.textAnimator.searchProperties(this.dynamicProperties)},Si.prototype.prepareFrame=function(t){this._mdf=!1,this.prepareRenderableFrame(t),this.prepareProperties(t,this.isInRange)},Si.prototype.createPathShape=function(t,e){var i,s,a=e.length,r="";for(i=0;i<a;i+=1)"sh"===e[i].ty&&(s=e[i].ks.k,r+=yi(s,s.i.length,!0,t));return r},Si.prototype.updateDocumentData=function(t,e){this.textProperty.updateDocumentData(t,e)},Si.prototype.canResizeFont=function(t){this.textProperty.canResizeFont(t)},Si.prototype.setMinimumFontSize=function(t){this.textProperty.setMinimumFontSize(t)},Si.prototype.applyTextPropertiesToMatrix=function(t,e,i,s,a){switch(t.ps&&e.translate(t.ps[0],t.ps[1]+t.ascent,0),e.translate(0,-t.ls,0),t.j){case 1:e.translate(t.justifyOffset+(t.boxWidth-t.lineWidths[i]),0,0);break;case 2:e.translate(t.justifyOffset+(t.boxWidth-t.lineWidths[i])/2,0,0)}e.translate(s,a,0)},Si.prototype.buildColor=function(t){return"rgb("+Math.round(255*t[0])+","+Math.round(255*t[1])+","+Math.round(255*t[2])+")"},Si.prototype.emptyProp=new _i,Si.prototype.destroy=function(){},Si.prototype.validateText=function(){(this.textProperty._mdf||this.textProperty._isFirstFrame)&&(this.buildNewText(),this.textProperty._isFirstFrame=!1,this.textProperty._mdf=!1)};var Ai={shapes:[]};function Di(t,e,i){this.textSpans=[],this.renderType="svg",this.initElement(t,e,i)}function Ci(t,e,i){this.initElement(t,e,i)}function Ei(t,e,i){this.initFrame(),this.initBaseData(t,e,i),this.initFrame(),this.initTransform(t,e,i),this.initHierarchy()}function Mi(){}function Ti(){}function Fi(t,e,i){this.layers=t.layers,this.supports3d=!0,this.completeLayers=!1,this.pendingElements=[],this.elements=this.layers?l(this.layers.length):[],this.initElement(t,e,i),this.tm=t.tm?_t.getProp(this,t.tm,0,e.frameRate,this):{_placeholder:!0}}function Ii(t,e){this.animationItem=t,this.layers=null,this.renderedFrame=-1,this.svgElement=X("svg");var i="";if(e&&e.title){var s=X("title"),a=B();s.setAttribute("id",a),s.textContent=e.title,this.svgElement.appendChild(s),i+=a}if(e&&e.description){var r=X("desc"),n=B();r.setAttribute("id",n),r.textContent=e.description,this.svgElement.appendChild(r),i+=" "+n}i&&this.svgElement.setAttribute("aria-labelledby",i);var h=X("defs");this.svgElement.appendChild(h);var o=X("g");this.svgElement.appendChild(o),this.layerElement=o,this.renderConfig={preserveAspectRatio:e&&e.preserveAspectRatio||"xMidYMid meet",imagePreserveAspectRatio:e&&e.imagePreserveAspectRatio||"xMidYMid slice",contentVisibility:e&&e.contentVisibility||"visible",progressiveLoad:e&&e.progressiveLoad||!1,hideOnTransparent:!(e&&!1===e.hideOnTransparent),viewBoxOnly:e&&e.viewBoxOnly||!1,viewBoxSize:e&&e.viewBoxSize||!1,className:e&&e.className||"",id:e&&e.id||"",focusable:e&&e.focusable,filterSize:{width:e&&e.filterSize&&e.filterSize.width||"100%",height:e&&e.filterSize&&e.filterSize.height||"100%",x:e&&e.filterSize&&e.filterSize.x||"0%",y:e&&e.filterSize&&e.filterSize.y||"0%"},width:e&&e.width,height:e&&e.height,runExpressions:!e||void 0===e.runExpressions||e.runExpressions},this.globalData={_mdf:!1,frameNum:-1,defs:h,renderConfig:this.renderConfig},this.elements=[],this.pendingElements=[],this.destroyed=!1,this.rendererType="svg"}r([Oe,Xe,Qe,$e,Ne,ti,Si],Di),Di.prototype.createContent=function(){this.data.singleShape&&!this.globalData.fontManager.chars&&(this.textContainer=X("text"))},Di.prototype.buildTextContents=function(t){for(var e=0,i=t.length,s=[],a="";e<i;)t[e]===String.fromCharCode(13)||t[e]===String.fromCharCode(3)?(s.push(a),a=""):a+=t[e],e+=1;return s.push(a),s},Di.prototype.buildShapeData=function(t,e){if(t.shapes&&t.shapes.length){var i=t.shapes[0];if(i.it){var s=i.it[i.it.length-1];s.s&&(s.s.k[0]=e,s.s.k[1]=e)}}return t},Di.prototype.buildNewText=function(){var t,e;this.addDynamicProperty(this);var i=this.textProperty.currentData;this.renderedLetters=l(i?i.l.length:0),i.fc?this.layerElement.setAttribute("fill",this.buildColor(i.fc)):this.layerElement.setAttribute("fill","rgba(0,0,0,0)"),i.sc&&(this.layerElement.setAttribute("stroke",this.buildColor(i.sc)),this.layerElement.setAttribute("stroke-width",i.sw)),this.layerElement.setAttribute("font-size",i.finalSize);var s=this.globalData.fontManager.getFontByName(i.f);if(s.fClass)this.layerElement.setAttribute("class",s.fClass);else{this.layerElement.setAttribute("font-family",s.fFamily);var a=i.fWeight,r=i.fStyle;this.layerElement.setAttribute("font-style",r),this.layerElement.setAttribute("font-weight",a)}this.layerElement.setAttribute("aria-label",i.t);var n,h=i.l||[],o=!!this.globalData.fontManager.chars;e=h.length;var p=this.mHelper,f=this.data.singleShape,m=0,d=0,c=!0,u=.001*i.tr*i.finalSize;if(!f||o||i.sz){var g,y=this.textSpans.length;for(t=0;t<e;t+=1){if(this.textSpans[t]||(this.textSpans[t]={span:null,childSpan:null,glyph:null}),!o||!f||0===t){if(n=y>t?this.textSpans[t].span:X(o?"g":"text"),y<=t){if(n.setAttribute("stroke-linecap","butt"),n.setAttribute("stroke-linejoin","round"),n.setAttribute("stroke-miterlimit","4"),this.textSpans[t].span=n,o){var v=X("g");n.appendChild(v),this.textSpans[t].childSpan=v}this.textSpans[t].span=n,this.layerElement.appendChild(n)}n.style.display="inherit"}if(p.reset(),f&&(h[t].n&&(m=-u,d+=i.yOffset,d+=c?1:0,c=!1),this.applyTextPropertiesToMatrix(i,p,h[t].line,m,d),m+=h[t].l||0,m+=u),o){var b;if(1===(g=this.globalData.fontManager.getCharData(i.finalText[t],s.fStyle,this.globalData.fontManager.getFontByName(i.f).fFamily)).t)b=new Fi(g.data,this.globalData,this);else{var _=Ai;g.data&&g.data.shapes&&(_=this.buildShapeData(g.data,i.finalSize)),b=new bi(_,this.globalData,this)}if(this.textSpans[t].glyph){var x=this.textSpans[t].glyph;this.textSpans[t].childSpan.removeChild(x.layerElement),x.destroy()}this.textSpans[t].glyph=b,b._debug=!0,b.prepareFrame(0),b.renderFrame(),this.textSpans[t].childSpan.appendChild(b.layerElement),1===g.t&&this.textSpans[t].childSpan.setAttribute("transform","scale("+i.finalSize/100+","+i.finalSize/100+")")}else f&&n.setAttribute("transform","translate("+p.props[12]+","+p.props[13]+")"),n.textContent=h[t].val,n.setAttributeNS("http://www.w3.org/XML/1998/namespace","xml:space","preserve")}f&&n&&n.setAttribute("d","")}else{var k=this.textContainer,w="start";switch(i.j){case 1:w="end";break;case 2:w="middle";break;default:w="start"}k.setAttribute("text-anchor",w),k.setAttribute("letter-spacing",u);var P=this.buildTextContents(i.finalText);for(e=P.length,d=i.ps?i.ps[1]+i.ascent:0,t=0;t<e;t+=1)(n=this.textSpans[t].span||X("tspan")).textContent=P[t],n.setAttribute("x",0),n.setAttribute("y",d),n.style.display="inherit",k.appendChild(n),this.textSpans[t]||(this.textSpans[t]={span:null,glyph:null}),this.textSpans[t].span=n,d+=i.finalLineHeight;this.layerElement.appendChild(k)}for(;t<this.textSpans.length;)this.textSpans[t].span.style.display="none",t+=1;this._sizeChanged=!0},Di.prototype.sourceRectAtTime=function(){if(this.prepareFrame(this.comp.renderedFrame-this.data.st),this.renderInnerContent(),this._sizeChanged){this._sizeChanged=!1;var t=this.layerElement.getBBox();this.bbox={top:t.y,left:t.x,width:t.width,height:t.height}}return this.bbox},Di.prototype.getValue=function(){var t,e,i=this.textSpans.length;for(this.renderedFrame=this.comp.renderedFrame,t=0;t<i;t+=1)(e=this.textSpans[t].glyph)&&(e.prepareFrame(this.comp.renderedFrame-this.data.st),e._mdf&&(this._mdf=!0))},Di.prototype.renderInnerContent=function(){if(this.validateText(),(!this.data.singleShape||this._mdf)&&(this.textAnimator.getMeasures(this.textProperty.currentData,this.lettersChangedFlag),this.lettersChangedFlag||this.textAnimator.lettersChangedFlag)){var t,e;this._sizeChanged=!0;var i,s,a,r=this.textAnimator.renderedLetters,n=this.textProperty.currentData.l;for(e=n.length,t=0;t<e;t+=1)n[t].n||(i=r[t],s=this.textSpans[t].span,(a=this.textSpans[t].glyph)&&a.renderFrame(),i._mdf.m&&s.setAttribute("transform",i.m),i._mdf.o&&s.setAttribute("opacity",i.o),i._mdf.sw&&s.setAttribute("stroke-width",i.sw),i._mdf.sc&&s.setAttribute("stroke",i.sc),i._mdf.fc&&s.setAttribute("fill",i.fc))}},r([ei],Ci),Ci.prototype.createContent=function(){var t=X("rect");t.setAttribute("width",this.data.sw),t.setAttribute("height",this.data.sh),t.setAttribute("fill",this.data.sc),this.layerElement.appendChild(t)},Ei.prototype.prepareFrame=function(t){this.prepareProperties(t,!0)},Ei.prototype.renderFrame=function(){},Ei.prototype.getBaseElement=function(){return null},Ei.prototype.destroy=function(){},Ei.prototype.sourceRectAtTime=function(){},Ei.prototype.hide=function(){},r([Oe,Xe,$e,Ne],Ei),r([We],Mi),Mi.prototype.createNull=function(t){return new Ei(t,this.globalData,this)},Mi.prototype.createShape=function(t){return new bi(t,this.globalData,this)},Mi.prototype.createText=function(t){return new Di(t,this.globalData,this)},Mi.prototype.createImage=function(t){return new ei(t,this.globalData,this)},Mi.prototype.createSolid=function(t){return new Ci(t,this.globalData,this)},Mi.prototype.configAnimation=function(t){this.svgElement.setAttribute("xmlns","http://www.w3.org/2000/svg"),this.svgElement.setAttribute("xmlns:xlink","http://www.w3.org/1999/xlink"),this.renderConfig.viewBoxSize?this.svgElement.setAttribute("viewBox",this.renderConfig.viewBoxSize):this.svgElement.setAttribute("viewBox","0 0 "+t.w+" "+t.h),this.renderConfig.viewBoxOnly||(this.svgElement.setAttribute("width",t.w),this.svgElement.setAttribute("height",t.h),this.svgElement.style.width="100%",this.svgElement.style.height="100%",this.svgElement.style.transform="translate3d(0,0,0)",this.svgElement.style.contentVisibility=this.renderConfig.contentVisibility),this.renderConfig.width&&this.svgElement.setAttribute("width",this.renderConfig.width),this.renderConfig.height&&this.svgElement.setAttribute("height",this.renderConfig.height),this.renderConfig.className&&this.svgElement.setAttribute("class",this.renderConfig.className),this.renderConfig.id&&this.svgElement.setAttribute("id",this.renderConfig.id),void 0!==this.renderConfig.focusable&&this.svgElement.setAttribute("focusable",this.renderConfig.focusable),this.svgElement.setAttribute("preserveAspectRatio",this.renderConfig.preserveAspectRatio),this.animationItem.wrapper.appendChild(this.svgElement);var e=this.globalData.defs;this.setupGlobalData(t,e),this.globalData.progressiveLoad=this.renderConfig.progressiveLoad,this.data=t;var i=X("clipPath"),a=X("rect");a.setAttribute("width",t.w),a.setAttribute("height",t.h),a.setAttribute("x",0),a.setAttribute("y",0);var r=B();i.setAttribute("id",r),i.appendChild(a),this.layerElement.setAttribute("clip-path","url("+s()+"#"+r+")"),e.appendChild(i),this.layers=t.layers,this.elements=l(t.layers.length)},Mi.prototype.destroy=function(){var t;this.animationItem.wrapper&&(this.animationItem.wrapper.innerText=""),this.layerElement=null,this.globalData.defs=null;var e=this.layers?this.layers.length:0;for(t=0;t<e;t+=1)this.elements[t]&&this.elements[t].destroy&&this.elements[t].destroy();this.elements.length=0,this.destroyed=!0,this.animationItem=null},Mi.prototype.updateContainerSize=function(){},Mi.prototype.findIndexByInd=function(t){var e=0,i=this.layers.length;for(e=0;e<i;e+=1)if(this.layers[e].ind===t)return e;return-1},Mi.prototype.buildItem=function(t){var e=this.elements;if(!e[t]&&99!==this.layers[t].ty){e[t]=!0;var i=this.createItem(this.layers[t]);if(e[t]=i,q()&&(0===this.layers[t].ty&&this.globalData.projectInterface.registerComposition(i),i.initExpressions()),this.appendElementInPos(i,t),this.layers[t].tt){var s="tp"in this.layers[t]?this.findIndexByInd(this.layers[t].tp):t-1;if(-1===s)return;if(this.elements[s]&&!0!==this.elements[s]){var a=e[s].getMatte(this.layers[t].tt);i.setMatte(a)}else this.buildItem(s),this.addPendingElement(i)}}},Mi.prototype.checkPendingElements=function(){for(;this.pendingElements.length;){var t=this.pendingElements.pop();if(t.checkParenting(),t.data.tt)for(var e=0,i=this.elements.length;e<i;){if(this.elements[e]===t){var s="tp"in t.data?this.findIndexByInd(t.data.tp):e-1,a=this.elements[s].getMatte(this.layers[e].tt);t.setMatte(a);break}e+=1}}},Mi.prototype.renderFrame=function(t){if(this.renderedFrame!==t&&!this.destroyed){var e;null===t?t=this.renderedFrame:this.renderedFrame=t,this.globalData.frameNum=t,this.globalData.frameId+=1,this.globalData.projectInterface.currentFrame=t,this.globalData._mdf=!1;var i=this.layers.length;for(this.completeLayers||this.checkLayers(t),e=i-1;e>=0;e-=1)(this.completeLayers||this.elements[e])&&this.elements[e].prepareFrame(t-this.layers[e].st);if(this.globalData._mdf)for(e=0;e<i;e+=1)(this.completeLayers||this.elements[e])&&this.elements[e].renderFrame()}},Mi.prototype.appendElementInPos=function(t,e){var i=t.getBaseElement();if(i){for(var s,a=0;a<e;)this.elements[a]&&!0!==this.elements[a]&&this.elements[a].getBaseElement()&&(s=this.elements[a].getBaseElement()),a+=1;s?this.layerElement.insertBefore(i,s):this.layerElement.appendChild(i)}},Mi.prototype.hide=function(){this.layerElement.style.display="none"},Mi.prototype.show=function(){this.layerElement.style.display="block"},r([Oe,Xe,$e,Ne,ti],Ti),Ti.prototype.initElement=function(t,e,i){this.initFrame(),this.initBaseData(t,e,i),this.initTransform(t,e,i),this.initRenderable(),this.initHierarchy(),this.initRendererElement(),this.createContainerElements(),this.createRenderableComponents(),!this.data.xt&&e.progressiveLoad||this.buildAllItems(),this.hide()},Ti.prototype.prepareFrame=function(t){if(this._mdf=!1,this.prepareRenderableFrame(t),this.prepareProperties(t,this.isInRange),this.isInRange||this.data.xt){if(this.tm._placeholder)this.renderedFrame=t/this.data.sr;else{var e=this.tm.v;e===this.data.op&&(e=this.data.op-1),this.renderedFrame=e}var i,s=this.elements.length;for(this.completeLayers||this.checkLayers(this.renderedFrame),i=s-1;i>=0;i-=1)(this.completeLayers||this.elements[i])&&(this.elements[i].prepareFrame(this.renderedFrame-this.layers[i].st),this.elements[i]._mdf&&(this._mdf=!0))}},Ti.prototype.renderInnerContent=function(){var t,e=this.layers.length;for(t=0;t<e;t+=1)(this.completeLayers||this.elements[t])&&this.elements[t].renderFrame()},Ti.prototype.setElements=function(t){this.elements=t},Ti.prototype.getElements=function(){return this.elements},Ti.prototype.destroyElements=function(){var t,e=this.layers.length;for(t=0;t<e;t+=1)this.elements[t]&&this.elements[t].destroy()},Ti.prototype.destroy=function(){this.destroyElements(),this.destroyBaseElement()},r([Mi,Ti,Qe],Fi),Fi.prototype.createComp=function(t){return new Fi(t,this.globalData,this)},r([Mi],Ii),Ii.prototype.createComp=function(t){return new Fi(t,this.globalData,this)};var Li,Bi={};function zi(t){var e,i,s=t.data.ef?t.data.ef.length:0;for(this.filters=[],e=0;e<s;e+=1){i=null;var a=t.data.ef[e].ty;if(Bi[a])i=new(0,Bi[a].effect)(t.effectsManager.effectElements[e],t);i&&this.filters.push(i)}this.filters.length&&t.addRenderableComponent(this)}function Vi(){}function Ri(t,e,i){this.initElement(t,e,i)}function Oi(t,e,i){this.shapes=[],this.shapesData=t.shapes,this.stylesList=[],this.shapeModifiers=[],this.itemsData=[],this.processedElements=[],this.animatedContents=[],this.shapesContainer=X("g"),this.initElement(t,e,i),this.prevViewData=[],this.currentBBox={x:999999,y:-999999,h:0,w:0}}function Ni(t,e,i){this.textSpans=[],this.textPaths=[],this.currentBBox={x:999999,y:-999999,h:0,w:0},this.renderType="svg",this.isMasked=!1,this.initElement(t,e,i)}function qi(t,e,i){this.initFrame(),this.initBaseData(t,e,i),this.initHierarchy();var s=_t.getProp;if(this.pe=s(this,t.pe,0,0,this),t.ks.p.s?(this.px=s(this,t.ks.p.x,1,0,this),this.py=s(this,t.ks.p.y,1,0,this),this.pz=s(this,t.ks.p.z,1,0,this)):this.p=s(this,t.ks.p,1,0,this),t.ks.a&&(this.a=s(this,t.ks.a,1,0,this)),t.ks.or.k.length&&t.ks.or.k[0].to){var a,r=t.ks.or.k.length;for(a=0;a<r;a+=1)t.ks.or.k[a].to=null,t.ks.or.k[a].ti=null}this.or=s(this,t.ks.or,1,w,this),this.or.sh=!0,this.rx=s(this,t.ks.rx,0,w,this),this.ry=s(this,t.ks.ry,0,w,this),this.rz=s(this,t.ks.rz,0,w,this),this.mat=new It,this._prevMat=new It,this._isFirstFrame=!0,this.finalTransform={mProp:this}}function ji(t,e,i){this.assetData=e.getAssetData(t.refId),this.initElement(t,e,i)}function Wi(t,e){this.animationItem=t,this.layers=null,this.renderedFrame=-1,this.renderConfig={className:e&&e.className||"",imagePreserveAspectRatio:e&&e.imagePreserveAspectRatio||"xMidYMid slice",hideOnTransparent:!(e&&!1===e.hideOnTransparent),filterSize:{width:e&&e.filterSize&&e.filterSize.width||"400%",height:e&&e.filterSize&&e.filterSize.height||"400%",x:e&&e.filterSize&&e.filterSize.x||"-100%",y:e&&e.filterSize&&e.filterSize.y||"-100%"}},this.globalData={_mdf:!1,frameNum:-1,renderConfig:this.renderConfig},this.pendingElements=[],this.elements=[],this.threeDElements=[],this.destroyed=!1,this.camera=null,this.supports3d=!0,this.rendererType="html"}function Hi(t,e,i){this.layers=t.layers,this.supports3d=!t.hasMask,this.completeLayers=!1,this.pendingElements=[],this.elements=this.layers?l(this.layers.length):[],this.initElement(t,e,i),this.tm=t.tm?_t.getProp(this,t.tm,0,e.frameRate,this):{_placeholder:!0}}function Xi(t,e){this.animationItem=t,this.layers=null,this.renderedFrame=-1,this.renderConfig={className:e&&e.className||"",imagePreserveAspectRatio:e&&e.imagePreserveAspectRatio||"xMidYMid slice",hideOnTransparent:!(e&&!1===e.hideOnTransparent),filterSize:{width:e&&e.filterSize&&e.filterSize.width||"400%",height:e&&e.filterSize&&e.filterSize.height||"400%",x:e&&e.filterSize&&e.filterSize.x||"-100%",y:e&&e.filterSize&&e.filterSize.y||"-100%"},runExpressions:!e||void 0===e.runExpressions||e.runExpressions},this.globalData={_mdf:!1,frameNum:-1,renderConfig:this.renderConfig},this.pendingElements=[],this.elements=[],this.threeDElements=[],this.destroyed=!1,this.camera=null,this.supports3d=!0,this.rendererType="html"}return zi.prototype.renderFrame=function(t){var e,i=this.filters.length;for(e=0;e<i;e+=1)this.filters[e].renderFrame(t)},zi.prototype.getEffects=function(t){var e,i=this.filters.length,s=[];for(e=0;e<i;e+=1)this.filters[e].type===t&&s.push(this.filters[e]);return s},Vi.prototype={checkBlendMode:function(){},initRendererElement:function(){this.baseElement=a(this.data.tg||"div"),this.data.hasMask?(this.svgElement=X("svg"),this.layerElement=X("g"),this.maskedElement=this.layerElement,this.svgElement.appendChild(this.layerElement),this.baseElement.appendChild(this.svgElement)):this.layerElement=this.baseElement,A(this.baseElement)},createContainerElements:function(){this.renderableEffectsManager=new zi(this),this.transformedElement=this.baseElement,this.maskedElement=this.layerElement,this.data.ln&&this.layerElement.setAttribute("id",this.data.ln),this.data.cl&&this.layerElement.setAttribute("class",this.data.cl),0!==this.data.bm&&this.setBlendMode()},renderElement:function(){var t=this.transformedElement?this.transformedElement.style:{};if(this.finalTransform._matMdf){var e=this.finalTransform.mat.toCSS();t.transform=e,t.webkitTransform=e}this.finalTransform._opMdf&&(t.opacity=this.finalTransform.mProp.o.v)},renderFrame:function(){this.data.hd||this.hidden||(this.renderTransform(),this.renderRenderable(),this.renderElement(),this.renderInnerContent(),this._isFirstFrame&&(this._isFirstFrame=!1))},destroy:function(){this.layerElement=null,this.transformedElement=null,this.matteElement&&(this.matteElement=null),this.maskManager&&(this.maskManager.destroy(),this.maskManager=null)},createRenderableComponents:function(){this.maskManager=new Ye(this.data,this,this.globalData)},addEffects:function(){},setMatte:function(){}},Vi.prototype.getBaseElement=Qe.prototype.getBaseElement,Vi.prototype.destroyBaseElement=Vi.prototype.destroy,Vi.prototype.buildElementParenting=We.prototype.buildElementParenting,r([Oe,Xe,Vi,$e,Ne,ti],Ri),Ri.prototype.createContent=function(){var t;this.data.hasMask?((t=X("rect")).setAttribute("width",this.data.sw),t.setAttribute("height",this.data.sh),t.setAttribute("fill",this.data.sc),this.svgElement.setAttribute("width",this.data.sw),this.svgElement.setAttribute("height",this.data.sh)):((t=a("div")).style.width=this.data.sw+"px",t.style.height=this.data.sh+"px",t.style.backgroundColor=this.data.sc),this.layerElement.appendChild(t)},r([Oe,Xe,Ri,bi,Vi,$e,Ne,Ae],Oi),Oi.prototype._renderShapeFrame=Oi.prototype.renderInnerContent,Oi.prototype.createContent=function(){var t;if(this.baseElement.style.fontSize=0,this.data.hasMask)this.layerElement.appendChild(this.shapesContainer),t=this.svgElement;else{t=X("svg");var e=this.comp.data?this.comp.data:this.globalData.compSize;t.setAttribute("width",e.w),t.setAttribute("height",e.h),t.appendChild(this.shapesContainer),this.layerElement.appendChild(t)}this.searchShapes(this.shapesData,this.itemsData,this.prevViewData,this.shapesContainer,0,[],!0),this.filterUniqueShapes(),this.shapeCont=t},Oi.prototype.getTransformedPoint=function(t,e){var i,s=t.length;for(i=0;i<s;i+=1)e=t[i].mProps.v.applyToPointArray(e[0],e[1],0);return e},Oi.prototype.calculateShapeBoundingBox=function(t,e){var i,s,a,r,n,h=t.sh.v,o=t.transformers,l=h._length;if(!(l<=1)){for(i=0;i<l-1;i+=1)s=this.getTransformedPoint(o,h.v[i]),a=this.getTransformedPoint(o,h.o[i]),r=this.getTransformedPoint(o,h.i[i+1]),n=this.getTransformedPoint(o,h.v[i+1]),this.checkBounds(s,a,r,n,e);h.c&&(s=this.getTransformedPoint(o,h.v[i]),a=this.getTransformedPoint(o,h.o[i]),r=this.getTransformedPoint(o,h.i[0]),n=this.getTransformedPoint(o,h.v[0]),this.checkBounds(s,a,r,n,e))}},Oi.prototype.checkBounds=function(t,e,i,s,a){this.getBoundsOfCurve(t,e,i,s);var r=this.shapeBoundingBox;a.x=_(r.left,a.x),a.xMax=b(r.right,a.xMax),a.y=_(r.top,a.y),a.yMax=b(r.bottom,a.yMax)},Oi.prototype.shapeBoundingBox={left:0,right:0,top:0,bottom:0},Oi.prototype.tempBoundingBox={x:0,xMax:0,y:0,yMax:0,width:0,height:0},Oi.prototype.getBoundsOfCurve=function(t,e,i,s){for(var a,r,n,h,o,l,p,f=[[t[0],s[0]],[t[1],s[1]]],m=0;m<2;++m)r=6*t[m]-12*e[m]+6*i[m],a=-3*t[m]+9*e[m]-9*i[m]+3*s[m],n=3*e[m]-3*t[m],r|=0,n|=0,0===(a|=0)&&0===r||(0===a?(h=-n/r)>0&&h<1&&f[m].push(this.calculateF(h,t,e,i,s,m)):(o=r*r-4*n*a)>=0&&((l=(-r+y(o))/(2*a))>0&&l<1&&f[m].push(this.calculateF(l,t,e,i,s,m)),(p=(-r-y(o))/(2*a))>0&&p<1&&f[m].push(this.calculateF(p,t,e,i,s,m))));this.shapeBoundingBox.left=_.apply(null,f[0]),this.shapeBoundingBox.top=_.apply(null,f[1]),this.shapeBoundingBox.right=b.apply(null,f[0]),this.shapeBoundingBox.bottom=b.apply(null,f[1])},Oi.prototype.calculateF=function(t,e,i,s,a,r){return g(1-t,3)*e[r]+3*g(1-t,2)*t*i[r]+3*(1-t)*g(t,2)*s[r]+g(t,3)*a[r]},Oi.prototype.calculateBoundingBox=function(t,e){var i,s=t.length;for(i=0;i<s;i+=1)t[i]&&t[i].sh?this.calculateShapeBoundingBox(t[i],e):t[i]&&t[i].it?this.calculateBoundingBox(t[i].it,e):t[i]&&t[i].style&&t[i].w&&this.expandStrokeBoundingBox(t[i].w,e)},Oi.prototype.expandStrokeBoundingBox=function(t,e){var i=0;if(t.keyframes){for(var s=0;s<t.keyframes.length;s+=1){var a=t.keyframes[s].s;a>i&&(i=a)}i*=t.mult}else i=t.v*t.mult;e.x-=i,e.xMax+=i,e.y-=i,e.yMax+=i},Oi.prototype.currentBoxContains=function(t){return this.currentBBox.x<=t.x&&this.currentBBox.y<=t.y&&this.currentBBox.width+this.currentBBox.x>=t.x+t.width&&this.currentBBox.height+this.currentBBox.y>=t.y+t.height},Oi.prototype.renderInnerContent=function(){if(this._renderShapeFrame(),!this.hidden&&(this._isFirstFrame||this._mdf)){var t=this.tempBoundingBox,e=999999;if(t.x=e,t.xMax=-e,t.y=e,t.yMax=-e,this.calculateBoundingBox(this.itemsData,t),t.width=t.xMax<t.x?0:t.xMax-t.x,t.height=t.yMax<t.y?0:t.yMax-t.y,this.currentBoxContains(t))return;var i=!1;if(this.currentBBox.w!==t.width&&(this.currentBBox.w=t.width,this.shapeCont.setAttribute("width",t.width),i=!0),this.currentBBox.h!==t.height&&(this.currentBBox.h=t.height,this.shapeCont.setAttribute("height",t.height),i=!0),i||this.currentBBox.x!==t.x||this.currentBBox.y!==t.y){this.currentBBox.w=t.width,this.currentBBox.h=t.height,this.currentBBox.x=t.x,this.currentBBox.y=t.y,this.shapeCont.setAttribute("viewBox",this.currentBBox.x+" "+this.currentBBox.y+" "+this.currentBBox.w+" "+this.currentBBox.h);var s=this.shapeCont.style,a="translate("+this.currentBBox.x+"px,"+this.currentBBox.y+"px)";s.transform=a,s.webkitTransform=a}}},r([Oe,Xe,Vi,$e,Ne,ti,Si],Ni),Ni.prototype.createContent=function(){if(this.isMasked=this.checkMasks(),this.isMasked){this.renderType="svg",this.compW=this.comp.data.w,this.compH=this.comp.data.h,this.svgElement.setAttribute("width",this.compW),this.svgElement.setAttribute("height",this.compH);var t=X("g");this.maskedElement.appendChild(t),this.innerElem=t}else this.renderType="html",this.innerElem=this.layerElement;this.checkParenting()},Ni.prototype.buildNewText=function(){var t=this.textProperty.currentData;this.renderedLetters=l(t.l?t.l.length:0);var e=this.innerElem.style,i=t.fc?this.buildColor(t.fc):"rgba(0,0,0,0)";e.fill=i,e.color=i,t.sc&&(e.stroke=this.buildColor(t.sc),e.strokeWidth=t.sw+"px");var s,r,n=this.globalData.fontManager.getFontByName(t.f);if(!this.globalData.fontManager.chars)if(e.fontSize=t.finalSize+"px",e.lineHeight=t.finalSize+"px",n.fClass)this.innerElem.className=n.fClass;else{e.fontFamily=n.fFamily;var h=t.fWeight,o=t.fStyle;e.fontStyle=o,e.fontWeight=h}var p,f,m,d=t.l;r=d.length;var c,u=this.mHelper,g="",y=0;for(s=0;s<r;s+=1){if(this.globalData.fontManager.chars?(this.textPaths[y]?p=this.textPaths[y]:((p=X("path")).setAttribute("stroke-linecap",ai[1]),p.setAttribute("stroke-linejoin",ri[2]),p.setAttribute("stroke-miterlimit","4")),this.isMasked||(this.textSpans[y]?m=(f=this.textSpans[y]).children[0]:((f=a("div")).style.lineHeight=0,(m=X("svg")).appendChild(p),A(f)))):this.isMasked?p=this.textPaths[y]?this.textPaths[y]:X("text"):this.textSpans[y]?(f=this.textSpans[y],p=this.textPaths[y]):(A(f=a("span")),A(p=a("span")),f.appendChild(p)),this.globalData.fontManager.chars){var v,b=this.globalData.fontManager.getCharData(t.finalText[s],n.fStyle,this.globalData.fontManager.getFontByName(t.f).fFamily);if(v=b?b.data:null,u.reset(),v&&v.shapes&&v.shapes.length&&(c=v.shapes[0].it,u.scale(t.finalSize/100,t.finalSize/100),g=this.createPathShape(u,c),p.setAttribute("d",g)),this.isMasked)this.innerElem.appendChild(p);else{if(this.innerElem.appendChild(f),v&&v.shapes){document.body.appendChild(m);var _=m.getBBox();m.setAttribute("width",_.width+2),m.setAttribute("height",_.height+2),m.setAttribute("viewBox",_.x-1+" "+(_.y-1)+" "+(_.width+2)+" "+(_.height+2));var x=m.style,k="translate("+(_.x-1)+"px,"+(_.y-1)+"px)";x.transform=k,x.webkitTransform=k,d[s].yOffset=_.y-1}else m.setAttribute("width",1),m.setAttribute("height",1);f.appendChild(m)}}else if(p.textContent=d[s].val,p.setAttributeNS("http://www.w3.org/XML/1998/namespace","xml:space","preserve"),this.isMasked)this.innerElem.appendChild(p);else{this.innerElem.appendChild(f);var w=p.style,P="translate3d(0,"+-t.finalSize/1.2+"px,0)";w.transform=P,w.webkitTransform=P}this.isMasked?this.textSpans[y]=p:this.textSpans[y]=f,this.textSpans[y].style.display="block",this.textPaths[y]=p,y+=1}for(;y<this.textSpans.length;)this.textSpans[y].style.display="none",y+=1},Ni.prototype.renderInnerContent=function(){var t;if(this.validateText(),this.data.singleShape){if(!this._isFirstFrame&&!this.lettersChangedFlag)return;if(this.isMasked&&this.finalTransform._matMdf){this.svgElement.setAttribute("viewBox",-this.finalTransform.mProp.p.v[0]+" "+-this.finalTransform.mProp.p.v[1]+" "+this.compW+" "+this.compH),t=this.svgElement.style;var e="translate("+-this.finalTransform.mProp.p.v[0]+"px,"+-this.finalTransform.mProp.p.v[1]+"px)";t.transform=e,t.webkitTransform=e}}if(this.textAnimator.getMeasures(this.textProperty.currentData,this.lettersChangedFlag),this.lettersChangedFlag||this.textAnimator.lettersChangedFlag){var i,s,a,r,n,h=0,o=this.textAnimator.renderedLetters,l=this.textProperty.currentData.l;for(s=l.length,i=0;i<s;i+=1)l[i].n?h+=1:(r=this.textSpans[i],n=this.textPaths[i],a=o[h],h+=1,a._mdf.m&&(this.isMasked?r.setAttribute("transform",a.m):(r.style.webkitTransform=a.m,r.style.transform=a.m)),r.style.opacity=a.o,a.sw&&a._mdf.sw&&n.setAttribute("stroke-width",a.sw),a.sc&&a._mdf.sc&&n.setAttribute("stroke",a.sc),a.fc&&a._mdf.fc&&(n.setAttribute("fill",a.fc),n.style.color=a.fc));if(this.innerElem.getBBox&&!this.hidden&&(this._isFirstFrame||this._mdf)){var p=this.innerElem.getBBox();this.currentBBox.w!==p.width&&(this.currentBBox.w=p.width,this.svgElement.setAttribute("width",p.width)),this.currentBBox.h!==p.height&&(this.currentBBox.h=p.height,this.svgElement.setAttribute("height",p.height));if(this.currentBBox.w!==p.width+2||this.currentBBox.h!==p.height+2||this.currentBBox.x!==p.x-1||this.currentBBox.y!==p.y-1){this.currentBBox.w=p.width+2,this.currentBBox.h=p.height+2,this.currentBBox.x=p.x-1,this.currentBBox.y=p.y-1,this.svgElement.setAttribute("viewBox",this.currentBBox.x+" "+this.currentBBox.y+" "+this.currentBBox.w+" "+this.currentBBox.h),t=this.svgElement.style;var f="translate("+this.currentBBox.x+"px,"+this.currentBBox.y+"px)";t.transform=f,t.webkitTransform=f}}}},r([Oe,Ne,$e],qi),qi.prototype.setup=function(){var t,e,i,s,a=this.comp.threeDElements.length;for(t=0;t<a;t+=1)if("3d"===(e=this.comp.threeDElements[t]).type){i=e.perspectiveElem.style,s=e.container.style;var r=this.pe.v+"px",n="0px 0px 0px",h="matrix3d(1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1)";i.perspective=r,i.webkitPerspective=r,s.transformOrigin=n,s.mozTransformOrigin=n,s.webkitTransformOrigin=n,i.transform=h,i.webkitTransform=h}},qi.prototype.createElements=function(){},qi.prototype.hide=function(){},qi.prototype.renderFrame=function(){var t,e,i=this._isFirstFrame;if(this.hierarchy)for(e=this.hierarchy.length,t=0;t<e;t+=1)i=this.hierarchy[t].finalTransform.mProp._mdf||i;if(i||this.pe._mdf||this.p&&this.p._mdf||this.px&&(this.px._mdf||this.py._mdf||this.pz._mdf)||this.rx._mdf||this.ry._mdf||this.rz._mdf||this.or._mdf||this.a&&this.a._mdf){if(this.mat.reset(),this.hierarchy)for(t=e=this.hierarchy.length-1;t>=0;t-=1){var s=this.hierarchy[t].finalTransform.mProp;this.mat.translate(-s.p.v[0],-s.p.v[1],s.p.v[2]),this.mat.rotateX(-s.or.v[0]).rotateY(-s.or.v[1]).rotateZ(s.or.v[2]),this.mat.rotateX(-s.rx.v).rotateY(-s.ry.v).rotateZ(s.rz.v),this.mat.scale(1/s.s.v[0],1/s.s.v[1],1/s.s.v[2]),this.mat.translate(s.a.v[0],s.a.v[1],s.a.v[2])}if(this.p?this.mat.translate(-this.p.v[0],-this.p.v[1],this.p.v[2]):this.mat.translate(-this.px.v,-this.py.v,this.pz.v),this.a){var a;a=this.p?[this.p.v[0]-this.a.v[0],this.p.v[1]-this.a.v[1],this.p.v[2]-this.a.v[2]]:[this.px.v-this.a.v[0],this.py.v-this.a.v[1],this.pz.v-this.a.v[2]];var r=Math.sqrt(Math.pow(a[0],2)+Math.pow(a[1],2)+Math.pow(a[2],2)),n=[a[0]/r,a[1]/r,a[2]/r],h=Math.sqrt(n[2]*n[2]+n[0]*n[0]),o=Math.atan2(n[1],h),l=Math.atan2(n[0],-n[2]);this.mat.rotateY(l).rotateX(-o)}this.mat.rotateX(-this.rx.v).rotateY(-this.ry.v).rotateZ(this.rz.v),this.mat.rotateX(-this.or.v[0]).rotateY(-this.or.v[1]).rotateZ(this.or.v[2]),this.mat.translate(this.globalData.compSize.w/2,this.globalData.compSize.h/2,0),this.mat.translate(0,0,this.pe.v);var p=!this._prevMat.equals(this.mat);if((p||this.pe._mdf)&&this.comp.threeDElements){var f,m,d;for(e=this.comp.threeDElements.length,t=0;t<e;t+=1)if("3d"===(f=this.comp.threeDElements[t]).type){if(p){var c=this.mat.toCSS();(d=f.container.style).transform=c,d.webkitTransform=c}this.pe._mdf&&((m=f.perspectiveElem.style).perspective=this.pe.v+"px",m.webkitPerspective=this.pe.v+"px")}this.mat.clone(this._prevMat)}}this._isFirstFrame=!1},qi.prototype.prepareFrame=function(t){this.prepareProperties(t,!0)},qi.prototype.destroy=function(){},qi.prototype.getBaseElement=function(){return null},r([Oe,Xe,Vi,Ri,$e,Ne,Ae],ji),ji.prototype.createContent=function(){var t=this.globalData.getAssetsPath(this.assetData),e=new Image;this.data.hasMask?(this.imageElem=X("image"),this.imageElem.setAttribute("width",this.assetData.w+"px"),this.imageElem.setAttribute("height",this.assetData.h+"px"),this.imageElem.setAttributeNS("http://www.w3.org/1999/xlink","href",t),this.layerElement.appendChild(this.imageElem),this.baseElement.setAttribute("width",this.assetData.w),this.baseElement.setAttribute("height",this.assetData.h)):this.layerElement.appendChild(e),e.crossOrigin="anonymous",e.src=t,this.data.ln&&this.baseElement.setAttribute("id",this.data.ln)},r([We],Wi),Wi.prototype.buildItem=Ii.prototype.buildItem,Wi.prototype.checkPendingElements=function(){for(;this.pendingElements.length;){this.pendingElements.pop().checkParenting()}},Wi.prototype.appendElementInPos=function(t,e){var i=t.getBaseElement();if(i){var s=this.layers[e];if(s.ddd&&this.supports3d)this.addTo3dContainer(i,e);else if(this.threeDElements)this.addTo3dContainer(i,e);else{for(var a,r,n=0;n<e;)this.elements[n]&&!0!==this.elements[n]&&this.elements[n].getBaseElement&&(r=this.elements[n],a=(this.layers[n].ddd?this.getThreeDContainerByPos(n):r.getBaseElement())||a),n+=1;a?s.ddd&&this.supports3d||this.layerElement.insertBefore(i,a):s.ddd&&this.supports3d||this.layerElement.appendChild(i)}}},Wi.prototype.createShape=function(t){return this.supports3d?new Oi(t,this.globalData,this):new bi(t,this.globalData,this)},Wi.prototype.createText=function(t){return this.supports3d?new Ni(t,this.globalData,this):new Di(t,this.globalData,this)},Wi.prototype.createCamera=function(t){return this.camera=new qi(t,this.globalData,this),this.camera},Wi.prototype.createImage=function(t){return this.supports3d?new ji(t,this.globalData,this):new ei(t,this.globalData,this)},Wi.prototype.createSolid=function(t){return this.supports3d?new Ri(t,this.globalData,this):new Ci(t,this.globalData,this)},Wi.prototype.createNull=Ii.prototype.createNull,Wi.prototype.getThreeDContainerByPos=function(t){for(var e=0,i=this.threeDElements.length;e<i;){if(this.threeDElements[e].startPos<=t&&this.threeDElements[e].endPos>=t)return this.threeDElements[e].perspectiveElem;e+=1}return null},Wi.prototype.createThreeDContainer=function(t,e){var i,s,r=a("div");A(r);var n=a("div");if(A(n),"3d"===e){(i=r.style).width=this.globalData.compSize.w+"px",i.height=this.globalData.compSize.h+"px";var h="50% 50%";i.webkitTransformOrigin=h,i.mozTransformOrigin=h,i.transformOrigin=h;var o="matrix3d(1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1)";(s=n.style).transform=o,s.webkitTransform=o}r.appendChild(n);var l={container:n,perspectiveElem:r,startPos:t,endPos:t,type:e};return this.threeDElements.push(l),l},Wi.prototype.build3dContainers=function(){var t,e,i=this.layers.length,s="";for(t=0;t<i;t+=1)this.layers[t].ddd&&3!==this.layers[t].ty?("3d"!==s&&(s="3d",e=this.createThreeDContainer(t,"3d")),e.endPos=Math.max(e.endPos,t)):("2d"!==s&&(s="2d",e=this.createThreeDContainer(t,"2d")),e.endPos=Math.max(e.endPos,t));for(t=(i=this.threeDElements.length)-1;t>=0;t-=1)this.resizerElem.appendChild(this.threeDElements[t].perspectiveElem)},Wi.prototype.addTo3dContainer=function(t,e){for(var i=0,s=this.threeDElements.length;i<s;){if(e<=this.threeDElements[i].endPos){for(var a,r=this.threeDElements[i].startPos;r<e;)this.elements[r]&&this.elements[r].getBaseElement&&(a=this.elements[r].getBaseElement()),r+=1;a?this.threeDElements[i].container.insertBefore(t,a):this.threeDElements[i].container.appendChild(t);break}i+=1}},Wi.prototype.configAnimation=function(t){var e=a("div"),i=this.animationItem.wrapper,s=e.style;s.width=t.w+"px",s.height=t.h+"px",this.resizerElem=e,A(e),s.transformStyle="flat",s.mozTransformStyle="flat",s.webkitTransformStyle="flat",this.renderConfig.className&&e.setAttribute("class",this.renderConfig.className),i.appendChild(e),s.overflow="hidden";var r=X("svg");r.setAttribute("width","1"),r.setAttribute("height","1"),A(r),this.resizerElem.appendChild(r);var n=X("defs");r.appendChild(n),this.data=t,this.setupGlobalData(t,r),this.globalData.defs=n,this.layers=t.layers,this.layerElement=this.resizerElem,this.build3dContainers(),this.updateContainerSize()},Wi.prototype.destroy=function(){var t;this.animationItem.wrapper&&(this.animationItem.wrapper.innerText=""),this.animationItem.container=null,this.globalData.defs=null;var e=this.layers?this.layers.length:0;for(t=0;t<e;t+=1)this.elements[t]&&this.elements[t].destroy&&this.elements[t].destroy();this.elements.length=0,this.destroyed=!0,this.animationItem=null},Wi.prototype.updateContainerSize=function(){var t,e,i,s,a=this.animationItem.wrapper.offsetWidth,r=this.animationItem.wrapper.offsetHeight,n=a/r;this.globalData.compSize.w/this.globalData.compSize.h>n?(t=a/this.globalData.compSize.w,e=a/this.globalData.compSize.w,i=0,s=(r-this.globalData.compSize.h*(a/this.globalData.compSize.w))/2):(t=r/this.globalData.compSize.h,e=r/this.globalData.compSize.h,i=(a-this.globalData.compSize.w*(r/this.globalData.compSize.h))/2,s=0);var h=this.resizerElem.style;h.webkitTransform="matrix3d("+t+",0,0,0,0,"+e+",0,0,0,0,1,0,"+i+","+s+",0,1)",h.transform=h.webkitTransform},Wi.prototype.renderFrame=Ii.prototype.renderFrame,Wi.prototype.hide=function(){this.resizerElem.style.display="none"},Wi.prototype.show=function(){this.resizerElem.style.display="block"},Wi.prototype.initItems=function(){if(this.buildAllItems(),this.camera)this.camera.setup();else{var t,e=this.globalData.compSize.w,i=this.globalData.compSize.h,s=this.threeDElements.length;for(t=0;t<s;t+=1){var a=this.threeDElements[t].perspectiveElem.style;a.webkitPerspective=Math.sqrt(Math.pow(e,2)+Math.pow(i,2))+"px",a.perspective=a.webkitPerspective}}},Wi.prototype.searchExtraCompositions=function(t){var e,i=t.length,s=a("div");for(e=0;e<i;e+=1)if(t[e].xt){var r=this.createComp(t[e],s,this.globalData.comp,null);r.initExpressions(),this.globalData.projectInterface.registerComposition(r)}},r([Wi,Ti,Vi],Hi),Hi.prototype._createBaseContainerElements=Hi.prototype.createContainerElements,Hi.prototype.createContainerElements=function(){this._createBaseContainerElements(),this.data.hasMask?(this.svgElement.setAttribute("width",this.data.w),this.svgElement.setAttribute("height",this.data.h),this.transformedElement=this.baseElement):this.transformedElement=this.layerElement},Hi.prototype.addTo3dContainer=function(t,e){for(var i,s=0;s<e;)this.elements[s]&&this.elements[s].getBaseElement&&(i=this.elements[s].getBaseElement()),s+=1;i?this.layerElement.insertBefore(t,i):this.layerElement.appendChild(t)},Hi.prototype.createComp=function(t){return this.supports3d?new Hi(t,this.globalData,this):new Fi(t,this.globalData,this)},r([Wi],Xi),Xi.prototype.createComp=function(t){return this.supports3d?new Hi(t,this.globalData,this):new Fi(t,this.globalData,this)},Li=Xi,Q["html"]=Li,jt.registerModifier("tm",Ht),jt.registerModifier("pb",Xt),jt.registerModifier("rp",Gt),jt.registerModifier("rd",Kt),jt.registerModifier("zz",fe),jt.registerModifier("op",ke),Bt}));
