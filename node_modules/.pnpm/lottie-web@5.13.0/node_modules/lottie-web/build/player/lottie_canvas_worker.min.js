(typeof navigator !== "undefined") && (function(root, factory) {
    if (typeof define === "function" && define.amd) {
        define(function() {
            return factory(root);
        });
    } else if (typeof module === "object" && module.exports) {
        module.exports = factory(root);
    } else {
        root.lottie = factory(root);
        root.bodymovin = root.lottie;
    }
}((self || {}), function(window) {
	function workerContent(){function extendPrototype(t,e){for(var i,r=t.length,s=0;s<r;s+=1)for(var a in i=t[s].prototype)Object.prototype.hasOwnProperty.call(i,a)&&(e.prototype[a]=i[a])}function ProxyElement(t,e){this._state="init",this._isDirty=!1,this._isProxy=!0,this._changedStyles=[],this._changedAttributes=[],this._changedElements=[],this._textContent=null,this.type=t,this.namespace=e,this.children=[],localIdCounter+=1,this.attributes={id:"l_d_"+localIdCounter},this.style=new Style(this)}ProxyElement.prototype={appendChild:function(t){(t.parentNode=this).children.push(t),this._isDirty=!0,this._changedElements.push([t,this.attributes.id])},insertBefore:function(t,e){for(var i=this.children,r=0;r<i.length;r+=1)if(i[r]===e)return i.splice(r,0,t),this._isDirty=!0,void this._changedElements.push([t,this.attributes.id,e.attributes.id]);i.push(e)},setAttribute:function(t,e){this.attributes[t]=e,this._isDirty||(this._isDirty=!0),this._changedAttributes.push(t)},serialize:function(){return{type:this.type,namespace:this.namespace,style:this.style.serialize(),attributes:this.attributes,children:this.children.map(function(t){return t.serialize()}),textContent:this._textContent}},addEventListener:function(t,e){setTimeout(e,1)},setAttributeNS:function(t,e,i){this.attributes[e]=i,this._isDirty||(this._isDirty=!0),this._changedAttributes.push(e)}},Object.defineProperty(ProxyElement.prototype,"textContent",{set:function(t){this._isDirty=!0,this._textContent=t}});var localIdCounter=0,animations={},styleProperties=["width","height","display","transform","opacity","contentVisibility","mix-blend-mode"];function convertArguments(t){for(var e=[],i=t.length,r=0;r<i;r+=1)e.push(t[r]);return e}function Style(t){this.element=t}function CanvasContext(t){this.element=t}Style.prototype={serialize:function(){for(var t={},e=0;e<styleProperties.length;e+=1){var i=styleProperties[e],r="_"+i;r in this&&(t[i]=this[r])}return t}},styleProperties.forEach(function(e){Object.defineProperty(Style.prototype,e,{set:function(t){this.element._isDirty||(this.element._isDirty=!0),this.element._changedStyles.push(e),this["_"+e]=t},get:function(){return this["_"+e]}})}),CanvasContext.prototype={createRadialGradient:function(){var t={t:"rGradient",a:convertArguments(arguments),stops:[]};return this.element.instructions.push(t),{addColorStop:function(){t.stops.push(convertArguments(arguments))}}},createLinearGradient:function(){var t={t:"lGradient",a:convertArguments(arguments),stops:[]};return this.element.instructions.push(t),{addColorStop:function(){t.stops.push(convertArguments(arguments))}}}},Object.defineProperties(CanvasContext.prototype,{canvas:{enumerable:!0,get:function(){return this.element}}});var canvasContextMethods=["fillRect","setTransform","drawImage","beginPath","moveTo","save","restore","fillText","setLineDash","clearRect","clip","rect","stroke","fill","closePath","bezierCurveTo","lineTo"],canvasContextProperties=(canvasContextMethods.forEach(function(t){CanvasContext.prototype[t]=function(){this.element.instructions.push({t:t,a:convertArguments(arguments)})}}),["globalAlpha","strokeStyle","fillStyle","lineCap","lineJoin","lineWidth","miterLimit","lineDashOffset","globalCompositeOperation"]);function CanvasElement(t,e){ProxyElement.call(this,t,e),this.instructions=[],this.width=0,this.height=0,this.context=new CanvasContext(this)}function createElement(t,e){return new("canvas"===e?CanvasElement:ProxyElement)(e,t)}canvasContextProperties.forEach(function(e){Object.defineProperty(CanvasContext.prototype,e,{set:function(t){this.element.instructions.push({t:e,a:t})}})}),CanvasElement.prototype={getContext:function(){return this.context},resetInstructions:function(){this.instructions.length=0}},extendPrototype([ProxyElement],CanvasElement);var window=self,document={createElementNS:function(t,e){return createElement(t,e)},createElement:function(t){return createElement("",t)},getElementsByTagName:function(){return[]},body:createElement("","body"),_isProxy:!0},lottieInternal=function(){function addElementToList(t,e){e.push(t),t._isDirty=!1,t._changedStyles.length=0,t._changedAttributes.length=0,t._changedElements.length=0,t._textContent=null,t.children.forEach(function(t){addElementToList(t,e)})}function addChangedAttributes(t){for(var e,i=t._changedAttributes,r=[],s=0;s<i.length;s+=1)e=i[s],r.push([e,t.attributes[e]]);return r}function addChangedStyles(t){for(var e,i=t._changedStyles,r=[],s=0;s<i.length;s+=1)e=i[s],r.push([e,t.style[e]]);return r}function addChangedElements(t,e){for(var i,r=t._changedElements,s=[],a=0;a<r.length;a+=1)i=r[a],s.push([i[0].serialize(),i[1],i[2]]),addElementToList(i[0],e);return s}function loadAnimation(a){var e,t,i,r,s=a.params,n=[];"svg"===s.renderer?(e=document.createElement("div"),s.container=e):((i=s.rendererSettings.canvas)||((i=document.createElement("canvas")).width=s.animationData.w,i.height=s.animationData.h),r=i.getContext("2d"),s.rendererSettings.context=r),(t=lottie.loadAnimation(s)).addEventListener("error",function(t){console.log(t)}),t.onError=function(t){console.log("ERRORO",t)},t.addEventListener("_play",function(){self.postMessage({type:"playing",payload:{id:a.id}})}),t.addEventListener("_pause",function(){self.postMessage({type:"paused",payload:{id:a.id}})}),"svg"===s.renderer?(t.addEventListener("DOMLoaded",function(){var t=e.serialize();addElementToList(e,n),self.postMessage({type:"SVGloaded",payload:{id:a.id,tree:t.children[0]}})}),t.addEventListener("drawnFrame",function(t){for(var e,i,r=[],s=0;s<n.length;s+=1)(i=n[s])._isDirty&&(e={id:i.attributes.id,styles:addChangedStyles(i),attributes:addChangedAttributes(i),elements:addChangedElements(i,n),textContent:i._textContent||void 0},r.push(e),i._isDirty=!1,i._changedAttributes.length=0,i._changedStyles.length=0,i._changedElements.length=0,i._textContent=null);self.postMessage({type:"SVGupdated",payload:{elements:r,id:a.id,currentTime:t.currentTime}})})):i._isProxy&&t.addEventListener("drawnFrame",function(t){self.postMessage({type:"CanvasUpdated",payload:{instructions:i.instructions,id:a.id,currentTime:t.currentTime}}),i.resetInstructions()}),t.addEventListener("DOMLoaded",function(){self.postMessage({type:"DOMLoaded",payload:{id:a.id,totalFrames:t.totalFrames,frameRate:t.frameRate,firstFrame:t.firstFrame,currentFrame:t.currentFrame,playDirection:t.playDirection,isSubframeEnabled:t.isSubframeEnabled,currentRawFrame:t.currentRawFrame,timeCompleted:t.timeCompleted}})}),animations[a.id]={animation:t,events:{}}}return void 0!==document&&"undefined"!=typeof navigator&&((t,e)=>{"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t="undefined"!=typeof globalThis?globalThis:t||self).lottie=e()})(this,function(){var svgNS="http://www.w3.org/2000/svg",locationHref="",_useWebWorker=!1,initialDefaultFrame=-999999,setWebWorker=function(t){_useWebWorker=!!t},getWebWorker=function(){return _useWebWorker},setLocationHref=function(t){locationHref=t},getLocationHref=function(){return locationHref};function createTag(t){return document.createElement(t)}function extendPrototype(t,e){for(var i,r=t.length,s=0;s<r;s+=1)for(var a in i=t[s].prototype)Object.prototype.hasOwnProperty.call(i,a)&&(e.prototype[a]=i[a])}function getDescriptor(t,e){return Object.getOwnPropertyDescriptor(t,e)}function createProxyFunction(t){function e(){}return e.prototype=t,e}var audioControllerFactory=(()=>{function t(t){this.audios=[],this.audioFactory=t,this._volume=1,this._isMuted=!1}return t.prototype={addAudio:function(t){this.audios.push(t)},pause:function(){for(var t=this.audios.length,e=0;e<t;e+=1)this.audios[e].pause()},resume:function(){for(var t=this.audios.length,e=0;e<t;e+=1)this.audios[e].resume()},setRate:function(t){for(var e=this.audios.length,i=0;i<e;i+=1)this.audios[i].setRate(t)},createAudio:function(t){return this.audioFactory?this.audioFactory(t):window.Howl?new window.Howl({src:[t]}):{isPlaying:!1,play:function(){this.isPlaying=!0},seek:function(){this.isPlaying=!1},playing:function(){},rate:function(){},setVolume:function(){}}},setAudioFactory:function(t){this.audioFactory=t},setVolume:function(t){this._volume=t,this._updateVolume()},mute:function(){this._isMuted=!0,this._updateVolume()},unmute:function(){this._isMuted=!1,this._updateVolume()},getVolume:function(){return this._volume},_updateVolume:function(){for(var t=this.audios.length,e=0;e<t;e+=1)this.audios[e].volume(this._volume*(this._isMuted?0:1))}},function(){return new t}})(),createTypedArray=(()=>{function i(t,e){var i,r=0,s=[];switch(t){case"int16":case"uint8c":i=1;break;default:i=1.1}for(r=0;r<e;r+=1)s.push(i);return s}return"function"==typeof Uint8ClampedArray&&"function"==typeof Float32Array?function(t,e){return"float32"===t?new Float32Array(e):"int16"===t?new Int16Array(e):"uint8c"===t?new Uint8ClampedArray(e):i(t,e)}:i})();function createSizedArray(t){return Array.apply(null,{length:t})}function _typeof$6(t){return(_typeof$6="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var subframeEnabled=!0,expressionsPlugin=null,expressionsInterfaces=null,idPrefix$1="",isSafari=/^((?!chrome|android).)*safari/i.test(navigator.userAgent),_shouldRoundValues=!1,bmPow=Math.pow,bmSqrt=Math.sqrt,bmFloor=Math.floor,bmMax=Math.max,bmMin=Math.min,BMMath={};function ProjectInterface$1(){}(()=>{for(var t=["abs","acos","acosh","asin","asinh","atan","atanh","atan2","ceil","cbrt","expm1","clz32","cos","cosh","exp","floor","fround","hypot","imul","log","log1p","log2","log10","max","min","pow","random","round","sign","sin","sinh","sqrt","tan","tanh","trunc","E","LN10","LN2","LOG10E","LOG2E","PI","SQRT1_2","SQRT2"],e=t.length,i=0;i<e;i+=1)BMMath[t[i]]=Math[t[i]]})(),BMMath.random=Math.random,BMMath.abs=function(t){if("object"===_typeof$6(t)&&t.length){for(var e=createSizedArray(t.length),i=t.length,r=0;r<i;r+=1)e[r]=Math.abs(t[r]);return e}return Math.abs(t)};var defaultCurveSegments=150,degToRads=Math.PI/180,roundCorner=.5519;function roundValues(t){_shouldRoundValues=!!t}function bmRnd(t){if(_shouldRoundValues)return Math.round(t)}function styleDiv(t){t.style.position="absolute",t.style.top=0,t.style.left=0,t.style.display="block",t.style.transformOrigin="0 0",t.style.webkitTransformOrigin="0 0",t.style.backfaceVisibility="visible",t.style.webkitBackfaceVisibility="visible",t.style.transformStyle="preserve-3d",t.style.webkitTransformStyle="preserve-3d",t.style.mozTransformStyle="preserve-3d"}function BMEnterFrameEvent(t,e,i,r){this.type=t,this.currentTime=e,this.totalTime=i,this.direction=r<0?-1:1}function BMCompleteEvent(t,e){this.type=t,this.direction=e<0?-1:1}function BMCompleteLoopEvent(t,e,i,r){this.type=t,this.currentLoop=i,this.totalLoops=e,this.direction=r<0?-1:1}function BMSegmentStartEvent(t,e,i){this.type=t,this.firstFrame=e,this.totalFrames=i}function BMDestroyEvent(t,e){this.type=t,this.target=e}function BMRenderFrameErrorEvent(t,e){this.type="renderFrameError",this.nativeError=t,this.currentTime=e}function BMConfigErrorEvent(t){this.type="configError",this.nativeError=t}function BMAnimationConfigErrorEvent(t,e){this.type=t,this.nativeError=e}var createElementID=(()=>{var t=0;return function(){return idPrefix$1+"__lottie_element_"+(t+=1)}})();function HSVtoRGB(t,e,i){var r,s,a,n=Math.floor(6*t),t=6*t-n,o=i*(1-e),h=i*(1-t*e),l=i*(1-(1-t)*e);switch(n%6){case 0:r=i,s=l,a=o;break;case 1:r=h,s=i,a=o;break;case 2:r=o,s=i,a=l;break;case 3:r=o,s=h,a=i;break;case 4:r=l,s=o,a=i;break;case 5:r=i,s=o,a=h}return[r,s,a]}function RGBtoHSV(t,e,i){var r,s=Math.max(t,e,i),a=Math.min(t,e,i),n=s-a,o=0===s?0:n/s,h=s/255;switch(s){case a:r=0;break;case t:r=e-i+n*(e<i?6:0),r/=6*n;break;case e:r=i-t+2*n,r/=6*n;break;case i:r=t-e+4*n,r/=6*n}return[r,o,h]}function addSaturationToRGB(t,e){t=RGBtoHSV(255*t[0],255*t[1],255*t[2]);return t[1]+=e,1<t[1]?t[1]=1:t[1]<=0&&(t[1]=0),HSVtoRGB(t[0],t[1],t[2])}function addBrightnessToRGB(t,e){t=RGBtoHSV(255*t[0],255*t[1],255*t[2]);return t[2]+=e,1<t[2]?t[2]=1:t[2]<0&&(t[2]=0),HSVtoRGB(t[0],t[1],t[2])}function addHueToRGB(t,e){t=RGBtoHSV(255*t[0],255*t[1],255*t[2]);return t[0]+=e/360,1<t[0]?--t[0]:t[0]<0&&(t[0]+=1),HSVtoRGB(t[0],t[1],t[2])}var rgbToHex=(()=>{for(var t,r=[],e=0;e<256;e+=1)t=e.toString(16),r[e]=1===t.length?"0"+t:t;return function(t,e,i){return"#"+r[t=t<0?0:t]+r[e=e<0?0:e]+r[i=i<0?0:i]}})(),setSubframeEnabled=function(t){subframeEnabled=!!t},getSubframeEnabled=function(){return subframeEnabled},setExpressionsPlugin=function(t){expressionsPlugin=t},getExpressionsPlugin=function(){return expressionsPlugin},setExpressionInterfaces=function(t){expressionsInterfaces=t},getExpressionInterfaces=function(){return expressionsInterfaces},setDefaultCurveSegments=function(t){defaultCurveSegments=t},getDefaultCurveSegments=function(){return defaultCurveSegments},setIdPrefix=function(t){idPrefix$1=t},getIdPrefix=function(){return idPrefix$1};function createNS(t){return document.createElementNS(svgNS,t)}function _typeof$5(t){return(_typeof$5="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var dataManager=(()=>{var i,r,s=1,a=[],n={onmessage:function(){},postMessage:function(t){i({data:t})}},o={postMessage:function(t){n.onmessage({data:t})}};function h(){var t,e;r||(t=function(e){function t(){function p(t,e){for(var i,r,s,a=t.length,n=0;n<a;n+=1)if("ks"in(s=t[n])&&!s.completed){if(s.completed=!0,s.hasMask)for(var o=s.masksProperties,h=o.length,l=0;l<h;l+=1)if(o[l].pt.k.i)c(o[l].pt.k);else for(r=o[l].pt.k.length,i=0;i<r;i+=1)o[l].pt.k[i].s&&c(o[l].pt.k[i].s[0]),o[l].pt.k[i].e&&c(o[l].pt.k[i].e[0]);0===s.ty?(s.layers=m(s.refId,e),p(s.layers,e)):4===s.ty?f(s.shapes):5===s.ty&&0===(s=s).t.a.length&&0 in s.t.p}}function m(t,e){t=((t,e)=>{for(var i=0,r=e.length;i<r;){if(e[i].id===t)return e[i];i+=1}return null})(t,e);return t?t.layers.__used?JSON.parse(JSON.stringify(t.layers)):(t.layers.__used=!0,t.layers):null}function f(t){for(var e,i,r=t.length-1;0<=r;--r)if("sh"===t[r].ty)if(t[r].ks.k.i)c(t[r].ks.k);else for(i=t[r].ks.k.length,e=0;e<i;e+=1)t[r].ks.k[e].s&&c(t[r].ks.k[e].s[0]),t[r].ks.k[e].e&&c(t[r].ks.k[e].e[0]);else"gr"===t[r].ty&&f(t[r].it)}function c(t){for(var e=t.i.length,i=0;i<e;i+=1)t.i[i][0]+=t.v[i][0],t.i[i][1]+=t.v[i][1],t.o[i][0]+=t.v[i][0],t.o[i][1]+=t.v[i][1]}function s(t,e){e=e?e.split("."):[100,100,100];return t[0]>e[0]||!(t[0]<e[0])&&(e[1]<t[1]||!(t[1]<e[1])&&e[2]<t[2])}r=[4,4,14];var r,a=function(t){if(s(r,t.v)&&(n(t.layers),t.assets))for(var e=t.assets.length,i=0;i<e;i+=1)t.assets[i].layers&&n(t.assets[i].layers)};function n(t){for(var e,i,r=t.length,s=0;s<r;s+=1)5===t[s].ty&&(e=t[s],i=void 0,i=e.t.d,e.t.d={k:[{s:i,t:0}]})}o=[4,7,99];var o,h,l=function(t){if(t.chars&&!s(o,t.v))for(var e=t.chars.length,i=0;i<e;i+=1){var r=t.chars[i];r.data&&r.data.shapes&&(f(r.data.shapes),r.data.ip=0,r.data.op=99999,r.data.st=0,r.data.sr=1,r.data.ks={p:{k:[0,0],a:0},s:{k:[100,100],a:0},a:{k:[0,0],a:0},r:{k:0,a:0},o:{k:100,a:0}},t.chars[i].t||(r.data.shapes.push({ty:"no"}),r.data.shapes[0].it.push({p:{k:[0,0],a:0},s:{k:[100,100],a:0},a:{k:[0,0],a:0},r:{k:0,a:0},o:{k:100,a:0},sk:{k:0,a:0},sa:{k:0,a:0},ty:"tr"})))}},d=(h=[5,7,15],function(t){if(s(h,t.v)&&(u(t.layers),t.assets))for(var e=t.assets.length,i=0;i<e;i+=1)t.assets[i].layers&&u(t.assets[i].layers)});function u(t){for(var e,i=t.length,r=0;r<i;r+=1)5===t[r].ty&&("number"==typeof(e=(e=t[r]).t.p).a&&(e.a={a:0,k:e.a}),"number"==typeof e.p&&(e.p={a:0,k:e.p}),"number"==typeof e.r)&&(e.r={a:0,k:e.r})}y=[4,1,9];var y,g=function(t){if(s(y,t.v)&&(v(t.layers),t.assets))for(var e=t.assets.length,i=0;i<e;i+=1)t.assets[i].layers&&v(t.assets[i].layers)};function v(t){for(var e=t.length,i=0;i<e;i+=1)4===t[i].ty&&!function t(e){for(var i,r,s=e.length,a=0;a<s;a+=1)if("gr"===e[a].ty)t(e[a].it);else if("fl"===e[a].ty||"st"===e[a].ty)if(e[a].c.k&&e[a].c.k[0].i)for(r=e[a].c.k.length,i=0;i<r;i+=1)e[a].c.k[i].s&&(e[a].c.k[i].s[0]/=255,e[a].c.k[i].s[1]/=255,e[a].c.k[i].s[2]/=255,e[a].c.k[i].s[3]/=255),e[a].c.k[i].e&&(e[a].c.k[i].e[0]/=255,e[a].c.k[i].e[1]/=255,e[a].c.k[i].e[2]/=255,e[a].c.k[i].e[3]/=255);else e[a].c.k[0]/=255,e[a].c.k[1]/=255,e[a].c.k[2]/=255,e[a].c.k[3]/=255}(t[i].shapes)}b=[4,4,18];var b,x=function(t){if(s(b,t.v)&&(P(t.layers),t.assets))for(var e=t.assets.length,i=0;i<e;i+=1)t.assets[i].layers&&P(t.assets[i].layers)};function P(t){for(var e,i,r,s=t.length,a=0;a<s;a+=1){if((e=t[a]).hasMask)for(var n=e.masksProperties,o=n.length,h=0;h<o;h+=1)if(n[h].pt.k.i)n[h].pt.k.c=n[h].cl;else for(r=n[h].pt.k.length,i=0;i<r;i+=1)n[h].pt.k[i].s&&(n[h].pt.k[i].s[0].c=n[h].cl),n[h].pt.k[i].e&&(n[h].pt.k[i].e[0].c=n[h].cl);4===e.ty&&!function t(e){for(var i,r,s=e.length-1;0<=s;--s)if("sh"===e[s].ty)if(e[s].ks.k.i)e[s].ks.k.c=e[s].closed;else for(r=e[s].ks.k.length,i=0;i<r;i+=1)e[s].ks.k[i].s&&(e[s].ks.k[i].s[0].c=e[s].closed),e[s].ks.k[i].e&&(e[s].ks.k[i].e[0].c=e[s].closed);else"gr"===e[s].ty&&t(e[s].it)}(e.shapes)}}var t={};return t.completeData=function(t){if(!t.__complete){g(t),a(t),l(t),d(t),x(t),p(t.layers,t.assets);var e=t.chars,i=t.assets;if(e)for(var r=0,s=e.length,r=0;r<s;r+=1)1===e[r].t&&(e[r].data.layers=m(e[r].data.refId,i),p(e[r].data.layers,i));t.__complete=!0}},t.checkColors=g,t.checkChars=l,t.checkPathProperties=d,t.checkShapes=x,t.completeLayers=p,t}function n(t){var e=t.getResponseHeader("content-type");return e&&"json"===t.responseType&&-1!==e.indexOf("json")||t.response&&"object"===_typeof$5(t.response)?t.response:t.response&&"string"==typeof t.response?JSON.parse(t.response):t.responseText?JSON.parse(t.responseText):null}var i;o.dataManager||(o.dataManager=t()),o.assetLoader||(o.assetLoader={load:function(e,i,t,r){var s,a=new XMLHttpRequest;try{a.responseType="json"}catch(t){}a.onreadystatechange=function(){if(4===a.readyState)if(200===a.status)s=n(a),t(s);else try{s=n(a),t(s)}catch(t){r&&r(t)}};try{a.open(["G","E","T"].join(""),e,!0)}catch(t){a.open(["G","E","T"].join(""),i+"/"+e,!0)}a.send()}}),"loadAnimation"===e.data.type?o.assetLoader.load(e.data.path,e.data.fullPath,function(t){o.dataManager.completeData(t),o.postMessage({id:e.data.id,payload:t,status:"success"})},function(){o.postMessage({id:e.data.id,status:"error"})}):"complete"===e.data.type?(i=e.data.animation,o.dataManager.completeData(i),o.postMessage({id:e.data.id,payload:i,status:"success"})):"loadData"===e.data.type&&o.assetLoader.load(e.data.path,e.data.fullPath,function(t){o.postMessage({id:e.data.id,payload:t,status:"success"})},function(){o.postMessage({id:e.data.id,status:"error"})})},(r=window.Worker&&window.Blob&&getWebWorker()?(e=new Blob(["var _workerSelf = self; self.onmessage = ",t.toString()],{type:"text/javascript"}),e=URL.createObjectURL(e),new Worker(e)):(i=t,n)).onmessage=function(t){var t=t.data,e=t.id,i=a[e];a[e]=null,"success"===t.status?i.onComplete(t.payload):i.onError&&i.onError()})}function l(t,e){var i="processId_"+(s+=1);return a[i]={onComplete:t,onError:e},i}return{loadAnimation:function(t,e,i){h(),e=l(e,i),r.postMessage({type:"loadAnimation",path:t,fullPath:window.location.origin+window.location.pathname,id:e})},loadData:function(t,e,i){h(),e=l(e,i),r.postMessage({type:"loadData",path:t,fullPath:window.location.origin+window.location.pathname,id:e})},completeAnimation:function(t,e,i){h(),e=l(e,i),r.postMessage({type:"complete",animation:t,id:e})}}})(),ImagePreloader=(()=>{(t=createTag("canvas")).width=1,t.height=1,(e=t.getContext("2d")).fillStyle="rgba(0,0,0,0)",e.fillRect(0,0,1,1);var t,e,s=t;function i(){this.loadedAssets+=1,this.loadedAssets===this.totalImages&&this.loadedFootagesCount===this.totalFootages&&this.imagesLoadedCb&&this.imagesLoadedCb(null)}function r(){this.loadedFootagesCount+=1,this.loadedAssets===this.totalImages&&this.loadedFootagesCount===this.totalFootages&&this.imagesLoadedCb&&this.imagesLoadedCb(null)}function a(t,e,i){var r="";return r=t.e?t.p:e?e+(e=-1!==(e=t.p).indexOf("images/")?e.split("/")[1]:e):(r=i,(r+=t.u||"")+t.p)}function n(){this._imageLoaded=i.bind(this),this._footageLoaded=r.bind(this),this.testImageLoaded=function(t){var e=0,i=setInterval(function(){(t.getBBox().width||500<e)&&(this._imageLoaded(),clearInterval(i)),e+=1}.bind(this),50)}.bind(this),this.createFootageData=function(t){var e={assetData:t},t=a(t,this.assetsPath,this.path);return dataManager.loadData(t,function(t){e.img=t,this._footageLoaded()}.bind(this),function(){e.img={},this._footageLoaded()}.bind(this)),e}.bind(this),this.assetsPath="",this.path="",this.totalImages=0,this.totalFootages=0,this.loadedAssets=0,this.loadedFootagesCount=0,this.imagesLoadedCb=null,this.images=[]}return n.prototype={loadAssets:function(t,e){this.imagesLoadedCb=e;for(var i=t.length,r=0;r<i;r+=1)t[r].layers||(t[r].t&&"seq"!==t[r].t?3===t[r].t&&(this.totalFootages+=1,this.images.push(this.createFootageData(t[r]))):(this.totalImages+=1,this.images.push(this._createImageData(t[r]))))},setAssetsPath:function(t){this.assetsPath=t||""},setPath:function(t){this.path=t||""},loadedImages:function(){return this.totalImages===this.loadedAssets},loadedFootages:function(){return this.totalFootages===this.loadedFootagesCount},destroy:function(){this.imagesLoadedCb=null,this.images.length=0},getAsset:function(t){for(var e=0,i=this.images.length;e<i;){if(this.images[e].assetData===t)return this.images[e].img;e+=1}return null},createImgData:function(t){var e=a(t,this.assetsPath,this.path),i=createTag("img"),r=(i.crossOrigin="anonymous",i.addEventListener("load",this._imageLoaded,!1),i.addEventListener("error",function(){r.img=s,this._imageLoaded()}.bind(this),!1),i.src=e,{img:i,assetData:t});return r},createImageData:function(t){var e=a(t,this.assetsPath,this.path),i=createNS("image"),r=(isSafari?this.testImageLoaded(i):i.addEventListener("load",this._imageLoaded,!1),i.addEventListener("error",function(){r.img=s,this._imageLoaded()}.bind(this),!1),i.setAttributeNS("http://www.w3.org/1999/xlink","href",e),this._elementHelper.append?this._elementHelper.append(i):this._elementHelper.appendChild(i),{img:i,assetData:t});return r},imageLoaded:i,footageLoaded:r,setCacheType:function(t,e){"svg"===t?(this._elementHelper=e,this._createImageData=this.createImageData.bind(this)):this._createImageData=this.createImgData.bind(this)}},n})();function BaseEvent(){}BaseEvent.prototype={triggerEvent:function(t,e){if(this._cbs[t])for(var i=this._cbs[t],r=0;r<i.length;r+=1)i[r](e)},addEventListener:function(t,e){return this._cbs[t]||(this._cbs[t]=[]),this._cbs[t].push(e),function(){this.removeEventListener(t,e)}.bind(this)},removeEventListener:function(t,e){if(e){if(this._cbs[t]){for(var i=0,r=this._cbs[t].length;i<r;)this._cbs[t][i]===e&&(this._cbs[t].splice(i,1),--i,--r),i+=1;this._cbs[t].length||(this._cbs[t]=null)}}else this._cbs[t]=null}};var markerParser=function(e){for(var t=[],i=0;i<e.length;i+=1){var r=e[i],r={time:r.tm,duration:r.dr};try{r.payload=JSON.parse(e[i].cm)}catch(t){try{r.payload=(t=>{for(var e,i=t.split("\r\n"),r={},s=0,a=0;a<i.length;a+=1)2===(e=i[a].split(":")).length&&(r[e[0]]=e[1].trim(),s+=1);if(0===s)throw new Error;return r})(e[i].cm)}catch(t){r.payload={name:e[i].cm}}}t.push(r)}return t},ProjectInterface=(()=>{function e(t){this.compositions.push(t)}return function(){function t(t){for(var e=0,i=this.compositions.length;e<i;){if(this.compositions[e].data&&this.compositions[e].data.nm===t)return this.compositions[e].prepareFrame&&this.compositions[e].data.xt&&this.compositions[e].prepareFrame(this.currentFrame),this.compositions[e].compInterface;e+=1}return null}return t.compositions=[],t.currentFrame=0,t.registerComposition=e,t}})(),renderers={},registerRenderer=function(t,e){renderers[t]=e};function getRenderer(t){return renderers[t]}function getRegisteredRenderer(){if(renderers.canvas)return"canvas";for(var t in renderers)if(renderers[t])return t;return""}function _typeof$4(t){return(_typeof$4="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var AnimationItem=function(){this._cbs=[],this.name="",this.path="",this.isLoaded=!1,this.currentFrame=0,this.currentRawFrame=0,this.firstFrame=0,this.totalFrames=0,this.frameRate=0,this.frameMult=0,this.playSpeed=1,this.playDirection=1,this.playCount=0,this.animationData={},this.assets=[],this.isPaused=!0,this.autoplay=!1,this.loop=!0,this.renderer=null,this.animationID=createElementID(),this.assetsPath="",this.timeCompleted=0,this.segmentPos=0,this.isSubframeEnabled=getSubframeEnabled(),this.segments=[],this._idle=!0,this._completedLoop=!1,this.projectInterface=ProjectInterface(),this.imagePreloader=new ImagePreloader,this.audioController=audioControllerFactory(),this.markers=[],this.configAnimation=this.configAnimation.bind(this),this.onSetupError=this.onSetupError.bind(this),this.onSegmentComplete=this.onSegmentComplete.bind(this),this.drawnFrameEvent=new BMEnterFrameEvent("drawnFrame",0,0,0),this.expressionsPlugin=getExpressionsPlugin()},animationManager=(extendPrototype([BaseEvent],AnimationItem),AnimationItem.prototype.setParams=function(t){(t.wrapper||t.container)&&(this.wrapper=t.wrapper||t.container);var e="svg",i=(t.animType?e=t.animType:t.renderer&&(e=t.renderer),getRenderer(e));this.renderer=new i(this,t.rendererSettings),this.imagePreloader.setCacheType(e,this.renderer.globalData.defs),this.renderer.setProjectInterface(this.projectInterface),this.animType=e,""===t.loop||null==t.loop||!0===t.loop?this.loop=!0:!1===t.loop?this.loop=!1:this.loop=parseInt(t.loop,10),this.autoplay=!("autoplay"in t)||t.autoplay,this.name=t.name||"",this.autoloadSegments=!Object.prototype.hasOwnProperty.call(t,"autoloadSegments")||t.autoloadSegments,this.assetsPath=t.assetsPath,this.initialSegment=t.initialSegment,t.audioFactory&&this.audioController.setAudioFactory(t.audioFactory),t.animationData?this.setupAnimation(t.animationData):t.path&&(-1!==t.path.lastIndexOf("\\")?this.path=t.path.substr(0,t.path.lastIndexOf("\\")+1):this.path=t.path.substr(0,t.path.lastIndexOf("/")+1),this.fileName=t.path.substr(t.path.lastIndexOf("/")+1),this.fileName=this.fileName.substr(0,this.fileName.lastIndexOf(".json")),dataManager.loadAnimation(t.path,this.configAnimation,this.onSetupError))},AnimationItem.prototype.onSetupError=function(){this.trigger("data_failed")},AnimationItem.prototype.setupAnimation=function(t){dataManager.completeAnimation(t,this.configAnimation)},AnimationItem.prototype.setData=function(t,e){var e={wrapper:t,animationData:e=e&&"object"!==_typeof$4(e)?JSON.parse(e):e},t=t.attributes,i=(e.path=t.getNamedItem("data-animation-path")?t.getNamedItem("data-animation-path").value:t.getNamedItem("data-bm-path")?t.getNamedItem("data-bm-path").value:t.getNamedItem("bm-path")?t.getNamedItem("bm-path").value:"",e.animType=t.getNamedItem("data-anim-type")?t.getNamedItem("data-anim-type").value:t.getNamedItem("data-bm-type")?t.getNamedItem("data-bm-type").value:t.getNamedItem("bm-type")?t.getNamedItem("bm-type").value:t.getNamedItem("data-bm-renderer")?t.getNamedItem("data-bm-renderer").value:t.getNamedItem("bm-renderer")?t.getNamedItem("bm-renderer").value:getRegisteredRenderer()||"canvas",t.getNamedItem("data-anim-loop")?t.getNamedItem("data-anim-loop").value:t.getNamedItem("data-bm-loop")?t.getNamedItem("data-bm-loop").value:t.getNamedItem("bm-loop")?t.getNamedItem("bm-loop").value:""),i=("false"===i?e.loop=!1:"true"===i?e.loop=!0:""!==i&&(e.loop=parseInt(i,10)),t.getNamedItem("data-anim-autoplay")?t.getNamedItem("data-anim-autoplay").value:t.getNamedItem("data-bm-autoplay")?t.getNamedItem("data-bm-autoplay").value:!t.getNamedItem("bm-autoplay")||t.getNamedItem("bm-autoplay").value);e.autoplay="false"!==i,e.name=t.getNamedItem("data-name")?t.getNamedItem("data-name").value:t.getNamedItem("data-bm-name")?t.getNamedItem("data-bm-name").value:t.getNamedItem("bm-name")?t.getNamedItem("bm-name").value:"","false"===(t.getNamedItem("data-anim-prerender")?t.getNamedItem("data-anim-prerender").value:t.getNamedItem("data-bm-prerender")?t.getNamedItem("data-bm-prerender").value:t.getNamedItem("bm-prerender")?t.getNamedItem("bm-prerender").value:"")&&(e.prerender=!1),e.path?this.setParams(e):this.trigger("destroy")},AnimationItem.prototype.includeLayers=function(t){t.op>this.animationData.op&&(this.animationData.op=t.op,this.totalFrames=Math.floor(t.op-this.animationData.ip));for(var e,i=this.animationData.layers,r=i.length,s=t.layers,a=s.length,n=0;n<a;n+=1)for(e=0;e<r;){if(i[e].id===s[n].id){i[e]=s[n];break}e+=1}if((t.chars||t.fonts)&&(this.renderer.globalData.fontManager.addChars(t.chars),this.renderer.globalData.fontManager.addFonts(t.fonts,this.renderer.globalData.defs)),t.assets)for(r=t.assets.length,e=0;e<r;e+=1)this.animationData.assets.push(t.assets[e]);this.animationData.__complete=!1,dataManager.completeAnimation(this.animationData,this.onSegmentComplete)},AnimationItem.prototype.onSegmentComplete=function(t){this.animationData=t;t=getExpressionsPlugin();t&&t.initExpressions(this),this.loadNextSegment()},AnimationItem.prototype.loadNextSegment=function(){var t=this.animationData.segments;t&&0!==t.length&&this.autoloadSegments?(t=t.shift(),this.timeCompleted=t.time*this.frameRate,t=this.path+this.fileName+"_"+this.segmentPos+".json",this.segmentPos+=1,dataManager.loadData(t,this.includeLayers.bind(this),function(){this.trigger("data_failed")}.bind(this))):(this.trigger("data_ready"),this.timeCompleted=this.totalFrames)},AnimationItem.prototype.loadSegments=function(){this.animationData.segments||(this.timeCompleted=this.totalFrames),this.loadNextSegment()},AnimationItem.prototype.imagesLoaded=function(){this.trigger("loaded_images"),this.checkLoaded()},AnimationItem.prototype.preloadImages=function(){this.imagePreloader.setAssetsPath(this.assetsPath),this.imagePreloader.setPath(this.path),this.imagePreloader.loadAssets(this.animationData.assets,this.imagesLoaded.bind(this))},AnimationItem.prototype.configAnimation=function(t){if(this.renderer)try{this.animationData=t,this.initialSegment?(this.totalFrames=Math.floor(this.initialSegment[1]-this.initialSegment[0]),this.firstFrame=Math.round(this.initialSegment[0])):(this.totalFrames=Math.floor(this.animationData.op-this.animationData.ip),this.firstFrame=Math.round(this.animationData.ip)),this.renderer.configAnimation(t),t.assets||(t.assets=[]),this.assets=this.animationData.assets,this.frameRate=this.animationData.fr,this.frameMult=this.animationData.fr/1e3,this.renderer.searchExtraCompositions(t.assets),this.markers=markerParser(t.markers||[]),this.trigger("config_ready"),this.preloadImages(),this.loadSegments(),this.updaFrameModifier(),this.waitForFontsLoaded(),this.isPaused&&this.audioController.pause()}catch(t){this.triggerConfigError(t)}},AnimationItem.prototype.waitForFontsLoaded=function(){this.renderer&&(this.renderer.globalData.fontManager.isLoaded?this.checkLoaded():setTimeout(this.waitForFontsLoaded.bind(this),20))},AnimationItem.prototype.checkLoaded=function(){var t;!this.isLoaded&&this.renderer.globalData.fontManager.isLoaded&&(this.imagePreloader.loadedImages()||"canvas"!==this.renderer.rendererType)&&this.imagePreloader.loadedFootages()&&(this.isLoaded=!0,(t=getExpressionsPlugin())&&t.initExpressions(this),this.renderer.initItems(),setTimeout(function(){this.trigger("DOMLoaded")}.bind(this),0),this.gotoFrame(),this.autoplay)&&this.play()},AnimationItem.prototype.resize=function(t,e){this.renderer.updateContainerSize("number"==typeof t?t:void 0,"number"==typeof e?e:void 0)},AnimationItem.prototype.setSubframe=function(t){this.isSubframeEnabled=!!t},AnimationItem.prototype.gotoFrame=function(){this.currentFrame=this.isSubframeEnabled?this.currentRawFrame:~~this.currentRawFrame,this.timeCompleted!==this.totalFrames&&this.currentFrame>this.timeCompleted&&(this.currentFrame=this.timeCompleted),this.trigger("enterFrame"),this.renderFrame(),this.trigger("drawnFrame")},AnimationItem.prototype.renderFrame=function(){if(!1!==this.isLoaded&&this.renderer)try{this.expressionsPlugin&&this.expressionsPlugin.resetFrame(),this.renderer.renderFrame(this.currentFrame+this.firstFrame)}catch(t){this.triggerRenderFrameError(t)}},AnimationItem.prototype.play=function(t){t&&this.name!==t||!0===this.isPaused&&(this.isPaused=!1,this.trigger("_play"),this.audioController.resume(),this._idle)&&(this._idle=!1,this.trigger("_active"))},AnimationItem.prototype.pause=function(t){t&&this.name!==t||!1===this.isPaused&&(this.isPaused=!0,this.trigger("_pause"),this._idle=!0,this.trigger("_idle"),this.audioController.pause())},AnimationItem.prototype.togglePause=function(t){t&&this.name!==t||(!0===this.isPaused?this.play():this.pause())},AnimationItem.prototype.stop=function(t){t&&this.name!==t||(this.pause(),this.playCount=0,this._completedLoop=!1,this.setCurrentRawFrameValue(0))},AnimationItem.prototype.getMarkerData=function(t){for(var e,i=0;i<this.markers.length;i+=1)if((e=this.markers[i]).payload&&e.payload.name===t)return e;return null},AnimationItem.prototype.goToAndStop=function(t,e,i){i&&this.name!==i||(i=Number(t),isNaN(i)?(i=this.getMarkerData(t))&&this.goToAndStop(i.time,!0):e?this.setCurrentRawFrameValue(t):this.setCurrentRawFrameValue(t*this.frameModifier),this.pause())},AnimationItem.prototype.goToAndPlay=function(t,e,i){var r;i&&this.name!==i||(r=Number(t),isNaN(r)?(t=this.getMarkerData(t))&&(t.duration?this.playSegments([t.time,t.time+t.duration],!0):this.goToAndStop(t.time,!0)):this.goToAndStop(r,e,i),this.play())},AnimationItem.prototype.advanceTime=function(t){var e;!0!==this.isPaused&&!1!==this.isLoaded&&(e=!1,(t=this.currentRawFrame+t*this.frameModifier)>=this.totalFrames-1&&0<this.frameModifier?this.loop&&this.playCount!==this.loop?t>=this.totalFrames?(this.playCount+=1,this.checkSegments(t%this.totalFrames)||(this.setCurrentRawFrameValue(t%this.totalFrames),this._completedLoop=!0,this.trigger("loopComplete"))):this.setCurrentRawFrameValue(t):this.checkSegments(t>this.totalFrames?t%this.totalFrames:0)||(e=!0,t=this.totalFrames-1):t<0?this.checkSegments(t%this.totalFrames)||(!this.loop||this.playCount--<=0&&!0!==this.loop?(e=!0,t=0):(this.setCurrentRawFrameValue(this.totalFrames+t%this.totalFrames),this._completedLoop?this.trigger("loopComplete"):this._completedLoop=!0)):this.setCurrentRawFrameValue(t),e)&&(this.setCurrentRawFrameValue(t),this.pause(),this.trigger("complete"))},AnimationItem.prototype.adjustSegment=function(t,e){this.playCount=0,t[1]<t[0]?(0<this.frameModifier&&(this.playSpeed<0?this.setSpeed(-this.playSpeed):this.setDirection(-1)),this.totalFrames=t[0]-t[1],this.timeCompleted=this.totalFrames,this.firstFrame=t[1],this.setCurrentRawFrameValue(this.totalFrames-.001-e)):t[1]>t[0]&&(this.frameModifier<0&&(this.playSpeed<0?this.setSpeed(-this.playSpeed):this.setDirection(1)),this.totalFrames=t[1]-t[0],this.timeCompleted=this.totalFrames,this.firstFrame=t[0],this.setCurrentRawFrameValue(.001+e)),this.trigger("segmentStart")},AnimationItem.prototype.setSegment=function(t,e){var i=-1;this.isPaused&&(this.currentRawFrame+this.firstFrame<t?i=t:this.currentRawFrame+this.firstFrame>e&&(i=e-t)),this.firstFrame=t,this.totalFrames=e-t,this.timeCompleted=this.totalFrames,-1!==i&&this.goToAndStop(i,!0)},AnimationItem.prototype.playSegments=function(t,e){if(e&&(this.segments.length=0),"object"===_typeof$4(t[0]))for(var i=t.length,r=0;r<i;r+=1)this.segments.push(t[r]);else this.segments.push(t);this.segments.length&&e&&this.adjustSegment(this.segments.shift(),0),this.isPaused&&this.play()},AnimationItem.prototype.resetSegments=function(t){this.segments.length=0,this.segments.push([this.animationData.ip,this.animationData.op]),t&&this.checkSegments(0)},AnimationItem.prototype.checkSegments=function(t){return!!this.segments.length&&(this.adjustSegment(this.segments.shift(),t),!0)},AnimationItem.prototype.destroy=function(t){t&&this.name!==t||!this.renderer||(this.renderer.destroy(),this.imagePreloader.destroy(),this.trigger("destroy"),this._cbs=null,this.onEnterFrame=null,this.onLoopComplete=null,this.onComplete=null,this.onSegmentStart=null,this.onDestroy=null,this.renderer=null,this.expressionsPlugin=null,this.imagePreloader=null,this.projectInterface=null)},AnimationItem.prototype.setCurrentRawFrameValue=function(t){this.currentRawFrame=t,this.gotoFrame()},AnimationItem.prototype.setSpeed=function(t){this.playSpeed=t,this.updaFrameModifier()},AnimationItem.prototype.setDirection=function(t){this.playDirection=t<0?-1:1,this.updaFrameModifier()},AnimationItem.prototype.setLoop=function(t){this.loop=t},AnimationItem.prototype.setVolume=function(t,e){e&&this.name!==e||this.audioController.setVolume(t)},AnimationItem.prototype.getVolume=function(){return this.audioController.getVolume()},AnimationItem.prototype.mute=function(t){t&&this.name!==t||this.audioController.mute()},AnimationItem.prototype.unmute=function(t){t&&this.name!==t||this.audioController.unmute()},AnimationItem.prototype.updaFrameModifier=function(){this.frameModifier=this.frameMult*this.playSpeed*this.playDirection,this.audioController.setRate(this.playSpeed*this.playDirection)},AnimationItem.prototype.getPath=function(){return this.path},AnimationItem.prototype.getAssetsPath=function(t){var e,i="";return i=t.e?t.p:this.assetsPath?(-1!==(e=t.p).indexOf("images/")&&(e=e.split("/")[1]),this.assetsPath+e):(i=this.path,(i+=t.u||"")+t.p)},AnimationItem.prototype.getAssetData=function(t){for(var e=0,i=this.assets.length;e<i;){if(t===this.assets[e].id)return this.assets[e];e+=1}return null},AnimationItem.prototype.hide=function(){this.renderer.hide()},AnimationItem.prototype.show=function(){this.renderer.show()},AnimationItem.prototype.getDuration=function(t){return t?this.totalFrames:this.totalFrames/this.frameRate},AnimationItem.prototype.updateDocumentData=function(t,e,i){try{this.renderer.getElementByPath(t).updateDocumentData(e,i)}catch(t){}},AnimationItem.prototype.trigger=function(t){if(this._cbs&&this._cbs[t])switch(t){case"enterFrame":this.triggerEvent(t,new BMEnterFrameEvent(t,this.currentFrame,this.totalFrames,this.frameModifier));break;case"drawnFrame":this.drawnFrameEvent.currentTime=this.currentFrame,this.drawnFrameEvent.totalTime=this.totalFrames,this.drawnFrameEvent.direction=this.frameModifier,this.triggerEvent(t,this.drawnFrameEvent);break;case"loopComplete":this.triggerEvent(t,new BMCompleteLoopEvent(t,this.loop,this.playCount,this.frameMult));break;case"complete":this.triggerEvent(t,new BMCompleteEvent(t,this.frameMult));break;case"segmentStart":this.triggerEvent(t,new BMSegmentStartEvent(t,this.firstFrame,this.totalFrames));break;case"destroy":this.triggerEvent(t,new BMDestroyEvent(t,this));break;default:this.triggerEvent(t)}"enterFrame"===t&&this.onEnterFrame&&this.onEnterFrame.call(this,new BMEnterFrameEvent(t,this.currentFrame,this.totalFrames,this.frameMult)),"loopComplete"===t&&this.onLoopComplete&&this.onLoopComplete.call(this,new BMCompleteLoopEvent(t,this.loop,this.playCount,this.frameMult)),"complete"===t&&this.onComplete&&this.onComplete.call(this,new BMCompleteEvent(t,this.frameMult)),"segmentStart"===t&&this.onSegmentStart&&this.onSegmentStart.call(this,new BMSegmentStartEvent(t,this.firstFrame,this.totalFrames)),"destroy"===t&&this.onDestroy&&this.onDestroy.call(this,new BMDestroyEvent(t,this))},AnimationItem.prototype.triggerRenderFrameError=function(t){t=new BMRenderFrameErrorEvent(t,this.currentFrame);this.triggerEvent("error",t),this.onError&&this.onError.call(this,t)},AnimationItem.prototype.triggerConfigError=function(t){t=new BMConfigErrorEvent(t,this.currentFrame);this.triggerEvent("error",t),this.onError&&this.onError.call(this,t)},(()=>{var t={},s=[],r=0,a=0,n=0,o=!0,h=!1;function i(t){for(var e=0,i=t.target;e<a;)s[e].animation===i&&(s.splice(e,1),--e,--a,i.isPaused||m()),e+=1}function l(t,e){if(!t)return null;for(var i=0;i<a;){if(s[i].elem===t&&null!==s[i].elem)return s[i].animation;i+=1}var r=new AnimationItem;return f(r,t),r.setData(t,e),r}function p(){n+=1,d()}function m(){--n}function f(t,e){t.addEventListener("destroy",i),t.addEventListener("_active",p),t.addEventListener("_idle",m),s.push({elem:e,animation:t}),a+=1}function c(t){for(var e=t-r,i=0;i<a;i+=1)s[i].animation.advanceTime(e);r=t,n&&!h?window.requestAnimationFrame(c):o=!0}function e(t){r=t,window.requestAnimationFrame(c)}function d(){!h&&n&&o&&(window.requestAnimationFrame(e),o=!1)}return t.registerAnimation=l,t.loadAnimation=function(t){var e=new AnimationItem;return f(e,null),e.setParams(t),e},t.setSpeed=function(t,e){for(var i=0;i<a;i+=1)s[i].animation.setSpeed(t,e)},t.setDirection=function(t,e){for(var i=0;i<a;i+=1)s[i].animation.setDirection(t,e)},t.play=function(t){for(var e=0;e<a;e+=1)s[e].animation.play(t)},t.pause=function(t){for(var e=0;e<a;e+=1)s[e].animation.pause(t)},t.stop=function(t){for(var e=0;e<a;e+=1)s[e].animation.stop(t)},t.togglePause=function(t){for(var e=0;e<a;e+=1)s[e].animation.togglePause(t)},t.searchAnimations=function(t,e,i){for(var r,s=[].concat([].slice.call(document.getElementsByClassName("lottie")),[].slice.call(document.getElementsByClassName("bodymovin"))),a=s.length,n=0;n<a;n+=1)i&&s[n].setAttribute("data-bm-type",i),l(s[n],t);e&&0===a&&(i=i||"svg",(e=document.getElementsByTagName("body")[0]).innerText="",(r=createTag("div")).style.width="100%",r.style.height="100%",r.setAttribute("data-bm-type",i),e.appendChild(r),l(r,t))},t.resize=function(){for(var t=0;t<a;t+=1)s[t].animation.resize()},t.goToAndStop=function(t,e,i){for(var r=0;r<a;r+=1)s[r].animation.goToAndStop(t,e,i)},t.destroy=function(t){for(var e=a-1;0<=e;--e)s[e].animation.destroy(t)},t.freeze=function(){h=!0},t.unfreeze=function(){h=!1,d()},t.setVolume=function(t,e){for(var i=0;i<a;i+=1)s[i].animation.setVolume(t,e)},t.mute=function(t){for(var e=0;e<a;e+=1)s[e].animation.mute(t)},t.unmute=function(t){for(var e=0;e<a;e+=1)s[e].animation.unmute(t)},t.getRegisteredAnimations=function(){for(var t=s.length,e=[],i=0;i<t;i+=1)e.push(s[i].animation);return e},t})()),BezierFactory=(()=>{var t={getBezierEasing:function(t,e,i,r,s){s=s||("bez_"+t+"_"+e+"_"+i+"_"+r).replace(/\./g,"p");if(a[s])return a[s];t=new n([t,e,i,r]);return a[s]=t}},a={},S=4,C=1e-7,_=10,A=11,T=1/(A-1),e="function"==typeof Float32Array;function r(t,e){return 1-3*e+3*t}function M(t,e,i){return((r(e,i)*t+(3*i-6*e))*t+3*e)*t}function k(t,e,i){return 3*r(e,i)*t*t+2*(3*i-6*e)*t+3*e}function n(t){this._p=t,this._mSampleValues=new(e?Float32Array:Array)(A),this._precomputed=!1,this.get=this.get.bind(this)}return n.prototype={get:function(t){var e=this._p[0],i=this._p[1],r=this._p[2],s=this._p[3];return this._precomputed||this._precompute(),e===i&&r===s?t:0===t?0:1===t?1:M(this._getTForX(t),i,s)},_precompute:function(){var t=this._p[0],e=this._p[1],i=this._p[2],r=this._p[3];this._precomputed=!0,t===e&&i===r||this._calcSampleValues()},_calcSampleValues:function(){for(var t=this._p[0],e=this._p[2],i=0;i<A;++i)this._mSampleValues[i]=M(i*T,t,e)},_getTForX:function(t){for(var e=this._p[0],i=this._p[2],r=this._mSampleValues,s=0,a=1,n=A-1;a!==n&&r[a]<=t;++a)s+=T;var o=s+(t-r[--a])/(r[a+1]-r[a])*T,h=k(o,e,i);if(.001<=h){for(var l=t,p=o,m=e,f=i,c=0;c<S;++c){var d=k(p,m,f);if(0===d)return p;p-=(M(p,m,f)-l)/d}return p}if(0===h)return o;for(var u,y,g=t,v=s,b=s+T,x=e,P=i,E=0;0<(u=M(y=v+(b-v)/2,x,P)-g)?b=y:v=y,Math.abs(u)>C&&++E<_;);return y}},t})(),pooling={double:function(t){return t.concat(createSizedArray(t.length))}},poolFactory=function(t,e,i){var r=0,s=t,a=createSizedArray(s);return{newElement:function(){var t;t=r?a[--r]:e();return t},release:function(t){r===s&&(a=pooling.double(a),s*=2);i&&i(t);a[r]=t,r+=1}}},bezierLengthPool=poolFactory(8,function(){return{addedLength:0,percents:createTypedArray("float32",getDefaultCurveSegments()),lengths:createTypedArray("float32",getDefaultCurveSegments())}}),segmentsLengthPool=poolFactory(8,function(){return{lengths:[],totalLength:0}},function(t){for(var e=t.lengths.length,i=0;i<e;i+=1)bezierLengthPool.release(t.lengths[i]);t.lengths.length=0});function bezFunction(){var A=Math;function y(t,e,i,r,s,a){s=t*r+e*s+i*a-s*r-a*t-i*e;return-.001<s&&s<.001}var p=function(t,e,i,r){for(var s,a,n,o,h=getDefaultCurveSegments(),l=0,p=[],m=[],f=bezierLengthPool.newElement(),c=i.length,d=0;d<h;d+=1){for(n=d/(h-1),s=o=0;s<c;s+=1)a=bmPow(1-n,3)*t[s]+3*bmPow(1-n,2)*n*i[s]+3*(1-n)*bmPow(n,2)*r[s]+bmPow(n,3)*e[s],p[s]=a,null!==m[s]&&(o+=bmPow(p[s]-m[s],2)),m[s]=p[s];o&&(l+=o=bmSqrt(o)),f.percents[d]=n,f.lengths[d]=l}return f.addedLength=l,f};function g(t){this.segmentLength=0,this.points=new Array(t)}function v(t,e){this.partialLength=t,this.point=e}function t(t,e,i,r){var s=(t[0]+"_"+t[1]+"_"+e[0]+"_"+e[1]+"_"+i[0]+"_"+i[1]+"_"+r[0]+"_"+r[1]).replace(/\./g,"p");if(!b[s]){for(var a,n,o,h,l,p=getDefaultCurveSegments(),m=0,f=null,c=new g(p=2===t.length&&(t[0]!==e[0]||t[1]!==e[1])&&y(t[0],t[1],e[0],e[1],t[0]+i[0],t[1]+i[1])&&y(t[0],t[1],e[0],e[1],e[0]+r[0],e[1]+r[1])?2:p),d=i.length,u=0;u<p;u+=1){for(l=createSizedArray(d),o=u/(p-1),a=h=0;a<d;a+=1)n=bmPow(1-o,3)*t[a]+3*bmPow(1-o,2)*o*(t[a]+i[a])+3*(1-o)*bmPow(o,2)*(e[a]+r[a])+bmPow(o,3)*e[a],l[a]=n,null!==f&&(h+=bmPow(l[a]-f[a],2));m+=h=bmSqrt(h),c.points[u]=new v(h,l),f=l}c.segmentLength=m,b[s]=c}return b[s]}var b;b={};function T(t,e){var i=e.percents,r=e.lengths,s=i.length,a=bmFloor((s-1)*t),n=t*e.addedLength,o=0;if(a===s-1||0===a||n===r[a])return i[a];for(var h=r[a]>n?-1:1,l=!0;l;)if(r[a]<=n&&r[a+1]>n?(o=(n-r[a])/(r[a+1]-r[a]),l=!1):a+=h,a<0||s-1<=a){if(a===s-1)return i[a];l=!1}return i[a]+(i[a+1]-i[a])*o}var M=createTypedArray("float32",8);return{getSegmentsLength:function(t){for(var e=segmentsLengthPool.newElement(),i=t.c,r=t.v,s=t.o,a=t.i,n=t._length,o=e.lengths,h=0,l=0;l<n-1;l+=1)o[l]=p(r[l],r[l+1],s[l],a[l+1]),h+=o[l].addedLength;return i&&n&&(o[l]=p(r[l],r[0],s[l],a[0]),h+=o[l].addedLength),e.totalLength=h,e},getNewSegment:function(t,e,i,r,s,a,n){s<0?s=0:1<s&&(s=1);for(var s=T(s,n),a=T(a=1<a?1:a,n),o=t.length,h=1-a,l=(n=1-s)*n*n,p=s*n*n*3,m=s*s*n*3,f=s*s*s,c=n*n*h,d=s*n*h+n*s*h+n*n*a,u=s*s*h+n*s*a+s*n*a,y=s*s*a,g=n*h*h,v=s*h*h+n*a*h+n*h*a,b=s*a*h+n*a*a+s*h*a,x=s*a*a,P=h*h*h,E=a*h*h+h*a*h+h*h*a,S=a*a*h+h*a*a+a*h*a,C=a*a*a,_=0;_<o;_+=1)M[4*_]=A.round(1e3*(l*t[_]+p*i[_]+m*r[_]+f*e[_]))/1e3,M[4*_+1]=A.round(1e3*(c*t[_]+d*i[_]+u*r[_]+y*e[_]))/1e3,M[4*_+2]=A.round(1e3*(g*t[_]+v*i[_]+b*r[_]+x*e[_]))/1e3,M[4*_+3]=A.round(1e3*(P*t[_]+E*i[_]+S*r[_]+C*e[_]))/1e3;return M},getPointInSegment:function(t,e,i,r,s,a){return s=T(s,a),a=1-s,[A.round(1e3*(a*a*a*t[0]+(s*a*a+a*s*a+a*a*s)*i[0]+(s*s*a+a*s*s+s*a*s)*r[0]+s*s*s*e[0]))/1e3,A.round(1e3*(a*a*a*t[1]+(s*a*a+a*s*a+a*a*s)*i[1]+(s*s*a+a*s*s+s*a*s)*r[1]+s*s*s*e[1]))/1e3]},buildBezierData:t,pointOnLine2D:y,pointOnLine3D:function(t,e,i,r,s,a,n,o,h){var l;return 0===i&&0===a&&0===h?y(t,e,r,s,n,o):(l=A.sqrt(A.pow(r-t,2)+A.pow(s-e,2)+A.pow(a-i,2)),t=A.sqrt(A.pow(n-t,2)+A.pow(o-e,2)+A.pow(h-i,2)),e=A.sqrt(A.pow(n-r,2)+A.pow(o-s,2)+A.pow(h-a,2)),-1e-4<(i=t<l?e<l?l-t-e:e-t-l:t<e?e-t-l:t-l-e)&&i<1e-4)}}}var bez=bezFunction(),initFrame=initialDefaultFrame,mathAbs=Math.abs;function interpolateValue(t,e){for(var i,r,s,a,n=this.offsetTime,o=("multidimensional"===this.propType&&(i=createTypedArray("float32",this.pv.length)),e.lastIndex),h=o,l=this.keyframes.length-1,p=!0;p;){if(r=this.keyframes[h],s=this.keyframes[h+1],h===l-1&&t>=s.t-n){r.h&&(r=s),o=0;break}if(s.t-n>t){o=h;break}h<l-1?h+=1:(o=0,p=!1)}var m=this.keyframesMetadata[h]||{},f=s.t-n,c=r.t-n;if(r.to){m.bezierData||(m.bezierData=bez.buildBezierData(r.s,s.s||r.e,r.to,r.ti));var d=m.bezierData;if(f<=t||t<c)for(var u=f<=t?d.points.length-1:0,y=d.points[u].point.length,g=0;g<y;g+=1)i[g]=d.points[u].point[g];else{m.__fnct?a=m.__fnct:(a=BezierFactory.getBezierEasing(r.o.x,r.o.y,r.i.x,r.i.y,r.n).get,m.__fnct=a);for(var v,b=a((t-c)/(f-c)),x=d.segmentLength*b,P=e.lastFrame<t&&e._lastKeyframeIndex===h?e._lastAddedLength:0,E=e.lastFrame<t&&e._lastKeyframeIndex===h?e._lastPoint:0,p=!0,S=d.points.length;p;){if(P+=d.points[E].partialLength,0==x||0===b||E===d.points.length-1){for(y=d.points[E].point.length,g=0;g<y;g+=1)i[g]=d.points[E].point[g];break}if(P<=x&&x<P+d.points[E+1].partialLength){for(v=(x-P)/d.points[E+1].partialLength,y=d.points[E].point.length,g=0;g<y;g+=1)i[g]=d.points[E].point[g]+(d.points[E+1].point[g]-d.points[E].point[g])*v;break}E<S-1?E+=1:p=!1}e._lastPoint=E,e._lastAddedLength=P-d.points[E].partialLength,e._lastKeyframeIndex=h}}else{var C,_,A,T,M,l=r.s.length,k=s.s||r.e;if(this.sh&&1!==r.h)f<=t?(i[0]=k[0],i[1]=k[1],i[2]=k[2]):t<=c?(i[0]=r.s[0],i[1]=r.s[1],i[2]=r.s[2]):quaternionToEuler(i,slerp(createQuaternion(r.s),createQuaternion(k),(t-c)/(f-c)));else for(h=0;h<l;h+=1)1!==r.h&&(b=f<=t?1:t<c?0:(r.o.x.constructor===Array?(m.__fnct||(m.__fnct=[]),m.__fnct[h]?a=m.__fnct[h]:(C=void 0===r.o.x[h]?r.o.x[0]:r.o.x[h],_=void 0===r.o.y[h]?r.o.y[0]:r.o.y[h],A=void 0===r.i.x[h]?r.i.x[0]:r.i.x[h],T=void 0===r.i.y[h]?r.i.y[0]:r.i.y[h],a=BezierFactory.getBezierEasing(C,_,A,T).get,m.__fnct[h]=a)):m.__fnct?a=m.__fnct:(C=r.o.x,_=r.o.y,A=r.i.x,T=r.i.y,a=BezierFactory.getBezierEasing(C,_,A,T).get,r.keyframeMetadata=a),a((t-c)/(f-c)))),k=s.s||r.e,M=1===r.h?r.s[h]:r.s[h]+(k[h]-r.s[h])*b,"multidimensional"===this.propType?i[h]=M:i=M}return e.lastIndex=o,i}function slerp(t,e,i){var r,s,a=[],n=t[0],o=t[1],h=t[2],t=t[3],l=e[0],p=e[1],m=e[2],e=e[3],f=n*l+o*p+h*m+t*e;return f<0&&(f=-f,l=-l,p=-p,m=-m,e=-e),f=1e-6<1-f?(f=Math.acos(f),r=Math.sin(f),s=Math.sin((1-i)*f)/r,Math.sin(i*f)/r):(s=1-i,i),a[0]=s*n+f*l,a[1]=s*o+f*p,a[2]=s*h+f*m,a[3]=s*t+f*e,a}function quaternionToEuler(t,e){var i=e[0],r=e[1],s=e[2],e=e[3],a=Math.atan2(2*r*e-2*i*s,1-2*r*r-2*s*s),n=Math.asin(2*i*r+2*s*e),e=Math.atan2(2*i*e-2*r*s,1-2*i*i-2*s*s);t[0]=a/degToRads,t[1]=n/degToRads,t[2]=e/degToRads}function createQuaternion(t){var e=t[0]*degToRads,i=t[1]*degToRads,t=t[2]*degToRads,r=Math.cos(e/2),s=Math.cos(i/2),a=Math.cos(t/2),e=Math.sin(e/2),i=Math.sin(i/2),t=Math.sin(t/2);return[e*i*a+r*s*t,e*s*a+r*i*t,r*i*a-e*s*t,r*s*a-e*i*t]}function getValueAtCurrentTime(){var t=this.comp.renderedFrame-this.offsetTime,e=this.keyframes[0].t-this.offsetTime,i=this.keyframes[this.keyframes.length-1].t-this.offsetTime;return t===this._caching.lastFrame||this._caching.lastFrame!==initFrame&&(this._caching.lastFrame>=i&&i<=t||this._caching.lastFrame<e&&t<e)||(this._caching.lastFrame>=t&&(this._caching._lastKeyframeIndex=-1,this._caching.lastIndex=0),i=this.interpolateValue(t,this._caching),this.pv=i),this._caching.lastFrame=t,this.pv}function setVValue(t){var e;if("unidimensional"===this.propType)e=t*this.mult,1e-5<mathAbs(this.v-e)&&(this.v=e,this._mdf=!0);else for(var i=0,r=this.v.length;i<r;)e=t[i]*this.mult,1e-5<mathAbs(this.v[i]-e)&&(this.v[i]=e,this._mdf=!0),i+=1}function processEffectsSequence(){if(this.elem.globalData.frameId!==this.frameId&&this.effectsSequence.length)if(this.lock)this.setVValue(this.pv);else{this.lock=!0,this._mdf=this._isFirstFrame;for(var t=this.effectsSequence.length,e=this.kf?this.pv:this.data.k,i=0;i<t;i+=1)e=this.effectsSequence[i](e);this.setVValue(e),this._isFirstFrame=!1,this.lock=!1,this.frameId=this.elem.globalData.frameId}}function addEffect(t){this.effectsSequence.push(t),this.container.addDynamicProperty(this)}function ValueProperty(t,e,i,r){this.propType="unidimensional",this.mult=i||1,this.data=e,this.v=i?e.k*i:e.k,this.pv=e.k,this._mdf=!1,this.elem=t,this.container=r,this.comp=t.comp,this.k=!1,this.kf=!1,this.vel=0,this.effectsSequence=[],this._isFirstFrame=!0,this.getValue=processEffectsSequence,this.setVValue=setVValue,this.addEffect=addEffect}function MultiDimensionalProperty(t,e,i,r){this.propType="multidimensional",this.mult=i||1,this.data=e,this._mdf=!1,this.elem=t,this.container=r,this.comp=t.comp,this.k=!1,this.kf=!1,this.frameId=-1;var s,a=e.k.length;for(this.v=createTypedArray("float32",a),this.pv=createTypedArray("float32",a),this.vel=createTypedArray("float32",a),s=0;s<a;s+=1)this.v[s]=e.k[s]*this.mult,this.pv[s]=e.k[s];this._isFirstFrame=!0,this.effectsSequence=[],this.getValue=processEffectsSequence,this.setVValue=setVValue,this.addEffect=addEffect}function KeyframedValueProperty(t,e,i,r){this.propType="unidimensional",this.keyframes=e.k,this.keyframesMetadata=[],this.offsetTime=t.data.st,this.frameId=-1,this._caching={lastFrame:initFrame,lastIndex:0,value:0,_lastKeyframeIndex:-1},this.k=!0,this.kf=!0,this.data=e,this.mult=i||1,this.elem=t,this.container=r,this.comp=t.comp,this.v=initFrame,this.pv=initFrame,this._isFirstFrame=!0,this.getValue=processEffectsSequence,this.setVValue=setVValue,this.interpolateValue=interpolateValue,this.effectsSequence=[getValueAtCurrentTime.bind(this)],this.addEffect=addEffect}function KeyframedMultidimensionalProperty(t,e,i,r){this.propType="multidimensional";for(var s,a,n,o,h=e.k.length,l=0;l<h-1;l+=1)e.k[l].to&&e.k[l].s&&e.k[l+1]&&e.k[l+1].s&&(s=e.k[l].s,a=e.k[l+1].s,n=e.k[l].to,o=e.k[l].ti,(2===s.length&&(s[0]!==a[0]||s[1]!==a[1])&&bez.pointOnLine2D(s[0],s[1],a[0],a[1],s[0]+n[0],s[1]+n[1])&&bez.pointOnLine2D(s[0],s[1],a[0],a[1],a[0]+o[0],a[1]+o[1])||3===s.length&&(s[0]!==a[0]||s[1]!==a[1]||s[2]!==a[2])&&bez.pointOnLine3D(s[0],s[1],s[2],a[0],a[1],a[2],s[0]+n[0],s[1]+n[1],s[2]+n[2])&&bez.pointOnLine3D(s[0],s[1],s[2],a[0],a[1],a[2],a[0]+o[0],a[1]+o[1],a[2]+o[2]))&&(e.k[l].to=null,e.k[l].ti=null),s[0]===a[0])&&s[1]===a[1]&&0===n[0]&&0===n[1]&&0===o[0]&&0===o[1]&&(2===s.length||s[2]===a[2]&&0===n[2]&&0===o[2])&&(e.k[l].to=null,e.k[l].ti=null);this.effectsSequence=[getValueAtCurrentTime.bind(this)],this.data=e,this.keyframes=e.k,this.keyframesMetadata=[],this.offsetTime=t.data.st,this.k=!0,this.kf=!0,this._isFirstFrame=!0,this.mult=i||1,this.elem=t,this.container=r,this.comp=t.comp,this.getValue=processEffectsSequence,this.setVValue=setVValue,this.interpolateValue=interpolateValue,this.frameId=-1;var p=e.k[0].s.length;for(this.v=createTypedArray("float32",p),this.pv=createTypedArray("float32",p),l=0;l<p;l+=1)this.v[l]=initFrame,this.pv[l]=initFrame;this._caching={lastFrame:initFrame,lastIndex:0,value:createTypedArray("float32",p)},this.addEffect=addEffect}var PropertyFactory={getProp:function(t,e,i,r,s){var a;if((e=e.sid?t.globalData.slotManager.getProp(e):e).k.length)if("number"==typeof e.k[0])a=new MultiDimensionalProperty(t,e,r,s);else switch(i){case 0:a=new KeyframedValueProperty(t,e,r,s);break;case 1:a=new KeyframedMultidimensionalProperty(t,e,r,s)}else a=new ValueProperty(t,e,r,s);return a.effectsSequence.length&&s.addDynamicProperty(a),a}};function DynamicPropertyContainer(){}DynamicPropertyContainer.prototype={addDynamicProperty:function(t){-1===this.dynamicProperties.indexOf(t)&&(this.dynamicProperties.push(t),this.container.addDynamicProperty(this),this._isAnimated=!0)},iterateDynamicProperties:function(){this._mdf=!1;for(var t=this.dynamicProperties.length,e=0;e<t;e+=1)this.dynamicProperties[e].getValue(),this.dynamicProperties[e]._mdf&&(this._mdf=!0)},initDynamicPropertyContainer:function(t){this.container=t,this.dynamicProperties=[],this._mdf=!1,this._isAnimated=!1}};var pointPool=poolFactory(8,function(){return createTypedArray("float32",2)});function ShapePath(){this.c=!1,this._length=0,this._maxLength=8,this.v=createSizedArray(this._maxLength),this.o=createSizedArray(this._maxLength),this.i=createSizedArray(this._maxLength)}ShapePath.prototype.setPathData=function(t,e){this.c=t,this.setLength(e);for(var i=0;i<e;)this.v[i]=pointPool.newElement(),this.o[i]=pointPool.newElement(),this.i[i]=pointPool.newElement(),i+=1},ShapePath.prototype.setLength=function(t){for(;this._maxLength<t;)this.doubleArrayLength();this._length=t},ShapePath.prototype.doubleArrayLength=function(){this.v=this.v.concat(createSizedArray(this._maxLength)),this.i=this.i.concat(createSizedArray(this._maxLength)),this.o=this.o.concat(createSizedArray(this._maxLength)),this._maxLength*=2},ShapePath.prototype.setXYAt=function(t,e,i,r,s){var a;switch(this._length=Math.max(this._length,r+1),this._length>=this._maxLength&&this.doubleArrayLength(),i){case"v":a=this.v;break;case"i":a=this.i;break;case"o":a=this.o;break;default:a=[]}a[r]&&(a[r],s)||(a[r]=pointPool.newElement()),a[r][0]=t,a[r][1]=e},ShapePath.prototype.setTripleAt=function(t,e,i,r,s,a,n,o){this.setXYAt(t,e,"v",n,o),this.setXYAt(i,r,"o",n,o),this.setXYAt(s,a,"i",n,o)},ShapePath.prototype.reverse=function(){for(var t=new ShapePath,e=(t.setPathData(this.c,this._length),this.v),i=this.o,r=this.i,s=0,a=(this.c&&(t.setTripleAt(e[0][0],e[0][1],r[0][0],r[0][1],i[0][0],i[0][1],0,!1),s=1),this._length-1),n=this._length,o=s;o<n;o+=1)t.setTripleAt(e[a][0],e[a][1],r[a][0],r[a][1],i[a][0],i[a][1],o,!1),--a;return t},ShapePath.prototype.length=function(){return this._length};var shapePool=(()=>{var s=poolFactory(4,function(){return new ShapePath},function(t){for(var e=t._length,i=0;i<e;i+=1)pointPool.release(t.v[i]),pointPool.release(t.i[i]),pointPool.release(t.o[i]),t.v[i]=null,t.i[i]=null,t.o[i]=null;t._length=0,t.c=!1});return s.clone=function(t){var e,i=s.newElement(),r=void 0===t._length?t.v.length:t._length;for(i.setLength(r),i.c=t.c,e=0;e<r;e+=1)i.setTripleAt(t.v[e][0],t.v[e][1],t.o[e][0],t.o[e][1],t.i[e][0],t.i[e][1],e);return i},s})();function ShapeCollection(){this._length=0,this._maxLength=4,this.shapes=createSizedArray(this._maxLength)}ShapeCollection.prototype.addShape=function(t){this._length===this._maxLength&&(this.shapes=this.shapes.concat(createSizedArray(this._maxLength)),this._maxLength*=2),this.shapes[this._length]=t,this._length+=1},ShapeCollection.prototype.releaseShapes=function(){for(var t=0;t<this._length;t+=1)shapePool.release(this.shapes[t]);this._length=0};var shapeCollectionPool=(()=>{var t={newShapeCollection:function(){var t;t=r?a[--r]:new ShapeCollection;return t},release:function(t){var e,i=t._length;for(e=0;e<i;e+=1)shapePool.release(t.shapes[e]);t._length=0,r===s&&(a=pooling.double(a),s*=2);a[r]=t,r+=1}},r=0,s=4,a=createSizedArray(s);return t})(),ShapePropertyFactory=(()=>{var s=-999999;function t(t,e,i){var r,s,a,n,o,h,l,p=i.lastIndex,m=this.keyframes;if(t<m[0].t-this.offsetTime)r=m[0].s[0],s=!0,p=0;else if(t>=m[m.length-1].t-this.offsetTime)r=(m[m.length-1].s?m[m.length-1].s:m[m.length-2].e)[0],s=!0;else{for(var f,c,d,u,y,g,v=p,b=m.length-1,x=!0;x&&(f=m[v],!((c=m[v+1]).t-this.offsetTime>t));)v<b-1?v+=1:x=!1;d=this.keyframesMetadata[v]||{},p=v,(s=1===f.h)||(y=t>=c.t-this.offsetTime?1:t<f.t-this.offsetTime?0:(d.__fnct?u=d.__fnct:(u=BezierFactory.getBezierEasing(f.o.x,f.o.y,f.i.x,f.i.y).get,d.__fnct=u),u((t-(f.t-this.offsetTime))/(c.t-this.offsetTime-(f.t-this.offsetTime)))),g=(c.s||f.e)[0]),r=f.s[0]}for(o=e._length,h=r.i[0].length,i.lastIndex=p,a=0;a<o;a+=1)for(n=0;n<h;n+=1)l=s?r.i[a][n]:r.i[a][n]+(g.i[a][n]-r.i[a][n])*y,e.i[a][n]=l,l=s?r.o[a][n]:r.o[a][n]+(g.o[a][n]-r.o[a][n])*y,e.o[a][n]=l,l=s?r.v[a][n]:r.v[a][n]+(g.v[a][n]-r.v[a][n])*y,e.v[a][n]=l}function r(){this.paths=this.localShapeCollection}function e(t){((t,e)=>{if(t._length===e._length&&t.c===e.c){for(var i=t._length,r=0;r<i;r+=1)if(t.v[r][0]!==e.v[r][0]||t.v[r][1]!==e.v[r][1]||t.o[r][0]!==e.o[r][0]||t.o[r][1]!==e.o[r][1]||t.i[r][0]!==e.i[r][0]||t.i[r][1]!==e.i[r][1])return;return 1}})(this.v,t)||(this.v=shapePool.clone(t),this.localShapeCollection.releaseShapes(),this.localShapeCollection.addShape(this.v),this._mdf=!0,this.paths=this.localShapeCollection)}function i(){if(this.elem.globalData.frameId!==this.frameId)if(this.effectsSequence.length)if(this.lock)this.setVValue(this.pv);else{this.lock=!0,this._mdf=!1,t=this.kf?this.pv:(this.data.ks||this.data.pt).k;for(var t,e=this.effectsSequence.length,i=0;i<e;i+=1)t=this.effectsSequence[i](t);this.setVValue(t),this.lock=!1,this.frameId=this.elem.globalData.frameId}else this._mdf=!1}function a(t,e,i){this.propType="shape",this.comp=t.comp,this.container=t,this.elem=t,this.data=e,this.k=!1,this.kf=!1,this._mdf=!1;t=(3===i?e.pt:e.ks).k;this.v=shapePool.clone(t),this.pv=shapePool.clone(this.v),this.localShapeCollection=shapeCollectionPool.newShapeCollection(),this.paths=this.localShapeCollection,this.paths.addShape(this.v),this.reset=r,this.effectsSequence=[]}function n(t){this.effectsSequence.push(t),this.container.addDynamicProperty(this)}function o(t,e,i){this.propType="shape",this.comp=t.comp,this.elem=t,this.container=t,this.offsetTime=t.data.st,this.keyframes=(3===i?e.pt:e.ks).k,this.keyframesMetadata=[],this.k=!0,this.kf=!0;t=this.keyframes[0].s[0].i.length;this.v=shapePool.newElement(),this.v.setPathData(this.keyframes[0].s[0].c,t),this.pv=shapePool.clone(this.v),this.localShapeCollection=shapeCollectionPool.newShapeCollection(),this.paths=this.localShapeCollection,this.paths.addShape(this.v),this.lastFrame=s,this.reset=r,this._caching={lastFrame:s,lastIndex:0},this.effectsSequence=[function(){var t=this.comp.renderedFrame-this.offsetTime,e=this.keyframes[0].t-this.offsetTime,i=this.keyframes[this.keyframes.length-1].t-this.offsetTime,r=this._caching.lastFrame;return r!==s&&(r<e&&t<e||i<r&&i<t)||(this._caching.lastIndex=r<t?this._caching.lastIndex:0,this.interpolateShape(t,this.pv,this._caching)),this._caching.lastFrame=t,this.pv}.bind(this)]}a.prototype.interpolateShape=t,a.prototype.getValue=i,a.prototype.setVValue=e,a.prototype.addEffect=n,o.prototype.getValue=i,o.prototype.interpolateShape=t,o.prototype.setVValue=e,o.prototype.addEffect=n,h=roundCorner,p.prototype={reset:r,getValue:function(){this.elem.globalData.frameId!==this.frameId&&(this.frameId=this.elem.globalData.frameId,this.iterateDynamicProperties(),this._mdf)&&this.convertEllToPath()},convertEllToPath:function(){var t=this.p.v[0],e=this.p.v[1],i=this.s.v[0]/2,r=this.s.v[1]/2,s=3!==this.d,a=this.v;a.v[0][0]=t,a.v[0][1]=e-r,a.v[1][0]=s?t+i:t-i,a.v[1][1]=e,a.v[2][0]=t,a.v[2][1]=e+r,a.v[3][0]=s?t-i:t+i,a.v[3][1]=e,a.i[0][0]=s?t-i*h:t+i*h,a.i[0][1]=e-r,a.i[1][0]=s?t+i:t-i,a.i[1][1]=e-r*h,a.i[2][0]=s?t+i*h:t-i*h,a.i[2][1]=e+r,a.i[3][0]=s?t-i:t+i,a.i[3][1]=e+r*h,a.o[0][0]=s?t+i*h:t-i*h,a.o[0][1]=e-r,a.o[1][0]=s?t+i:t-i,a.o[1][1]=e+r*h,a.o[2][0]=s?t-i*h:t+i*h,a.o[2][1]=e+r,a.o[3][0]=s?t-i:t+i,a.o[3][1]=e-r*h}},extendPrototype([DynamicPropertyContainer],p);var h,l=p;function p(t,e){this.v=shapePool.newElement(),this.v.setPathData(!0,4),this.localShapeCollection=shapeCollectionPool.newShapeCollection(),this.paths=this.localShapeCollection,this.localShapeCollection.addShape(this.v),this.d=e.d,this.elem=t,this.comp=t.comp,this.frameId=-1,this.initDynamicPropertyContainer(t),this.p=PropertyFactory.getProp(t,e.p,1,0,this),this.s=PropertyFactory.getProp(t,e.s,1,0,this),this.dynamicProperties.length?this.k=!0:(this.k=!1,this.convertEllToPath())}f.prototype={reset:r,getValue:function(){this.elem.globalData.frameId!==this.frameId&&(this.frameId=this.elem.globalData.frameId,this.iterateDynamicProperties(),this._mdf)&&this.convertToPath()},convertStarToPath:function(){for(var t=2*Math.floor(this.pt.v),e=2*Math.PI/t,i=!0,r=this.or.v,s=this.ir.v,a=this.os.v,n=this.is.v,o=2*Math.PI*r/(2*t),h=2*Math.PI*s/(2*t),l=-Math.PI/2,p=(l+=this.r.v,3===this.data.d?-1:1),m=this.v._length=0;m<t;m+=1){var f=i?a:n,c=i?o:h,d=(u=i?r:s)*Math.cos(l),u=u*Math.sin(l),y=0==d&&0==u?0:u/Math.sqrt(d*d+u*u),g=0==d&&0==u?0:-d/Math.sqrt(d*d+u*u);d+=+this.p.v[0],u+=+this.p.v[1],this.v.setTripleAt(d,u,d-y*c*f*p,u-g*c*f*p,d+y*c*f*p,u+g*c*f*p,m,!0),i=!i,l+=e*p}},convertPolygonToPath:function(){var t,e=Math.floor(this.pt.v),i=2*Math.PI/e,r=this.or.v,s=this.os.v,a=2*Math.PI*r/(4*e),n=.5*-Math.PI,o=3===this.data.d?-1:1;for(n+=this.r.v,t=this.v._length=0;t<e;t+=1){var h=r*Math.cos(n),l=r*Math.sin(n),p=0==h&&0==l?0:l/Math.sqrt(h*h+l*l),m=0==h&&0==l?0:-h/Math.sqrt(h*h+l*l);h+=+this.p.v[0],l+=+this.p.v[1],this.v.setTripleAt(h,l,h-p*a*s*o,l-m*a*s*o,h+p*a*s*o,l+m*a*s*o,t,!0),n+=i*o}this.paths.length=0,this.paths[0]=this.v}},extendPrototype([DynamicPropertyContainer],f);var m=f;function f(t,e){this.v=shapePool.newElement(),this.v.setPathData(!0,0),this.elem=t,this.comp=t.comp,this.data=e,this.frameId=-1,this.d=e.d,this.initDynamicPropertyContainer(t),1===e.sy?(this.ir=PropertyFactory.getProp(t,e.ir,0,0,this),this.is=PropertyFactory.getProp(t,e.is,0,.01,this),this.convertToPath=this.convertStarToPath):this.convertToPath=this.convertPolygonToPath,this.pt=PropertyFactory.getProp(t,e.pt,0,0,this),this.p=PropertyFactory.getProp(t,e.p,1,0,this),this.r=PropertyFactory.getProp(t,e.r,0,degToRads,this),this.or=PropertyFactory.getProp(t,e.or,0,0,this),this.os=PropertyFactory.getProp(t,e.os,0,.01,this),this.localShapeCollection=shapeCollectionPool.newShapeCollection(),this.localShapeCollection.addShape(this.v),this.paths=this.localShapeCollection,this.dynamicProperties.length?this.k=!0:(this.k=!1,this.convertToPath())}d.prototype={convertRectToPath:function(){var t=this.p.v[0],e=this.p.v[1],i=this.s.v[0]/2,r=this.s.v[1]/2,s=bmMin(i,r,this.r.v),a=s*(1-roundCorner);this.v._length=0,2===this.d||1===this.d?(this.v.setTripleAt(t+i,e-r+s,t+i,e-r+s,t+i,e-r+a,0,!0),this.v.setTripleAt(t+i,e+r-s,t+i,e+r-a,t+i,e+r-s,1,!0),0!==s?(this.v.setTripleAt(t+i-s,e+r,t+i-s,e+r,t+i-a,e+r,2,!0),this.v.setTripleAt(t-i+s,e+r,t-i+a,e+r,t-i+s,e+r,3,!0),this.v.setTripleAt(t-i,e+r-s,t-i,e+r-s,t-i,e+r-a,4,!0),this.v.setTripleAt(t-i,e-r+s,t-i,e-r+a,t-i,e-r+s,5,!0),this.v.setTripleAt(t-i+s,e-r,t-i+s,e-r,t-i+a,e-r,6,!0),this.v.setTripleAt(t+i-s,e-r,t+i-a,e-r,t+i-s,e-r,7,!0)):(this.v.setTripleAt(t-i,e+r,t-i+a,e+r,t-i,e+r,2),this.v.setTripleAt(t-i,e-r,t-i,e-r+a,t-i,e-r,3))):(this.v.setTripleAt(t+i,e-r+s,t+i,e-r+a,t+i,e-r+s,0,!0),0!==s?(this.v.setTripleAt(t+i-s,e-r,t+i-s,e-r,t+i-a,e-r,1,!0),this.v.setTripleAt(t-i+s,e-r,t-i+a,e-r,t-i+s,e-r,2,!0),this.v.setTripleAt(t-i,e-r+s,t-i,e-r+s,t-i,e-r+a,3,!0),this.v.setTripleAt(t-i,e+r-s,t-i,e+r-a,t-i,e+r-s,4,!0),this.v.setTripleAt(t-i+s,e+r,t-i+s,e+r,t-i+a,e+r,5,!0),this.v.setTripleAt(t+i-s,e+r,t+i-a,e+r,t+i-s,e+r,6,!0),this.v.setTripleAt(t+i,e+r-s,t+i,e+r-s,t+i,e+r-a,7,!0)):(this.v.setTripleAt(t-i,e-r,t-i+a,e-r,t-i,e-r,1,!0),this.v.setTripleAt(t-i,e+r,t-i,e+r-a,t-i,e+r,2,!0),this.v.setTripleAt(t+i,e+r,t+i-a,e+r,t+i,e+r,3,!0)))},getValue:function(){this.elem.globalData.frameId!==this.frameId&&(this.frameId=this.elem.globalData.frameId,this.iterateDynamicProperties(),this._mdf)&&this.convertRectToPath()},reset:r},extendPrototype([DynamicPropertyContainer],d);var c=d;function d(t,e){this.v=shapePool.newElement(),this.v.c=!0,this.localShapeCollection=shapeCollectionPool.newShapeCollection(),this.localShapeCollection.addShape(this.v),this.paths=this.localShapeCollection,this.elem=t,this.comp=t.comp,this.frameId=-1,this.d=e.d,this.initDynamicPropertyContainer(t),this.p=PropertyFactory.getProp(t,e.p,1,0,this),this.s=PropertyFactory.getProp(t,e.s,1,0,this),this.r=PropertyFactory.getProp(t,e.r,0,0,this),this.dynamicProperties.length?this.k=!0:(this.k=!1,this.convertRectToPath())}var u={getShapeProp:function(t,e,i){var r;return 3===i||4===i?r=new((3===i?e.pt:e.ks).k.length?o:a)(t,e,i):5===i?r=new c(t,e):6===i?r=new l(t,e):7===i&&(r=new m(t,e)),r.k&&t.addDynamicProperty(r),r},getConstructorFunction:function(){return a},getKeyframedConstructorFunction:function(){return o}};return u})(),Matrix=(()=>{var r=Math.cos,s=Math.sin,a=Math.tan,n=Math.round;function t(){return this.props[0]=1,this.props[1]=0,this.props[2]=0,this.props[3]=0,this.props[4]=0,this.props[5]=1,this.props[6]=0,this.props[7]=0,this.props[8]=0,this.props[9]=0,this.props[10]=1,this.props[11]=0,this.props[12]=0,this.props[13]=0,this.props[14]=0,this.props[15]=1,this}function e(t){var e;return 0===t?this:(e=r(t),t=s(t),this._t(e,-t,0,0,t,e,0,0,0,0,1,0,0,0,0,1))}function i(t){var e;return 0===t?this:(e=r(t),t=s(t),this._t(1,0,0,0,0,e,-t,0,0,t,e,0,0,0,0,1))}function o(t){var e;return 0===t?this:(e=r(t),t=s(t),this._t(e,0,t,0,0,1,0,0,-t,0,e,0,0,0,0,1))}function h(t){var e;return 0===t?this:(e=r(t),t=s(t),this._t(e,-t,0,0,t,e,0,0,0,0,1,0,0,0,0,1))}function l(t,e){return this._t(1,e,t,1,0,0)}function p(t,e){return this.shear(a(t),a(e))}function m(t,e){var i=r(e),e=s(e);return this._t(i,e,0,0,-e,i,0,0,0,0,1,0,0,0,0,1)._t(1,0,0,0,a(t),1,0,0,0,0,1,0,0,0,0,1)._t(i,-e,0,0,e,i,0,0,0,0,1,0,0,0,0,1)}function f(t,e,i){return i||0===i||(i=1),1===t&&1===e&&1===i?this:this._t(t,0,0,0,0,e,0,0,0,0,i,0,0,0,0,1)}function c(t,e,i,r,s,a,n,o,h,l,p,m,f,c,d,u){return this.props[0]=t,this.props[1]=e,this.props[2]=i,this.props[3]=r,this.props[4]=s,this.props[5]=a,this.props[6]=n,this.props[7]=o,this.props[8]=h,this.props[9]=l,this.props[10]=p,this.props[11]=m,this.props[12]=f,this.props[13]=c,this.props[14]=d,this.props[15]=u,this}function d(t,e,i){return i=i||0,0!==t||0!==e||0!==i?this._t(1,0,0,0,0,1,0,0,0,0,1,0,t,e,i,1):this}function u(t,e,i,r,s,a,n,o,h,l,p,m,f,c,d,u){var y,g,v,b,x,P,E,S,C,_,A,T,M,k,D,F,w=this.props;return 1===t&&0===e&&0===i&&0===r&&0===s&&1===a&&0===n&&0===o&&0===h&&0===l&&1===p&&0===m?(w[12]=w[12]*t+w[15]*f,w[13]=w[13]*a+w[15]*c,w[14]=w[14]*p+w[15]*d,w[15]*=u,this._identityCalculated=!1):(y=w[0],x=w[4],P=w[5],E=w[6],S=w[7],C=w[8],_=w[9],A=w[10],T=w[11],M=w[12],k=w[13],D=w[14],F=w[15],w[0]=y*t+(g=w[1])*s+(v=w[2])*h+(b=w[3])*f,w[1]=y*e+g*a+v*l+b*c,w[2]=y*i+g*n+v*p+b*d,w[3]=y*r+g*o+v*m+b*u,w[4]=x*t+P*s+E*h+S*f,w[5]=x*e+P*a+E*l+S*c,w[6]=x*i+P*n+E*p+S*d,w[7]=x*r+P*o+E*m+S*u,w[8]=C*t+_*s+A*h+T*f,w[9]=C*e+_*a+A*l+T*c,w[10]=C*i+_*n+A*p+T*d,w[11]=C*r+_*o+A*m+T*u,w[12]=M*t+k*s+D*h+F*f,w[13]=M*e+k*a+D*l+F*c,w[14]=M*i+k*n+D*p+F*d,w[15]=M*r+k*o+D*m+F*u,this._identityCalculated=!1),this}function y(t){t=t.props;return this.transform(t[0],t[1],t[2],t[3],t[4],t[5],t[6],t[7],t[8],t[9],t[10],t[11],t[12],t[13],t[14],t[15])}function g(){return this._identityCalculated||(this._identity=!(1!==this.props[0]||0!==this.props[1]||0!==this.props[2]||0!==this.props[3]||0!==this.props[4]||1!==this.props[5]||0!==this.props[6]||0!==this.props[7]||0!==this.props[8]||0!==this.props[9]||1!==this.props[10]||0!==this.props[11]||0!==this.props[12]||0!==this.props[13]||0!==this.props[14]||1!==this.props[15]),this._identityCalculated=!0),this._identity}function v(t){for(var e=0;e<16;){if(t.props[e]!==this.props[e])return!1;e+=1}return!0}function b(t){for(var e=0;e<16;e+=1)t.props[e]=this.props[e];return t}function x(t){for(var e=0;e<16;e+=1)this.props[e]=t[e]}function P(t,e,i){return{x:t*this.props[0]+e*this.props[4]+i*this.props[8]+this.props[12],y:t*this.props[1]+e*this.props[5]+i*this.props[9]+this.props[13],z:t*this.props[2]+e*this.props[6]+i*this.props[10]+this.props[14]}}function E(t,e,i){return t*this.props[0]+e*this.props[4]+i*this.props[8]+this.props[12]}function S(t,e,i){return t*this.props[1]+e*this.props[5]+i*this.props[9]+this.props[13]}function C(t,e,i){return t*this.props[2]+e*this.props[6]+i*this.props[10]+this.props[14]}function _(){var t=this.props[0]*this.props[5]-this.props[1]*this.props[4],e=this.props[5]/t,i=-this.props[1]/t,r=-this.props[4]/t,s=this.props[0]/t,a=(this.props[4]*this.props[13]-this.props[5]*this.props[12])/t,t=-(this.props[0]*this.props[13]-this.props[1]*this.props[12])/t,n=new Matrix;return n.props[0]=e,n.props[1]=i,n.props[4]=r,n.props[5]=s,n.props[12]=a,n.props[13]=t,n}function A(t){return this.getInverseMatrix().applyToPointArray(t[0],t[1],t[2]||0)}function T(t){for(var e=t.length,i=[],r=0;r<e;r+=1)i[r]=A(t[r]);return i}function M(t,e,i){var r,s,a,n,o,h,l=createTypedArray("float32",6);return this.isIdentity()?(l[0]=t[0],l[1]=t[1],l[2]=e[0],l[3]=e[1],l[4]=i[0],l[5]=i[1]):(r=this.props[0],s=this.props[1],a=this.props[4],n=this.props[5],o=this.props[12],h=this.props[13],l[0]=t[0]*r+t[1]*a+o,l[1]=t[0]*s+t[1]*n+h,l[2]=e[0]*r+e[1]*a+o,l[3]=e[0]*s+e[1]*n+h,l[4]=i[0]*r+i[1]*a+o,l[5]=i[0]*s+i[1]*n+h),l}function k(t,e,i){t=this.isIdentity()?[t,e,i]:[t*this.props[0]+e*this.props[4]+i*this.props[8]+this.props[12],t*this.props[1]+e*this.props[5]+i*this.props[9]+this.props[13],t*this.props[2]+e*this.props[6]+i*this.props[10]+this.props[14]];return t}function D(t,e){var i;return this.isIdentity()?t+","+e:(i=this.props,Math.round(100*(t*i[0]+e*i[4]+i[12]))/100+","+Math.round(100*(t*i[1]+e*i[5]+i[13]))/100)}function F(){for(var t=0,e=this.props,i="matrix3d(";t<16;)i=i+n(1e4*e[t])/1e4+(15===t?")":","),t+=1;return i}function w(t){return t<1e-6&&0<t||-1e-6<t&&t<0?n(1e4*t)/1e4:t}function I(){var t=this.props;return"matrix("+w(t[0])+","+w(t[1])+","+w(t[4])+","+w(t[5])+","+w(t[12])+","+w(t[13])+")"}return function(){this.reset=t,this.rotate=e,this.rotateX=i,this.rotateY=o,this.rotateZ=h,this.skew=p,this.skewFromAxis=m,this.shear=l,this.scale=f,this.setTransform=c,this.translate=d,this.transform=u,this.multiply=y,this.applyToPoint=P,this.applyToX=E,this.applyToY=S,this.applyToZ=C,this.applyToPointArray=k,this.applyToTriplePoints=M,this.applyToPointStringified=D,this.toCSS=F,this.to2dCSS=I,this.clone=b,this.cloneFromProps=x,this.equals=v,this.inversePoints=T,this.inversePoint=A,this.getInverseMatrix=_,this._t=this.transform,this.isIdentity=g,this._identity=!0,this._identityCalculated=!1,this.props=createTypedArray("float32",16),this.reset()}})();function _typeof$3(t){return(_typeof$3="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var lottie={},standalone="__[STANDALONE]__",animationData="__[ANIMATIONDATA]__",renderer="";function setLocation(t){setLocationHref(t)}function searchAnimations(){!0===standalone?animationManager.searchAnimations(animationData,standalone,renderer):animationManager.searchAnimations()}function setSubframeRendering(t){setSubframeEnabled(t)}function setPrefix(t){setIdPrefix(t)}function loadAnimation(t){return!0===standalone&&(t.animationData=JSON.parse(animationData)),animationManager.loadAnimation(t)}function setQuality(t){if("string"==typeof t)switch(t){case"high":setDefaultCurveSegments(200);break;default:case"medium":setDefaultCurveSegments(50);break;case"low":setDefaultCurveSegments(10)}else!isNaN(t)&&1<t&&setDefaultCurveSegments(t);50<=getDefaultCurveSegments()?roundValues(!1):roundValues(!0)}function inBrowser(){return"undefined"!=typeof navigator}function installPlugin(t,e){"expressions"===t&&setExpressionsPlugin(e)}function getFactory(t){switch(t){case"propertyFactory":return PropertyFactory;case"shapePropertyFactory":return ShapePropertyFactory;case"matrix":return Matrix;default:return null}}function checkReady(){"complete"===document.readyState&&(clearInterval(readyStateCheckInterval),searchAnimations())}function getQueryVariable(t){for(var e=queryString.split("&"),i=0;i<e.length;i+=1){var r=e[i].split("=");if(decodeURIComponent(r[0])==t)return decodeURIComponent(r[1])}return null}lottie.play=animationManager.play,lottie.pause=animationManager.pause,lottie.setLocationHref=setLocation,lottie.togglePause=animationManager.togglePause,lottie.setSpeed=animationManager.setSpeed,lottie.setDirection=animationManager.setDirection,lottie.stop=animationManager.stop,lottie.searchAnimations=searchAnimations,lottie.registerAnimation=animationManager.registerAnimation,lottie.loadAnimation=loadAnimation,lottie.setSubframeRendering=setSubframeRendering,lottie.resize=animationManager.resize,lottie.goToAndStop=animationManager.goToAndStop,lottie.destroy=animationManager.destroy,lottie.setQuality=setQuality,lottie.inBrowser=inBrowser,lottie.installPlugin=installPlugin,lottie.freeze=animationManager.freeze,lottie.unfreeze=animationManager.unfreeze,lottie.setVolume=animationManager.setVolume,lottie.mute=animationManager.mute,lottie.unmute=animationManager.unmute,lottie.getRegisteredAnimations=animationManager.getRegisteredAnimations,lottie.useWebWorker=setWebWorker,lottie.setIDPrefix=setPrefix,lottie.__getFactory=getFactory,lottie.version="5.13.0";var queryString="",scripts,index,myScript,queryString,renderer,readyStateCheckInterval=(standalone&&(scripts=document.getElementsByTagName("script"),index=scripts.length-1,myScript=scripts[index]||{src:""},queryString=myScript.src?myScript.src.replace(/^[^\?]+\??/,""):"",renderer=getQueryVariable("renderer")),setInterval(checkReady,100));try{"object"===("undefined"==typeof exports?"undefined":_typeof$3(exports))&&"undefined"!=typeof module||"function"==typeof define&&define.amd||(window.bodymovin=lottie)}catch(err){}var ShapeModifiers=(()=>{var t={},r={};return t.registerModifier=function(t,e){r[t]||(r[t]=e)},t.getModifier=function(t,e,i){return new r[t](e,i)},t})();function ShapeModifier(){}function TrimModifier(){}function PuckerAndBloatModifier(){}ShapeModifier.prototype.initModifierProperties=function(){},ShapeModifier.prototype.addShapeToModifier=function(){},ShapeModifier.prototype.addShape=function(t){var e;this.closed||(t.sh.container.addDynamicProperty(t.sh),e={shape:t.sh,data:t,localShapeCollection:shapeCollectionPool.newShapeCollection()},this.shapes.push(e),this.addShapeToModifier(e),this._isAnimated&&t.setAsAnimated())},ShapeModifier.prototype.init=function(t,e){this.shapes=[],this.elem=t,this.initDynamicPropertyContainer(t),this.initModifierProperties(t,e),this.frameId=initialDefaultFrame,this.closed=!1,this.k=!1,this.dynamicProperties.length?this.k=!0:this.getValue(!0)},ShapeModifier.prototype.processKeys=function(){this.elem.globalData.frameId!==this.frameId&&(this.frameId=this.elem.globalData.frameId,this.iterateDynamicProperties())},extendPrototype([DynamicPropertyContainer],ShapeModifier),extendPrototype([ShapeModifier],TrimModifier),TrimModifier.prototype.initModifierProperties=function(t,e){this.s=PropertyFactory.getProp(t,e.s,0,.01,this),this.e=PropertyFactory.getProp(t,e.e,0,.01,this),this.o=PropertyFactory.getProp(t,e.o,0,0,this),this.sValue=0,this.eValue=0,this.getValue=this.processKeys,this.m=e.m,this._isAnimated=!!this.s.effectsSequence.length||!!this.e.effectsSequence.length||!!this.o.effectsSequence.length},TrimModifier.prototype.addShapeToModifier=function(t){t.pathsData=[]},TrimModifier.prototype.calculateShapeEdges=function(t,e,i,r,s){for(var a,n,o=[],h=(e<=1?o.push({s:t,e:e}):1<=t?o.push({s:t-1,e:e-1}):(o.push({s:t,e:1}),o.push({s:0,e:e-1})),[]),l=o.length,p=0;p<l;p+=1)(n=o[p]).e*s<r||n.s*s>r+i||(a=n.s*s<=r?0:(n.s*s-r)/i,n=n.e*s>=r+i?1:(n.e*s-r)/i,h.push([a,n]));return h.length||h.push([0,0]),h},TrimModifier.prototype.releasePathsData=function(t){for(var e=t.length,i=0;i<e;i+=1)segmentsLengthPool.release(t[i]);return t.length=0,t},TrimModifier.prototype.processShapes=function(t){this._mdf||t?((e=this.o.v%360/360)<0&&(e+=1),i=1<this.s.v?1+e:this.s.v<0?0+e:this.s.v+e,(r=1<this.e.v?1+e:this.e.v<0?0+e:this.e.v+e)<i&&(e=i,i=r,r=e),i=1e-4*Math.round(1e4*i),r=1e-4*Math.round(1e4*r),this.sValue=i,this.eValue=r):(i=this.sValue,r=this.eValue);var e,i,r,s,a,n,o,h,l,p=this.shapes.length,m=0;if(r===i)for(u=0;u<p;u+=1)this.shapes[u].localShapeCollection.releaseShapes(),this.shapes[u].shape._mdf=!0,this.shapes[u].shape.paths=this.shapes[u].localShapeCollection,this._mdf&&(this.shapes[u].pathsData.length=0);else if(1===r&&0===i||0===r&&1===i){if(this._mdf)for(u=0;u<p;u+=1)this.shapes[u].pathsData.length=0,this.shapes[u].shape._mdf=!0}else{for(var f,c,d=[],u=0;u<p;u+=1)if((f=this.shapes[u]).shape._mdf||this._mdf||t||2===this.m){if(n=(s=f.shape.paths)._length,l=0,!f.shape._mdf&&f.pathsData.length)l=f.totalShapeLength;else{for(o=this.releasePathsData(f.pathsData),a=0;a<n;a+=1)h=bez.getSegmentsLength(s.shapes[a]),o.push(h),l+=h.totalLength;f.totalShapeLength=l,f.pathsData=o}m+=l,f.shape._mdf=!0}else f.shape.paths=f.localShapeCollection;var y,g=i,v=r,b=0;for(u=p-1;0<=u;--u)if((f=this.shapes[u]).shape._mdf){for((c=f.localShapeCollection).releaseShapes(),2===this.m&&1<p?(y=this.calculateShapeEdges(i,r,f.totalShapeLength,b,m),b+=f.totalShapeLength):y=[[g,v]],n=y.length,a=0;a<n;a+=1){g=y[a][0],v=y[a][1],d.length=0,v<=1?d.push({s:f.totalShapeLength*g,e:f.totalShapeLength*v}):1<=g?d.push({s:f.totalShapeLength*(g-1),e:f.totalShapeLength*(v-1)}):(d.push({s:f.totalShapeLength*g,e:f.totalShapeLength}),d.push({s:0,e:f.totalShapeLength*(v-1)}));var x,P=this.addShapes(f,d[0]);d[0].s!==d[0].e&&(1<d.length&&(P=f.shape.paths.shapes[f.shape.paths._length-1].c?(x=P.pop(),this.addPaths(P,c),this.addShapes(f,d[1],x)):(this.addPaths(P,c),this.addShapes(f,d[1]))),this.addPaths(P,c))}f.shape.paths=c}}},TrimModifier.prototype.addPaths=function(t,e){for(var i=t.length,r=0;r<i;r+=1)e.addShape(t[r])},TrimModifier.prototype.addSegment=function(t,e,i,r,s,a,n){s.setXYAt(e[0],e[1],"o",a),s.setXYAt(i[0],i[1],"i",a+1),n&&s.setXYAt(t[0],t[1],"v",a),s.setXYAt(r[0],r[1],"v",a+1)},TrimModifier.prototype.addSegmentFromArray=function(t,e,i,r){e.setXYAt(t[1],t[5],"o",i),e.setXYAt(t[2],t[6],"i",i+1),r&&e.setXYAt(t[0],t[4],"v",i),e.setXYAt(t[3],t[7],"v",i+1)},TrimModifier.prototype.addShapes=function(t,e,i){var r,s,a,n,o,h,l,p,m=t.pathsData,f=t.shape.paths.shapes,c=t.shape.paths._length,d=0,u=[],y=!0,g=i?(n=i._length,i._length):(i=shapePool.newElement(),n=0);for(u.push(i),r=0;r<c;r+=1){for(o=m[r].lengths,i.c=f[r].c,a=f[r].c?o.length:o.length+1,s=1;s<a;s+=1)if(d+(p=o[s-1]).addedLength<e.s)d+=p.addedLength,i.c=!1;else{if(d>e.e){i.c=!1;break}e.s<=d&&e.e>=d+p.addedLength?(this.addSegment(f[r].v[s-1],f[r].o[s-1],f[r].i[s],f[r].v[s],i,n,y),y=!1):(h=bez.getNewSegment(f[r].v[s-1],f[r].v[s],f[r].o[s-1],f[r].i[s],(e.s-d)/p.addedLength,(e.e-d)/p.addedLength,o[s-1]),this.addSegmentFromArray(h,i,n,y),i.c=y=!1),d+=p.addedLength,n+=1}if(f[r].c&&o.length&&(p=o[s-1],d<=e.e?(l=o[s-1].addedLength,e.s<=d&&e.e>=d+l?(this.addSegment(f[r].v[s-1],f[r].o[s-1],f[r].i[0],f[r].v[0],i,n,y),y=!1):(h=bez.getNewSegment(f[r].v[s-1],f[r].v[0],f[r].o[s-1],f[r].i[0],(e.s-d)/l,(e.e-d)/l,o[s-1]),this.addSegmentFromArray(h,i,n,y),i.c=y=!1)):i.c=!1,d+=p.addedLength,n+=1),i._length&&(i.setXYAt(i.v[g][0],i.v[g][1],"i",g),i.setXYAt(i.v[i._length-1][0],i.v[i._length-1][1],"o",i._length-1)),d>e.e)break;r<c-1&&(i=shapePool.newElement(),y=!0,u.push(i),n=0)}return u},extendPrototype([ShapeModifier],PuckerAndBloatModifier),PuckerAndBloatModifier.prototype.initModifierProperties=function(t,e){this.getValue=this.processKeys,this.amount=PropertyFactory.getProp(t,e.a,0,null,this),this._isAnimated=!!this.amount.effectsSequence.length},PuckerAndBloatModifier.prototype.processPath=function(t,e){for(var i=e/100,r=[0,0],s=t._length,a=0,a=0;a<s;a+=1)r[0]+=t.v[a][0],r[1]+=t.v[a][1];r[0]/=s,r[1]/=s;var n,o,h,l,p,m,f=shapePool.newElement();for(f.c=t.c,a=0;a<s;a+=1)n=t.v[a][0]+(r[0]-t.v[a][0])*i,o=t.v[a][1]+(r[1]-t.v[a][1])*i,h=t.o[a][0]+(r[0]-t.o[a][0])*-i,l=t.o[a][1]+(r[1]-t.o[a][1])*-i,p=t.i[a][0]+(r[0]-t.i[a][0])*-i,m=t.i[a][1]+(r[1]-t.i[a][1])*-i,f.setTripleAt(n,o,h,l,p,m,a);return f},PuckerAndBloatModifier.prototype.processShapes=function(t){var e,i,r,s=this.shapes.length,a=this.amount.v;if(0!==a)for(var n,o,h=0;h<s;h+=1){if(o=(n=this.shapes[h]).localShapeCollection,n.shape._mdf||this._mdf||t)for(o.releaseShapes(),n.shape._mdf=!0,e=n.shape.paths.shapes,r=n.shape.paths._length,i=0;i<r;i+=1)o.addShape(this.processPath(e[i],a));n.shape.paths=n.localShapeCollection}this.dynamicProperties.length||(this._mdf=!1)};var TransformPropertyFactory=(()=>{var a=[0,0];function r(t,e,i){if(this.elem=t,this.frameId=-1,this.propType="transform",this.data=e,this.v=new Matrix,this.pre=new Matrix,this.appliedTransformations=0,this.initDynamicPropertyContainer(i||t),e.p&&e.p.s?(this.px=PropertyFactory.getProp(t,e.p.x,0,0,this),this.py=PropertyFactory.getProp(t,e.p.y,0,0,this),e.p.z&&(this.pz=PropertyFactory.getProp(t,e.p.z,0,0,this))):this.p=PropertyFactory.getProp(t,e.p||{k:[0,0,0]},1,0,this),e.rx){if(this.rx=PropertyFactory.getProp(t,e.rx,0,degToRads,this),this.ry=PropertyFactory.getProp(t,e.ry,0,degToRads,this),this.rz=PropertyFactory.getProp(t,e.rz,0,degToRads,this),e.or.k[0].ti)for(var r=e.or.k.length,s=0;s<r;s+=1)e.or.k[s].to=null,e.or.k[s].ti=null;this.or=PropertyFactory.getProp(t,e.or,1,degToRads,this),this.or.sh=!0}else this.r=PropertyFactory.getProp(t,e.r||{k:0},0,degToRads,this);e.sk&&(this.sk=PropertyFactory.getProp(t,e.sk,0,degToRads,this),this.sa=PropertyFactory.getProp(t,e.sa,0,degToRads,this)),this.a=PropertyFactory.getProp(t,e.a||{k:[0,0,0]},1,0,this),this.s=PropertyFactory.getProp(t,e.s||{k:[100,100,100]},1,.01,this),e.o?this.o=PropertyFactory.getProp(t,e.o,0,.01,t):this.o={_mdf:!1,v:1},this._isDirty=!0,this.dynamicProperties.length||this.getValue(!0)}return r.prototype={applyToMatrix:function(t){var e=this._mdf;this.iterateDynamicProperties(),this._mdf=this._mdf||e,this.a&&t.translate(-this.a.v[0],-this.a.v[1],this.a.v[2]),this.s&&t.scale(this.s.v[0],this.s.v[1],this.s.v[2]),this.sk&&t.skewFromAxis(-this.sk.v,this.sa.v),this.r?t.rotate(-this.r.v):t.rotateZ(-this.rz.v).rotateY(this.ry.v).rotateX(this.rx.v).rotateZ(-this.or.v[2]).rotateY(this.or.v[1]).rotateX(this.or.v[0]),this.data.p.s?this.data.p.z?t.translate(this.px.v,this.py.v,-this.pz.v):t.translate(this.px.v,this.py.v,0):t.translate(this.p.v[0],this.p.v[1],-this.p.v[2])},getValue:function(t){var e,i,r,s;this.elem.globalData.frameId!==this.frameId&&(this._isDirty&&(this.precalculateMatrix(),this._isDirty=!1),this.iterateDynamicProperties(),(this._mdf||t)&&(this.v.cloneFromProps(this.pre.props),this.appliedTransformations<1&&this.v.translate(-this.a.v[0],-this.a.v[1],this.a.v[2]),this.appliedTransformations<2&&this.v.scale(this.s.v[0],this.s.v[1],this.s.v[2]),this.sk&&this.appliedTransformations<3&&this.v.skewFromAxis(-this.sk.v,this.sa.v),this.r&&this.appliedTransformations<4?this.v.rotate(-this.r.v):!this.r&&this.appliedTransformations<4&&this.v.rotateZ(-this.rz.v).rotateY(this.ry.v).rotateX(this.rx.v).rotateZ(-this.or.v[2]).rotateY(this.or.v[1]).rotateX(this.or.v[0]),this.autoOriented&&(t=this.elem.globalData.frameRate,this.p&&this.p.keyframes&&this.p.getValueAtTime?i=this.p._caching.lastFrame+this.p.offsetTime<=this.p.keyframes[0].t?(e=this.p.getValueAtTime((this.p.keyframes[0].t+.01)/t,0),this.p.getValueAtTime(this.p.keyframes[0].t/t,0)):this.p._caching.lastFrame+this.p.offsetTime>=this.p.keyframes[this.p.keyframes.length-1].t?(e=this.p.getValueAtTime(this.p.keyframes[this.p.keyframes.length-1].t/t,0),this.p.getValueAtTime((this.p.keyframes[this.p.keyframes.length-1].t-.05)/t,0)):(e=this.p.pv,this.p.getValueAtTime((this.p._caching.lastFrame+this.p.offsetTime-.01)/t,this.p.offsetTime)):this.px&&this.px.keyframes&&this.py.keyframes&&this.px.getValueAtTime&&this.py.getValueAtTime?(e=[],i=[],r=this.px,s=this.py,r._caching.lastFrame+r.offsetTime<=r.keyframes[0].t?(e[0]=r.getValueAtTime((r.keyframes[0].t+.01)/t,0),e[1]=s.getValueAtTime((s.keyframes[0].t+.01)/t,0),i[0]=r.getValueAtTime(r.keyframes[0].t/t,0),i[1]=s.getValueAtTime(s.keyframes[0].t/t,0)):r._caching.lastFrame+r.offsetTime>=r.keyframes[r.keyframes.length-1].t?(e[0]=r.getValueAtTime(r.keyframes[r.keyframes.length-1].t/t,0),e[1]=s.getValueAtTime(s.keyframes[s.keyframes.length-1].t/t,0),i[0]=r.getValueAtTime((r.keyframes[r.keyframes.length-1].t-.01)/t,0),i[1]=s.getValueAtTime((s.keyframes[s.keyframes.length-1].t-.01)/t,0)):(e=[r.pv,s.pv],i[0]=r.getValueAtTime((r._caching.lastFrame+r.offsetTime-.01)/t,r.offsetTime),i[1]=s.getValueAtTime((s._caching.lastFrame+s.offsetTime-.01)/t,s.offsetTime))):e=i=a,this.v.rotate(-Math.atan2(e[1]-i[1],e[0]-i[0]))),this.data.p&&this.data.p.s?this.data.p.z?this.v.translate(this.px.v,this.py.v,-this.pz.v):this.v.translate(this.px.v,this.py.v,0):this.v.translate(this.p.v[0],this.p.v[1],-this.p.v[2])),this.frameId=this.elem.globalData.frameId)},precalculateMatrix:function(){if(this.appliedTransformations=0,this.pre.reset(),!this.a.effectsSequence.length&&(this.pre.translate(-this.a.v[0],-this.a.v[1],this.a.v[2]),this.appliedTransformations=1,!this.s.effectsSequence.length)){if(this.pre.scale(this.s.v[0],this.s.v[1],this.s.v[2]),this.appliedTransformations=2,this.sk){if(this.sk.effectsSequence.length||this.sa.effectsSequence.length)return;this.pre.skewFromAxis(-this.sk.v,this.sa.v),this.appliedTransformations=3}this.r?this.r.effectsSequence.length||(this.pre.rotate(-this.r.v),this.appliedTransformations=4):this.rz.effectsSequence.length||this.ry.effectsSequence.length||this.rx.effectsSequence.length||this.or.effectsSequence.length||(this.pre.rotateZ(-this.rz.v).rotateY(this.ry.v).rotateX(this.rx.v).rotateZ(-this.or.v[2]).rotateY(this.or.v[1]).rotateX(this.or.v[0]),this.appliedTransformations=4)}},autoOrient:function(){}},extendPrototype([DynamicPropertyContainer],r),r.prototype.addDynamicProperty=function(t){this._addDynamicProperty(t),this.elem.addDynamicProperty(t),this._isDirty=!0},r.prototype._addDynamicProperty=DynamicPropertyContainer.prototype.addDynamicProperty,{getTransformProperty:function(t,e,i){return new r(t,e,i)}}})();function RepeaterModifier(){}function RoundCornersModifier(){}function floatEqual(t,e){return 1e5*Math.abs(t-e)<=Math.min(Math.abs(t),Math.abs(e))}function floatZero(t){return Math.abs(t)<=1e-5}function lerp(t,e,i){return t*(1-i)+e*i}function lerpPoint(t,e,i){return[lerp(t[0],e[0],i),lerp(t[1],e[1],i)]}function quadRoots(t,e,i){return 0===t||(i=e*e-4*t*i)<0?[]:(e=-e/(2*t),0==i?[e]:[e-(i=Math.sqrt(i)/(2*t)),e+i])}function polynomialCoefficients(t,e,i,r){return[3*e-t-3*i+r,3*t-6*e+3*i,-3*t+3*e,t]}function singlePoint(t){return new PolynomialBezier(t,t,t,t,!1)}function PolynomialBezier(t,e,i,r,s){s&&pointEqual(t,e)&&(e=lerpPoint(t,r,1/3)),s&&pointEqual(i,r)&&(i=lerpPoint(t,r,2/3));var s=polynomialCoefficients(t[0],e[0],i[0],r[0]),a=polynomialCoefficients(t[1],e[1],i[1],r[1]);this.a=[s[0],a[0]],this.b=[s[1],a[1]],this.c=[s[2],a[2]],this.d=[s[3],a[3]],this.points=[t,e,i,r]}function extrema(t,e){for(var i,r,s=t.points[0][e],a=t.points[t.points.length-1][e],n=(a<s&&(i=a,a=s,s=i),quadRoots(3*t.a[e],2*t.b[e],t.c[e])),o=0;o<n.length;o+=1)0<n[o]&&n[o]<1&&((r=t.point(n[o])[e])<s?s=r:a<r&&(a=r));return{min:s,max:a}}function intersectData(t,e,i){var r=t.boundingBox();return{cx:r.cx,cy:r.cy,width:r.width,height:r.height,bez:t,t:(e+i)/2,t1:e,t2:i}}function splitData(t){var e=t.bez.split(.5);return[intersectData(e[0],t.t1,t.t),intersectData(e[1],t.t,t.t2)]}function boxIntersect(t,e){return 2*Math.abs(t.cx-e.cx)<t.width+e.width&&2*Math.abs(t.cy-e.cy)<t.height+e.height}function intersectsImpl(t,e,i,r,s,a){boxIntersect(t,e)&&(a<=i||t.width<=r&&t.height<=r&&e.width<=r&&e.height<=r?s.push([t.t,e.t]):(t=splitData(t),e=splitData(e),intersectsImpl(t[0],e[0],i+1,r,s,a),intersectsImpl(t[0],e[1],i+1,r,s,a),intersectsImpl(t[1],e[0],i+1,r,s,a),intersectsImpl(t[1],e[1],i+1,r,s,a)))}function crossProduct(t,e){return[t[1]*e[2]-t[2]*e[1],t[2]*e[0]-t[0]*e[2],t[0]*e[1]-t[1]*e[0]]}function lineIntersection(t,e,i,r){t=[t[0],t[1],1],e=[e[0],e[1],1],i=[i[0],i[1],1],r=[r[0],r[1],1],t=crossProduct(crossProduct(t,e),crossProduct(i,r));return floatZero(t[2])?null:[t[0]/t[2],t[1]/t[2]]}function polarOffset(t,e,i){return[t[0]+Math.cos(e)*i,t[1]-Math.sin(e)*i]}function pointDistance(t,e){return Math.hypot(t[0]-e[0],t[1]-e[1])}function pointEqual(t,e){return floatEqual(t[0],e[0])&&floatEqual(t[1],e[1])}function ZigZagModifier(){}function setPoint(t,e,i,r,s,a,n){var o=i-Math.PI/2,h=i+Math.PI/2,l=e[0]+Math.cos(i)*r*s,e=e[1]-Math.sin(i)*r*s;t.setTripleAt(l,e,l+Math.cos(o)*a,e-Math.sin(o)*a,l+Math.cos(h)*n,e-Math.sin(h)*n,t.length())}function getPerpendicularVector(t,e){e=[e[0]-t[0],e[1]-t[1]],t=.5*-Math.PI;return[Math.cos(t)*e[0]-Math.sin(t)*e[1],Math.sin(t)*e[0]+Math.cos(t)*e[1]]}function getProjectingAngle(t,e){var i=0===e?t.length()-1:e-1,e=(e+1)%t.length(),i=getPerpendicularVector(t.v[i],t.v[e]);return Math.atan2(0,1)-Math.atan2(i[1],i[0])}function zigZagCorner(t,e,i,r,s,a,n){var o=getProjectingAngle(e,i),h=e.v[i%e._length],l=e.v[0===i?e._length-1:i-1],p=e.v[(i+1)%e._length],l=2===a?Math.sqrt(Math.pow(h[0]-l[0],2)+Math.pow(h[1]-l[1],2)):0,h=2===a?Math.sqrt(Math.pow(h[0]-p[0],2)+Math.pow(h[1]-p[1],2)):0;setPoint(t,e.v[i%e._length],o,n,r,h/(2*(s+1)),l/(2*(s+1)),a)}function zigZagSegment(t,e,i,r,s,a){for(var n=0;n<r;n+=1){var o=(n+1)/(r+1),h=2===s?Math.sqrt(Math.pow(e.points[3][0]-e.points[0][0],2)+Math.pow(e.points[3][1]-e.points[0][1],2)):0,l=e.normalAngle(o);setPoint(t,e.point(o),l,a,i,h/(2*(r+1)),h/(2*(r+1)),s),a=-a}return a}function linearOffset(t,e,i){var r=Math.atan2(e[0]-t[0],e[1]-t[1]);return[polarOffset(t,r,i),polarOffset(e,r,i)]}function offsetSegment(t,e){var i=linearOffset(t.points[0],t.points[1],e),r=i[0],s=i[1],a=(i=linearOffset(t.points[1],t.points[2],e))[0],n=i[1],t=(i=linearOffset(t.points[2],t.points[3],e))[0],e=i[1],i=lineIntersection(r,s,a,n),s=(null===i&&(i=s),lineIntersection(t,e,a,n));return new PolynomialBezier(r,i,s=null===s?t:s,e)}function joinLines(t,e,i,r,s){var a,n,o=e.points[3],h=i.points[0];return 3===r||pointEqual(o,h)?o:2===r?(r=-e.tangentAngle(1),n=-i.tangentAngle(0)+Math.PI,a=lineIntersection(o,polarOffset(o,r+Math.PI/2,100),h,polarOffset(h,r+Math.PI/2,100)),a=polarOffset(o,r,2*(r=a?pointDistance(a,o):pointDistance(o,h)/2)*roundCorner),t.setXYAt(a[0],a[1],"o",t.length()-1),a=polarOffset(h,n,2*r*roundCorner),t.setTripleAt(h[0],h[1],h[0],h[1],a[0],a[1],t.length()),h):(n=lineIntersection(pointEqual(o,e.points[2])?e.points[0]:e.points[2],o,h,pointEqual(h,i.points[1])?i.points[3]:i.points[1]))&&pointDistance(n,o)<s?(t.setTripleAt(n[0],n[1],n[0],n[1],n[0],n[1],t.length()),n):o}function getIntersection(t,e){t=t.intersections(e);return t.length&&floatEqual(t[0][0],1)&&t.shift(),t.length?t[0]:null}function pruneSegmentIntersection(t,e){var i=t.slice(),r=e.slice(),s=getIntersection(t[t.length-1],e[0]);return s&&(i[t.length-1]=t[t.length-1].split(s[0])[0],r[0]=e[0].split(s[1])[1]),1<t.length&&1<e.length&&(s=getIntersection(t[0],e[e.length-1]))?[[t[0].split(s[0])[0]],[e[e.length-1].split(s[1])[1]]]:[i,r]}function pruneIntersections(t){for(var e,i=1;i<t.length;i+=1)e=pruneSegmentIntersection(t[i-1],t[i]),t[i-1]=e[0],t[i]=e[1];return 1<t.length&&(e=pruneSegmentIntersection(t[t.length-1],t[0]),t[t.length-1]=e[0],t[0]=e[1]),t}function offsetSegmentSplit(t,e){var i,r,s,a=t.inflectionPoints();return 0===a.length?[offsetSegment(t,e)]:1===a.length||floatEqual(a[1],1)?(r=(i=t.split(a[0]))[0],s=i[1],[offsetSegment(r,e),offsetSegment(s,e)]):(r=(i=t.split(a[0]))[0],t=(a[1]-a[0])/(1-a[0]),a=(i=i[1].split(t))[0],s=i[1],[offsetSegment(r,e),offsetSegment(a,e),offsetSegment(s,e)])}function OffsetPathModifier(){}function getFontProperties(t){for(var e=t.fStyle?t.fStyle.split(" "):[],i="normal",r="normal",s=e.length,a=0;a<s;a+=1)switch(e[a].toLowerCase()){case"italic":r="italic";break;case"bold":i="700";break;case"black":i="900";break;case"medium":i="500";break;case"regular":case"normal":i="400";break;case"light":case"thin":i="200"}return{style:r,weight:t.fWeight||i}}extendPrototype([ShapeModifier],RepeaterModifier),RepeaterModifier.prototype.initModifierProperties=function(t,e){this.getValue=this.processKeys,this.c=PropertyFactory.getProp(t,e.c,0,null,this),this.o=PropertyFactory.getProp(t,e.o,0,null,this),this.tr=TransformPropertyFactory.getTransformProperty(t,e.tr,this),this.so=PropertyFactory.getProp(t,e.tr.so,0,.01,this),this.eo=PropertyFactory.getProp(t,e.tr.eo,0,.01,this),this.data=e,this.dynamicProperties.length||this.getValue(!0),this._isAnimated=!!this.dynamicProperties.length,this.pMatrix=new Matrix,this.rMatrix=new Matrix,this.sMatrix=new Matrix,this.tMatrix=new Matrix,this.matrix=new Matrix},RepeaterModifier.prototype.applyTransforms=function(t,e,i,r,s,a){var n=a?-1:1,o=r.s.v[0]+(1-r.s.v[0])*(1-s),h=r.s.v[1]+(1-r.s.v[1])*(1-s);t.translate(r.p.v[0]*n*s,r.p.v[1]*n*s,r.p.v[2]),e.translate(-r.a.v[0],-r.a.v[1],r.a.v[2]),e.rotate(-r.r.v*n*s),e.translate(r.a.v[0],r.a.v[1],r.a.v[2]),i.translate(-r.a.v[0],-r.a.v[1],r.a.v[2]),i.scale(a?1/o:o,a?1/h:h),i.translate(r.a.v[0],r.a.v[1],r.a.v[2])},RepeaterModifier.prototype.init=function(t,e,i,r){for(this.elem=t,this.arr=e,this.pos=i,this.elemsData=r,this._currentCopies=0,this._elements=[],this._groups=[],this.frameId=-1,this.initDynamicPropertyContainer(t),this.initModifierProperties(t,e[i]);0<i;)this._elements.unshift(e[--i]);this.dynamicProperties.length?this.k=!0:this.getValue(!0)},RepeaterModifier.prototype.resetElements=function(t){for(var e=t.length,i=0;i<e;i+=1)t[i]._processed=!1,"gr"===t[i].ty&&this.resetElements(t[i].it)},RepeaterModifier.prototype.cloneElements=function(t){t=JSON.parse(JSON.stringify(t));return this.resetElements(t),t},RepeaterModifier.prototype.changeGroupRender=function(t,e){for(var i=t.length,r=0;r<i;r+=1)t[r]._render=e,"gr"===t[r].ty&&this.changeGroupRender(t[r].it,e)},RepeaterModifier.prototype.processShapes=function(t){var e,i,r,s,a,n=!1;if(this._mdf||t){var o,h=Math.ceil(this.c.v);if(this._groups.length<h){for(;this._groups.length<h;){var l={it:this.cloneElements(this._elements),ty:"gr"};l.it.push({a:{a:0,ix:1,k:[0,0]},nm:"Transform",o:{a:0,ix:7,k:100},p:{a:0,ix:2,k:[0,0]},r:{a:1,ix:6,k:[{s:0,e:0,t:0},{s:0,e:0,t:1}]},s:{a:0,ix:3,k:[100,100]},sa:{a:0,ix:5,k:0},sk:{a:0,ix:4,k:0},ty:"tr"}),this.arr.splice(0,0,l),this._groups.splice(0,0,l),this._currentCopies+=1}this.elem.reloadShapes(),n=!0}for(r=a=0;r<=this._groups.length-1;r+=1)this._groups[r]._render=o=a<h,this.changeGroupRender(this._groups[r].it,o),o||(0!==(o=(o=this.elemsData[r].it)[o.length-1]).transform.op.v?(o.transform.op._mdf=!0,o.transform.op.v=0):o.transform.op._mdf=!1),a+=1;this._currentCopies=h;var p,m,t=this.o.v,f=t%1,c=0<t?Math.floor(t):Math.ceil(t),d=this.pMatrix.props,u=this.rMatrix.props,y=this.sMatrix.props,g=(this.pMatrix.reset(),this.rMatrix.reset(),this.sMatrix.reset(),this.tMatrix.reset(),this.matrix.reset(),0);if(0<t){for(;g<c;)this.applyTransforms(this.pMatrix,this.rMatrix,this.sMatrix,this.tr,1,!1),g+=1;f&&(this.applyTransforms(this.pMatrix,this.rMatrix,this.sMatrix,this.tr,f,!1),g+=f)}else if(t<0){for(;c<g;)this.applyTransforms(this.pMatrix,this.rMatrix,this.sMatrix,this.tr,1,!0),--g;f&&(this.applyTransforms(this.pMatrix,this.rMatrix,this.sMatrix,this.tr,-f,!0),g-=f)}for(r=1===this.data.m?0:this._currentCopies-1,s=1===this.data.m?1:-1,a=this._currentCopies;a;){if(m=(i=(e=this.elemsData[r].it)[e.length-1].transform.mProps.v.props).length,e[e.length-1].transform.mProps._mdf=!0,e[e.length-1].transform.op._mdf=!0,e[e.length-1].transform.op.v=1===this._currentCopies?this.so.v:this.so.v+(this.eo.v-this.so.v)*(r/(this._currentCopies-1)),0!==g){for((0!==r&&1===s||r!==this._currentCopies-1&&-1===s)&&this.applyTransforms(this.pMatrix,this.rMatrix,this.sMatrix,this.tr,1,!1),this.matrix.transform(u[0],u[1],u[2],u[3],u[4],u[5],u[6],u[7],u[8],u[9],u[10],u[11],u[12],u[13],u[14],u[15]),this.matrix.transform(y[0],y[1],y[2],y[3],y[4],y[5],y[6],y[7],y[8],y[9],y[10],y[11],y[12],y[13],y[14],y[15]),this.matrix.transform(d[0],d[1],d[2],d[3],d[4],d[5],d[6],d[7],d[8],d[9],d[10],d[11],d[12],d[13],d[14],d[15]),p=0;p<m;p+=1)i[p]=this.matrix.props[p];this.matrix.reset()}else for(this.matrix.reset(),p=0;p<m;p+=1)i[p]=this.matrix.props[p];g+=1,--a,r+=s}}else for(a=this._currentCopies,r=0,s=1;a;)i=(e=this.elemsData[r].it)[e.length-1].transform.mProps.v.props,e[e.length-1].transform.mProps._mdf=!1,e[e.length-1].transform.op._mdf=!1,--a,r+=s;return n},RepeaterModifier.prototype.addShape=function(){},extendPrototype([ShapeModifier],RoundCornersModifier),RoundCornersModifier.prototype.initModifierProperties=function(t,e){this.getValue=this.processKeys,this.rd=PropertyFactory.getProp(t,e.r,0,null,this),this._isAnimated=!!this.rd.effectsSequence.length},RoundCornersModifier.prototype.processPath=function(t,e){for(var i,r,s,a,n,o,h,l,p,m,f=shapePool.newElement(),c=(f.c=t.c,t._length),d=0,u=0;u<c;u+=1)i=t.v[u],s=t.o[u],r=t.i[u],i[0]===s[0]&&i[1]===s[1]&&i[0]===r[0]&&i[1]===r[1]?0!==u&&u!==c-1||t.c?(a=0===u?t.v[c-1]:t.v[u-1],o=(n=Math.sqrt(Math.pow(i[0]-a[0],2)+Math.pow(i[1]-a[1],2)))?Math.min(n/2,e)/n:0,p=i[0]+(a[0]-i[0])*o,m=i[1]-(i[1]-a[1])*o,h=p-(p-i[0])*roundCorner,l=m-(m-i[1])*roundCorner,f.setTripleAt(p,m,h,l,p,m,d),d+=1,a=u===c-1?t.v[0]:t.v[u+1],o=(n=Math.sqrt(Math.pow(i[0]-a[0],2)+Math.pow(i[1]-a[1],2)))?Math.min(n/2,e)/n:0,h=i[0]+(a[0]-i[0])*o,l=i[1]+(a[1]-i[1])*o,p=h-(h-i[0])*roundCorner,m=l-(l-i[1])*roundCorner,f.setTripleAt(h,l,h,l,p,m,d)):f.setTripleAt(i[0],i[1],s[0],s[1],r[0],r[1],d):f.setTripleAt(t.v[u][0],t.v[u][1],t.o[u][0],t.o[u][1],t.i[u][0],t.i[u][1],d),d+=1;return f},RoundCornersModifier.prototype.processShapes=function(t){var e,i,r,s=this.shapes.length,a=this.rd.v;if(0!==a)for(var n,o,h=0;h<s;h+=1){if(o=(n=this.shapes[h]).localShapeCollection,n.shape._mdf||this._mdf||t)for(o.releaseShapes(),n.shape._mdf=!0,e=n.shape.paths.shapes,r=n.shape.paths._length,i=0;i<r;i+=1)o.addShape(this.processPath(e[i],a));n.shape.paths=n.localShapeCollection}this.dynamicProperties.length||(this._mdf=!1)},PolynomialBezier.prototype.point=function(t){return[((this.a[0]*t+this.b[0])*t+this.c[0])*t+this.d[0],((this.a[1]*t+this.b[1])*t+this.c[1])*t+this.d[1]]},PolynomialBezier.prototype.derivative=function(t){return[(3*t*this.a[0]+2*this.b[0])*t+this.c[0],(3*t*this.a[1]+2*this.b[1])*t+this.c[1]]},PolynomialBezier.prototype.tangentAngle=function(t){t=this.derivative(t);return Math.atan2(t[1],t[0])},PolynomialBezier.prototype.normalAngle=function(t){t=this.derivative(t);return Math.atan2(t[0],t[1])},PolynomialBezier.prototype.inflectionPoints=function(){var t,e=this.a[1]*this.b[0]-this.a[0]*this.b[1];return floatZero(e)||(e=(t=-.5*(this.a[1]*this.c[0]-this.a[0]*this.c[1])/e)*t-1/3*(this.b[1]*this.c[0]-this.b[0]*this.c[1])/e)<0?[]:floatZero(e=Math.sqrt(e))?0<e&&e<1?[t]:[]:[t-e,t+e].filter(function(t){return 0<t&&t<1})},PolynomialBezier.prototype.split=function(t){var e,i,r,s;return t<=0?[singlePoint(this.points[0]),this]:1<=t?[this,singlePoint(this.points[this.points.length-1])]:(e=lerpPoint(this.points[0],this.points[1],t),s=lerpPoint(this.points[1],this.points[2],t),i=lerpPoint(this.points[2],this.points[3],t),r=lerpPoint(e,s,t),s=lerpPoint(s,i,t),t=lerpPoint(r,s,t),[new PolynomialBezier(this.points[0],e,r,t,!0),new PolynomialBezier(t,s,i,this.points[3],!0)])},PolynomialBezier.prototype.bounds=function(){return{x:extrema(this,0),y:extrema(this,1)}},PolynomialBezier.prototype.boundingBox=function(){var t=this.bounds();return{left:t.x.min,right:t.x.max,top:t.y.min,bottom:t.y.max,width:t.x.max-t.x.min,height:t.y.max-t.y.min,cx:(t.x.max+t.x.min)/2,cy:(t.y.max+t.y.min)/2}},PolynomialBezier.prototype.intersections=function(t,e,i){void 0===e&&(e=2),void 0===i&&(i=7);var r=[];return intersectsImpl(intersectData(this,0,1),intersectData(t,0,1),0,e,r,i),r},PolynomialBezier.shapeSegment=function(t,e){var i=(e+1)%t.length();return new PolynomialBezier(t.v[e],t.o[e],t.i[i],t.v[i],!0)},PolynomialBezier.shapeSegmentInverted=function(t,e){var i=(e+1)%t.length();return new PolynomialBezier(t.v[i],t.i[i],t.o[e],t.v[e],!0)},extendPrototype([ShapeModifier],ZigZagModifier),ZigZagModifier.prototype.initModifierProperties=function(t,e){this.getValue=this.processKeys,this.amplitude=PropertyFactory.getProp(t,e.s,0,null,this),this.frequency=PropertyFactory.getProp(t,e.r,0,null,this),this.pointsType=PropertyFactory.getProp(t,e.pt,0,null,this),this._isAnimated=0!==this.amplitude.effectsSequence.length||0!==this.frequency.effectsSequence.length||0!==this.pointsType.effectsSequence.length},ZigZagModifier.prototype.processPath=function(t,e,i,r){var s=t._length,a=shapePool.newElement();if(a.c=t.c,t.c||--s,0!==s){var n=-1,o=PolynomialBezier.shapeSegment(t,0);zigZagCorner(a,t,0,e,i,r,n);for(var h=0;h<s;h+=1)n=zigZagSegment(a,o,e,i,r,-n),o=h!==s-1||t.c?PolynomialBezier.shapeSegment(t,(h+1)%s):null,zigZagCorner(a,t,h+1,e,i,r,n)}return a},ZigZagModifier.prototype.processShapes=function(t){var e,i,r,s=this.shapes.length,a=this.amplitude.v,n=Math.max(0,Math.round(this.frequency.v)),o=this.pointsType.v;if(0!==a)for(var h,l,p=0;p<s;p+=1){if(l=(h=this.shapes[p]).localShapeCollection,h.shape._mdf||this._mdf||t)for(l.releaseShapes(),h.shape._mdf=!0,e=h.shape.paths.shapes,r=h.shape.paths._length,i=0;i<r;i+=1)l.addShape(this.processPath(e[i],a,n,o));h.shape.paths=h.localShapeCollection}this.dynamicProperties.length||(this._mdf=!1)},extendPrototype([ShapeModifier],OffsetPathModifier),OffsetPathModifier.prototype.initModifierProperties=function(t,e){this.getValue=this.processKeys,this.amount=PropertyFactory.getProp(t,e.a,0,null,this),this.miterLimit=PropertyFactory.getProp(t,e.ml,0,null,this),this.lineJoin=e.lj,this._isAnimated=0!==this.amount.effectsSequence.length},OffsetPathModifier.prototype.processPath=function(t,e,i,r){for(var s,a,n=shapePool.newElement(),o=(n.c=t.c,t.length()),h=(t.c||--o,[]),l=0;l<o;l+=1)a=PolynomialBezier.shapeSegment(t,l),h.push(offsetSegmentSplit(a,e));if(!t.c)for(l=o-1;0<=l;--l)a=PolynomialBezier.shapeSegmentInverted(t,l),h.push(offsetSegmentSplit(a,e));var h=pruneIntersections(h),p=null,m=null;for(l=0;l<h.length;l+=1){var f=h[l];for(m&&(p=joinLines(n,m,f[0],i,r)),m=f[f.length-1],s=0;s<f.length;s+=1)a=f[s],p&&pointEqual(a.points[0],p)?n.setXYAt(a.points[1][0],a.points[1][1],"o",n.length()-1):n.setTripleAt(a.points[0][0],a.points[0][1],a.points[1][0],a.points[1][1],a.points[0][0],a.points[0][1],n.length()),n.setTripleAt(a.points[3][0],a.points[3][1],a.points[3][0],a.points[3][1],a.points[2][0],a.points[2][1],n.length()),p=a.points[3]}return h.length&&joinLines(n,m,h[0][0],i,r),n},OffsetPathModifier.prototype.processShapes=function(t){var e,i,r,s=this.shapes.length,a=this.amount.v,n=this.miterLimit.v,o=this.lineJoin;if(0!==a)for(var h,l,p=0;p<s;p+=1){if(l=(h=this.shapes[p]).localShapeCollection,h.shape._mdf||this._mdf||t)for(l.releaseShapes(),h.shape._mdf=!0,e=h.shape.paths.shapes,r=h.shape.paths._length,i=0;i<r;i+=1)l.addShape(this.processPath(e[i],a,o,n));h.shape.paths=h.localShapeCollection}this.dynamicProperties.length||(this._mdf=!1)};var FontManager=(()=>{var a=5e3,n={w:0,size:0,shapes:[],data:{shapes:[]}},e=(e=[]).concat([2304,2305,2306,2307,2362,2363,2364,2364,2366,2367,2368,2369,2370,2371,2372,2373,2374,2375,2376,2377,2378,2379,2380,2381,2382,2383,2387,2388,2389,2390,2391,2402,2403]),s=127988,o=917631,h=917601,l=917626,i=65039,r=8205,p=127462,m=127487,f=["d83cdffb","d83cdffc","d83cdffd","d83cdffe","d83cdfff"];function c(t,e){var i=createTag("span"),r=(i.setAttribute("aria-hidden",!0),i.style.fontFamily=e,createTag("span")),s=(r.innerText="giItT1WQy@!-/#",i.style.position="absolute",i.style.left="-10000px",i.style.top="-10000px",i.style.fontSize="300px",i.style.fontVariant="normal",i.style.fontStyle="normal",i.style.fontWeight="normal",i.style.letterSpacing="0",i.appendChild(r),document.body.appendChild(i),r.offsetWidth);return r.style.fontFamily=(t=>{for(var e=t.split(","),i=e.length,r=[],s=0;s<i;s+=1)"sans-serif"!==e[s]&&"monospace"!==e[s]&&r.push(e[s]);return r.join(",")})(t)+", "+e,{node:r,w:s,parent:i}}function d(t,e){var i,r,s=document.body&&e?"svg":"canvas",a=getFontProperties(t);return i="svg"==s?((r=createNS("text")).style.fontSize="100px",r.setAttribute("font-family",t.fFamily),r.setAttribute("font-style",a.style),r.setAttribute("font-weight",a.weight),r.textContent="1",t.fClass?(r.style.fontFamily="inherit",r.setAttribute("class",t.fClass)):r.style.fontFamily=t.fFamily,e.appendChild(r),r):((e=new OffscreenCanvas(500,500).getContext("2d")).font=a.style+" "+a.weight+" 100px "+t.fFamily,e),{measureText:function(t){return"svg"==s?(i.textContent=t,i.getComputedTextLength()):i.measureText(t).width}}}function u(t){var e=0,i=t.charCodeAt(0);return e=55296<=i&&i<=56319&&56320<=(t=t.charCodeAt(1))&&t<=57343?1024*(i-55296)+t-56320+65536:e}function y(t){t=u(t);return p<=t&&t<=m}function t(){this.fonts=[],this.chars=null,this.typekitLoaded=0,this.isLoaded=!1,this._warned=!1,this.initTime=Date.now(),this.setIsLoadedBinded=this.setIsLoaded.bind(this),this.checkLoadedFontsBinded=this.checkLoadedFonts.bind(this)}return t.isModifier=function(t,e){return t=t.toString(16)+e.toString(16),-1!==f.indexOf(t)},t.isZeroWidthJoiner=function(t){return t===r},t.isFlagEmoji=function(t){return y(t.substr(0,2))&&y(t.substr(2,2))},t.isRegionalCode=y,t.isCombinedCharacter=function(t){return-1!==e.indexOf(t)},t.isRegionalFlag=function(t,e){var i;if(u(t.substr(e,2))!==s)return!1;var r=0;for(e+=2;r<5;){if((i=u(t.substr(e,2)))<h||l<i)return!1;r+=1,e+=2}return u(t.substr(e,2))===o},t.isVariationSelector=function(t){return t===i},t.BLACK_FLAG_CODE_POINT=s,t.prototype={addChars:function(t){if(t){this.chars||(this.chars=[]);for(var e,i,r=t.length,s=this.chars.length,a=0;a<r;a+=1){for(e=0,i=!1;e<s;)this.chars[e].style===t[a].style&&this.chars[e].fFamily===t[a].fFamily&&this.chars[e].ch===t[a].ch&&(i=!0),e+=1;i||(this.chars.push(t[a]),s+=1)}}},addFonts:function(t,e){if(t)if(this.chars)this.isLoaded=!0,this.fonts=t.list;else if(document.body){for(var i=t.list,r=i.length,s=r,a=0;a<r;a+=1){var n,o,h,l,p=!0;if(i[a].loaded=!1,i[a].monoCase=c(i[a].fFamily,"monospace"),i[a].sansCase=c(i[a].fFamily,"sans-serif"),i[a].fPath){if("p"===i[a].fOrigin||3===i[a].origin)(p=0<(o=document.querySelectorAll('style[f-forigin="p"][f-family="'+i[a].fFamily+'"], style[f-origin="3"][f-family="'+i[a].fFamily+'"]')).length?!1:p)&&((h=createTag("style")).setAttribute("f-forigin",i[a].fOrigin),h.setAttribute("f-origin",i[a].origin),h.setAttribute("f-family",i[a].fFamily),h.type="text/css",h.innerText="@font-face {font-family: "+i[a].fFamily+"; font-style: normal; src: url('"+i[a].fPath+"');}",e.appendChild(h));else if("g"===i[a].fOrigin||1===i[a].origin){for(o=document.querySelectorAll('link[f-forigin="g"], link[f-origin="1"]'),n=0;n<o.length;n+=1)-1!==o[n].href.indexOf(i[a].fPath)&&(p=!1);p&&((h=createTag("link")).setAttribute("f-forigin",i[a].fOrigin),h.setAttribute("f-origin",i[a].origin),h.type="text/css",h.rel="stylesheet",h.href=i[a].fPath,document.body.appendChild(h))}else if("t"===i[a].fOrigin||2===i[a].origin){for(o=document.querySelectorAll('script[f-forigin="t"], script[f-origin="2"]'),n=0;n<o.length;n+=1)i[a].fPath===o[n].src&&(p=!1);p&&((l=createTag("link")).setAttribute("f-forigin",i[a].fOrigin),l.setAttribute("f-origin",i[a].origin),l.setAttribute("rel","stylesheet"),l.setAttribute("href",i[a].fPath),e.appendChild(l))}}else i[a].loaded=!0,--s;i[a].helper=d(i[a],e),i[a].cache={},this.fonts.push(i[a])}0===s?this.isLoaded=!0:setTimeout(this.checkLoadedFonts.bind(this),100)}else this.isLoaded=!0,t.list.forEach(function(t){t.helper=d(t),t.cache={}}),this.fonts=t.list;else this.isLoaded=!0},getCharData:function(t,e,i){for(var r=0,s=this.chars.length;r<s;){if(this.chars[r].ch===t&&this.chars[r].style===e&&this.chars[r].fFamily===i)return this.chars[r];r+=1}return("string"==typeof t&&13!==t.charCodeAt(0)||!t)&&console&&console.warn&&!this._warned&&(this._warned=!0,console.warn("Missing character from exported characters list: ",t,e,i)),n},getFontByName:function(t){for(var e=0,i=this.fonts.length;e<i;){if(this.fonts[e].fName===t)return this.fonts[e];e+=1}return this.fonts[0]},measureText:function(t,e,i){var r,s,a,e=this.getFontByName(e),n=t;return e.cache[n]||(r=e.helper," "===t?(s=r.measureText("|"+t+"|"),a=r.measureText("||"),e.cache[n]=(s-a)/100):e.cache[n]=r.measureText(t)/100),e.cache[n]*i},checkLoadedFonts:function(){for(var t,e,i=this.fonts.length,r=i,s=0;s<i;s+=1)this.fonts[s].loaded?--r:"n"===this.fonts[s].fOrigin||0===this.fonts[s].origin?this.fonts[s].loaded=!0:(t=this.fonts[s].monoCase.node,e=this.fonts[s].monoCase.w,t.offsetWidth===e&&(t=this.fonts[s].sansCase.node,e=this.fonts[s].sansCase.w,t.offsetWidth===e)||(--r,this.fonts[s].loaded=!0),this.fonts[s].loaded&&(this.fonts[s].sansCase.parent.parentNode.removeChild(this.fonts[s].sansCase.parent),this.fonts[s].monoCase.parent.parentNode.removeChild(this.fonts[s].monoCase.parent)));0!==r&&Date.now()-this.initTime<a?setTimeout(this.checkLoadedFontsBinded,20):setTimeout(this.setIsLoadedBinded,10)},setIsLoaded:function(){this.isLoaded=!0}},t})();function SlotManager(t){this.animationData=t}function slotFactory(t){return new SlotManager(t)}function RenderableElement(){}SlotManager.prototype.getProp=function(t){return this.animationData.slots&&this.animationData.slots[t.sid]?Object.assign(t,this.animationData.slots[t.sid].p):t},RenderableElement.prototype={initRenderable:function(){this.isInRange=!1,this.hidden=!1,this.isTransparent=!1,this.renderableComponents=[]},addRenderableComponent:function(t){-1===this.renderableComponents.indexOf(t)&&this.renderableComponents.push(t)},removeRenderableComponent:function(t){-1!==this.renderableComponents.indexOf(t)&&this.renderableComponents.splice(this.renderableComponents.indexOf(t),1)},prepareRenderableFrame:function(t){this.checkLayerLimits(t)},checkTransparency:function(){this.finalTransform.mProp.o.v<=0?!this.isTransparent&&this.globalData.renderConfig.hideOnTransparent&&(this.isTransparent=!0,this.hide()):this.isTransparent&&(this.isTransparent=!1,this.show())},checkLayerLimits:function(t){this.data.ip-this.data.st<=t&&this.data.op-this.data.st>t?!0!==this.isInRange&&(this.globalData._mdf=!0,this._mdf=!0,this.isInRange=!0,this.show()):!1!==this.isInRange&&(this.globalData._mdf=!0,this.isInRange=!1,this.hide())},renderRenderable:function(){for(var t=this.renderableComponents.length,e=0;e<t;e+=1)this.renderableComponents[e].renderFrame(this._isFirstFrame)},sourceRectAtTime:function(){return{top:0,left:0,width:100,height:100}},getLayerSize:function(){return 5===this.data.ty?{w:this.data.textData.width,h:this.data.textData.height}:{w:this.data.width,h:this.data.height}}};var getBlendMode=(()=>{var e={0:"source-over",1:"multiply",2:"screen",3:"overlay",4:"darken",5:"lighten",6:"color-dodge",7:"color-burn",8:"hard-light",9:"soft-light",10:"difference",11:"exclusion",12:"hue",13:"saturation",14:"color",15:"luminosity"};return function(t){return e[t]||""}})();function SliderEffect(t,e,i){this.p=PropertyFactory.getProp(e,t.v,0,0,i)}function AngleEffect(t,e,i){this.p=PropertyFactory.getProp(e,t.v,0,0,i)}function ColorEffect(t,e,i){this.p=PropertyFactory.getProp(e,t.v,1,0,i)}function PointEffect(t,e,i){this.p=PropertyFactory.getProp(e,t.v,1,0,i)}function LayerIndexEffect(t,e,i){this.p=PropertyFactory.getProp(e,t.v,0,0,i)}function MaskIndexEffect(t,e,i){this.p=PropertyFactory.getProp(e,t.v,0,0,i)}function CheckboxEffect(t,e,i){this.p=PropertyFactory.getProp(e,t.v,0,0,i)}function NoValueEffect(){this.p={}}function EffectsManager(t,e){for(var i,r=t.ef||[],s=(this.effectElements=[],r.length),a=0;a<s;a+=1)i=new GroupEffect(r[a],e),this.effectElements.push(i)}function GroupEffect(t,e){this.init(t,e)}function BaseElement(){}function FrameElement(){}function FootageElement(t,e,i){this.initFrame(),this.initRenderable(),this.assetData=e.getAssetData(t.refId),this.footageData=e.imageLoader.getAsset(this.assetData),this.initBaseData(t,e,i)}function AudioElement(t,e,i){this.initFrame(),this.initRenderable(),this.assetData=e.getAssetData(t.refId),this.initBaseData(t,e,i),this._isPlaying=!1,this._canPlay=!1;i=this.globalData.getAssetsPath(this.assetData);this.audio=this.globalData.audioController.createAudio(i),this._currentTime=0,this.globalData.audioController.addAudio(this),this._volumeMultiplier=1,this._volume=1,this._previousVolume=null,this.tm=t.tm?PropertyFactory.getProp(this,t.tm,0,e.frameRate,this):{_placeholder:!0},this.lv=PropertyFactory.getProp(this,t.au&&t.au.lv?t.au.lv:{k:[100]},1,.01,this)}function BaseRenderer(){}extendPrototype([DynamicPropertyContainer],GroupEffect),GroupEffect.prototype.getValue=GroupEffect.prototype.iterateDynamicProperties,GroupEffect.prototype.init=function(t,e){this.data=t,this.effectElements=[],this.initDynamicPropertyContainer(e);for(var i,r=this.data.ef.length,s=this.data.ef,a=0;a<r;a+=1){switch(i=null,s[a].ty){case 0:i=new SliderEffect(s[a],e,this);break;case 1:i=new AngleEffect(s[a],e,this);break;case 2:i=new ColorEffect(s[a],e,this);break;case 3:i=new PointEffect(s[a],e,this);break;case 4:case 7:i=new CheckboxEffect(s[a],e,this);break;case 10:i=new LayerIndexEffect(s[a],e,this);break;case 11:i=new MaskIndexEffect(s[a],e,this);break;case 5:i=new EffectsManager(s[a],e,this);break;default:i=new NoValueEffect(s[a],e,this)}i&&this.effectElements.push(i)}},BaseElement.prototype={checkMasks:function(){if(this.data.hasMask)for(var t=0,e=this.data.masksProperties.length;t<e;){if("n"!==this.data.masksProperties[t].mode&&!1!==this.data.masksProperties[t].cl)return!0;t+=1}return!1},initExpressions:function(){var t,e,i,r,s=getExpressionInterfaces();s&&(r=s("layer"),t=s("effects"),e=s("shape"),i=s("text"),s=s("comp"),this.layerInterface=r(this),this.data.hasMask&&this.maskManager&&this.layerInterface.registerMaskInterface(this.maskManager),r=t.createEffectsInterface(this,this.layerInterface),this.layerInterface.registerEffectsInterface(r),0===this.data.ty||this.data.xt?this.compInterface=s(this):4===this.data.ty?(this.layerInterface.shapeInterface=e(this.shapesData,this.itemsData,this.layerInterface),this.layerInterface.content=this.layerInterface.shapeInterface):5===this.data.ty&&(this.layerInterface.textInterface=i(this),this.layerInterface.text=this.layerInterface.textInterface))},setBlendMode:function(){var t=getBlendMode(this.data.bm);(this.baseElement||this.layerElement).style["mix-blend-mode"]=t},initBaseData:function(t,e,i){this.globalData=e,this.comp=i,this.data=t,this.layerId=createElementID(),this.data.sr||(this.data.sr=1),this.effectsManager=new EffectsManager(this.data,this,this.dynamicProperties)},getType:function(){return this.type},sourceRectAtTime:function(){}},FrameElement.prototype={initFrame:function(){this._isFirstFrame=!1,this.dynamicProperties=[],this._mdf=!1},prepareProperties:function(t,e){for(var i=this.dynamicProperties.length,r=0;r<i;r+=1)(e||this._isParent&&"transform"===this.dynamicProperties[r].propType)&&(this.dynamicProperties[r].getValue(),this.dynamicProperties[r]._mdf)&&(this.globalData._mdf=!0,this._mdf=!0)},addDynamicProperty:function(t){-1===this.dynamicProperties.indexOf(t)&&this.dynamicProperties.push(t)}},FootageElement.prototype.prepareFrame=function(){},extendPrototype([RenderableElement,BaseElement,FrameElement],FootageElement),FootageElement.prototype.getBaseElement=function(){return null},FootageElement.prototype.renderFrame=function(){},FootageElement.prototype.destroy=function(){},FootageElement.prototype.initExpressions=function(){var t=getExpressionInterfaces();t&&(t=t("footage"),this.layerInterface=t(this))},FootageElement.prototype.getFootageData=function(){return this.footageData},AudioElement.prototype.prepareFrame=function(t){this.prepareRenderableFrame(t,!0),this.prepareProperties(t,!0),this.tm._placeholder?this._currentTime=t/this.data.sr:(t=this.tm.v,this._currentTime=t),this._volume=this.lv.v[0];t=this._volume*this._volumeMultiplier;this._previousVolume!==t&&(this._previousVolume=t,this.audio.volume(t))},extendPrototype([RenderableElement,BaseElement,FrameElement],AudioElement),AudioElement.prototype.renderFrame=function(){this.isInRange&&this._canPlay&&(this._isPlaying?(!this.audio.playing()||.1<Math.abs(this._currentTime/this.globalData.frameRate-this.audio.seek()))&&this.audio.seek(this._currentTime/this.globalData.frameRate):(this.audio.play(),this.audio.seek(this._currentTime/this.globalData.frameRate),this._isPlaying=!0))},AudioElement.prototype.show=function(){},AudioElement.prototype.hide=function(){this.audio.pause(),this._isPlaying=!1},AudioElement.prototype.pause=function(){this.audio.pause(),this._isPlaying=!1,this._canPlay=!1},AudioElement.prototype.resume=function(){this._canPlay=!0},AudioElement.prototype.setRate=function(t){this.audio.rate(t)},AudioElement.prototype.volume=function(t){this._volumeMultiplier=t,this._previousVolume=t*this._volume,this.audio.volume(this._previousVolume)},AudioElement.prototype.getBaseElement=function(){return null},AudioElement.prototype.destroy=function(){},AudioElement.prototype.sourceRectAtTime=function(){},AudioElement.prototype.initExpressions=function(){},BaseRenderer.prototype.checkLayers=function(t){var e,i,r=this.layers.length;for(this.completeLayers=!0,e=r-1;0<=e;--e)this.elements[e]||(i=this.layers[e]).ip-i.st<=t-this.layers[e].st&&i.op-i.st>t-this.layers[e].st&&this.buildItem(e),this.completeLayers=!!this.elements[e]&&this.completeLayers;this.checkPendingElements()},BaseRenderer.prototype.createItem=function(t){switch(t.ty){case 2:return this.createImage(t);case 0:return this.createComp(t);case 1:return this.createSolid(t);case 3:return this.createNull(t);case 4:return this.createShape(t);case 5:return this.createText(t);case 6:return this.createAudio(t);case 13:return this.createCamera(t);case 15:return this.createFootage(t);default:return this.createNull(t)}},BaseRenderer.prototype.createCamera=function(){throw new Error("You're using a 3d camera. Try the html renderer.")},BaseRenderer.prototype.createAudio=function(t){return new AudioElement(t,this.globalData,this)},BaseRenderer.prototype.createFootage=function(t){return new FootageElement(t,this.globalData,this)},BaseRenderer.prototype.buildAllItems=function(){for(var t=this.layers.length,e=0;e<t;e+=1)this.buildItem(e);this.checkPendingElements()},BaseRenderer.prototype.includeLayers=function(t){this.completeLayers=!1;for(var e,i=t.length,r=this.layers.length,s=0;s<i;s+=1)for(e=0;e<r;){if(this.layers[e].id===t[s].id){this.layers[e]=t[s];break}e+=1}},BaseRenderer.prototype.setProjectInterface=function(t){this.globalData.projectInterface=t},BaseRenderer.prototype.initItems=function(){this.globalData.progressiveLoad||this.buildAllItems()},BaseRenderer.prototype.buildElementParenting=function(t,e,i){for(var r=this.elements,s=this.layers,a=0,n=s.length;a<n;)s[a].ind==e&&(r[a]&&!0!==r[a]?(i.push(r[a]),r[a].setAsParent(),void 0!==s[a].parent?this.buildElementParenting(t,s[a].parent,i):t.setHierarchy(i)):(this.buildItem(a),this.addPendingElement(t))),a+=1},BaseRenderer.prototype.addPendingElement=function(t){this.pendingElements.push(t)},BaseRenderer.prototype.searchExtraCompositions=function(t){for(var e,i=t.length,r=0;r<i;r+=1)t[r].xt&&((e=this.createComp(t[r])).initExpressions(),this.globalData.projectInterface.registerComposition(e))},BaseRenderer.prototype.getElementById=function(t){for(var e=this.elements.length,i=0;i<e;i+=1)if(this.elements[i].data.ind===t)return this.elements[i];return null},BaseRenderer.prototype.getElementByPath=function(t){var e,i=t.shift();if("number"==typeof i)e=this.elements[i];else for(var r=this.elements.length,s=0;s<r;s+=1)if(this.elements[s].data.nm===i){e=this.elements[s];break}return 0===t.length?e:e.getElementByPath(t)},BaseRenderer.prototype.setupGlobalData=function(t,e){this.globalData.fontManager=new FontManager,this.globalData.slotManager=slotFactory(t),this.globalData.fontManager.addChars(t.chars),this.globalData.fontManager.addFonts(t.fonts,e),this.globalData.getAssetData=this.animationItem.getAssetData.bind(this.animationItem),this.globalData.getAssetsPath=this.animationItem.getAssetsPath.bind(this.animationItem),this.globalData.imageLoader=this.animationItem.imagePreloader,this.globalData.audioController=this.animationItem.audioController,this.globalData.frameId=0,this.globalData.frameRate=t.fr,this.globalData.nm=t.nm,this.globalData.compSize={w:t.w,h:t.h}};var effectTypes={TRANSFORM_EFFECT:"transformEFfect"};function TransformElement(){}function MaskElement(t,e,i){this.data=t,this.element=e,this.globalData=i,this.storedData=[],this.masksProperties=this.data.masksProperties||[],this.maskElement=null;for(var r,s,a,n,o,h=this.globalData.defs,l=this.masksProperties?this.masksProperties.length:0,p=(this.viewData=createSizedArray(l),this.solidPath="",this.masksProperties),m=0,f=[],c=createElementID(),d="clipPath",u="clip-path",y=0;y<l;y+=1)if(("a"!==p[y].mode&&"n"!==p[y].mode||p[y].inv||100!==p[y].o.k||p[y].o.x)&&(u=d="mask"),"s"!==p[y].mode&&"i"!==p[y].mode||0!==m?s=null:((s=createNS("rect")).setAttribute("fill","#ffffff"),s.setAttribute("width",this.element.comp.data.w||0),s.setAttribute("height",this.element.comp.data.h||0),f.push(s)),r=createNS("path"),"n"===p[y].mode)this.viewData[y]={op:PropertyFactory.getProp(this.element,p[y].o,0,.01,this.element),prop:ShapePropertyFactory.getShapeProp(this.element,p[y],3),elem:r,lastPath:""},h.appendChild(r);else{if(m+=1,r.setAttribute("fill","s"===p[y].mode?"#000000":"#ffffff"),r.setAttribute("clip-rule","nonzero"),0!==p[y].x.k?(u=d="mask",n=PropertyFactory.getProp(this.element,p[y].x,0,null,this.element),o=createElementID(),(x=createNS("filter")).setAttribute("id",o),(a=createNS("feMorphology")).setAttribute("operator","erode"),a.setAttribute("in","SourceGraphic"),a.setAttribute("radius","0"),x.appendChild(a),h.appendChild(x),r.setAttribute("stroke","s"===p[y].mode?"#000000":"#ffffff")):n=a=null,this.storedData[y]={elem:r,x:n,expan:a,lastPath:"",lastOperator:"",filterId:o,lastRadius:0},"i"===p[y].mode){for(var g=f.length,v=createNS("g"),b=0;b<g;b+=1)v.appendChild(f[b]);var x=createNS("mask");x.setAttribute("mask-type","alpha"),x.setAttribute("id",c+"_"+m),x.appendChild(r),h.appendChild(x),v.setAttribute("mask","url("+getLocationHref()+"#"+c+"_"+m+")"),f.length=0,f.push(v)}else f.push(r);p[y].inv&&!this.solidPath&&(this.solidPath=this.createLayerSolidPath()),this.viewData[y]={elem:r,lastPath:"",op:PropertyFactory.getProp(this.element,p[y].o,0,.01,this.element),prop:ShapePropertyFactory.getShapeProp(this.element,p[y],3),invRect:s},this.viewData[y].prop.k||this.drawPath(p[y],this.viewData[y].prop.v,this.viewData[y])}for(this.maskElement=createNS(d),l=f.length,y=0;y<l;y+=1)this.maskElement.appendChild(f[y]);0<m&&(this.maskElement.setAttribute("id",c),this.element.maskedElement.setAttribute(u,"url("+getLocationHref()+"#"+c+")"),h.appendChild(this.maskElement)),this.viewData.length&&this.element.addRenderableComponent(this)}TransformElement.prototype={initTransform:function(){var t=new Matrix;this.finalTransform={mProp:this.data.ks?TransformPropertyFactory.getTransformProperty(this,this.data.ks,this):{o:0},_matMdf:!1,_localMatMdf:!1,_opMdf:!1,mat:t,localMat:t,localOpacity:1},this.data.ao&&(this.finalTransform.mProp.autoOriented=!0),this.data.ty},renderTransform:function(){if(this.finalTransform._opMdf=this.finalTransform.mProp.o._mdf||this._isFirstFrame,this.finalTransform._matMdf=this.finalTransform.mProp._mdf||this._isFirstFrame,this.hierarchy){var t,e=this.finalTransform.mat,i=0,r=this.hierarchy.length;if(!this.finalTransform._matMdf)for(;i<r;){if(this.hierarchy[i].finalTransform.mProp._mdf){this.finalTransform._matMdf=!0;break}i+=1}if(this.finalTransform._matMdf)for(t=this.finalTransform.mProp.v.props,e.cloneFromProps(t),i=0;i<r;i+=1)e.multiply(this.hierarchy[i].finalTransform.mProp.v)}this.localTransforms&&!this.finalTransform._matMdf||(this.finalTransform._localMatMdf=this.finalTransform._matMdf),this.finalTransform._opMdf&&(this.finalTransform.localOpacity=this.finalTransform.mProp.o.v)},renderLocalTransform:function(){if(this.localTransforms){var t=0,e=this.localTransforms.length;if(this.finalTransform._localMatMdf=this.finalTransform._matMdf,!this.finalTransform._localMatMdf||!this.finalTransform._opMdf)for(;t<e;)this.localTransforms[t]._mdf&&(this.finalTransform._localMatMdf=!0),this.localTransforms[t]._opMdf&&!this.finalTransform._opMdf&&(this.finalTransform.localOpacity=this.finalTransform.mProp.o.v,this.finalTransform._opMdf=!0),t+=1;if(this.finalTransform._localMatMdf){var i=this.finalTransform.localMat;for(this.localTransforms[0].matrix.clone(i),t=1;t<e;t+=1){var r=this.localTransforms[t].matrix;i.multiply(r)}i.multiply(this.finalTransform.mat)}if(this.finalTransform._opMdf){for(var s=this.finalTransform.localOpacity,t=0;t<e;t+=1)s*=.01*this.localTransforms[t].opacity;this.finalTransform.localOpacity=s}}},searchEffectTransforms:function(){if(this.renderableEffectsManager){var t=this.renderableEffectsManager.getEffects(effectTypes.TRANSFORM_EFFECT);if(t.length){this.localTransforms=[],this.finalTransform.localMat=new Matrix;for(var e=0,i=t.length,e=0;e<i;e+=1)this.localTransforms.push(t[e])}}},globalToLocal:function(t){for(var e=[],i=(e.push(this.finalTransform),!0),r=this.comp;i;)r.finalTransform?(r.data.hasMask&&e.splice(0,0,r.finalTransform),r=r.comp):i=!1;for(var s,a=e.length,n=0;n<a;n+=1)s=e[n].mat.applyToPointArray(0,0,0),t=[t[0]-s[0],t[1]-s[1],0];return t},mHelper:new Matrix},MaskElement.prototype.getMaskProperty=function(t){return this.viewData[t].prop},MaskElement.prototype.renderFrame=function(t){for(var e,i=this.element.finalTransform.mat,r=this.masksProperties.length,s=0;s<r;s+=1)(this.viewData[s].prop._mdf||t)&&this.drawPath(this.masksProperties[s],this.viewData[s].prop.v,this.viewData[s]),(this.viewData[s].op._mdf||t)&&this.viewData[s].elem.setAttribute("fill-opacity",this.viewData[s].op.v),"n"!==this.masksProperties[s].mode&&(this.viewData[s].invRect&&(this.element.finalTransform.mProp._mdf||t)&&this.viewData[s].invRect.setAttribute("transform",i.getInverseMatrix().to2dCSS()),this.storedData[s].x)&&(this.storedData[s].x._mdf||t)&&(e=this.storedData[s].expan,this.storedData[s].x.v<0?("erode"!==this.storedData[s].lastOperator&&(this.storedData[s].lastOperator="erode",this.storedData[s].elem.setAttribute("filter","url("+getLocationHref()+"#"+this.storedData[s].filterId+")")),e.setAttribute("radius",-this.storedData[s].x.v)):("dilate"!==this.storedData[s].lastOperator&&(this.storedData[s].lastOperator="dilate",this.storedData[s].elem.setAttribute("filter",null)),this.storedData[s].elem.setAttribute("stroke-width",2*this.storedData[s].x.v)))},MaskElement.prototype.getMaskelement=function(){return this.maskElement},MaskElement.prototype.createLayerSolidPath=function(){var t="M0,0 ";return(t+=" h"+this.globalData.compSize.w)+(" v"+this.globalData.compSize.h)+(" h-"+this.globalData.compSize.w)+(" v-"+this.globalData.compSize.h+" ")},MaskElement.prototype.drawPath=function(t,e,i){for(var r,s=" M"+e.v[0][0]+","+e.v[0][1],a=e._length,n=1;n<a;n+=1)s+=" C"+e.o[n-1][0]+","+e.o[n-1][1]+" "+e.i[n][0]+","+e.i[n][1]+" "+e.v[n][0]+","+e.v[n][1];e.c&&1<a&&(s+=" C"+e.o[n-1][0]+","+e.o[n-1][1]+" "+e.i[0][0]+","+e.i[0][1]+" "+e.v[0][0]+","+e.v[0][1]),i.lastPath!==s&&(r="",i.elem&&(e.c&&(r=t.inv?this.solidPath+s:s),i.elem.setAttribute("d",r)),i.lastPath=s)},MaskElement.prototype.destroy=function(){this.element=null,this.globalData=null,this.maskElement=null,this.data=null,this.masksProperties=null};var filtersFactory=(()=>{var t={createFilter:function(t,e){var i=createNS("filter");i.setAttribute("id",t),!0!==e&&(i.setAttribute("filterUnits","objectBoundingBox"),i.setAttribute("x","0%"),i.setAttribute("y","0%"),i.setAttribute("width","100%"),i.setAttribute("height","100%"));return i},createAlphaToLuminanceFilter:function(){var t=createNS("feColorMatrix");return t.setAttribute("type","matrix"),t.setAttribute("color-interpolation-filters","sRGB"),t.setAttribute("values","0 0 0 1 0  0 0 0 1 0  0 0 0 1 0  0 0 0 1 1"),t}};return t})(),featureSupport=(()=>{var t={maskType:!0,svgLumaHidden:!0,offscreenCanvas:"undefined"!=typeof OffscreenCanvas};return(/MSIE 10/i.test(navigator.userAgent)||/MSIE 9/i.test(navigator.userAgent)||/rv:11.0/i.test(navigator.userAgent)||/Edge\/\d./i.test(navigator.userAgent))&&(t.maskType=!1),/firefox/i.test(navigator.userAgent)&&(t.svgLumaHidden=!1),t})(),registeredEffects$1={},idPrefix="filter_result_";function SVGEffects(t){var e,i="SourceGraphic",r=t.data.ef?t.data.ef.length:0,s=createElementID(),a=filtersFactory.createFilter(s,!0),n=0;for(this.filters=[],e=0;e<r;e+=1){o=null;var o,h=t.data.ef[e].ty;registeredEffects$1[h]&&(o=new registeredEffects$1[h].effect(a,t.effectsManager.effectElements[e],t,idPrefix+n,i),i=idPrefix+n,registeredEffects$1[h].countsAsEffect)&&(n+=1),o&&this.filters.push(o)}n&&(t.globalData.defs.appendChild(a),t.layerElement.setAttribute("filter","url("+getLocationHref()+"#"+s+")")),this.filters.length&&t.addRenderableComponent(this)}function registerEffect$1(t,e,i){registeredEffects$1[t]={effect:e,countsAsEffect:i}}function SVGBaseElement(){}function HierarchyElement(){}function RenderableDOMElement(){}function IImageElement(t,e,i){this.assetData=e.getAssetData(t.refId),this.assetData&&this.assetData.sid&&(this.assetData=e.slotManager.getProp(this.assetData)),this.initElement(t,e,i),this.sourceRect={top:0,left:0,width:this.assetData.w,height:this.assetData.h}}function ProcessedElement(t,e){this.elem=t,this.pos=e}function IShapeElement(){}SVGEffects.prototype.renderFrame=function(t){for(var e=this.filters.length,i=0;i<e;i+=1)this.filters[i].renderFrame(t)},SVGEffects.prototype.getEffects=function(t){for(var e=this.filters.length,i=[],r=0;r<e;r+=1)this.filters[r].type===t&&i.push(this.filters[r]);return i},SVGBaseElement.prototype={initRendererElement:function(){this.layerElement=createNS("g")},createContainerElements:function(){this.matteElement=createNS("g"),this.transformedElement=this.layerElement,this.maskedElement=this.layerElement,this._sizeChanged=!1;var t,e,i,r=null;this.data.td?(this.matteMasks={},(t=createNS("g")).setAttribute("id",this.layerId),t.appendChild(this.layerElement),this.globalData.defs.appendChild(r=t)):this.data.tt?(this.matteElement.appendChild(this.layerElement),r=this.matteElement,this.baseElement=this.matteElement):this.baseElement=this.layerElement,this.data.ln&&this.layerElement.setAttribute("id",this.data.ln),this.data.cl&&this.layerElement.setAttribute("class",this.data.cl),0!==this.data.ty||this.data.hd||(t=createNS("clipPath"),(i=createNS("path")).setAttribute("d","M0,0 L"+this.data.w+",0 L"+this.data.w+","+this.data.h+" L0,"+this.data.h+"z"),e=createElementID(),t.setAttribute("id",e),t.appendChild(i),this.globalData.defs.appendChild(t),this.checkMasks()?((i=createNS("g")).setAttribute("clip-path","url("+getLocationHref()+"#"+e+")"),i.appendChild(this.layerElement),this.transformedElement=i,r?r.appendChild(this.transformedElement):this.baseElement=this.transformedElement):this.layerElement.setAttribute("clip-path","url("+getLocationHref()+"#"+e+")")),0!==this.data.bm&&this.setBlendMode()},renderElement:function(){this.finalTransform._localMatMdf&&this.transformedElement.setAttribute("transform",this.finalTransform.localMat.to2dCSS()),this.finalTransform._opMdf&&this.transformedElement.setAttribute("opacity",this.finalTransform.localOpacity)},destroyBaseElement:function(){this.layerElement=null,this.matteElement=null,this.maskManager.destroy()},getBaseElement:function(){return this.data.hd?null:this.baseElement},createRenderableComponents:function(){this.maskManager=new MaskElement(this.data,this,this.globalData),this.renderableEffectsManager=new SVGEffects(this),this.searchEffectTransforms()},getMatte:function(t){var e,i,r,s,a,n,o,h,l;return this.matteMasks||(this.matteMasks={}),this.matteMasks[t]||(e=this.layerId+"_"+t,1===t||3===t?((n=createNS("mask")).setAttribute("id",e),n.setAttribute("mask-type",3===t?"luminance":"alpha"),(s=createNS("use")).setAttributeNS("http://www.w3.org/1999/xlink","href","#"+this.layerId),n.appendChild(s),this.globalData.defs.appendChild(n),featureSupport.maskType||1!==t||(n.setAttribute("mask-type","luminance"),i=createElementID(),r=filtersFactory.createFilter(i),this.globalData.defs.appendChild(r),r.appendChild(filtersFactory.createAlphaToLuminanceFilter()),(a=createNS("g")).appendChild(s),n.appendChild(a),a.setAttribute("filter","url("+getLocationHref()+"#"+i+")"))):2===t&&((n=createNS("mask")).setAttribute("id",e),n.setAttribute("mask-type","alpha"),o=createNS("g"),n.appendChild(o),i=createElementID(),r=filtersFactory.createFilter(i),(l=createNS("feComponentTransfer")).setAttribute("in","SourceGraphic"),r.appendChild(l),(h=createNS("feFuncA")).setAttribute("type","table"),h.setAttribute("tableValues","1.0 0.0"),l.appendChild(h),this.globalData.defs.appendChild(r),(l=createNS("rect")).setAttribute("width",this.comp.data.w),l.setAttribute("height",this.comp.data.h),l.setAttribute("x","0"),l.setAttribute("y","0"),l.setAttribute("fill","#ffffff"),l.setAttribute("opacity","0"),o.setAttribute("filter","url("+getLocationHref()+"#"+i+")"),o.appendChild(l),(s=createNS("use")).setAttributeNS("http://www.w3.org/1999/xlink","href","#"+this.layerId),o.appendChild(s),featureSupport.maskType||(n.setAttribute("mask-type","luminance"),r.appendChild(filtersFactory.createAlphaToLuminanceFilter()),a=createNS("g"),o.appendChild(l),a.appendChild(this.layerElement),o.appendChild(a)),this.globalData.defs.appendChild(n)),this.matteMasks[t]=e),this.matteMasks[t]},setMatte:function(t){this.matteElement&&this.matteElement.setAttribute("mask","url("+getLocationHref()+"#"+t+")")}},HierarchyElement.prototype={initHierarchy:function(){this.hierarchy=[],this._isParent=!1,this.checkParenting()},setHierarchy:function(t){this.hierarchy=t},setAsParent:function(){this._isParent=!0},checkParenting:function(){void 0!==this.data.parent&&this.comp.buildElementParenting(this,this.data.parent,[])}},extendPrototype([RenderableElement,createProxyFunction({initElement:function(t,e,i){this.initFrame(),this.initBaseData(t,e,i),this.initTransform(t,e,i),this.initHierarchy(),this.initRenderable(),this.initRendererElement(),this.createContainerElements(),this.createRenderableComponents(),this.createContent(),this.hide()},hide:function(){this.hidden||this.isInRange&&!this.isTransparent||((this.baseElement||this.layerElement).style.display="none",this.hidden=!0)},show:function(){this.isInRange&&!this.isTransparent&&(this.data.hd||((this.baseElement||this.layerElement).style.display="block"),this.hidden=!1,this._isFirstFrame=!0)},renderFrame:function(){this.data.hd||this.hidden||(this.renderTransform(),this.renderRenderable(),this.renderLocalTransform(),this.renderElement(),this.renderInnerContent(),this._isFirstFrame&&(this._isFirstFrame=!1))},renderInnerContent:function(){},prepareFrame:function(t){this._mdf=!1,this.prepareRenderableFrame(t),this.prepareProperties(t,this.isInRange),this.checkTransparency()},destroy:function(){this.innerElem=null,this.destroyBaseElement()}})],RenderableDOMElement),extendPrototype([BaseElement,TransformElement,SVGBaseElement,HierarchyElement,FrameElement,RenderableDOMElement],IImageElement),IImageElement.prototype.createContent=function(){var t=this.globalData.getAssetsPath(this.assetData);this.innerElem=createNS("image"),this.innerElem.setAttribute("width",this.assetData.w+"px"),this.innerElem.setAttribute("height",this.assetData.h+"px"),this.innerElem.setAttribute("preserveAspectRatio",this.assetData.pr||this.globalData.renderConfig.imagePreserveAspectRatio),this.innerElem.setAttributeNS("http://www.w3.org/1999/xlink","href",t),this.layerElement.appendChild(this.innerElem)},IImageElement.prototype.sourceRectAtTime=function(){return this.sourceRect},IShapeElement.prototype={addShapeToModifiers:function(t){for(var e=this.shapeModifiers.length,i=0;i<e;i+=1)this.shapeModifiers[i].addShape(t)},isShapeInAnimatedModifiers:function(t){for(var e=this.shapeModifiers.length;0<e;)if(this.shapeModifiers[0].isAnimatedWithShape(t))return!0;return!1},renderModifiers:function(){if(this.shapeModifiers.length){for(var t=this.shapes.length,e=0;e<t;e+=1)this.shapes[e].sh.reset();for(e=(t=this.shapeModifiers.length)-1;0<=e&&!this.shapeModifiers[e].processShapes(this._isFirstFrame);--e);}},searchProcessedElement:function(t){for(var e=this.processedElements,i=0,r=e.length;i<r;){if(e[i].elem===t)return e[i].pos;i+=1}return 0},addProcessedElement:function(t,e){for(var i=this.processedElements,r=i.length;r;)if(i[--r].elem===t)return void(i[r].pos=e);i.push(new ProcessedElement(t,e))},prepareFrame:function(t){this.prepareRenderableFrame(t),this.prepareProperties(t,this.isInRange)}};var lineCapEnum={1:"butt",2:"round",3:"square"},lineJoinEnum={1:"miter",2:"round",3:"bevel"};function SVGShapeData(t,e,i){this.caches=[],this.styles=[],this.transformers=t,this.lStr="",this.sh=i,this.lvl=e,this._isAnimated=!!i.k;for(var r=0,s=t.length;r<s;){if(t[r].mProps.dynamicProperties.length){this._isAnimated=!0;break}r+=1}}function SVGStyleData(t,e){this.data=t,this.type=t.ty,this.d="",this.lvl=e,this._mdf=!1,this.closed=!0===t.hd,this.pElem=createNS("path"),this.msElem=null}function DashProperty(t,e,i,r){this.elem=t,this.frameId=-1,this.dataProps=createSizedArray(e.length),this.renderer=i,this.k=!1,this.dashStr="",this.dashArray=createTypedArray("float32",e.length?e.length-1:0),this.dashoffset=createTypedArray("float32",1),this.initDynamicPropertyContainer(r);for(var s,a=e.length||0,n=0;n<a;n+=1)s=PropertyFactory.getProp(t,e[n].v,0,0,this),this.k=s.k||this.k,this.dataProps[n]={n:e[n].n,p:s};this.k||this.getValue(!0),this._isAnimated=this.k}function SVGStrokeStyleData(t,e,i){this.initDynamicPropertyContainer(t),this.getValue=this.iterateDynamicProperties,this.o=PropertyFactory.getProp(t,e.o,0,.01,this),this.w=PropertyFactory.getProp(t,e.w,0,null,this),this.d=new DashProperty(t,e.d||{},"svg",this),this.c=PropertyFactory.getProp(t,e.c,1,255,this),this.style=i,this._isAnimated=!!this._isAnimated}function SVGFillStyleData(t,e,i){this.initDynamicPropertyContainer(t),this.getValue=this.iterateDynamicProperties,this.o=PropertyFactory.getProp(t,e.o,0,.01,this),this.c=PropertyFactory.getProp(t,e.c,1,255,this),this.style=i}function SVGNoStyleData(t,e,i){this.initDynamicPropertyContainer(t),this.getValue=this.iterateDynamicProperties,this.style=i}function GradientProperty(t,e,i){this.data=e,this.c=createTypedArray("uint8c",4*e.p);var r=e.k.k[0].s?e.k.k[0].s.length-4*e.p:e.k.k.length-4*e.p;this.o=createTypedArray("float32",r),this._cmdf=!1,this._omdf=!1,this._collapsable=this.checkCollapsable(),this._hasOpacity=r,this.initDynamicPropertyContainer(i),this.prop=PropertyFactory.getProp(t,e.k,1,null,this),this.k=this.prop.k,this.getValue(!0)}function SVGGradientFillStyleData(t,e,i){this.initDynamicPropertyContainer(t),this.getValue=this.iterateDynamicProperties,this.initGradientData(t,e,i)}function SVGGradientStrokeStyleData(t,e,i){this.initDynamicPropertyContainer(t),this.getValue=this.iterateDynamicProperties,this.w=PropertyFactory.getProp(t,e.w,0,null,this),this.d=new DashProperty(t,e.d||{},"svg",this),this.initGradientData(t,e,i),this._isAnimated=!!this._isAnimated}function ShapeGroupData(){this.it=[],this.prevViewData=[],this.gr=createNS("g")}function SVGTransformData(t,e,i){this.transform={mProps:t,op:e,container:i},this.elements=[],this._isAnimated=this.transform.mProps.dynamicProperties.length||this.transform.op.effectsSequence.length}SVGShapeData.prototype.setAsAnimated=function(){this._isAnimated=!0},SVGStyleData.prototype.reset=function(){this.d="",this._mdf=!1},DashProperty.prototype.getValue=function(t){if((this.elem.globalData.frameId!==this.frameId||t)&&(this.frameId=this.elem.globalData.frameId,this.iterateDynamicProperties(),this._mdf=this._mdf||t,this._mdf)){var e=0,i=this.dataProps.length;for("svg"===this.renderer&&(this.dashStr=""),e=0;e<i;e+=1)"o"!==this.dataProps[e].n?"svg"===this.renderer?this.dashStr+=" "+this.dataProps[e].p.v:this.dashArray[e]=this.dataProps[e].p.v:this.dashoffset[0]=this.dataProps[e].p.v}},extendPrototype([DynamicPropertyContainer],DashProperty),extendPrototype([DynamicPropertyContainer],SVGStrokeStyleData),extendPrototype([DynamicPropertyContainer],SVGFillStyleData),extendPrototype([DynamicPropertyContainer],SVGNoStyleData),GradientProperty.prototype.comparePoints=function(t,e){for(var i=0,r=this.o.length/2;i<r;){if(.01<Math.abs(t[4*i]-t[4*e+2*i]))return!1;i+=1}return!0},GradientProperty.prototype.checkCollapsable=function(){if(this.o.length/2!=this.c.length/4)return!1;if(this.data.k.k[0].s)for(var t=0,e=this.data.k.k.length;t<e;){if(!this.comparePoints(this.data.k.k[t].s,this.data.p))return!1;t+=1}else if(!this.comparePoints(this.data.k.k,this.data.p))return!1;return!0},GradientProperty.prototype.getValue=function(t){if(this.prop.getValue(),this._mdf=!1,this._cmdf=!1,this._omdf=!1,this.prop._mdf||t){for(var e,i,r=4*this.data.p,s=0;s<r;s+=1)e=s%4==0?100:255,i=Math.round(this.prop.v[s]*e),this.c[s]!==i&&(this.c[s]=i,this._cmdf=!t);if(this.o.length)for(r=this.prop.v.length,s=4*this.data.p;s<r;s+=1)e=s%2==0?100:1,i=s%2==0?Math.round(100*this.prop.v[s]):this.prop.v[s],this.o[s-4*this.data.p]!==i&&(this.o[s-4*this.data.p]=i,this._omdf=!t);this._mdf=!t}},extendPrototype([DynamicPropertyContainer],GradientProperty),SVGGradientFillStyleData.prototype.initGradientData=function(t,e,i){this.o=PropertyFactory.getProp(t,e.o,0,.01,this),this.s=PropertyFactory.getProp(t,e.s,1,null,this),this.e=PropertyFactory.getProp(t,e.e,1,null,this),this.h=PropertyFactory.getProp(t,e.h||{k:0},0,.01,this),this.a=PropertyFactory.getProp(t,e.a||{k:0},0,degToRads,this),this.g=new GradientProperty(t,e.g,this),this.style=i,this.stops=[],this.setGradientData(i.pElem,e),this.setGradientOpacity(e,i),this._isAnimated=!!this._isAnimated},SVGGradientFillStyleData.prototype.setGradientData=function(t,e){for(var i,r=createElementID(),s=createNS(1===e.t?"linearGradient":"radialGradient"),a=(s.setAttribute("id",r),s.setAttribute("spreadMethod","pad"),s.setAttribute("gradientUnits","userSpaceOnUse"),[]),n=4*e.g.p,o=0;o<n;o+=4)i=createNS("stop"),s.appendChild(i),a.push(i);t.setAttribute("gf"===e.ty?"fill":"stroke","url("+getLocationHref()+"#"+r+")"),this.gf=s,this.cst=a},SVGGradientFillStyleData.prototype.setGradientOpacity=function(t,e){if(this.g._hasOpacity&&!this.g._collapsable){for(var i,r,s=createNS("mask"),a=createNS("path"),n=(s.appendChild(a),createElementID()),o=createElementID(),h=(s.setAttribute("id",o),createNS(1===t.t?"linearGradient":"radialGradient")),l=(h.setAttribute("id",n),h.setAttribute("spreadMethod","pad"),h.setAttribute("gradientUnits","userSpaceOnUse"),r=(t.g.k.k[0].s||t.g.k.k).length,this.stops),p=4*t.g.p;p<r;p+=2)(i=createNS("stop")).setAttribute("stop-color","rgb(255,255,255)"),h.appendChild(i),l.push(i);a.setAttribute("gf"===t.ty?"fill":"stroke","url("+getLocationHref()+"#"+n+")"),"gs"===t.ty&&(a.setAttribute("stroke-linecap",lineCapEnum[t.lc||2]),a.setAttribute("stroke-linejoin",lineJoinEnum[t.lj||2]),1===t.lj)&&a.setAttribute("stroke-miterlimit",t.ml),this.of=h,this.ms=s,this.ost=l,this.maskId=o,e.msElem=a}},extendPrototype([DynamicPropertyContainer],SVGGradientFillStyleData),extendPrototype([SVGGradientFillStyleData,DynamicPropertyContainer],SVGGradientStrokeStyleData);var buildShapeString=function(t,e,i,r){if(0===e)return"";for(var s=t.o,a=t.i,n=t.v,o=" M"+r.applyToPointStringified(n[0][0],n[0][1]),h=1;h<e;h+=1)o+=" C"+r.applyToPointStringified(s[h-1][0],s[h-1][1])+" "+r.applyToPointStringified(a[h][0],a[h][1])+" "+r.applyToPointStringified(n[h][0],n[h][1]);return o=i&&e?o+(" C"+r.applyToPointStringified(s[h-1][0],s[h-1][1])+" "+r.applyToPointStringified(a[0][0],a[0][1])+" "+r.applyToPointStringified(n[0][0],n[0][1]))+"z":o},SVGElementsRenderer=(()=>{var u=new Matrix,y=new Matrix;function e(t,e,i){(i||e.transform.op._mdf)&&e.transform.container.setAttribute("opacity",e.transform.op.v),(i||e.transform.mProps._mdf)&&e.transform.container.setAttribute("transform",e.transform.mProps.v.to2dCSS())}function i(){}function r(t,e,i){for(var r,s,a,n,o,h,l,p,m,f=e.styles.length,c=e.lvl,d=0;d<f;d+=1){if(n=e.sh._mdf||i,e.styles[d].lvl<c){for(l=y.reset(),p=c-e.styles[d].lvl,m=e.transformers.length-1;!n&&0<p;)n=e.transformers[m].mProps._mdf||n,--p,--m;if(n)for(p=c-e.styles[d].lvl,m=e.transformers.length-1;0<p;)l.multiply(e.transformers[m].mProps.v),--p,--m}else l=u;if(s=(h=e.sh.paths)._length,n){for(a="",r=0;r<s;r+=1)(o=h.shapes[r])&&o._length&&(a+=buildShapeString(o,o._length,o.c,l));e.caches[d]=a}else a=e.caches[d];e.styles[d].d+=!0===t.hd?"":a,e.styles[d]._mdf=n||e.styles[d]._mdf}}function s(t,e,i){var r=e.style;(e.c._mdf||i)&&r.pElem.setAttribute("fill","rgb("+bmFloor(e.c.v[0])+","+bmFloor(e.c.v[1])+","+bmFloor(e.c.v[2])+")"),(e.o._mdf||i)&&r.pElem.setAttribute("fill-opacity",e.o.v)}function a(t,e,i){n(t,e,i),o(0,e,i)}function n(t,e,i){var r,s,a,n,o=e.gf,h=e.g._hasOpacity,l=e.s.v,p=e.e.v;if((e.o._mdf||i)&&(a="gf"===t.ty?"fill-opacity":"stroke-opacity",e.style.pElem.setAttribute(a,e.o.v)),(e.s._mdf||i)&&(n="x1"==(a=1===t.t?"x1":"cx")?"y1":"cy",o.setAttribute(a,l[0]),o.setAttribute(n,l[1]),h)&&!e.g._collapsable&&(e.of.setAttribute(a,l[0]),e.of.setAttribute(n,l[1])),e.g._cmdf||i)for(var m=e.cst,f=e.g.c,c=m.length,d=0;d<c;d+=1)(r=m[d]).setAttribute("offset",f[4*d]+"%"),r.setAttribute("stop-color","rgb("+f[4*d+1]+","+f[4*d+2]+","+f[4*d+3]+")");if(h&&(e.g._omdf||i)){var u=e.g.o;for(c=(m=e.g._collapsable?e.cst:e.ost).length,d=0;d<c;d+=1)r=m[d],e.g._collapsable||r.setAttribute("offset",u[2*d]+"%"),r.setAttribute("stop-opacity",u[2*d+1])}1===t.t?(e.e._mdf||i)&&(o.setAttribute("x2",p[0]),o.setAttribute("y2",p[1]),h)&&!e.g._collapsable&&(e.of.setAttribute("x2",p[0]),e.of.setAttribute("y2",p[1])):((e.s._mdf||e.e._mdf||i)&&(s=Math.sqrt(Math.pow(l[0]-p[0],2)+Math.pow(l[1]-p[1],2)),o.setAttribute("r",s),h)&&!e.g._collapsable&&e.of.setAttribute("r",s),(e.s._mdf||e.e._mdf||e.h._mdf||e.a._mdf||i)&&(s=s||Math.sqrt(Math.pow(l[0]-p[0],2)+Math.pow(l[1]-p[1],2)),a=Math.atan2(p[1]-l[1],p[0]-l[0]),1<=(n=e.h.v)?n=.99:n<=-1&&(n=-.99),t=s*n,i=Math.cos(a+e.a.v)*t+l[0],p=Math.sin(a+e.a.v)*t+l[1],o.setAttribute("fx",i),o.setAttribute("fy",p),h)&&!e.g._collapsable&&(e.of.setAttribute("fx",i),e.of.setAttribute("fy",p)))}function o(t,e,i){var r=e.style,s=e.d;s&&(s._mdf||i)&&s.dashStr&&(r.pElem.setAttribute("stroke-dasharray",s.dashStr),r.pElem.setAttribute("stroke-dashoffset",s.dashoffset[0])),e.c&&(e.c._mdf||i)&&r.pElem.setAttribute("stroke","rgb("+bmFloor(e.c.v[0])+","+bmFloor(e.c.v[1])+","+bmFloor(e.c.v[2])+")"),(e.o._mdf||i)&&r.pElem.setAttribute("stroke-opacity",e.o.v),(e.w._mdf||i)&&(r.pElem.setAttribute("stroke-width",e.w.v),r.msElem)&&r.msElem.setAttribute("stroke-width",e.w.v)}return{createRenderFunction:function(t){switch(t.ty){case"fl":return s;case"gf":return n;case"gs":return a;case"st":return o;case"sh":case"el":case"rc":case"sr":return r;case"tr":return e;case"no":return i;default:return null}}}})();function SVGShapeElement(t,e,i){this.shapes=[],this.shapesData=t.shapes,this.stylesList=[],this.shapeModifiers=[],this.itemsData=[],this.processedElements=[],this.animatedContents=[],this.initElement(t,e,i),this.prevViewData=[]}function LetterProps(t,e,i,r,s,a){this.o=t,this.sw=e,this.sc=i,this.fc=r,this.m=s,this.p=a,this._mdf={o:!0,sw:!!e,sc:!!i,fc:!!r,m:!0,p:!0}}function TextProperty(t,e){this._frameId=initialDefaultFrame,this.pv="",this.v="",this.kf=!1,this._isFirstFrame=!0,this._mdf=!1,e.d&&e.d.sid&&(e.d=t.globalData.slotManager.getProp(e.d)),this.data=e,this.elem=t,this.comp=this.elem.comp,this.keysIndex=0,this.canResize=!1,this.minimumFontSize=1,this.effectsSequence=[],this.currentData={ascent:0,boxWidth:this.defaultBoxWidth,f:"",fStyle:"",fWeight:"",fc:"",j:"",justifyOffset:"",l:[],lh:0,lineWidths:[],ls:"",of:"",s:"",sc:"",sw:0,t:0,tr:0,sz:0,ps:null,fillColorAnim:!1,strokeColorAnim:!1,strokeWidthAnim:!1,yOffset:0,finalSize:0,finalText:[],finalLineHeight:0,__complete:!1},this.copyData(this.currentData,this.data.d.k[0].s),this.searchProperty()||this.completeTextData(this.currentData)}extendPrototype([BaseElement,TransformElement,SVGBaseElement,IShapeElement,HierarchyElement,FrameElement,RenderableDOMElement],SVGShapeElement),SVGShapeElement.prototype.initSecondaryElement=function(){},SVGShapeElement.prototype.identityMatrix=new Matrix,SVGShapeElement.prototype.buildExpressionInterface=function(){},SVGShapeElement.prototype.createContent=function(){this.searchShapes(this.shapesData,this.itemsData,this.prevViewData,this.layerElement,0,[],!0),this.filterUniqueShapes()},SVGShapeElement.prototype.filterUniqueShapes=function(){for(var t,e,i,r=this.shapes.length,s=this.stylesList.length,a=[],n=!1,o=0;o<s;o+=1){for(i=this.stylesList[o],n=!1,t=a.length=0;t<r;t+=1)-1!==(e=this.shapes[t]).styles.indexOf(i)&&(a.push(e),n=e._isAnimated||n);1<a.length&&n&&this.setShapesAsAnimated(a)}},SVGShapeElement.prototype.setShapesAsAnimated=function(t){for(var e=t.length,i=0;i<e;i+=1)t[i].setAsAnimated()},SVGShapeElement.prototype.createStyleElement=function(t,e){var i,e=new SVGStyleData(t,e),r=e.pElem;return"st"===t.ty?i=new SVGStrokeStyleData(this,t,e):"fl"===t.ty?i=new SVGFillStyleData(this,t,e):"gf"===t.ty||"gs"===t.ty?(i=new("gf"===t.ty?SVGGradientFillStyleData:SVGGradientStrokeStyleData)(this,t,e),this.globalData.defs.appendChild(i.gf),i.maskId&&(this.globalData.defs.appendChild(i.ms),this.globalData.defs.appendChild(i.of),r.setAttribute("mask","url("+getLocationHref()+"#"+i.maskId+")"))):"no"===t.ty&&(i=new SVGNoStyleData(this,t,e)),"st"!==t.ty&&"gs"!==t.ty||(r.setAttribute("stroke-linecap",lineCapEnum[t.lc||2]),r.setAttribute("stroke-linejoin",lineJoinEnum[t.lj||2]),r.setAttribute("fill-opacity","0"),1===t.lj&&r.setAttribute("stroke-miterlimit",t.ml)),2===t.r&&r.setAttribute("fill-rule","evenodd"),t.ln&&r.setAttribute("id",t.ln),t.cl&&r.setAttribute("class",t.cl),t.bm&&(r.style["mix-blend-mode"]=getBlendMode(t.bm)),this.stylesList.push(e),this.addToAnimatedContents(t,i),i},SVGShapeElement.prototype.createGroupElement=function(t){var e=new ShapeGroupData;return t.ln&&e.gr.setAttribute("id",t.ln),t.cl&&e.gr.setAttribute("class",t.cl),t.bm&&(e.gr.style["mix-blend-mode"]=getBlendMode(t.bm)),e},SVGShapeElement.prototype.createTransformElement=function(t,e){var i=TransformPropertyFactory.getTransformProperty(this,t,this),i=new SVGTransformData(i,i.o,e);return this.addToAnimatedContents(t,i),i},SVGShapeElement.prototype.createShapeElement=function(t,e,i){var r=4;"rc"===t.ty?r=5:"el"===t.ty?r=6:"sr"===t.ty&&(r=7);e=new SVGShapeData(e,i,ShapePropertyFactory.getShapeProp(this,t,r,this));return this.shapes.push(e),this.addShapeToModifiers(e),this.addToAnimatedContents(t,e),e},SVGShapeElement.prototype.addToAnimatedContents=function(t,e){for(var i=0,r=this.animatedContents.length;i<r;){if(this.animatedContents[i].element===e)return;i+=1}this.animatedContents.push({fn:SVGElementsRenderer.createRenderFunction(t),element:e,data:t})},SVGShapeElement.prototype.setElementStyles=function(t){for(var e=t.styles,i=this.stylesList.length,r=0;r<i;r+=1)-1!==e.indexOf(this.stylesList[r])||this.stylesList[r].closed||e.push(this.stylesList[r])},SVGShapeElement.prototype.reloadShapes=function(){this._isFirstFrame=!0;for(var t=this.itemsData.length,e=0;e<t;e+=1)this.prevViewData[e]=this.itemsData[e];for(this.searchShapes(this.shapesData,this.itemsData,this.prevViewData,this.layerElement,0,[],!0),this.filterUniqueShapes(),t=this.dynamicProperties.length,e=0;e<t;e+=1)this.dynamicProperties[e].getValue();this.renderModifiers()},SVGShapeElement.prototype.searchShapes=function(t,e,i,r,s,a,n){for(var o,h,l,p,m,f=[].concat(a),c=t.length-1,d=[],u=[],y=c;0<=y;--y){if((m=this.searchProcessedElement(t[y]))?e[y]=i[m-1]:t[y]._render=n,"fl"===t[y].ty||"st"===t[y].ty||"gf"===t[y].ty||"gs"===t[y].ty||"no"===t[y].ty)m?e[y].style.closed=t[y].hd:e[y]=this.createStyleElement(t[y],s),t[y]._render&&e[y].style.pElem.parentNode!==r&&r.appendChild(e[y].style.pElem),d.push(e[y].style);else if("gr"===t[y].ty){if(m)for(h=e[y].it.length,o=0;o<h;o+=1)e[y].prevViewData[o]=e[y].it[o];else e[y]=this.createGroupElement(t[y]);this.searchShapes(t[y].it,e[y].it,e[y].prevViewData,e[y].gr,s+1,f,n),t[y]._render&&e[y].gr.parentNode!==r&&r.appendChild(e[y].gr)}else"tr"===t[y].ty?(m||(e[y]=this.createTransformElement(t[y],r)),l=e[y].transform,f.push(l)):"sh"===t[y].ty||"rc"===t[y].ty||"el"===t[y].ty||"sr"===t[y].ty?(m||(e[y]=this.createShapeElement(t[y],f,s)),this.setElementStyles(e[y])):"tm"===t[y].ty||"rd"===t[y].ty||"ms"===t[y].ty||"pb"===t[y].ty||"zz"===t[y].ty||"op"===t[y].ty?(m?(p=e[y]).closed=!1:((p=ShapeModifiers.getModifier(t[y].ty)).init(this,t[y]),e[y]=p,this.shapeModifiers.push(p)),u.push(p)):"rp"===t[y].ty&&(m?(p=e[y]).closed=!0:(p=ShapeModifiers.getModifier(t[y].ty),(e[y]=p).init(this,t,y,e),this.shapeModifiers.push(p),n=!1),u.push(p));this.addProcessedElement(t[y],y+1)}for(c=d.length,y=0;y<c;y+=1)d[y].closed=!0;for(c=u.length,y=0;y<c;y+=1)u[y].closed=!0},SVGShapeElement.prototype.renderInnerContent=function(){this.renderModifiers();for(var t=this.stylesList.length,e=0;e<t;e+=1)this.stylesList[e].reset();for(this.renderShape(),e=0;e<t;e+=1)(this.stylesList[e]._mdf||this._isFirstFrame)&&(this.stylesList[e].msElem&&(this.stylesList[e].msElem.setAttribute("d",this.stylesList[e].d),this.stylesList[e].d="M0 0"+this.stylesList[e].d),this.stylesList[e].pElem.setAttribute("d",this.stylesList[e].d||"M0 0"))},SVGShapeElement.prototype.renderShape=function(){for(var t,e=this.animatedContents.length,i=0;i<e;i+=1)t=this.animatedContents[i],(this._isFirstFrame||t.element._isAnimated)&&!0!==t.data&&t.fn(t.data,t.element,this._isFirstFrame)},SVGShapeElement.prototype.destroy=function(){this.destroyBaseElement(),this.shapesData=null,this.itemsData=null},LetterProps.prototype.update=function(t,e,i,r,s,a){this._mdf.o=!1,this._mdf.sw=!1,this._mdf.sc=!1,this._mdf.fc=!1,this._mdf.m=!1;var n=this._mdf.p=!1;return this.o!==t&&(this.o=t,n=this._mdf.o=!0),this.sw!==e&&(this.sw=e,n=this._mdf.sw=!0),this.sc!==i&&(this.sc=i,n=this._mdf.sc=!0),this.fc!==r&&(this.fc=r,n=this._mdf.fc=!0),this.m!==s&&(this.m=s,n=this._mdf.m=!0),!a.length||this.p[0]===a[0]&&this.p[1]===a[1]&&this.p[4]===a[4]&&this.p[5]===a[5]&&this.p[12]===a[12]&&this.p[13]===a[13]||(this.p=a,n=this._mdf.p=!0),n},TextProperty.prototype.defaultBoxWidth=[0,0],TextProperty.prototype.copyData=function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t},TextProperty.prototype.setCurrentData=function(t){t.__complete||this.completeTextData(t),this.currentData=t,this.currentData.boxWidth=this.currentData.boxWidth||this.defaultBoxWidth,this._mdf=!0},TextProperty.prototype.searchProperty=function(){return this.searchKeyframes()},TextProperty.prototype.searchKeyframes=function(){return this.kf=1<this.data.d.k.length,this.kf&&this.addEffect(this.getKeyframeValue.bind(this)),this.kf},TextProperty.prototype.addEffect=function(t){this.effectsSequence.push(t),this.elem.addDynamicProperty(this)},TextProperty.prototype.getValue=function(t){if(this.elem.globalData.frameId!==this.frameId&&this.effectsSequence.length||t){this.currentData.t=this.data.d.k[this.keysIndex].s.t;var e=this.currentData,i=this.keysIndex;if(this.lock)this.setCurrentData(this.currentData);else{this.lock=!0,this._mdf=!1;for(var r=this.effectsSequence.length,s=t||this.data.d.k[this.keysIndex].s,a=0;a<r;a+=1)s=i!==this.keysIndex?this.effectsSequence[a](s,s.t):this.effectsSequence[a](this.currentData,s.t);e!==s&&this.setCurrentData(s),this.v=this.currentData,this.pv=this.v,this.lock=!1,this.frameId=this.elem.globalData.frameId}}},TextProperty.prototype.getKeyframeValue=function(){for(var t=this.data.d.k,e=this.elem.comp.renderedFrame,i=0,r=t.length;i<=r-1&&!(i===r-1||t[i+1].t>e);)i+=1;return this.keysIndex!==i&&(this.keysIndex=i),this.data.d.k[this.keysIndex].s},TextProperty.prototype.buildFinalText=function(t){for(var e,i,r=[],s=0,a=t.length,n=!1,o=!1,h="";s<a;)n=o,o=!1,e=t.charCodeAt(s),h=t.charAt(s),FontManager.isCombinedCharacter(e)?n=!0:55296<=e&&e<=56319?FontManager.isRegionalFlag(t,s)?h=t.substr(s,14):56320<=(i=t.charCodeAt(s+1))&&i<=57343&&(FontManager.isModifier(e,i)?(h=t.substr(s,2),n=!0):h=FontManager.isFlagEmoji(t.substr(s,4))?t.substr(s,4):t.substr(s,2)):56319<e?(i=t.charCodeAt(s+1),FontManager.isVariationSelector(e)&&(n=!0)):FontManager.isZeroWidthJoiner(e)&&(o=n=!0),n?(r[r.length-1]+=h,n=!1):r.push(h),s+=h.length;return r},TextProperty.prototype.completeTextData=function(t){t.__complete=!0;var e,i,r,s,a=this.elem.globalData.fontManager,n=this.data,o=[],h=0,l=n.m.g,p=0,m=0,f=0,c=[],d=0,u=0,y=a.getFontByName(t.f),g=0,v=getFontProperties(y),b=(t.fWeight=v.weight,t.fStyle=v.style,t.finalSize=t.s,t.finalText=this.buildFinalText(t.t),S=t.finalText.length,t.finalLineHeight=t.lh,t.tr/1e3*t.finalSize);if(t.sz)for(var x=!0,R=t.sz[0],L=t.sz[1];x;){for(var P,E=0,d=0,S=(P=this.buildFinalText(t.t)).length,b=t.tr/1e3*t.finalSize,C=-1,_=0;_<S;_+=1)s=P[_].charCodeAt(0),e=!1," "===P[_]?C=_:13!==s&&3!==s||(e=!(d=0),E+=t.finalLineHeight||1.2*t.finalSize),d=R<d+(g=a.chars?(r=a.getCharData(P[_],y.fStyle,y.fFamily),e?0:r.w*t.finalSize/100):a.measureText(P[_],t.f,t.finalSize))&&" "!==P[_]?(-1===C?S+=1:_=C,E+=t.finalLineHeight||1.2*t.finalSize,P.splice(_,C===_?1:0,"\r"),C=-1,0):d+g+b;E+=y.ascent*t.finalSize/100,this.canResize&&t.finalSize>this.minimumFontSize&&L<E?(--t.finalSize,t.finalLineHeight=t.finalSize*t.lh/t.s):(t.finalText=P,S=t.finalText.length,x=!1)}d=-b;var A,g=0,T=0;for(_=0;_<S;_+=1)if(e=!1,13===(s=(A=t.finalText[_]).charCodeAt(0))||3===s?(T=0,c.push(d),u=u<d?d:u,d=-2*b,e=!(i=""),f+=1):i=A,g=a.chars?(r=a.getCharData(A,y.fStyle,a.getFontByName(t.f).fFamily),e?0:r.w*t.finalSize/100):a.measureText(i,t.f,t.finalSize)," "===A?T+=g+b:(d+=g+b+T,T=0),o.push({l:g,an:g,add:p,n:e,anIndexes:[],val:i,line:f,animatorJustifyOffset:0}),2==l){if(p+=g,""===i||" "===i||_===S-1){for(""!==i&&" "!==i||(p-=g);m<=_;)o[m].an=p,o[m].ind=h,o[m].extra=g,m+=1;h+=1,p=0}}else if(3==l){if(p+=g,""===i||_===S-1){for(""===i&&(p-=g);m<=_;)o[m].an=p,o[m].ind=h,o[m].extra=g,m+=1;p=0,h+=1}}else o[h].ind=h,o[h].extra=0,h+=1;if(t.l=o,u=u<d?d:u,c.push(d),t.sz)t.boxWidth=t.sz[0],t.justifyOffset=0;else switch(t.boxWidth=u,t.j){case 1:t.justifyOffset=-t.boxWidth;break;case 2:t.justifyOffset=-t.boxWidth/2;break;default:t.justifyOffset=0}t.lineWidths=c;for(var M,k,D,F,w=n.a,z=w.length,I=[],V=0;V<z;V+=1){for((M=w[V]).a.sc&&(t.strokeColorAnim=!0),M.a.sw&&(t.strokeWidthAnim=!0),(M.a.fc||M.a.fh||M.a.fs||M.a.fb)&&(t.fillColorAnim=!0),D=M.s.b,_=F=0;_<S;_+=1)(k=o[_]).anIndexes[V]=F,(1==D&&""!==k.val||2==D&&""!==k.val&&" "!==k.val||3==D&&(k.n||" "==k.val||_==S-1)||4==D&&(k.n||_==S-1))&&(1===M.s.rn&&I.push(F),F+=1);n.a[V].s.totalChars=F;var B,G=-1;if(1===M.s.rn)for(_=0;_<S;_+=1)G!=(k=o[_]).anIndexes[V]&&(G=k.anIndexes[V],B=I.splice(Math.floor(Math.random()*I.length),1)[0]),k.anIndexes[V]=B}t.yOffset=t.finalLineHeight||1.2*t.finalSize,t.ls=t.ls||0,t.ascent=y.ascent*t.finalSize/100},TextProperty.prototype.updateDocumentData=function(t,e){e=void 0===e?this.keysIndex:e;var i=this.copyData({},this.data.d.k[e].s),i=this.copyData(i,t);this.data.d.k[e].s=i,this.recalculate(e),this.setCurrentData(i),this.elem.addDynamicProperty(this)},TextProperty.prototype.recalculate=function(t){t=this.data.d.k[t].s;t.__complete=!1,this.keysIndex=0,this._isFirstFrame=!0,this.getValue(t)},TextProperty.prototype.canResizeFont=function(t){this.canResize=t,this.recalculate(this.keysIndex),this.elem.addDynamicProperty(this)},TextProperty.prototype.setMinimumFontSize=function(t){this.minimumFontSize=Math.floor(t)||1,this.recalculate(this.keysIndex),this.elem.addDynamicProperty(this)};var TextSelectorProp=(()=>{var o=Math.max,h=Math.min,l=Math.floor;function r(t,e){this._currentTextLength=-1,this.k=!1,this.data=e,this.elem=t,this.comp=t.comp,this.finalS=0,this.finalE=0,this.initDynamicPropertyContainer(t),this.s=PropertyFactory.getProp(t,e.s||{k:0},0,0,this),"e"in e?this.e=PropertyFactory.getProp(t,e.e,0,0,this):this.e={v:100},this.o=PropertyFactory.getProp(t,e.o||{k:0},0,0,this),this.xe=PropertyFactory.getProp(t,e.xe||{k:0},0,0,this),this.ne=PropertyFactory.getProp(t,e.ne||{k:0},0,0,this),this.sm=PropertyFactory.getProp(t,e.sm||{k:100},0,0,this),this.a=PropertyFactory.getProp(t,e.a,0,.01,this),this.dynamicProperties.length||this.getValue()}return r.prototype={getMult:function(t){this._currentTextLength!==this.elem.textProperty.currentData.l.length&&this.getValue();var e,i=0,r=0,s=1,a=1,i=(0<this.ne.v?i=this.ne.v/100:r=-this.ne.v/100,0<this.xe.v?s=1-this.xe.v/100:a=1+this.xe.v/100,BezierFactory.getBezierEasing(i,r,s,a).get),r=0,s=this.finalS,a=this.finalE,n=this.data.sh;return r=2===n?i(r=a===s?a<=t?1:0:o(0,h(.5/(a-s)+(t-s)/(a-s),1))):3===n?i(r=a===s?a<=t?0:1:1-o(0,h(.5/(a-s)+(t-s)/(a-s),1))):4===n?(a===s?r=0:(r=o(0,h(.5/(a-s)+(t-s)/(a-s),1)))<.5?r*=2:r=1-2*(r-.5),i(r)):i(r=5===n?a===s?0:(e=-(i=a-s)/2+(t=h(o(0,t+.5-s),a-s)),i=i/2,Math.sqrt(1-e*e/(i*i))):6===n?a===s?0:(t=h(o(0,t+.5-s),a-s),(1+Math.cos(Math.PI+2*Math.PI*t/(a-s)))/2):t>=l(s)?o(0,h(t-s<0?h(a,1)-(s-t):a-t,1)):r),100!==this.sm.v&&(r<(i=.5-.5*(e=0===(e=.01*this.sm.v)?1e-8:e))?r=0:1<(r=(r-i)/e)&&(r=1)),r*this.a.v},getValue:function(t){this.iterateDynamicProperties(),this._mdf=t||this._mdf,this._currentTextLength=this.elem.textProperty.currentData.l.length||0,t&&2===this.data.r&&(this.e.v=this._currentTextLength);var t=2===this.data.r?1:100/this.data.totalChars,e=this.o.v/t,i=this.s.v/t+e,t=this.e.v/t+e;t<i&&(e=i,i=t,t=e),this.finalS=i,this.finalE=t}},extendPrototype([DynamicPropertyContainer],r),{getTextSelectorProp:function(t,e,i){return new r(t,e)}}})();function TextAnimatorDataProperty(t,e,i){var r={propType:!1},s=PropertyFactory.getProp,a=e.a;this.a={r:a.r?s(t,a.r,0,degToRads,i):r,rx:a.rx?s(t,a.rx,0,degToRads,i):r,ry:a.ry?s(t,a.ry,0,degToRads,i):r,sk:a.sk?s(t,a.sk,0,degToRads,i):r,sa:a.sa?s(t,a.sa,0,degToRads,i):r,s:a.s?s(t,a.s,1,.01,i):r,a:a.a?s(t,a.a,1,0,i):r,o:a.o?s(t,a.o,0,.01,i):r,p:a.p?s(t,a.p,1,0,i):r,sw:a.sw?s(t,a.sw,0,0,i):r,sc:a.sc?s(t,a.sc,1,0,i):r,fc:a.fc?s(t,a.fc,1,0,i):r,fh:a.fh?s(t,a.fh,0,0,i):r,fs:a.fs?s(t,a.fs,0,.01,i):r,fb:a.fb?s(t,a.fb,0,.01,i):r,t:a.t?s(t,a.t,0,0,i):r},this.s=TextSelectorProp.getTextSelectorProp(t,e.s,i),this.s.t=e.s.t}function TextAnimatorProperty(t,e,i){this._isFirstFrame=!0,this._hasMaskedPath=!1,this._frameId=-1,this._textData=t,this._renderType=e,this._elem=i,this._animatorsData=createSizedArray(this._textData.a.length),this._pathData={},this._moreOptions={alignment:{}},this.renderedLetters=[],this.lettersChangedFlag=!1,this.initDynamicPropertyContainer(i)}function ITextElement(){}TextAnimatorProperty.prototype.searchProperties=function(){for(var t,e=this._textData.a.length,i=PropertyFactory.getProp,r=0;r<e;r+=1)t=this._textData.a[r],this._animatorsData[r]=new TextAnimatorDataProperty(this._elem,t,this);this._textData.p&&"m"in this._textData.p?(this._pathData={a:i(this._elem,this._textData.p.a,0,0,this),f:i(this._elem,this._textData.p.f,0,0,this),l:i(this._elem,this._textData.p.l,0,0,this),r:i(this._elem,this._textData.p.r,0,0,this),p:i(this._elem,this._textData.p.p,0,0,this),m:this._elem.maskManager.getMaskProperty(this._textData.p.m)},this._hasMaskedPath=!0):this._hasMaskedPath=!1,this._moreOptions.alignment=i(this._elem,this._textData.m.a,1,0,this)},TextAnimatorProperty.prototype.getMeasures=function(t,R){if(this.lettersChangedFlag=R,this._mdf||this._isFirstFrame||R||this._hasMaskedPath&&this._pathData.m._mdf){this._isFirstFrame=!1;var e,i,r,L,s,a,n,o,h,z,l,G,p,m=this._moreOptions.alignment.v,f=this._animatorsData,c=this._textData,d=this.mHelper,O=this._renderType,N=this.renderedLetters.length,u=t.l;if(this._hasMaskedPath){if(p=this._pathData.m,!this._pathData.n||this._pathData._mdf){for(var y,g=p.v,v={tLength:0,segments:[]},b=(g=this._pathData.r.v?g.reverse():g)._length-1,x=l=0;x<b;x+=1)y=bez.buildBezierData(g.v[x],g.v[x+1],[g.o[x][0]-g.v[x][0],g.o[x][1]-g.v[x][1]],[g.i[x+1][0]-g.v[x+1][0],g.i[x+1][1]-g.v[x+1][1]]),v.tLength+=y.segmentLength,v.segments.push(y),l+=y.segmentLength;x=b,p.v.c&&(y=bez.buildBezierData(g.v[x],g.v[0],[g.o[x][0]-g.v[x][0],g.o[x][1]-g.v[x][1]],[g.i[0][0]-g.v[0][0],g.i[0][1]-g.v[0][1]]),v.tLength+=y.segmentLength,v.segments.push(y),l+=y.segmentLength),this._pathData.pi=v}if(v=this._pathData.pi,e=this._pathData.f.v,s=1,L=!(r=a=0),h=v.segments,e<0&&p.v.c)for(v.tLength<Math.abs(e)&&(e=-Math.abs(e)%v.tLength),s=(o=h[a=h.length-1].points).length-1;e<0;)e+=o[s].partialLength,--s<0&&(s=(o=h[--a].points).length-1);n=(o=h[a].points)[s-1],z=(i=o[s]).partialLength}b=u.length;var P,H,E,S,j,q,C,_,A,T,M,W,$,Y,J,X,k=0,Z=0,K=1.2*t.finalSize*.714,U=!0,D=f.length,F=-1,Q=e,tt=a,et=s,it=-1,rt="",st=this.defaultPropsArray;if(2===t.j||1===t.j){var w=0,at=0,nt=2===t.j?-.5:-1,I=0,ot=!0;for(x=0;x<b;x+=1)if(u[x].n){for(w&&(w+=at);I<x;)u[I].animatorJustifyOffset=w,I+=1;ot=!(w=0)}else{for(B=0;B<D;B+=1)(P=f[B].a).t.propType&&(ot&&2===t.j&&(at+=P.t.v*nt),(E=f[B].s.getMult(u[x].anIndexes[B],c.a[B].s.totalChars)).length?w+=P.t.v*E[0]*nt:w+=P.t.v*E*nt);ot=!1}for(w&&(w+=at);I<x;)u[I].animatorJustifyOffset=w,I+=1}for(x=0;x<b;x+=1){if(d.reset(),C=1,u[x].n)k=0,Z=Z+t.yOffset+(U?1:0),e=Q,U=!1,this._hasMaskedPath&&(n=(o=h[a=tt].points)[(s=et)-1],z=(i=o[s]).partialLength,r=0),J=W=Y=rt="",st=this.defaultPropsArray;else{if(this._hasMaskedPath){if(it!==u[x].line){switch(t.j){case 1:e+=l-t.lineWidths[u[x].line];break;case 2:e+=(l-t.lineWidths[u[x].line])/2}it=u[x].line}F!==u[x].ind&&(u[F]&&(e+=u[F].extra),e+=u[x].an/2,F=u[x].ind),e+=m[0]*u[x].an*.005;for(var V=0,B=0;B<D;B+=1)(P=f[B].a).p.propType&&((E=f[B].s.getMult(u[x].anIndexes[B],c.a[B].s.totalChars)).length?V+=P.p.v[0]*E[0]:V+=P.p.v[0]*E),P.a.propType&&((E=f[B].s.getMult(u[x].anIndexes[B],c.a[B].s.totalChars)).length?V+=P.a.v[0]*E[0]:V+=P.a.v[0]*E);for(L=!0,this._pathData.a.v&&(e=.5*u[0].an+(l-this._pathData.f.v-.5*u[0].an-.5*u[u.length-1].an)*F/(b-1),e+=this._pathData.f.v);L;)e+V<=r+z||!o?(G=(e+V-r)/i.partialLength,j=n.point[0]+(i.point[0]-n.point[0])*G,q=n.point[1]+(i.point[1]-n.point[1])*G,d.translate(-m[0]*u[x].an*.005,-m[1]*K*.01),L=!1):o&&(r+=i.partialLength,(s+=1)>=o.length&&(s=0,o=h[a+=1]?h[a].points:p.v.c?h[a=s=0].points:(r-=i.partialLength,null)),o)&&(n=i,z=(i=o[s]).partialLength);S=u[x].an/2-u[x].add,d.translate(-S,0,0)}else S=u[x].an/2-u[x].add,d.translate(-S,0,0),d.translate(-m[0]*u[x].an*.005,-m[1]*K*.01,0);for(B=0;B<D;B+=1)(P=f[B].a).t.propType&&(E=f[B].s.getMult(u[x].anIndexes[B],c.a[B].s.totalChars),0===k&&0===t.j||(this._hasMaskedPath?E.length?e+=P.t.v*E[0]:e+=P.t.v*E:E.length?k+=P.t.v*E[0]:k+=P.t.v*E));for(t.strokeWidthAnim&&(A=t.sw||0),t.strokeColorAnim&&(_=t.sc?[t.sc[0],t.sc[1],t.sc[2]]:[0,0,0]),t.fillColorAnim&&t.fc&&(T=[t.fc[0],t.fc[1],t.fc[2]]),B=0;B<D;B+=1)(P=f[B].a).a.propType&&((E=f[B].s.getMult(u[x].anIndexes[B],c.a[B].s.totalChars)).length?d.translate(-P.a.v[0]*E[0],-P.a.v[1]*E[1],P.a.v[2]*E[2]):d.translate(-P.a.v[0]*E,-P.a.v[1]*E,P.a.v[2]*E));for(B=0;B<D;B+=1)(P=f[B].a).s.propType&&((E=f[B].s.getMult(u[x].anIndexes[B],c.a[B].s.totalChars)).length?d.scale(1+(P.s.v[0]-1)*E[0],1+(P.s.v[1]-1)*E[1],1):d.scale(1+(P.s.v[0]-1)*E,1+(P.s.v[1]-1)*E,1));for(B=0;B<D;B+=1){if(P=f[B].a,E=f[B].s.getMult(u[x].anIndexes[B],c.a[B].s.totalChars),P.sk.propType&&(E.length?d.skewFromAxis(-P.sk.v*E[0],P.sa.v*E[1]):d.skewFromAxis(-P.sk.v*E,P.sa.v*E)),P.r.propType&&(E.length?d.rotateZ(-P.r.v*E[2]):d.rotateZ(-P.r.v*E)),P.ry.propType&&(E.length?d.rotateY(P.ry.v*E[1]):d.rotateY(P.ry.v*E)),P.rx.propType&&(E.length?d.rotateX(P.rx.v*E[0]):d.rotateX(P.rx.v*E)),P.o.propType&&(E.length?C+=(P.o.v*E[0]-C)*E[0]:C+=(P.o.v*E-C)*E),t.strokeWidthAnim&&P.sw.propType&&(E.length?A+=P.sw.v*E[0]:A+=P.sw.v*E),t.strokeColorAnim&&P.sc.propType)for(M=0;M<3;M+=1)E.length?_[M]+=(P.sc.v[M]-_[M])*E[0]:_[M]+=(P.sc.v[M]-_[M])*E;if(t.fillColorAnim&&t.fc){if(P.fc.propType)for(M=0;M<3;M+=1)E.length?T[M]+=(P.fc.v[M]-T[M])*E[0]:T[M]+=(P.fc.v[M]-T[M])*E;P.fh.propType&&(T=E.length?addHueToRGB(T,P.fh.v*E[0]):addHueToRGB(T,P.fh.v*E)),P.fs.propType&&(T=E.length?addSaturationToRGB(T,P.fs.v*E[0]):addSaturationToRGB(T,P.fs.v*E)),P.fb.propType&&(T=E.length?addBrightnessToRGB(T,P.fb.v*E[0]):addBrightnessToRGB(T,P.fb.v*E))}}for(B=0;B<D;B+=1)(P=f[B].a).p.propType&&(E=f[B].s.getMult(u[x].anIndexes[B],c.a[B].s.totalChars),this._hasMaskedPath?E.length?d.translate(0,P.p.v[1]*E[0],-P.p.v[2]*E[1]):d.translate(0,P.p.v[1]*E,-P.p.v[2]*E):E.length?d.translate(P.p.v[0]*E[0],P.p.v[1]*E[1],-P.p.v[2]*E[2]):d.translate(P.p.v[0]*E,P.p.v[1]*E,-P.p.v[2]*E));if(t.strokeWidthAnim&&(W=A<0?0:A),t.strokeColorAnim&&($="rgb("+Math.round(255*_[0])+","+Math.round(255*_[1])+","+Math.round(255*_[2])+")"),t.fillColorAnim&&t.fc&&(Y="rgb("+Math.round(255*T[0])+","+Math.round(255*T[1])+","+Math.round(255*T[2])+")"),this._hasMaskedPath)d.translate(0,-t.ls),d.translate(0,m[1]*K*.01+Z,0),this._pathData.p.v&&(X=(i.point[1]-n.point[1])/(i.point[0]-n.point[0]),X=180*Math.atan(X)/Math.PI,i.point[0]<n.point[0]&&(X+=180),d.rotate(-X*Math.PI/180)),d.translate(j,q,0),e-=m[0]*u[x].an*.005,u[x+1]&&F!==u[x+1].ind&&(e=(e+=u[x].an/2)+.001*t.tr*t.finalSize);else{switch(d.translate(k,Z,0),t.ps&&d.translate(t.ps[0],t.ps[1]+t.ascent,0),t.j){case 1:d.translate(u[x].animatorJustifyOffset+t.justifyOffset+(t.boxWidth-t.lineWidths[u[x].line]),0,0);break;case 2:d.translate(u[x].animatorJustifyOffset+t.justifyOffset+(t.boxWidth-t.lineWidths[u[x].line])/2,0,0)}d.translate(0,-t.ls),d.translate(S,0,0),d.translate(m[0]*u[x].an*.005,m[1]*K*.01,0),k+=u[x].l+.001*t.tr*t.finalSize}"html"===O?rt=d.toCSS():"svg"===O?rt=d.to2dCSS():st=[d.props[0],d.props[1],d.props[2],d.props[3],d.props[4],d.props[5],d.props[6],d.props[7],d.props[8],d.props[9],d.props[10],d.props[11],d.props[12],d.props[13],d.props[14],d.props[15]],J=C}N<=x?(H=new LetterProps(J,W,$,Y,rt,st),this.renderedLetters.push(H),N+=1,this.lettersChangedFlag=!0):(H=this.renderedLetters[x],this.lettersChangedFlag=H.update(J,W,$,Y,rt,st)||this.lettersChangedFlag)}}},TextAnimatorProperty.prototype.getValue=function(){this._elem.globalData.frameId!==this._frameId&&(this._frameId=this._elem.globalData.frameId,this.iterateDynamicProperties())},TextAnimatorProperty.prototype.mHelper=new Matrix,TextAnimatorProperty.prototype.defaultPropsArray=[],extendPrototype([DynamicPropertyContainer],TextAnimatorProperty),ITextElement.prototype.initElement=function(t,e,i){this.lettersChangedFlag=!0,this.initFrame(),this.initBaseData(t,e,i),this.textProperty=new TextProperty(this,t.t,this.dynamicProperties),this.textAnimator=new TextAnimatorProperty(t.t,this.renderType,this),this.initTransform(t,e,i),this.initHierarchy(),this.initRenderable(),this.initRendererElement(),this.createContainerElements(),this.createRenderableComponents(),this.createContent(),this.hide(),this.textAnimator.searchProperties(this.dynamicProperties)},ITextElement.prototype.prepareFrame=function(t){this._mdf=!1,this.prepareRenderableFrame(t),this.prepareProperties(t,this.isInRange)},ITextElement.prototype.createPathShape=function(t,e){for(var i,r=e.length,s="",a=0;a<r;a+=1)"sh"===e[a].ty&&(i=e[a].ks.k,s+=buildShapeString(i,i.i.length,!0,t));return s},ITextElement.prototype.updateDocumentData=function(t,e){this.textProperty.updateDocumentData(t,e)},ITextElement.prototype.canResizeFont=function(t){this.textProperty.canResizeFont(t)},ITextElement.prototype.setMinimumFontSize=function(t){this.textProperty.setMinimumFontSize(t)},ITextElement.prototype.applyTextPropertiesToMatrix=function(t,e,i,r,s){switch(t.ps&&e.translate(t.ps[0],t.ps[1]+t.ascent,0),e.translate(0,-t.ls,0),t.j){case 1:e.translate(t.justifyOffset+(t.boxWidth-t.lineWidths[i]),0,0);break;case 2:e.translate(t.justifyOffset+(t.boxWidth-t.lineWidths[i])/2,0,0)}e.translate(r,s,0)},ITextElement.prototype.buildColor=function(t){return"rgb("+Math.round(255*t[0])+","+Math.round(255*t[1])+","+Math.round(255*t[2])+")"},ITextElement.prototype.emptyProp=new LetterProps,ITextElement.prototype.destroy=function(){},ITextElement.prototype.validateText=function(){(this.textProperty._mdf||this.textProperty._isFirstFrame)&&(this.buildNewText(),this.textProperty._isFirstFrame=!1,this.textProperty._mdf=!1)};var emptyShapeData={shapes:[]};function SVGTextLottieElement(t,e,i){this.textSpans=[],this.renderType="svg",this.initElement(t,e,i)}function ISolidElement(t,e,i){this.initElement(t,e,i)}function NullElement(t,e,i){this.initFrame(),this.initBaseData(t,e,i),this.initFrame(),this.initTransform(t,e,i),this.initHierarchy()}function SVGRendererBase(){}function ICompElement(){}function SVGCompElement(t,e,i){this.layers=t.layers,this.supports3d=!0,this.completeLayers=!1,this.pendingElements=[],this.elements=this.layers?createSizedArray(this.layers.length):[],this.initElement(t,e,i),this.tm=t.tm?PropertyFactory.getProp(this,t.tm,0,e.frameRate,this):{_placeholder:!0}}function SVGRenderer(t,e){this.animationItem=t,this.layers=null,this.renderedFrame=-1,this.svgElement=createNS("svg");var t="",i=(e&&e.title&&(i=createNS("title"),r=createElementID(),i.setAttribute("id",r),i.textContent=e.title,this.svgElement.appendChild(i),t+=r),e&&e.description&&(i=createNS("desc"),r=createElementID(),i.setAttribute("id",r),i.textContent=e.description,this.svgElement.appendChild(i),t+=" "+r),t&&this.svgElement.setAttribute("aria-labelledby",t),createNS("defs")),r=(this.svgElement.appendChild(i),createNS("g"));this.svgElement.appendChild(r),this.layerElement=r,this.renderConfig={preserveAspectRatio:e&&e.preserveAspectRatio||"xMidYMid meet",imagePreserveAspectRatio:e&&e.imagePreserveAspectRatio||"xMidYMid slice",contentVisibility:e&&e.contentVisibility||"visible",progressiveLoad:e&&e.progressiveLoad||!1,hideOnTransparent:!(e&&!1===e.hideOnTransparent),viewBoxOnly:e&&e.viewBoxOnly||!1,viewBoxSize:e&&e.viewBoxSize||!1,className:e&&e.className||"",id:e&&e.id||"",focusable:e&&e.focusable,filterSize:{width:e&&e.filterSize&&e.filterSize.width||"100%",height:e&&e.filterSize&&e.filterSize.height||"100%",x:e&&e.filterSize&&e.filterSize.x||"0%",y:e&&e.filterSize&&e.filterSize.y||"0%"},width:e&&e.width,height:e&&e.height,runExpressions:!e||void 0===e.runExpressions||e.runExpressions},this.globalData={_mdf:!1,frameNum:-1,defs:i,renderConfig:this.renderConfig},this.elements=[],this.pendingElements=[],this.destroyed=!1,this.rendererType="svg"}function ShapeTransformManager(){this.sequences={},this.sequenceList=[],this.transform_key_count=0}extendPrototype([BaseElement,TransformElement,SVGBaseElement,HierarchyElement,FrameElement,RenderableDOMElement,ITextElement],SVGTextLottieElement),SVGTextLottieElement.prototype.createContent=function(){this.data.singleShape&&!this.globalData.fontManager.chars&&(this.textContainer=createNS("text"))},SVGTextLottieElement.prototype.buildTextContents=function(t){for(var e=0,i=t.length,r=[],s="";e<i;)t[e]===String.fromCharCode(13)||t[e]===String.fromCharCode(3)?(r.push(s),s=""):s+=t[e],e+=1;return r.push(s),r},SVGTextLottieElement.prototype.buildShapeData=function(t,e){var i;return t.shapes&&t.shapes.length&&(i=t.shapes[0]).it&&(i=i.it[i.it.length-1]).s&&(i.s.k[0]=e,i.s.k[1]=e),t},SVGTextLottieElement.prototype.buildNewText=function(){this.addDynamicProperty(this);var t,e,i=this.textProperty.currentData,r=(this.renderedLetters=createSizedArray(i?i.l.length:0),i.fc?this.layerElement.setAttribute("fill",this.buildColor(i.fc)):this.layerElement.setAttribute("fill","rgba(0,0,0,0)"),i.sc&&(this.layerElement.setAttribute("stroke",this.buildColor(i.sc)),this.layerElement.setAttribute("stroke-width",i.sw)),this.layerElement.setAttribute("font-size",i.finalSize),this.globalData.fontManager.getFontByName(i.f)),s=(r.fClass?this.layerElement.setAttribute("class",r.fClass):(this.layerElement.setAttribute("font-family",r.fFamily),t=i.fWeight,e=i.fStyle,this.layerElement.setAttribute("font-style",e),this.layerElement.setAttribute("font-weight",t)),this.layerElement.setAttribute("aria-label",i.t),i.l||[]),a=!!this.globalData.fontManager.chars,n=(x=s.length,this.mHelper),o=this.data.singleShape,h=0,l=0,p=!0,m=.001*i.tr*i.finalSize;if(!o||a||i.sz){var f,c,d,u,y=this.textSpans.length;for(P=0;P<x;P+=1)this.textSpans[P]||(this.textSpans[P]={span:null,childSpan:null,glyph:null}),a&&o&&0!==P||(f=P<y?this.textSpans[P].span:createNS(a?"g":"text"),y<=P&&(f.setAttribute("stroke-linecap","butt"),f.setAttribute("stroke-linejoin","round"),f.setAttribute("stroke-miterlimit","4"),this.textSpans[P].span=f,a&&(u=createNS("g"),f.appendChild(u),this.textSpans[P].childSpan=u),this.textSpans[P].span=f,this.layerElement.appendChild(f)),f.style.display="inherit"),n.reset(),o&&(s[P].n&&(h=-m,l=l+i.yOffset+(p?1:0),p=!1),this.applyTextPropertiesToMatrix(i,n,s[P].line,h,l),h=h+(s[P].l||0)+m),a?(c=1===(u=this.globalData.fontManager.getCharData(i.finalText[P],r.fStyle,this.globalData.fontManager.getFontByName(i.f).fFamily)).t?new SVGCompElement(u.data,this.globalData,this):(c=emptyShapeData,new SVGShapeElement(c=u.data&&u.data.shapes?this.buildShapeData(u.data,i.finalSize):c,this.globalData,this)),this.textSpans[P].glyph&&(d=this.textSpans[P].glyph,this.textSpans[P].childSpan.removeChild(d.layerElement),d.destroy()),(this.textSpans[P].glyph=c)._debug=!0,c.prepareFrame(0),c.renderFrame(),this.textSpans[P].childSpan.appendChild(c.layerElement),1===u.t&&this.textSpans[P].childSpan.setAttribute("transform","scale("+i.finalSize/100+","+i.finalSize/100+")")):(o&&f.setAttribute("transform","translate("+n.props[12]+","+n.props[13]+")"),f.textContent=s[P].val,f.setAttributeNS("http://www.w3.org/XML/1998/namespace","xml:space","preserve"));o&&f&&f.setAttribute("d","")}else{var g=this.textContainer,v="start";switch(i.j){case 1:v="end";break;case 2:v="middle";break;default:v="start"}g.setAttribute("text-anchor",v),g.setAttribute("letter-spacing",m);for(var b=this.buildTextContents(i.finalText),x=b.length,l=i.ps?i.ps[1]+i.ascent:0,P=0;P<x;P+=1)(f=this.textSpans[P].span||createNS("tspan")).textContent=b[P],f.setAttribute("x",0),f.setAttribute("y",l),f.style.display="inherit",g.appendChild(f),this.textSpans[P]||(this.textSpans[P]={span:null,glyph:null}),this.textSpans[P].span=f,l+=i.finalLineHeight;this.layerElement.appendChild(g)}for(;P<this.textSpans.length;)this.textSpans[P].span.style.display="none",P+=1;this._sizeChanged=!0},SVGTextLottieElement.prototype.sourceRectAtTime=function(){var t;return this.prepareFrame(this.comp.renderedFrame-this.data.st),this.renderInnerContent(),this._sizeChanged&&(this._sizeChanged=!1,t=this.layerElement.getBBox(),this.bbox={top:t.y,left:t.x,width:t.width,height:t.height}),this.bbox},SVGTextLottieElement.prototype.getValue=function(){var t,e,i=this.textSpans.length;for(this.renderedFrame=this.comp.renderedFrame,t=0;t<i;t+=1)(e=this.textSpans[t].glyph)&&(e.prepareFrame(this.comp.renderedFrame-this.data.st),e._mdf)&&(this._mdf=!0)},SVGTextLottieElement.prototype.renderInnerContent=function(){if(this.validateText(),(!this.data.singleShape||this._mdf)&&(this.textAnimator.getMeasures(this.textProperty.currentData,this.lettersChangedFlag),this.lettersChangedFlag||this.textAnimator.lettersChangedFlag)){this._sizeChanged=!0;for(var t,e,i,r=this.textAnimator.renderedLetters,s=this.textProperty.currentData.l,a=s.length,n=0;n<a;n+=1)s[n].n||(t=r[n],e=this.textSpans[n].span,(i=this.textSpans[n].glyph)&&i.renderFrame(),t._mdf.m&&e.setAttribute("transform",t.m),t._mdf.o&&e.setAttribute("opacity",t.o),t._mdf.sw&&e.setAttribute("stroke-width",t.sw),t._mdf.sc&&e.setAttribute("stroke",t.sc),t._mdf.fc&&e.setAttribute("fill",t.fc))}},extendPrototype([IImageElement],ISolidElement),ISolidElement.prototype.createContent=function(){var t=createNS("rect");t.setAttribute("width",this.data.sw),t.setAttribute("height",this.data.sh),t.setAttribute("fill",this.data.sc),this.layerElement.appendChild(t)},NullElement.prototype.prepareFrame=function(t){this.prepareProperties(t,!0)},NullElement.prototype.renderFrame=function(){},NullElement.prototype.getBaseElement=function(){return null},NullElement.prototype.destroy=function(){},NullElement.prototype.sourceRectAtTime=function(){},NullElement.prototype.hide=function(){},extendPrototype([BaseElement,TransformElement,HierarchyElement,FrameElement],NullElement),extendPrototype([BaseRenderer],SVGRendererBase),SVGRendererBase.prototype.createNull=function(t){return new NullElement(t,this.globalData,this)},SVGRendererBase.prototype.createShape=function(t){return new SVGShapeElement(t,this.globalData,this)},SVGRendererBase.prototype.createText=function(t){return new SVGTextLottieElement(t,this.globalData,this)},SVGRendererBase.prototype.createImage=function(t){return new IImageElement(t,this.globalData,this)},SVGRendererBase.prototype.createSolid=function(t){return new ISolidElement(t,this.globalData,this)},SVGRendererBase.prototype.configAnimation=function(t){this.svgElement.setAttribute("xmlns","http://www.w3.org/2000/svg"),this.svgElement.setAttribute("xmlns:xlink","http://www.w3.org/1999/xlink"),this.renderConfig.viewBoxSize?this.svgElement.setAttribute("viewBox",this.renderConfig.viewBoxSize):this.svgElement.setAttribute("viewBox","0 0 "+t.w+" "+t.h),this.renderConfig.viewBoxOnly||(this.svgElement.setAttribute("width",t.w),this.svgElement.setAttribute("height",t.h),this.svgElement.style.width="100%",this.svgElement.style.height="100%",this.svgElement.style.transform="translate3d(0,0,0)",this.svgElement.style.contentVisibility=this.renderConfig.contentVisibility),this.renderConfig.width&&this.svgElement.setAttribute("width",this.renderConfig.width),this.renderConfig.height&&this.svgElement.setAttribute("height",this.renderConfig.height),this.renderConfig.className&&this.svgElement.setAttribute("class",this.renderConfig.className),this.renderConfig.id&&this.svgElement.setAttribute("id",this.renderConfig.id),void 0!==this.renderConfig.focusable&&this.svgElement.setAttribute("focusable",this.renderConfig.focusable),this.svgElement.setAttribute("preserveAspectRatio",this.renderConfig.preserveAspectRatio),this.animationItem.wrapper.appendChild(this.svgElement);var e=this.globalData.defs,i=(this.setupGlobalData(t,e),this.globalData.progressiveLoad=this.renderConfig.progressiveLoad,this.data=t,createNS("clipPath")),r=createNS("rect"),s=(r.setAttribute("width",t.w),r.setAttribute("height",t.h),r.setAttribute("x",0),r.setAttribute("y",0),createElementID());i.setAttribute("id",s),i.appendChild(r),this.layerElement.setAttribute("clip-path","url("+getLocationHref()+"#"+s+")"),e.appendChild(i),this.layers=t.layers,this.elements=createSizedArray(t.layers.length)},SVGRendererBase.prototype.destroy=function(){this.animationItem.wrapper&&(this.animationItem.wrapper.innerText=""),this.layerElement=null,this.globalData.defs=null;for(var t=this.layers?this.layers.length:0,e=0;e<t;e+=1)this.elements[e]&&this.elements[e].destroy&&this.elements[e].destroy();this.elements.length=0,this.destroyed=!0,this.animationItem=null},SVGRendererBase.prototype.updateContainerSize=function(){},SVGRendererBase.prototype.findIndexByInd=function(t){for(var e=0,i=this.layers.length,e=0;e<i;e+=1)if(this.layers[e].ind===t)return e;return-1},SVGRendererBase.prototype.buildItem=function(t){var e,i,r=this.elements;r[t]||99===this.layers[t].ty||(r[t]=!0,e=this.createItem(this.layers[t]),r[t]=e,getExpressionsPlugin()&&(0===this.layers[t].ty&&this.globalData.projectInterface.registerComposition(e),e.initExpressions()),this.appendElementInPos(e,t),this.layers[t].tt&&-1!==(i="tp"in this.layers[t]?this.findIndexByInd(this.layers[t].tp):t-1)&&(this.elements[i]&&!0!==this.elements[i]?(r=r[i].getMatte(this.layers[t].tt),e.setMatte(r)):(this.buildItem(i),this.addPendingElement(e))))},SVGRendererBase.prototype.checkPendingElements=function(){for(;this.pendingElements.length;){var t=this.pendingElements.pop();if(t.checkParenting(),t.data.tt)for(var e=0,i=this.elements.length;e<i;){if(this.elements[e]===t){var r="tp"in t.data?this.findIndexByInd(t.data.tp):e-1,r=this.elements[r].getMatte(this.layers[e].tt);t.setMatte(r);break}e+=1}}},SVGRendererBase.prototype.renderFrame=function(t){if(this.renderedFrame!==t&&!this.destroyed){null===t?t=this.renderedFrame:this.renderedFrame=t,this.globalData.frameNum=t,this.globalData.frameId+=1,this.globalData.projectInterface.currentFrame=t,this.globalData._mdf=!1;var e,i=this.layers.length;for(this.completeLayers||this.checkLayers(t),e=i-1;0<=e;--e)(this.completeLayers||this.elements[e])&&this.elements[e].prepareFrame(t-this.layers[e].st);if(this.globalData._mdf)for(e=0;e<i;e+=1)(this.completeLayers||this.elements[e])&&this.elements[e].renderFrame()}},SVGRendererBase.prototype.appendElementInPos=function(t,e){t=t.getBaseElement();if(t){for(var i,r=0;r<e;)this.elements[r]&&!0!==this.elements[r]&&this.elements[r].getBaseElement()&&(i=this.elements[r].getBaseElement()),r+=1;i?this.layerElement.insertBefore(t,i):this.layerElement.appendChild(t)}},SVGRendererBase.prototype.hide=function(){this.layerElement.style.display="none"},SVGRendererBase.prototype.show=function(){this.layerElement.style.display="block"},extendPrototype([BaseElement,TransformElement,HierarchyElement,FrameElement,RenderableDOMElement],ICompElement),ICompElement.prototype.initElement=function(t,e,i){this.initFrame(),this.initBaseData(t,e,i),this.initTransform(t,e,i),this.initRenderable(),this.initHierarchy(),this.initRendererElement(),this.createContainerElements(),this.createRenderableComponents(),!this.data.xt&&e.progressiveLoad||this.buildAllItems(),this.hide()},ICompElement.prototype.prepareFrame=function(t){if(this._mdf=!1,this.prepareRenderableFrame(t),this.prepareProperties(t,this.isInRange),this.isInRange||this.data.xt){this.tm._placeholder?this.renderedFrame=t/this.data.sr:((t=this.tm.v)===this.data.op&&(t=this.data.op-1),this.renderedFrame=t);var e,t=this.elements.length;for(this.completeLayers||this.checkLayers(this.renderedFrame),e=t-1;0<=e;--e)(this.completeLayers||this.elements[e])&&(this.elements[e].prepareFrame(this.renderedFrame-this.layers[e].st),this.elements[e]._mdf)&&(this._mdf=!0)}},ICompElement.prototype.renderInnerContent=function(){for(var t=this.layers.length,e=0;e<t;e+=1)(this.completeLayers||this.elements[e])&&this.elements[e].renderFrame()},ICompElement.prototype.setElements=function(t){this.elements=t},ICompElement.prototype.getElements=function(){return this.elements},ICompElement.prototype.destroyElements=function(){for(var t=this.layers.length,e=0;e<t;e+=1)this.elements[e]&&this.elements[e].destroy()},ICompElement.prototype.destroy=function(){this.destroyElements(),this.destroyBaseElement()},extendPrototype([SVGRendererBase,ICompElement,SVGBaseElement],SVGCompElement),SVGCompElement.prototype.createComp=function(t){return new SVGCompElement(t,this.globalData,this)},extendPrototype([SVGRendererBase],SVGRenderer),SVGRenderer.prototype.createComp=function(t){return new SVGCompElement(t,this.globalData,this)},ShapeTransformManager.prototype={addTransformSequence:function(t){for(var e=t.length,i="_",r=0;r<e;r+=1)i+=t[r].transform.key+"_";var s=this.sequences[i];return s||(s={transforms:[].concat(t),finalTransform:new Matrix,_mdf:!1},this.sequences[i]=s,this.sequenceList.push(s)),s},processSequence:function(t,e){for(var i=0,r=t.transforms.length,s=e;i<r&&!e;){if(t.transforms[i].transform.mProps._mdf){s=!0;break}i+=1}if(s)for(t.finalTransform.reset(),i=r-1;0<=i;--i)t.finalTransform.multiply(t.transforms[i].transform.mProps.v);t._mdf=s},processSequences:function(t){for(var e=this.sequenceList.length,i=0;i<e;i+=1)this.processSequence(this.sequenceList[i],t)},getNewKey:function(){return this.transform_key_count+=1,"_"+this.transform_key_count}};var lumaLoader=function(){var r="__lottie_element_luma_buffer",s=null,a=null,n=null;function e(){var t,e,i;s||(t=createNS("svg"),e=createNS("filter"),i=createNS("feColorMatrix"),e.setAttribute("id",r),i.setAttribute("type","matrix"),i.setAttribute("color-interpolation-filters","sRGB"),i.setAttribute("values","0.3, 0.3, 0.3, 0, 0, 0.3, 0.3, 0.3, 0, 0, 0.3, 0.3, 0.3, 0, 0, 0.3, 0.3, 0.3, 0, 0"),e.appendChild(i),t.appendChild(e),t.setAttribute("id",r+"_svg"),featureSupport.svgLumaHidden&&(t.style.display="none"),n=t,document.body.appendChild(n),s=createTag("canvas"),(a=s.getContext("2d")).filter="url(#"+r+")",a.fillStyle="rgba(0,0,0,0)",a.fillRect(0,0,1,1))}return{load:e,get:function(t){return s||e(),s.width=t.width,s.height=t.height,a.filter="url(#"+r+")",s}}};function createCanvas(t,e){var i;return featureSupport.offscreenCanvas?new OffscreenCanvas(t,e):((i=createTag("canvas")).width=t,i.height=e,i)}var assetLoader={loadLumaCanvas:lumaLoader.load,getLumaCanvas:lumaLoader.get,createCanvas:createCanvas},registeredEffects={};function CVEffects(t){var e,i=t.data.ef?t.data.ef.length:0;for(this.filters=[],e=0;e<i;e+=1){r=null;var r,s=t.data.ef[e].ty;(r=registeredEffects[s]?new registeredEffects[s].effect(t.effectsManager.effectElements[e],t):r)&&this.filters.push(r)}this.filters.length&&t.addRenderableComponent(this)}function registerEffect(t,e){registeredEffects[t]={effect:e}}function CVMaskElement(t,e){this.data=t,this.element=e,this.masksProperties=this.data.masksProperties||[],this.viewData=createSizedArray(this.masksProperties.length);for(var i=this.masksProperties.length,r=!1,s=0;s<i;s+=1)"n"!==this.masksProperties[s].mode&&(r=!0),this.viewData[s]=ShapePropertyFactory.getShapeProp(this.element,this.masksProperties[s],3);(this.hasMasks=r)&&this.element.addRenderableComponent(this)}function CVBaseElement(){}CVEffects.prototype.renderFrame=function(t){for(var e=this.filters.length,i=0;i<e;i+=1)this.filters[i].renderFrame(t)},CVEffects.prototype.getEffects=function(t){for(var e=this.filters.length,i=[],r=0;r<e;r+=1)this.filters[r].type===t&&i.push(this.filters[r]);return i},CVMaskElement.prototype.renderFrame=function(){if(this.hasMasks){var t,e,i,r,s=this.element.finalTransform.mat,a=this.element.canvasContext,n=this.masksProperties.length;for(a.beginPath(),t=0;t<n;t+=1)if("n"!==this.masksProperties[t].mode){this.masksProperties[t].inv&&(a.moveTo(0,0),a.lineTo(this.element.globalData.compSize.w,0),a.lineTo(this.element.globalData.compSize.w,this.element.globalData.compSize.h),a.lineTo(0,this.element.globalData.compSize.h),a.lineTo(0,0)),r=this.viewData[t].v,e=s.applyToPointArray(r.v[0][0],r.v[0][1],0),a.moveTo(e[0],e[1]);for(var o=r._length,h=1;h<o;h+=1)i=s.applyToTriplePoints(r.o[h-1],r.i[h],r.v[h]),a.bezierCurveTo(i[0],i[1],i[2],i[3],i[4],i[5]);i=s.applyToTriplePoints(r.o[h-1],r.i[0],r.v[0]),a.bezierCurveTo(i[0],i[1],i[2],i[3],i[4],i[5])}this.element.globalData.renderer.save(!0),a.clip()}},CVMaskElement.prototype.getMaskProperty=MaskElement.prototype.getMaskProperty,CVMaskElement.prototype.destroy=function(){this.element=null};var operationsMap={1:"source-in",2:"source-out",3:"source-in",4:"source-out"};function CVShapeData(t,e,i,r){this.styledShapes=[],this.tr=[0,0,0,0,0,0];for(var s,a=4,n=("rc"===e.ty?a=5:"el"===e.ty?a=6:"sr"===e.ty&&(a=7),this.sh=ShapePropertyFactory.getShapeProp(t,e,a,t),i.length),o=0;o<n;o+=1)i[o].closed||(s={transforms:r.addTransformSequence(i[o].transforms),trNodes:[]},this.styledShapes.push(s),i[o].elements.push(s))}function CVShapeElement(t,e,i){this.shapes=[],this.shapesData=t.shapes,this.stylesList=[],this.itemsData=[],this.prevViewData=[],this.shapeModifiers=[],this.processedElements=[],this.transformsManager=new ShapeTransformManager,this.initElement(t,e,i)}function CVTextElement(t,e,i){this.textSpans=[],this.yOffset=0,this.fillColorAnim=!1,this.strokeColorAnim=!1,this.strokeWidthAnim=!1,this.stroke=!1,this.fill=!1,this.justifyOffset=0,this.currentRender=null,this.renderType="canvas",this.values={fill:"rgba(0,0,0,0)",stroke:"rgba(0,0,0,0)",sWidth:0,fValue:""},this.initElement(t,e,i)}function CVImageElement(t,e,i){this.assetData=e.getAssetData(t.refId),this.img=e.imageLoader.getAsset(this.assetData),this.initElement(t,e,i)}function CVSolidElement(t,e,i){this.initElement(t,e,i)}function CanvasRendererBase(){}function CanvasContext(){this.opacity=-1,this.transform=createTypedArray("float32",16),this.fillStyle="",this.strokeStyle="",this.lineWidth="",this.lineCap="",this.lineJoin="",this.miterLimit="",this.id=Math.random()}function CVContextData(){this.stack=[],this.cArrPos=0,this.cTr=new Matrix;for(var t=0;t<15;t+=1){var e=new CanvasContext;this.stack[t]=e}this._length=15,this.nativeContext=null,this.transformMat=new Matrix,this.currentOpacity=1,this.currentFillStyle="",this.appliedFillStyle="",this.currentStrokeStyle="",this.appliedStrokeStyle="",this.currentLineWidth="",this.appliedLineWidth="",this.currentLineCap="",this.appliedLineCap="",this.currentLineJoin="",this.appliedLineJoin="",this.appliedMiterLimit="",this.currentMiterLimit=""}function CVCompElement(t,e,i){this.completeLayers=!1,this.layers=t.layers,this.pendingElements=[],this.elements=createSizedArray(this.layers.length),this.initElement(t,e,i),this.tm=t.tm?PropertyFactory.getProp(this,t.tm,0,e.frameRate,this):{_placeholder:!0}}function CanvasRenderer(t,e){this.animationItem=t,this.renderConfig={clearCanvas:!e||void 0===e.clearCanvas||e.clearCanvas,context:e&&e.context||null,progressiveLoad:e&&e.progressiveLoad||!1,preserveAspectRatio:e&&e.preserveAspectRatio||"xMidYMid meet",imagePreserveAspectRatio:e&&e.imagePreserveAspectRatio||"xMidYMid slice",contentVisibility:e&&e.contentVisibility||"visible",className:e&&e.className||"",id:e&&e.id||"",runExpressions:!e||void 0===e.runExpressions||e.runExpressions},this.renderConfig.dpr=e&&e.dpr||1,this.animationItem.wrapper&&(this.renderConfig.dpr=e&&e.dpr||window.devicePixelRatio||1),this.renderedFrame=-1,this.globalData={frameNum:-1,_mdf:!1,renderConfig:this.renderConfig,currentGlobalAlpha:-1},this.contextData=new CVContextData,this.elements=[],this.pendingElements=[],this.transformMat=new Matrix,this.completeLayers=!1,this.rendererType="canvas",this.renderConfig.clearCanvas&&(this.ctxTransform=this.contextData.transform.bind(this.contextData),this.ctxOpacity=this.contextData.opacity.bind(this.contextData),this.ctxFillStyle=this.contextData.fillStyle.bind(this.contextData),this.ctxStrokeStyle=this.contextData.strokeStyle.bind(this.contextData),this.ctxLineWidth=this.contextData.lineWidth.bind(this.contextData),this.ctxLineCap=this.contextData.lineCap.bind(this.contextData),this.ctxLineJoin=this.contextData.lineJoin.bind(this.contextData),this.ctxMiterLimit=this.contextData.miterLimit.bind(this.contextData),this.ctxFill=this.contextData.fill.bind(this.contextData),this.ctxFillRect=this.contextData.fillRect.bind(this.contextData),this.ctxStroke=this.contextData.stroke.bind(this.contextData),this.save=this.contextData.save.bind(this.contextData))}function HBaseElement(){}function HSolidElement(t,e,i){this.initElement(t,e,i)}function HShapeElement(t,e,i){this.shapes=[],this.shapesData=t.shapes,this.stylesList=[],this.shapeModifiers=[],this.itemsData=[],this.processedElements=[],this.animatedContents=[],this.shapesContainer=createNS("g"),this.initElement(t,e,i),this.prevViewData=[],this.currentBBox={x:999999,y:-999999,h:0,w:0}}function HTextElement(t,e,i){this.textSpans=[],this.textPaths=[],this.currentBBox={x:999999,y:-999999,h:0,w:0},this.renderType="svg",this.isMasked=!1,this.initElement(t,e,i)}function HCameraElement(t,e,i){this.initFrame(),this.initBaseData(t,e,i),this.initHierarchy();e=PropertyFactory.getProp;if(this.pe=e(this,t.pe,0,0,this),t.ks.p.s?(this.px=e(this,t.ks.p.x,1,0,this),this.py=e(this,t.ks.p.y,1,0,this),this.pz=e(this,t.ks.p.z,1,0,this)):this.p=e(this,t.ks.p,1,0,this),t.ks.a&&(this.a=e(this,t.ks.a,1,0,this)),t.ks.or.k.length&&t.ks.or.k[0].to)for(var r=t.ks.or.k.length,s=0;s<r;s+=1)t.ks.or.k[s].to=null,t.ks.or.k[s].ti=null;this.or=e(this,t.ks.or,1,degToRads,this),this.or.sh=!0,this.rx=e(this,t.ks.rx,0,degToRads,this),this.ry=e(this,t.ks.ry,0,degToRads,this),this.rz=e(this,t.ks.rz,0,degToRads,this),this.mat=new Matrix,this._prevMat=new Matrix,this._isFirstFrame=!0,this.finalTransform={mProp:this}}function HImageElement(t,e,i){this.assetData=e.getAssetData(t.refId),this.initElement(t,e,i)}function HybridRendererBase(t,e){this.animationItem=t,this.layers=null,this.renderedFrame=-1,this.renderConfig={className:e&&e.className||"",imagePreserveAspectRatio:e&&e.imagePreserveAspectRatio||"xMidYMid slice",hideOnTransparent:!(e&&!1===e.hideOnTransparent),filterSize:{width:e&&e.filterSize&&e.filterSize.width||"400%",height:e&&e.filterSize&&e.filterSize.height||"400%",x:e&&e.filterSize&&e.filterSize.x||"-100%",y:e&&e.filterSize&&e.filterSize.y||"-100%"}},this.globalData={_mdf:!1,frameNum:-1,renderConfig:this.renderConfig},this.pendingElements=[],this.elements=[],this.threeDElements=[],this.destroyed=!1,this.camera=null,this.supports3d=!0,this.rendererType="html"}function HCompElement(t,e,i){this.layers=t.layers,this.supports3d=!t.hasMask,this.completeLayers=!1,this.pendingElements=[],this.elements=this.layers?createSizedArray(this.layers.length):[],this.initElement(t,e,i),this.tm=t.tm?PropertyFactory.getProp(this,t.tm,0,e.frameRate,this):{_placeholder:!0}}function HybridRenderer(t,e){this.animationItem=t,this.layers=null,this.renderedFrame=-1,this.renderConfig={className:e&&e.className||"",imagePreserveAspectRatio:e&&e.imagePreserveAspectRatio||"xMidYMid slice",hideOnTransparent:!(e&&!1===e.hideOnTransparent),filterSize:{width:e&&e.filterSize&&e.filterSize.width||"400%",height:e&&e.filterSize&&e.filterSize.height||"400%",x:e&&e.filterSize&&e.filterSize.x||"-100%",y:e&&e.filterSize&&e.filterSize.y||"-100%"},runExpressions:!e||void 0===e.runExpressions||e.runExpressions},this.globalData={_mdf:!1,frameNum:-1,renderConfig:this.renderConfig},this.pendingElements=[],this.elements=[],this.threeDElements=[],this.destroyed=!1,this.camera=null,this.supports3d=!0,this.rendererType="html"}CVBaseElement.prototype={createElements:function(){},initRendererElement:function(){},createContainerElements:function(){var t,e;1<=this.data.tt&&(this.buffers=[],t=this.globalData.canvasContext,e=assetLoader.createCanvas(t.canvas.width,t.canvas.height),this.buffers.push(e),e=assetLoader.createCanvas(t.canvas.width,t.canvas.height),this.buffers.push(e),3<=this.data.tt)&&!document._isProxy&&assetLoader.loadLumaCanvas(),this.canvasContext=this.globalData.canvasContext,this.transformCanvas=this.globalData.transformCanvas,this.renderableEffectsManager=new CVEffects(this),this.searchEffectTransforms()},createContent:function(){},setBlendMode:function(){var t,e=this.globalData;e.blendMode!==this.data.bm&&(e.blendMode=this.data.bm,t=getBlendMode(this.data.bm),e.canvasContext.globalCompositeOperation=t)},createRenderableComponents:function(){this.maskManager=new CVMaskElement(this.data,this),this.transformEffects=this.renderableEffectsManager.getEffects(effectTypes.TRANSFORM_EFFECT)},hideElement:function(){this.hidden||this.isInRange&&!this.isTransparent||(this.hidden=!0)},showElement:function(){this.isInRange&&!this.isTransparent&&(this.hidden=!1,this._isFirstFrame=!0,this.maskManager._isFirstFrame=!0)},clearCanvas:function(t){t.clearRect(this.transformCanvas.tx,this.transformCanvas.ty,this.transformCanvas.w*this.transformCanvas.sx,this.transformCanvas.h*this.transformCanvas.sy)},prepareLayer:function(){var t;1<=this.data.tt&&(t=this.buffers[0].getContext("2d"),this.clearCanvas(t),t.drawImage(this.canvasContext.canvas,0,0),this.currentTransform=this.canvasContext.getTransform(),this.canvasContext.setTransform(1,0,0,1,0,0),this.clearCanvas(this.canvasContext),this.canvasContext.setTransform(this.currentTransform))},exitLayer:function(){var t,e;1<=this.data.tt&&(e=(t=this.buffers[1]).getContext("2d"),this.clearCanvas(e),e.drawImage(this.canvasContext.canvas,0,0),this.canvasContext.setTransform(1,0,0,1,0,0),this.clearCanvas(this.canvasContext),this.canvasContext.setTransform(this.currentTransform),this.comp.getElementById("tp"in this.data?this.data.tp:this.data.ind-1).renderFrame(!0),this.canvasContext.setTransform(1,0,0,1,0,0),3<=this.data.tt&&!document._isProxy&&((e=assetLoader.getLumaCanvas(this.canvasContext.canvas)).getContext("2d").drawImage(this.canvasContext.canvas,0,0),this.clearCanvas(this.canvasContext),this.canvasContext.drawImage(e,0,0)),this.canvasContext.globalCompositeOperation=operationsMap[this.data.tt],this.canvasContext.drawImage(t,0,0),this.canvasContext.globalCompositeOperation="destination-over",this.canvasContext.drawImage(this.buffers[0],0,0),this.canvasContext.setTransform(this.currentTransform),this.canvasContext.globalCompositeOperation="source-over")},renderFrame:function(t){this.hidden||this.data.hd||(1!==this.data.td||t)&&(this.renderTransform(),this.renderRenderable(),this.renderLocalTransform(),this.setBlendMode(),t=0===this.data.ty,this.prepareLayer(),this.globalData.renderer.save(t),this.globalData.renderer.ctxTransform(this.finalTransform.localMat.props),this.globalData.renderer.ctxOpacity(this.finalTransform.localOpacity),this.renderInnerContent(),this.globalData.renderer.restore(t),this.exitLayer(),this.maskManager.hasMasks&&this.globalData.renderer.restore(!0),this._isFirstFrame)&&(this._isFirstFrame=!1)},destroy:function(){this.canvasContext=null,this.data=null,this.globalData=null,this.maskManager.destroy()},mHelper:new Matrix},CVBaseElement.prototype.hide=CVBaseElement.prototype.hideElement,CVBaseElement.prototype.show=CVBaseElement.prototype.showElement,CVShapeData.prototype.setAsAnimated=SVGShapeData.prototype.setAsAnimated,extendPrototype([BaseElement,TransformElement,CVBaseElement,IShapeElement,HierarchyElement,FrameElement,RenderableElement],CVShapeElement),CVShapeElement.prototype.initElement=RenderableDOMElement.prototype.initElement,CVShapeElement.prototype.transformHelper={opacity:1,_opMdf:!1},CVShapeElement.prototype.dashResetter=[],CVShapeElement.prototype.createContent=function(){this.searchShapes(this.shapesData,this.itemsData,this.prevViewData,!0,[])},CVShapeElement.prototype.createStyleElement=function(t,e){var i,e={data:t,type:t.ty,preTransforms:this.transformsManager.addTransformSequence(e),transforms:[],elements:[],closed:!0===t.hd},r={};return"fl"===t.ty||"st"===t.ty?(r.c=PropertyFactory.getProp(this,t.c,1,255,this),r.c.k||(e.co="rgb("+bmFloor(r.c.v[0])+","+bmFloor(r.c.v[1])+","+bmFloor(r.c.v[2])+")")):"gf"!==t.ty&&"gs"!==t.ty||(r.s=PropertyFactory.getProp(this,t.s,1,null,this),r.e=PropertyFactory.getProp(this,t.e,1,null,this),r.h=PropertyFactory.getProp(this,t.h||{k:0},0,.01,this),r.a=PropertyFactory.getProp(this,t.a||{k:0},0,degToRads,this),r.g=new GradientProperty(this,t.g,this)),r.o=PropertyFactory.getProp(this,t.o,0,.01,this),"st"===t.ty||"gs"===t.ty?(e.lc=lineCapEnum[t.lc||2],e.lj=lineJoinEnum[t.lj||2],1==t.lj&&(e.ml=t.ml),r.w=PropertyFactory.getProp(this,t.w,0,null,this),r.w.k||(e.wi=r.w.v),t.d&&(i=new DashProperty(this,t.d,"canvas",this),r.d=i,r.d.k||(e.da=r.d.dashArray,e.do=r.d.dashoffset[0]))):e.r=2===t.r?"evenodd":"nonzero",this.stylesList.push(e),r.style=e,r},CVShapeElement.prototype.createGroupElement=function(){return{it:[],prevViewData:[]}},CVShapeElement.prototype.createTransformElement=function(t){return{transform:{opacity:1,_opMdf:!1,key:this.transformsManager.getNewKey(),op:PropertyFactory.getProp(this,t.o,0,.01,this),mProps:TransformPropertyFactory.getTransformProperty(this,t,this)}}},CVShapeElement.prototype.createShapeElement=function(t){t=new CVShapeData(this,t,this.stylesList,this.transformsManager);return this.shapes.push(t),this.addShapeToModifiers(t),t},CVShapeElement.prototype.reloadShapes=function(){this._isFirstFrame=!0;for(var t=this.itemsData.length,e=0;e<t;e+=1)this.prevViewData[e]=this.itemsData[e];for(this.searchShapes(this.shapesData,this.itemsData,this.prevViewData,!0,[]),t=this.dynamicProperties.length,e=0;e<t;e+=1)this.dynamicProperties[e].getValue();this.renderModifiers(),this.transformsManager.processSequences(this._isFirstFrame)},CVShapeElement.prototype.addTransformToStyleList=function(t){for(var e=this.stylesList.length,i=0;i<e;i+=1)this.stylesList[i].closed||this.stylesList[i].transforms.push(t)},CVShapeElement.prototype.removeTransformFromStyleList=function(){for(var t=this.stylesList.length,e=0;e<t;e+=1)this.stylesList[e].closed||this.stylesList[e].transforms.pop()},CVShapeElement.prototype.closeStyles=function(t){for(var e=t.length,i=0;i<e;i+=1)t[i].closed=!0},CVShapeElement.prototype.searchShapes=function(t,e,i,r,s){for(var a,n,o,h,l,p=t.length-1,m=[],f=[],c=[].concat(s),d=p;0<=d;--d){if((o=this.searchProcessedElement(t[d]))?e[d]=i[o-1]:t[d]._shouldRender=r,"fl"===t[d].ty||"st"===t[d].ty||"gf"===t[d].ty||"gs"===t[d].ty)o?e[d].style.closed=!1:e[d]=this.createStyleElement(t[d],c),m.push(e[d].style);else if("gr"===t[d].ty){if(o)for(n=e[d].it.length,a=0;a<n;a+=1)e[d].prevViewData[a]=e[d].it[a];else e[d]=this.createGroupElement(t[d]);this.searchShapes(t[d].it,e[d].it,e[d].prevViewData,r,c)}else"tr"===t[d].ty?(o||(l=this.createTransformElement(t[d]),e[d]=l),c.push(e[d]),this.addTransformToStyleList(e[d])):"sh"===t[d].ty||"rc"===t[d].ty||"el"===t[d].ty||"sr"===t[d].ty?o||(e[d]=this.createShapeElement(t[d])):"tm"===t[d].ty||"rd"===t[d].ty||"pb"===t[d].ty||"zz"===t[d].ty||"op"===t[d].ty?(o?(h=e[d]).closed=!1:((h=ShapeModifiers.getModifier(t[d].ty)).init(this,t[d]),e[d]=h,this.shapeModifiers.push(h)),f.push(h)):"rp"===t[d].ty&&(o?(h=e[d]).closed=!0:(h=ShapeModifiers.getModifier(t[d].ty),(e[d]=h).init(this,t,d,e),this.shapeModifiers.push(h),r=!1),f.push(h));this.addProcessedElement(t[d],d+1)}for(this.removeTransformFromStyleList(),this.closeStyles(m),p=f.length,d=0;d<p;d+=1)f[d].closed=!0},CVShapeElement.prototype.renderInnerContent=function(){this.transformHelper.opacity=1,this.transformHelper._opMdf=!1,this.renderModifiers(),this.transformsManager.processSequences(this._isFirstFrame),this.renderShape(this.transformHelper,this.shapesData,this.itemsData,!0)},CVShapeElement.prototype.renderShapeTransform=function(t,e){(t._opMdf||e.op._mdf||this._isFirstFrame)&&(e.opacity=t.opacity,e.opacity*=e.op.v,e._opMdf=!0)},CVShapeElement.prototype.drawLayer=function(){for(var t,e,i,r,s,a,n,o,h=this.stylesList.length,l=this.globalData.renderer,p=this.globalData.canvasContext,m=0;m<h;m+=1)if(("st"!==(n=(o=this.stylesList[m]).type)&&"gs"!==n||0!==o.wi)&&o.data._shouldRender&&0!==o.coOp&&0!==this.globalData.currentGlobalAlpha){for(l.save(),s=o.elements,"st"===n||"gs"===n?(l.ctxStrokeStyle("st"===n?o.co:o.grd),l.ctxLineWidth(o.wi),l.ctxLineCap(o.lc),l.ctxLineJoin(o.lj),l.ctxMiterLimit(o.ml||0)):l.ctxFillStyle("fl"===n?o.co:o.grd),l.ctxOpacity(o.coOp),"st"!==n&&"gs"!==n&&p.beginPath(),l.ctxTransform(o.preTransforms.finalTransform.props),e=s.length,t=0;t<e;t+=1){for("st"!==n&&"gs"!==n||(p.beginPath(),o.da&&(p.setLineDash(o.da),p.lineDashOffset=o.do)),r=(a=s[t].trNodes).length,i=0;i<r;i+=1)"m"===a[i].t?p.moveTo(a[i].p[0],a[i].p[1]):"c"===a[i].t?p.bezierCurveTo(a[i].pts[0],a[i].pts[1],a[i].pts[2],a[i].pts[3],a[i].pts[4],a[i].pts[5]):p.closePath();"st"!==n&&"gs"!==n||(l.ctxStroke(),o.da&&p.setLineDash(this.dashResetter))}"st"!==n&&"gs"!==n&&this.globalData.renderer.ctxFill(o.r),l.restore()}},CVShapeElement.prototype.renderShape=function(t,e,i,r){for(var s=t,a=e.length-1;0<=a;--a)"tr"===e[a].ty?(s=i[a].transform,this.renderShapeTransform(t,s)):"sh"===e[a].ty||"el"===e[a].ty||"rc"===e[a].ty||"sr"===e[a].ty?this.renderPath(e[a],i[a]):"fl"===e[a].ty?this.renderFill(e[a],i[a],s):"st"===e[a].ty?this.renderStroke(e[a],i[a],s):"gf"===e[a].ty||"gs"===e[a].ty?this.renderGradientFill(e[a],i[a],s):"gr"===e[a].ty?this.renderShape(s,e[a].it,i[a].it):e[a].ty;r&&this.drawLayer()},CVShapeElement.prototype.renderStyledShape=function(t,e){if(this._isFirstFrame||e._mdf||t.transforms._mdf){for(var i,r,s=t.trNodes,a=e.paths,n=a._length,o=(s.length=0,t.transforms.finalTransform),h=0;h<n;h+=1){var l=a.shapes[h];if(l&&l.v){for(r=l._length,i=1;i<r;i+=1)1===i&&s.push({t:"m",p:o.applyToPointArray(l.v[0][0],l.v[0][1],0)}),s.push({t:"c",pts:o.applyToTriplePoints(l.o[i-1],l.i[i],l.v[i])});1===r&&s.push({t:"m",p:o.applyToPointArray(l.v[0][0],l.v[0][1],0)}),l.c&&r&&(s.push({t:"c",pts:o.applyToTriplePoints(l.o[i-1],l.i[0],l.v[0])}),s.push({t:"z"}))}}t.trNodes=s}},CVShapeElement.prototype.renderPath=function(t,e){if(!0!==t.hd&&t._shouldRender)for(var i=e.styledShapes.length,r=0;r<i;r+=1)this.renderStyledShape(e.styledShapes[r],e.sh)},CVShapeElement.prototype.renderFill=function(t,e,i){var r=e.style;(e.c._mdf||this._isFirstFrame)&&(r.co="rgb("+bmFloor(e.c.v[0])+","+bmFloor(e.c.v[1])+","+bmFloor(e.c.v[2])+")"),(e.o._mdf||i._opMdf||this._isFirstFrame)&&(r.coOp=e.o.v*i.opacity)},CVShapeElement.prototype.renderGradientFill=function(t,e,i){var r=e.style;if(!r.grd||e.g._mdf||e.s._mdf||e.e._mdf||1!==t.t&&(e.h._mdf||e.a._mdf)){for(var s,a,n,o,h=this.globalData.canvasContext,l=e.s.v,p=e.e.v,m=(o=1===t.t?h.createLinearGradient(l[0],l[1],p[0],p[1]):(s=Math.sqrt(Math.pow(l[0]-p[0],2)+Math.pow(l[1]-p[1],2)),p=Math.atan2(p[1]-l[1],p[0]-l[0]),1<=(a=e.h.v)?a=.99:a<=-1&&(a=-.99),a=s*a,n=Math.cos(p+e.a.v)*a+l[0],p=Math.sin(p+e.a.v)*a+l[1],h.createRadialGradient(n,p,0,l[0],l[1],s)),t.g.p),f=e.g.c,c=1,d=0;d<m;d+=1)e.g._hasOpacity&&e.g._collapsable&&(c=e.g.o[2*d+1]),o.addColorStop(f[4*d]/100,"rgba("+f[4*d+1]+","+f[4*d+2]+","+f[4*d+3]+","+c+")");r.grd=o}r.coOp=e.o.v*i.opacity},CVShapeElement.prototype.renderStroke=function(t,e,i){var r=e.style,s=e.d;s&&(s._mdf||this._isFirstFrame)&&(r.da=s.dashArray,r.do=s.dashoffset[0]),(e.c._mdf||this._isFirstFrame)&&(r.co="rgb("+bmFloor(e.c.v[0])+","+bmFloor(e.c.v[1])+","+bmFloor(e.c.v[2])+")"),(e.o._mdf||i._opMdf||this._isFirstFrame)&&(r.coOp=e.o.v*i.opacity),(e.w._mdf||this._isFirstFrame)&&(r.wi=e.w.v)},CVShapeElement.prototype.destroy=function(){this.shapesData=null,this.globalData=null,this.canvasContext=null,this.stylesList.length=0,this.itemsData.length=0},extendPrototype([BaseElement,TransformElement,CVBaseElement,HierarchyElement,FrameElement,RenderableElement,ITextElement],CVTextElement),CVTextElement.prototype.tHelper=createTag("canvas").getContext("2d"),CVTextElement.prototype.buildNewText=function(){for(var t,e,i,r,s,a,n,o,h=this.textProperty.currentData,l=(this.renderedLetters=createSizedArray(h.l?h.l.length:0),!1),l=(h.fc?(l=!0,this.values.fill=this.buildColor(h.fc)):this.values.fill="rgba(0,0,0,0)",this.fill=l,!1),p=(h.sc&&(l=!0,this.values.stroke=this.buildColor(h.sc),this.values.sWidth=h.sw),this.globalData.fontManager.getFontByName(h.f)),m=h.l,f=this.mHelper,c=(this.stroke=l,this.values.fValue=h.finalSize+"px "+this.globalData.fontManager.getFontByName(h.f).fFamily,t=h.finalText.length,this.data.singleShape),d=.001*h.tr*h.finalSize,u=0,y=0,g=!0,v=0,b=0;b<t;b+=1){e=(e=this.globalData.fontManager.getCharData(h.finalText[b],p.fStyle,this.globalData.fontManager.getFontByName(h.f).fFamily))&&e.data||{},f.reset(),c&&m[b].n&&(u=-d,y=y+h.yOffset+(g?1:0),g=!1),a=(s=e.shapes?e.shapes[0].it:[]).length,f.scale(h.finalSize/100,h.finalSize/100),c&&this.applyTextPropertiesToMatrix(h,f,m[b].line,u,y);for(var x=createSizedArray(a-1),P=0,E=0;E<a;E+=1)if("sh"===s[E].ty){for(r=s[E].ks.k.i.length,n=s[E].ks.k,o=[],i=1;i<r;i+=1)1===i&&o.push(f.applyToX(n.v[0][0],n.v[0][1],0),f.applyToY(n.v[0][0],n.v[0][1],0)),o.push(f.applyToX(n.o[i-1][0],n.o[i-1][1],0),f.applyToY(n.o[i-1][0],n.o[i-1][1],0),f.applyToX(n.i[i][0],n.i[i][1],0),f.applyToY(n.i[i][0],n.i[i][1],0),f.applyToX(n.v[i][0],n.v[i][1],0),f.applyToY(n.v[i][0],n.v[i][1],0));o.push(f.applyToX(n.o[i-1][0],n.o[i-1][1],0),f.applyToY(n.o[i-1][0],n.o[i-1][1],0),f.applyToX(n.i[0][0],n.i[0][1],0),f.applyToY(n.i[0][0],n.i[0][1],0),f.applyToX(n.v[0][0],n.v[0][1],0),f.applyToY(n.v[0][0],n.v[0][1],0)),x[P]=o,P+=1}c&&(u=u+m[b].l+d),this.textSpans[v]?this.textSpans[v].elem=x:this.textSpans[v]={elem:x},v+=1}},CVTextElement.prototype.renderInnerContent=function(){this.validateText();this.canvasContext.font=this.values.fValue,this.globalData.renderer.ctxLineCap("butt"),this.globalData.renderer.ctxLineJoin("miter"),this.globalData.renderer.ctxMiterLimit(4),this.data.singleShape||this.textAnimator.getMeasures(this.textProperty.currentData,this.lettersChangedFlag);for(var t,e,i,r,s,a,n,o=this.textAnimator.renderedLetters,h=this.textProperty.currentData.l,l=h.length,p=null,m=null,f=null,c=this.globalData.renderer,d=0;d<l;d+=1)if(!h[d].n){if((s=o[d])&&(c.save(),c.ctxTransform(s.p),c.ctxOpacity(s.o)),this.fill){for(s&&s.fc?p!==s.fc&&(c.ctxFillStyle(s.fc),p=s.fc):p!==this.values.fill&&(p=this.values.fill,c.ctxFillStyle(this.values.fill)),e=(a=this.textSpans[d].elem).length,this.globalData.canvasContext.beginPath(),t=0;t<e;t+=1)for(r=(n=a[t]).length,this.globalData.canvasContext.moveTo(n[0],n[1]),i=2;i<r;i+=6)this.globalData.canvasContext.bezierCurveTo(n[i],n[i+1],n[i+2],n[i+3],n[i+4],n[i+5]);this.globalData.canvasContext.closePath(),c.ctxFill()}if(this.stroke){for(s&&s.sw?f!==s.sw&&(f=s.sw,c.ctxLineWidth(s.sw)):f!==this.values.sWidth&&(f=this.values.sWidth,c.ctxLineWidth(this.values.sWidth)),s&&s.sc?m!==s.sc&&(m=s.sc,c.ctxStrokeStyle(s.sc)):m!==this.values.stroke&&(m=this.values.stroke,c.ctxStrokeStyle(this.values.stroke)),e=(a=this.textSpans[d].elem).length,this.globalData.canvasContext.beginPath(),t=0;t<e;t+=1)for(r=(n=a[t]).length,this.globalData.canvasContext.moveTo(n[0],n[1]),i=2;i<r;i+=6)this.globalData.canvasContext.bezierCurveTo(n[i],n[i+1],n[i+2],n[i+3],n[i+4],n[i+5]);this.globalData.canvasContext.closePath(),c.ctxStroke()}s&&this.globalData.renderer.restore()}},extendPrototype([BaseElement,TransformElement,CVBaseElement,HierarchyElement,FrameElement,RenderableElement],CVImageElement),CVImageElement.prototype.initElement=SVGShapeElement.prototype.initElement,CVImageElement.prototype.prepareFrame=IImageElement.prototype.prepareFrame,CVImageElement.prototype.createContent=function(){var t,e,i,r,s,a,n,o,h;!this.img.width||this.assetData.w===this.img.width&&this.assetData.h===this.img.height||((t=createTag("canvas")).width=this.assetData.w,t.height=this.assetData.h,e=t.getContext("2d"),i=this.img.width,r=this.img.height,a=this.assetData.w/this.assetData.h,h=this.assetData.pr||this.globalData.renderConfig.imagePreserveAspectRatio,a<(s=i/r)&&"xMidYMid slice"===h||s<a&&"xMidYMid slice"!==h?n=(o=r)*a:o=(n=i)/a,e.drawImage(this.img,(i-n)/2,(r-o)/2,n,o,0,0,this.assetData.w,this.assetData.h),this.img=t)},CVImageElement.prototype.renderInnerContent=function(){this.canvasContext.drawImage(this.img,0,0)},CVImageElement.prototype.destroy=function(){this.img=null},extendPrototype([BaseElement,TransformElement,CVBaseElement,HierarchyElement,FrameElement,RenderableElement],CVSolidElement),CVSolidElement.prototype.initElement=SVGShapeElement.prototype.initElement,CVSolidElement.prototype.prepareFrame=IImageElement.prototype.prepareFrame,CVSolidElement.prototype.renderInnerContent=function(){this.globalData.renderer.ctxFillStyle(this.data.sc),this.globalData.renderer.ctxFillRect(0,0,this.data.sw,this.data.sh)},extendPrototype([BaseRenderer],CanvasRendererBase),CanvasRendererBase.prototype.createShape=function(t){return new CVShapeElement(t,this.globalData,this)},CanvasRendererBase.prototype.createText=function(t){return new CVTextElement(t,this.globalData,this)},CanvasRendererBase.prototype.createImage=function(t){return new CVImageElement(t,this.globalData,this)},CanvasRendererBase.prototype.createSolid=function(t){return new CVSolidElement(t,this.globalData,this)},CanvasRendererBase.prototype.createNull=SVGRenderer.prototype.createNull,CanvasRendererBase.prototype.ctxTransform=function(t){1===t[0]&&0===t[1]&&0===t[4]&&1===t[5]&&0===t[12]&&0===t[13]||this.canvasContext.transform(t[0],t[1],t[4],t[5],t[12],t[13])},CanvasRendererBase.prototype.ctxOpacity=function(t){this.canvasContext.globalAlpha*=t<0?0:t},CanvasRendererBase.prototype.ctxFillStyle=function(t){this.canvasContext.fillStyle=t},CanvasRendererBase.prototype.ctxStrokeStyle=function(t){this.canvasContext.strokeStyle=t},CanvasRendererBase.prototype.ctxLineWidth=function(t){this.canvasContext.lineWidth=t},CanvasRendererBase.prototype.ctxLineCap=function(t){this.canvasContext.lineCap=t},CanvasRendererBase.prototype.ctxLineJoin=function(t){this.canvasContext.lineJoin=t},CanvasRendererBase.prototype.ctxMiterLimit=function(t){this.canvasContext.miterLimit=t},CanvasRendererBase.prototype.ctxFill=function(t){this.canvasContext.fill(t)},CanvasRendererBase.prototype.ctxFillRect=function(t,e,i,r){this.canvasContext.fillRect(t,e,i,r)},CanvasRendererBase.prototype.ctxStroke=function(){this.canvasContext.stroke()},CanvasRendererBase.prototype.reset=function(){this.renderConfig.clearCanvas?this.contextData.reset():this.canvasContext.restore()},CanvasRendererBase.prototype.save=function(){this.canvasContext.save()},CanvasRendererBase.prototype.restore=function(t){this.renderConfig.clearCanvas?(t&&(this.globalData.blendMode="source-over"),this.contextData.restore(t)):this.canvasContext.restore()},CanvasRendererBase.prototype.configAnimation=function(t){var e,i;this.animationItem.wrapper?(this.animationItem.container=createTag("canvas"),(e=this.animationItem.container.style).width="100%",e.height="100%",e.transformOrigin=i="0px 0px 0px",e.mozTransformOrigin=i,e.webkitTransformOrigin=i,e["-webkit-transform"]=i,e.contentVisibility=this.renderConfig.contentVisibility,this.animationItem.wrapper.appendChild(this.animationItem.container),this.canvasContext=this.animationItem.container.getContext("2d"),this.renderConfig.className&&this.animationItem.container.setAttribute("class",this.renderConfig.className),this.renderConfig.id&&this.animationItem.container.setAttribute("id",this.renderConfig.id)):this.canvasContext=this.renderConfig.context,this.contextData.setContext(this.canvasContext),this.data=t,this.layers=t.layers,this.transformCanvas={w:t.w,h:t.h,sx:0,sy:0,tx:0,ty:0},this.setupGlobalData(t,document.body),this.globalData.canvasContext=this.canvasContext,(this.globalData.renderer=this).globalData.isDashed=!1,this.globalData.progressiveLoad=this.renderConfig.progressiveLoad,this.globalData.transformCanvas=this.transformCanvas,this.elements=createSizedArray(t.layers.length),this.updateContainerSize()},CanvasRendererBase.prototype.updateContainerSize=function(t,e){var i,r,s,a,n;this.reset(),t?(r=e,this.canvasContext.canvas.width=i=t,this.canvasContext.canvas.height=r):(r=this.animationItem.wrapper&&this.animationItem.container?(i=this.animationItem.wrapper.offsetWidth,this.animationItem.wrapper.offsetHeight):(i=this.canvasContext.canvas.width,this.canvasContext.canvas.height),this.canvasContext.canvas.width=i*this.renderConfig.dpr,this.canvasContext.canvas.height=r*this.renderConfig.dpr),-1!==this.renderConfig.preserveAspectRatio.indexOf("meet")||-1!==this.renderConfig.preserveAspectRatio.indexOf("slice")?(t=(e=this.renderConfig.preserveAspectRatio.split(" "))[1]||"meet",s=(e=e[0]||"xMidYMid").substr(0,4),e=e.substr(4),(a=i/r)<(n=this.transformCanvas.w/this.transformCanvas.h)&&"meet"===t||n<a&&"slice"===t?(this.transformCanvas.sx=i/(this.transformCanvas.w/this.renderConfig.dpr),this.transformCanvas.sy=i/(this.transformCanvas.w/this.renderConfig.dpr)):(this.transformCanvas.sx=r/(this.transformCanvas.h/this.renderConfig.dpr),this.transformCanvas.sy=r/(this.transformCanvas.h/this.renderConfig.dpr)),this.transformCanvas.tx="xMid"===s&&(n<a&&"meet"===t||a<n&&"slice"===t)?(i-this.transformCanvas.w*(r/this.transformCanvas.h))/2*this.renderConfig.dpr:"xMax"===s&&(n<a&&"meet"===t||a<n&&"slice"===t)?(i-this.transformCanvas.w*(r/this.transformCanvas.h))*this.renderConfig.dpr:0,this.transformCanvas.ty="YMid"===e&&(a<n&&"meet"===t||n<a&&"slice"===t)?(r-this.transformCanvas.h*(i/this.transformCanvas.w))/2*this.renderConfig.dpr:"YMax"===e&&(a<n&&"meet"===t||n<a&&"slice"===t)?(r-this.transformCanvas.h*(i/this.transformCanvas.w))*this.renderConfig.dpr:0):("none"===this.renderConfig.preserveAspectRatio?(this.transformCanvas.sx=i/(this.transformCanvas.w/this.renderConfig.dpr),this.transformCanvas.sy=r/(this.transformCanvas.h/this.renderConfig.dpr)):(this.transformCanvas.sx=this.renderConfig.dpr,this.transformCanvas.sy=this.renderConfig.dpr),this.transformCanvas.tx=0,this.transformCanvas.ty=0),this.transformCanvas.props=[this.transformCanvas.sx,0,0,0,0,this.transformCanvas.sy,0,0,0,0,1,0,this.transformCanvas.tx,this.transformCanvas.ty,0,1],this.ctxTransform(this.transformCanvas.props),this.canvasContext.beginPath(),this.canvasContext.rect(0,0,this.transformCanvas.w,this.transformCanvas.h),this.canvasContext.closePath(),this.canvasContext.clip(),this.renderFrame(this.renderedFrame,!0)},CanvasRendererBase.prototype.destroy=function(){this.renderConfig.clearCanvas&&this.animationItem.wrapper&&(this.animationItem.wrapper.innerText="");for(var t=(this.layers?this.layers.length:0)-1;0<=t;--t)this.elements[t]&&this.elements[t].destroy&&this.elements[t].destroy();this.elements.length=0,this.globalData.canvasContext=null,this.animationItem.container=null,this.destroyed=!0},CanvasRendererBase.prototype.renderFrame=function(t,e){if((this.renderedFrame!==t||!0!==this.renderConfig.clearCanvas||e)&&!this.destroyed&&-1!==t){this.renderedFrame=t,this.globalData.frameNum=t-this.animationItem._isFirstFrame,this.globalData.frameId+=1,this.globalData._mdf=!this.renderConfig.clearCanvas||e,this.globalData.projectInterface.currentFrame=t;var i,e=this.layers.length;for(this.completeLayers||this.checkLayers(t),i=e-1;0<=i;--i)(this.completeLayers||this.elements[i])&&this.elements[i].prepareFrame(t-this.layers[i].st);if(this.globalData._mdf){for(!0===this.renderConfig.clearCanvas?this.canvasContext.clearRect(0,0,this.transformCanvas.w,this.transformCanvas.h):this.save(),i=e-1;0<=i;--i)(this.completeLayers||this.elements[i])&&this.elements[i].renderFrame();!0!==this.renderConfig.clearCanvas&&this.restore()}}},CanvasRendererBase.prototype.buildItem=function(t){var e,i=this.elements;i[t]||99===this.layers[t].ty||(e=this.createItem(this.layers[t],this,this.globalData),(i[t]=e).initExpressions())},CanvasRendererBase.prototype.checkPendingElements=function(){for(;this.pendingElements.length;)this.pendingElements.pop().checkParenting()},CanvasRendererBase.prototype.hide=function(){this.animationItem.container.style.display="none"},CanvasRendererBase.prototype.show=function(){this.animationItem.container.style.display="block"},CVContextData.prototype.duplicate=function(){for(var t=2*this._length,e=0,e=this._length;e<t;e+=1)this.stack[e]=new CanvasContext;this._length=t},CVContextData.prototype.reset=function(){this.cArrPos=0,this.cTr.reset(),this.stack[this.cArrPos].opacity=1},CVContextData.prototype.restore=function(t){--this.cArrPos;for(var e,i=this.stack[this.cArrPos],r=i.transform,s=this.cTr.props,a=0;a<16;a+=1)s[a]=r[a];t&&(this.nativeContext.restore(),e=this.stack[this.cArrPos+1],this.appliedFillStyle=e.fillStyle,this.appliedStrokeStyle=e.strokeStyle,this.appliedLineWidth=e.lineWidth,this.appliedLineCap=e.lineCap,this.appliedLineJoin=e.lineJoin,this.appliedMiterLimit=e.miterLimit),this.nativeContext.setTransform(r[0],r[1],r[4],r[5],r[12],r[13]),(t||-1!==i.opacity&&this.currentOpacity!==i.opacity)&&(this.nativeContext.globalAlpha=i.opacity,this.currentOpacity=i.opacity),this.currentFillStyle=i.fillStyle,this.currentStrokeStyle=i.strokeStyle,this.currentLineWidth=i.lineWidth,this.currentLineCap=i.lineCap,this.currentLineJoin=i.lineJoin,this.currentMiterLimit=i.miterLimit},CVContextData.prototype.save=function(t){t&&this.nativeContext.save();for(var e=this.cTr.props,i=(this._length<=this.cArrPos&&this.duplicate(),this.stack[this.cArrPos]),r=0;r<16;r+=1)i.transform[r]=e[r];this.cArrPos+=1;t=this.stack[this.cArrPos];t.opacity=i.opacity,t.fillStyle=i.fillStyle,t.strokeStyle=i.strokeStyle,t.lineWidth=i.lineWidth,t.lineCap=i.lineCap,t.lineJoin=i.lineJoin,t.miterLimit=i.miterLimit},CVContextData.prototype.setOpacity=function(t){this.stack[this.cArrPos].opacity=t},CVContextData.prototype.setContext=function(t){this.nativeContext=t},CVContextData.prototype.fillStyle=function(t){this.stack[this.cArrPos].fillStyle!==t&&(this.currentFillStyle=t,this.stack[this.cArrPos].fillStyle=t)},CVContextData.prototype.strokeStyle=function(t){this.stack[this.cArrPos].strokeStyle!==t&&(this.currentStrokeStyle=t,this.stack[this.cArrPos].strokeStyle=t)},CVContextData.prototype.lineWidth=function(t){this.stack[this.cArrPos].lineWidth!==t&&(this.currentLineWidth=t,this.stack[this.cArrPos].lineWidth=t)},CVContextData.prototype.lineCap=function(t){this.stack[this.cArrPos].lineCap!==t&&(this.currentLineCap=t,this.stack[this.cArrPos].lineCap=t)},CVContextData.prototype.lineJoin=function(t){this.stack[this.cArrPos].lineJoin!==t&&(this.currentLineJoin=t,this.stack[this.cArrPos].lineJoin=t)},CVContextData.prototype.miterLimit=function(t){this.stack[this.cArrPos].miterLimit!==t&&(this.currentMiterLimit=t,this.stack[this.cArrPos].miterLimit=t)},CVContextData.prototype.transform=function(t){this.transformMat.cloneFromProps(t);t=this.cTr,this.transformMat.multiply(t),t.cloneFromProps(this.transformMat.props),t=t.props;this.nativeContext.setTransform(t[0],t[1],t[4],t[5],t[12],t[13])},CVContextData.prototype.opacity=function(t){var e=this.stack[this.cArrPos].opacity;this.stack[this.cArrPos].opacity!==(e*=t<0?0:t)&&(this.currentOpacity!==t&&(this.nativeContext.globalAlpha=t,this.currentOpacity=t),this.stack[this.cArrPos].opacity=e)},CVContextData.prototype.fill=function(t){this.appliedFillStyle!==this.currentFillStyle&&(this.appliedFillStyle=this.currentFillStyle,this.nativeContext.fillStyle=this.appliedFillStyle),this.nativeContext.fill(t)},CVContextData.prototype.fillRect=function(t,e,i,r){this.appliedFillStyle!==this.currentFillStyle&&(this.appliedFillStyle=this.currentFillStyle,this.nativeContext.fillStyle=this.appliedFillStyle),this.nativeContext.fillRect(t,e,i,r)},CVContextData.prototype.stroke=function(){this.appliedStrokeStyle!==this.currentStrokeStyle&&(this.appliedStrokeStyle=this.currentStrokeStyle,this.nativeContext.strokeStyle=this.appliedStrokeStyle),this.appliedLineWidth!==this.currentLineWidth&&(this.appliedLineWidth=this.currentLineWidth,this.nativeContext.lineWidth=this.appliedLineWidth),this.appliedLineCap!==this.currentLineCap&&(this.appliedLineCap=this.currentLineCap,this.nativeContext.lineCap=this.appliedLineCap),this.appliedLineJoin!==this.currentLineJoin&&(this.appliedLineJoin=this.currentLineJoin,this.nativeContext.lineJoin=this.appliedLineJoin),this.appliedMiterLimit!==this.currentMiterLimit&&(this.appliedMiterLimit=this.currentMiterLimit,this.nativeContext.miterLimit=this.appliedMiterLimit),this.nativeContext.stroke()},extendPrototype([CanvasRendererBase,ICompElement,CVBaseElement],CVCompElement),CVCompElement.prototype.renderInnerContent=function(){var t=this.canvasContext;t.beginPath(),t.moveTo(0,0),t.lineTo(this.data.w,0),t.lineTo(this.data.w,this.data.h),t.lineTo(0,this.data.h),t.lineTo(0,0),t.clip();for(var e=this.layers.length-1;0<=e;--e)(this.completeLayers||this.elements[e])&&this.elements[e].renderFrame()},CVCompElement.prototype.destroy=function(){for(var t=this.layers.length-1;0<=t;--t)this.elements[t]&&this.elements[t].destroy();this.layers=null,this.elements=null},CVCompElement.prototype.createComp=function(t){return new CVCompElement(t,this.globalData,this)},extendPrototype([CanvasRendererBase],CanvasRenderer),CanvasRenderer.prototype.createComp=function(t){return new CVCompElement(t,this.globalData,this)},HBaseElement.prototype={checkBlendMode:function(){},initRendererElement:function(){this.baseElement=createTag(this.data.tg||"div"),this.data.hasMask?(this.svgElement=createNS("svg"),this.layerElement=createNS("g"),this.maskedElement=this.layerElement,this.svgElement.appendChild(this.layerElement),this.baseElement.appendChild(this.svgElement)):this.layerElement=this.baseElement,styleDiv(this.baseElement)},createContainerElements:function(){this.renderableEffectsManager=new CVEffects(this),this.transformedElement=this.baseElement,this.maskedElement=this.layerElement,this.data.ln&&this.layerElement.setAttribute("id",this.data.ln),this.data.cl&&this.layerElement.setAttribute("class",this.data.cl),0!==this.data.bm&&this.setBlendMode()},renderElement:function(){var t,e=this.transformedElement?this.transformedElement.style:{};this.finalTransform._matMdf&&(t=this.finalTransform.mat.toCSS(),e.transform=t,e.webkitTransform=t),this.finalTransform._opMdf&&(e.opacity=this.finalTransform.mProp.o.v)},renderFrame:function(){this.data.hd||this.hidden||(this.renderTransform(),this.renderRenderable(),this.renderElement(),this.renderInnerContent(),this._isFirstFrame&&(this._isFirstFrame=!1))},destroy:function(){this.layerElement=null,this.transformedElement=null,this.matteElement&&(this.matteElement=null),this.maskManager&&(this.maskManager.destroy(),this.maskManager=null)},createRenderableComponents:function(){this.maskManager=new MaskElement(this.data,this,this.globalData)},addEffects:function(){},setMatte:function(){}},HBaseElement.prototype.getBaseElement=SVGBaseElement.prototype.getBaseElement,HBaseElement.prototype.destroyBaseElement=HBaseElement.prototype.destroy,HBaseElement.prototype.buildElementParenting=BaseRenderer.prototype.buildElementParenting,extendPrototype([BaseElement,TransformElement,HBaseElement,HierarchyElement,FrameElement,RenderableDOMElement],HSolidElement),HSolidElement.prototype.createContent=function(){var t;this.data.hasMask?((t=createNS("rect")).setAttribute("width",this.data.sw),t.setAttribute("height",this.data.sh),t.setAttribute("fill",this.data.sc),this.svgElement.setAttribute("width",this.data.sw),this.svgElement.setAttribute("height",this.data.sh)):((t=createTag("div")).style.width=this.data.sw+"px",t.style.height=this.data.sh+"px",t.style.backgroundColor=this.data.sc),this.layerElement.appendChild(t)},extendPrototype([BaseElement,TransformElement,HSolidElement,SVGShapeElement,HBaseElement,HierarchyElement,FrameElement,RenderableElement],HShapeElement),HShapeElement.prototype._renderShapeFrame=HShapeElement.prototype.renderInnerContent,HShapeElement.prototype.createContent=function(){var t,e;this.baseElement.style.fontSize=0,this.data.hasMask?(this.layerElement.appendChild(this.shapesContainer),t=this.svgElement):(t=createNS("svg"),e=this.comp.data||this.globalData.compSize,t.setAttribute("width",e.w),t.setAttribute("height",e.h),t.appendChild(this.shapesContainer),this.layerElement.appendChild(t)),this.searchShapes(this.shapesData,this.itemsData,this.prevViewData,this.shapesContainer,0,[],!0),this.filterUniqueShapes(),this.shapeCont=t},HShapeElement.prototype.getTransformedPoint=function(t,e){for(var i=t.length,r=0;r<i;r+=1)e=t[r].mProps.v.applyToPointArray(e[0],e[1],0);return e},HShapeElement.prototype.calculateShapeBoundingBox=function(t,e){var i,r,s,a,n,o=t.sh.v,h=t.transformers,l=o._length;if(!(l<=1)){for(i=0;i<l-1;i+=1)r=this.getTransformedPoint(h,o.v[i]),s=this.getTransformedPoint(h,o.o[i]),a=this.getTransformedPoint(h,o.i[i+1]),n=this.getTransformedPoint(h,o.v[i+1]),this.checkBounds(r,s,a,n,e);o.c&&(r=this.getTransformedPoint(h,o.v[i]),s=this.getTransformedPoint(h,o.o[i]),a=this.getTransformedPoint(h,o.i[0]),n=this.getTransformedPoint(h,o.v[0]),this.checkBounds(r,s,a,n,e))}},HShapeElement.prototype.checkBounds=function(t,e,i,r,s){this.getBoundsOfCurve(t,e,i,r);t=this.shapeBoundingBox;s.x=bmMin(t.left,s.x),s.xMax=bmMax(t.right,s.xMax),s.y=bmMin(t.top,s.y),s.yMax=bmMax(t.bottom,s.yMax)},HShapeElement.prototype.shapeBoundingBox={left:0,right:0,top:0,bottom:0},HShapeElement.prototype.tempBoundingBox={x:0,xMax:0,y:0,yMax:0,width:0,height:0},HShapeElement.prototype.getBoundsOfCurve=function(t,e,i,r){for(var s,a,n,o,h=[[t[0],r[0]],[t[1],r[1]]],l=0;l<2;++l)a=6*t[l]-12*e[l]+6*i[l],s=-3*t[l]+9*e[l]-9*i[l]+3*r[l],o=3*e[l]-3*t[l],a|=0,o|=0,0==(s|=0)&&0==a||(0==s?0<(n=-o/a)&&n<1&&h[l].push(this.calculateF(n,t,e,i,r,l)):0<=(n=a*a-4*o*s)&&(0<(o=(-a+bmSqrt(n))/(2*s))&&o<1&&h[l].push(this.calculateF(o,t,e,i,r,l)),0<(o=(-a-bmSqrt(n))/(2*s)))&&o<1&&h[l].push(this.calculateF(o,t,e,i,r,l)));this.shapeBoundingBox.left=bmMin.apply(null,h[0]),this.shapeBoundingBox.top=bmMin.apply(null,h[1]),this.shapeBoundingBox.right=bmMax.apply(null,h[0]),this.shapeBoundingBox.bottom=bmMax.apply(null,h[1])},HShapeElement.prototype.calculateF=function(t,e,i,r,s,a){return bmPow(1-t,3)*e[a]+3*bmPow(1-t,2)*t*i[a]+3*(1-t)*bmPow(t,2)*r[a]+bmPow(t,3)*s[a]},HShapeElement.prototype.calculateBoundingBox=function(t,e){for(var i=t.length,r=0;r<i;r+=1)t[r]&&t[r].sh?this.calculateShapeBoundingBox(t[r],e):t[r]&&t[r].it?this.calculateBoundingBox(t[r].it,e):t[r]&&t[r].style&&t[r].w&&this.expandStrokeBoundingBox(t[r].w,e)},HShapeElement.prototype.expandStrokeBoundingBox=function(t,e){var i=0;if(t.keyframes){for(var r=0;r<t.keyframes.length;r+=1){var s=t.keyframes[r].s;i<s&&(i=s)}i*=t.mult}else i=t.v*t.mult;e.x-=i,e.xMax+=i,e.y-=i,e.yMax+=i},HShapeElement.prototype.currentBoxContains=function(t){return this.currentBBox.x<=t.x&&this.currentBBox.y<=t.y&&this.currentBBox.width+this.currentBBox.x>=t.x+t.width&&this.currentBBox.height+this.currentBBox.y>=t.y+t.height},HShapeElement.prototype.renderInnerContent=function(){var t,e;this._renderShapeFrame(),this.hidden||!this._isFirstFrame&&!this._mdf||((e=this.tempBoundingBox).x=t=999999,e.xMax=-t,e.y=t,e.yMax=-t,this.calculateBoundingBox(this.itemsData,e),e.width=e.xMax<e.x?0:e.xMax-e.x,e.height=e.yMax<e.y?0:e.yMax-e.y,!this.currentBoxContains(e)&&(t=!1,this.currentBBox.w!==e.width&&(this.currentBBox.w=e.width,this.shapeCont.setAttribute("width",e.width),t=!0),this.currentBBox.h!==e.height&&(this.currentBBox.h=e.height,this.shapeCont.setAttribute("height",e.height),t=!0),t||this.currentBBox.x!==e.x||this.currentBBox.y!==e.y)&&(this.currentBBox.w=e.width,this.currentBBox.h=e.height,this.currentBBox.x=e.x,this.currentBBox.y=e.y,this.shapeCont.setAttribute("viewBox",this.currentBBox.x+" "+this.currentBBox.y+" "+this.currentBBox.w+" "+this.currentBBox.h),t=this.shapeCont.style,e="translate("+this.currentBBox.x+"px,"+this.currentBBox.y+"px)",t.transform=e,t.webkitTransform=e))},extendPrototype([BaseElement,TransformElement,HBaseElement,HierarchyElement,FrameElement,RenderableDOMElement,ITextElement],HTextElement),HTextElement.prototype.createContent=function(){var t;this.isMasked=this.checkMasks(),this.isMasked?(this.renderType="svg",this.compW=this.comp.data.w,this.compH=this.comp.data.h,this.svgElement.setAttribute("width",this.compW),this.svgElement.setAttribute("height",this.compH),t=createNS("g"),this.maskedElement.appendChild(t),this.innerElem=t):(this.renderType="html",this.innerElem=this.layerElement),this.checkParenting()},HTextElement.prototype.buildNewText=function(){for(var t,e,i,r,s,a,n,o=this.textProperty.currentData,h=(this.renderedLetters=createSizedArray(o.l?o.l.length:0),this.innerElem.style),l=o.fc?this.buildColor(o.fc):"rgba(0,0,0,0)",p=(h.fill=l,h.color=l,o.sc&&(h.stroke=this.buildColor(o.sc),h.strokeWidth=o.sw+"px"),this.globalData.fontManager.getFontByName(o.f)),m=(this.globalData.fontManager.chars||(h.fontSize=o.finalSize+"px",h.lineHeight=o.finalSize+"px",p.fClass?this.innerElem.className=p.fClass:(h.fontFamily=p.fFamily,l=o.fWeight,t=o.fStyle,h.fontStyle=t,h.fontWeight=l)),o.l),f=m.length,c=this.mHelper,d=0,u=0;u<f;u+=1)this.globalData.fontManager.chars?(this.textPaths[d]?e=this.textPaths[d]:((e=createNS("path")).setAttribute("stroke-linecap",lineCapEnum[1]),e.setAttribute("stroke-linejoin",lineJoinEnum[2]),e.setAttribute("stroke-miterlimit","4")),this.isMasked||(this.textSpans[d]?r=(i=this.textSpans[d]).children[0]:((i=createTag("div")).style.lineHeight=0,(r=createNS("svg")).appendChild(e),styleDiv(i)))):this.isMasked?e=this.textPaths[d]||createNS("text"):this.textSpans[d]?(i=this.textSpans[d],e=this.textPaths[d]):(styleDiv(i=createTag("span")),styleDiv(e=createTag("span")),i.appendChild(e)),this.globalData.fontManager.chars?(a=(a=this.globalData.fontManager.getCharData(o.finalText[u],p.fStyle,this.globalData.fontManager.getFontByName(o.f).fFamily))?a.data:null,c.reset(),a&&a.shapes&&a.shapes.length&&(s=a.shapes[0].it,c.scale(o.finalSize/100,o.finalSize/100),s=this.createPathShape(c,s),e.setAttribute("d",s)),this.isMasked?this.innerElem.appendChild(e):(this.innerElem.appendChild(i),a&&a.shapes?(document.body.appendChild(r),s=r.getBBox(),r.setAttribute("width",s.width+2),r.setAttribute("height",s.height+2),r.setAttribute("viewBox",s.x-1+" "+(s.y-1)+" "+(s.width+2)+" "+(s.height+2)),a=r.style,n="translate("+(s.x-1)+"px,"+(s.y-1)+"px)",a.transform=n,a.webkitTransform=n,m[u].yOffset=s.y-1):(r.setAttribute("width",1),r.setAttribute("height",1)),i.appendChild(r))):(e.textContent=m[u].val,e.setAttributeNS("http://www.w3.org/XML/1998/namespace","xml:space","preserve"),this.isMasked?this.innerElem.appendChild(e):(this.innerElem.appendChild(i),a=e.style,n="translate3d(0,"+-o.finalSize/1.2+"px,0)",a.transform=n,a.webkitTransform=n)),this.isMasked?this.textSpans[d]=e:this.textSpans[d]=i,this.textSpans[d].style.display="block",this.textPaths[d]=e,d+=1;for(;d<this.textSpans.length;)this.textSpans[d].style.display="none",d+=1},HTextElement.prototype.renderInnerContent=function(){var t;if(this.validateText(),this.data.singleShape){if(!this._isFirstFrame&&!this.lettersChangedFlag)return;this.isMasked&&this.finalTransform._matMdf&&(this.svgElement.setAttribute("viewBox",-this.finalTransform.mProp.p.v[0]+" "+-this.finalTransform.mProp.p.v[1]+" "+this.compW+" "+this.compH),t=this.svgElement.style,s="translate("+-this.finalTransform.mProp.p.v[0]+"px,"+-this.finalTransform.mProp.p.v[1]+"px)",t.transform=s,t.webkitTransform=s)}if(this.textAnimator.getMeasures(this.textProperty.currentData,this.lettersChangedFlag),this.lettersChangedFlag||this.textAnimator.lettersChangedFlag){for(var e,i,r,s,a=0,n=this.textAnimator.renderedLetters,o=this.textProperty.currentData.l,h=o.length,l=0;l<h;l+=1)o[l].n?a+=1:(i=this.textSpans[l],r=this.textPaths[l],e=n[a],a+=1,e._mdf.m&&(this.isMasked?i.setAttribute("transform",e.m):(i.style.webkitTransform=e.m,i.style.transform=e.m)),i.style.opacity=e.o,e.sw&&e._mdf.sw&&r.setAttribute("stroke-width",e.sw),e.sc&&e._mdf.sc&&r.setAttribute("stroke",e.sc),e.fc&&e._mdf.fc&&(r.setAttribute("fill",e.fc),r.style.color=e.fc));this.innerElem.getBBox&&!this.hidden&&(this._isFirstFrame||this._mdf)&&(s=this.innerElem.getBBox(),this.currentBBox.w!==s.width&&(this.currentBBox.w=s.width,this.svgElement.setAttribute("width",s.width)),this.currentBBox.h!==s.height&&(this.currentBBox.h=s.height,this.svgElement.setAttribute("height",s.height)),this.currentBBox.w===s.width+2&&this.currentBBox.h===s.height+2&&this.currentBBox.x===s.x-1&&this.currentBBox.y===s.y-1||(this.currentBBox.w=s.width+2,this.currentBBox.h=s.height+2,this.currentBBox.x=s.x-1,this.currentBBox.y=s.y-1,this.svgElement.setAttribute("viewBox",this.currentBBox.x+" "+this.currentBBox.y+" "+this.currentBBox.w+" "+this.currentBBox.h),t=this.svgElement.style,s="translate("+this.currentBBox.x+"px,"+this.currentBBox.y+"px)",t.transform=s,t.webkitTransform=s))}},extendPrototype([BaseElement,FrameElement,HierarchyElement],HCameraElement),HCameraElement.prototype.setup=function(){for(var t,e,i,r,s,a=this.comp.threeDElements.length,n=0;n<a;n+=1)"3d"===(s=this.comp.threeDElements[n]).type&&(t=s.perspectiveElem.style,s=s.container.style,e=this.pe.v+"px",i="0px 0px 0px",r="matrix3d(1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1)",t.perspective=e,t.webkitPerspective=e,s.transformOrigin=i,s.mozTransformOrigin=i,s.webkitTransformOrigin=i,t.transform=r,t.webkitTransform=r)},HCameraElement.prototype.createElements=function(){},HCameraElement.prototype.hide=function(){},HCameraElement.prototype.renderFrame=function(){var t=this._isFirstFrame;if(this.hierarchy)for(h=this.hierarchy.length,l=0;l<h;l+=1)t=this.hierarchy[l].finalTransform.mProp._mdf||t;if(t||this.pe._mdf||this.p&&this.p._mdf||this.px&&(this.px._mdf||this.py._mdf||this.pz._mdf)||this.rx._mdf||this.ry._mdf||this.rz._mdf||this.or._mdf||this.a&&this.a._mdf){if(this.mat.reset(),this.hierarchy)for(l=h=this.hierarchy.length-1;0<=l;--l){var e=this.hierarchy[l].finalTransform.mProp;this.mat.translate(-e.p.v[0],-e.p.v[1],e.p.v[2]),this.mat.rotateX(-e.or.v[0]).rotateY(-e.or.v[1]).rotateZ(e.or.v[2]),this.mat.rotateX(-e.rx.v).rotateY(-e.ry.v).rotateZ(e.rz.v),this.mat.scale(1/e.s.v[0],1/e.s.v[1],1/e.s.v[2]),this.mat.translate(e.a.v[0],e.a.v[1],e.a.v[2])}this.p?this.mat.translate(-this.p.v[0],-this.p.v[1],this.p.v[2]):this.mat.translate(-this.px.v,-this.py.v,this.pz.v),this.a&&(r=this.p?[this.p.v[0]-this.a.v[0],this.p.v[1]-this.a.v[1],this.p.v[2]-this.a.v[2]]:[this.px.v-this.a.v[0],this.py.v-this.a.v[1],this.pz.v-this.a.v[2]],i=Math.sqrt(Math.pow(r[0],2)+Math.pow(r[1],2)+Math.pow(r[2],2)),r=[r[0]/i,r[1]/i,r[2]/i],i=Math.sqrt(r[2]*r[2]+r[0]*r[0]),i=Math.atan2(r[1],i),r=Math.atan2(r[0],-r[2]),this.mat.rotateY(r).rotateX(-i)),this.mat.rotateX(-this.rx.v).rotateY(-this.ry.v).rotateZ(this.rz.v),this.mat.rotateX(-this.or.v[0]).rotateY(-this.or.v[1]).rotateZ(this.or.v[2]),this.mat.translate(this.globalData.compSize.w/2,this.globalData.compSize.h/2,0),this.mat.translate(0,0,this.pe.v);var i,r,s=!this._prevMat.equals(this.mat);if((s||this.pe._mdf)&&this.comp.threeDElements){for(var a,n,o,h=this.comp.threeDElements.length,l=0;l<h;l+=1)"3d"===(o=this.comp.threeDElements[l]).type&&(s&&(a=this.mat.toCSS(),(n=o.container.style).transform=a,n.webkitTransform=a),this.pe._mdf)&&((n=o.perspectiveElem.style).perspective=this.pe.v+"px",n.webkitPerspective=this.pe.v+"px");this.mat.clone(this._prevMat)}}this._isFirstFrame=!1},HCameraElement.prototype.prepareFrame=function(t){this.prepareProperties(t,!0)},HCameraElement.prototype.destroy=function(){},HCameraElement.prototype.getBaseElement=function(){return null},extendPrototype([BaseElement,TransformElement,HBaseElement,HSolidElement,HierarchyElement,FrameElement,RenderableElement],HImageElement),HImageElement.prototype.createContent=function(){var t=this.globalData.getAssetsPath(this.assetData),e=new Image;this.data.hasMask?(this.imageElem=createNS("image"),this.imageElem.setAttribute("width",this.assetData.w+"px"),this.imageElem.setAttribute("height",this.assetData.h+"px"),this.imageElem.setAttributeNS("http://www.w3.org/1999/xlink","href",t),this.layerElement.appendChild(this.imageElem),this.baseElement.setAttribute("width",this.assetData.w),this.baseElement.setAttribute("height",this.assetData.h)):this.layerElement.appendChild(e),e.crossOrigin="anonymous",e.src=t,this.data.ln&&this.baseElement.setAttribute("id",this.data.ln)},extendPrototype([BaseRenderer],HybridRendererBase),HybridRendererBase.prototype.buildItem=SVGRenderer.prototype.buildItem,HybridRendererBase.prototype.checkPendingElements=function(){for(;this.pendingElements.length;)this.pendingElements.pop().checkParenting()},HybridRendererBase.prototype.appendElementInPos=function(t,e){t=t.getBaseElement();if(t){var i=this.layers[e];if(i.ddd&&this.supports3d)this.addTo3dContainer(t,e);else if(this.threeDElements)this.addTo3dContainer(t,e);else{for(var r,s,a=0;a<e;)this.elements[a]&&!0!==this.elements[a]&&this.elements[a].getBaseElement&&(s=this.elements[a],r=(this.layers[a].ddd?this.getThreeDContainerByPos(a):s.getBaseElement())||r),a+=1;r?i.ddd&&this.supports3d||this.layerElement.insertBefore(t,r):i.ddd&&this.supports3d||this.layerElement.appendChild(t)}}},HybridRendererBase.prototype.createShape=function(t){return new(this.supports3d?HShapeElement:SVGShapeElement)(t,this.globalData,this)},HybridRendererBase.prototype.createText=function(t){return new(this.supports3d?HTextElement:SVGTextLottieElement)(t,this.globalData,this)},HybridRendererBase.prototype.createCamera=function(t){return this.camera=new HCameraElement(t,this.globalData,this),this.camera},HybridRendererBase.prototype.createImage=function(t){return new(this.supports3d?HImageElement:IImageElement)(t,this.globalData,this)},HybridRendererBase.prototype.createSolid=function(t){return new(this.supports3d?HSolidElement:ISolidElement)(t,this.globalData,this)},HybridRendererBase.prototype.createNull=SVGRenderer.prototype.createNull,HybridRendererBase.prototype.getThreeDContainerByPos=function(t){for(var e=0,i=this.threeDElements.length;e<i;){if(this.threeDElements[e].startPos<=t&&this.threeDElements[e].endPos>=t)return this.threeDElements[e].perspectiveElem;e+=1}return null},HybridRendererBase.prototype.createThreeDContainer=function(t,e){var i,r=createTag("div"),s=(styleDiv(r),createTag("div")),a=(styleDiv(s),"3d"===e&&((a=r.style).width=this.globalData.compSize.w+"px",a.height=this.globalData.compSize.h+"px",a.webkitTransformOrigin=i="50% 50%",a.mozTransformOrigin=i,a.transformOrigin=i,(a=s.style).transform=i="matrix3d(1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1)",a.webkitTransform=i),r.appendChild(s),{container:s,perspectiveElem:r,startPos:t,endPos:t,type:e});return this.threeDElements.push(a),a},HybridRendererBase.prototype.build3dContainers=function(){for(var t,e=this.layers.length,i="",r=0;r<e;r+=1)this.layers[r].ddd&&3!==this.layers[r].ty?(t="3d"!==i?this.createThreeDContainer(r,i="3d"):t).endPos=Math.max(t.endPos,r):(t="2d"!==i?this.createThreeDContainer(r,i="2d"):t).endPos=Math.max(t.endPos,r);for(r=(e=this.threeDElements.length)-1;0<=r;--r)this.resizerElem.appendChild(this.threeDElements[r].perspectiveElem)},HybridRendererBase.prototype.addTo3dContainer=function(t,e){for(var i=0,r=this.threeDElements.length;i<r;){if(e<=this.threeDElements[i].endPos){for(var s,a=this.threeDElements[i].startPos;a<e;)this.elements[a]&&this.elements[a].getBaseElement&&(s=this.elements[a].getBaseElement()),a+=1;s?this.threeDElements[i].container.insertBefore(t,s):this.threeDElements[i].container.appendChild(t);break}i+=1}},HybridRendererBase.prototype.configAnimation=function(t){var e=createTag("div"),i=this.animationItem.wrapper,r=e.style,i=(r.width=t.w+"px",r.height=t.h+"px",styleDiv(this.resizerElem=e),r.transformStyle="flat",r.mozTransformStyle="flat",r.webkitTransformStyle="flat",this.renderConfig.className&&e.setAttribute("class",this.renderConfig.className),i.appendChild(e),r.overflow="hidden",createNS("svg")),e=(i.setAttribute("width","1"),i.setAttribute("height","1"),styleDiv(i),this.resizerElem.appendChild(i),createNS("defs"));i.appendChild(e),this.data=t,this.setupGlobalData(t,i),this.globalData.defs=e,this.layers=t.layers,this.layerElement=this.resizerElem,this.build3dContainers(),this.updateContainerSize()},HybridRendererBase.prototype.destroy=function(){this.animationItem.wrapper&&(this.animationItem.wrapper.innerText=""),this.animationItem.container=null,this.globalData.defs=null;for(var t=this.layers?this.layers.length:0,e=0;e<t;e+=1)this.elements[e]&&this.elements[e].destroy&&this.elements[e].destroy();this.elements.length=0,this.destroyed=!0,this.animationItem=null},HybridRendererBase.prototype.updateContainerSize=function(){var t,e,i,r=this.animationItem.wrapper.offsetWidth,s=this.animationItem.wrapper.offsetHeight,r=r/s<this.globalData.compSize.w/this.globalData.compSize.h?(t=r/this.globalData.compSize.w,e=r/this.globalData.compSize.w,i=0,(s-this.globalData.compSize.h*(r/this.globalData.compSize.w))/2):(t=s/this.globalData.compSize.h,e=s/this.globalData.compSize.h,i=(r-this.globalData.compSize.w*(s/this.globalData.compSize.h))/2,0),s=this.resizerElem.style;s.webkitTransform="matrix3d("+t+",0,0,0,0,"+e+",0,0,0,0,1,0,"+i+","+r+",0,1)",s.transform=s.webkitTransform},HybridRendererBase.prototype.renderFrame=SVGRenderer.prototype.renderFrame,HybridRendererBase.prototype.hide=function(){this.resizerElem.style.display="none"},HybridRendererBase.prototype.show=function(){this.resizerElem.style.display="block"},HybridRendererBase.prototype.initItems=function(){if(this.buildAllItems(),this.camera)this.camera.setup();else for(var t=this.globalData.compSize.w,e=this.globalData.compSize.h,i=this.threeDElements.length,r=0;r<i;r+=1){var s=this.threeDElements[r].perspectiveElem.style;s.webkitPerspective=Math.sqrt(Math.pow(t,2)+Math.pow(e,2))+"px",s.perspective=s.webkitPerspective}},HybridRendererBase.prototype.searchExtraCompositions=function(t){for(var e,i=t.length,r=createTag("div"),s=0;s<i;s+=1)t[s].xt&&((e=this.createComp(t[s],r,this.globalData.comp,null)).initExpressions(),this.globalData.projectInterface.registerComposition(e))},extendPrototype([HybridRendererBase,ICompElement,HBaseElement],HCompElement),HCompElement.prototype._createBaseContainerElements=HCompElement.prototype.createContainerElements,HCompElement.prototype.createContainerElements=function(){this._createBaseContainerElements(),this.data.hasMask?(this.svgElement.setAttribute("width",this.data.w),this.svgElement.setAttribute("height",this.data.h),this.transformedElement=this.baseElement):this.transformedElement=this.layerElement},HCompElement.prototype.addTo3dContainer=function(t,e){for(var i,r=0;r<e;)this.elements[r]&&this.elements[r].getBaseElement&&(i=this.elements[r].getBaseElement()),r+=1;i?this.layerElement.insertBefore(t,i):this.layerElement.appendChild(t)},HCompElement.prototype.createComp=function(t){return new(this.supports3d?HCompElement:SVGCompElement)(t,this.globalData,this)},extendPrototype([HybridRendererBase],HybridRenderer),HybridRenderer.prototype.createComp=function(t){return new(this.supports3d?HCompElement:SVGCompElement)(t,this.globalData,this)};var CompExpressionInterface=function(r){function t(t){for(var e=0,i=r.layers.length;e<i;){if(r.layers[e].nm===t||r.layers[e].ind===t)return r.elements[e].layerInterface;e+=1}return null}return Object.defineProperty(t,"_name",{value:r.data.nm}),(t.layer=t).pixelAspect=1,t.height=r.data.h||r.globalData.compSize.h,t.width=r.data.w||r.globalData.compSize.w,t.pixelAspect=1,t.frameDuration=1/r.globalData.frameRate,t.displayStartTime=0,t.numLayers=r.layers.length,t};function _typeof$2(t){return(_typeof$2="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function seedRandom(n,o){var h,l=this,p=256,m=6,f="random",c=o.pow(p,m),d=o.pow(2,52),u=2*d,y=p-1;function g(t){var e,i=t.length,n=this,r=0,s=n.i=n.j=0,a=n.S=[];for(i||(t=[i++]);r<p;)a[r]=r++;for(r=0;r<p;r++)a[r]=a[s=y&s+t[r%i]+(e=a[r])],a[s]=e;n.g=function(t){for(var e,i=0,r=n.i,s=n.j,a=n.S;t--;)e=a[r=y&r+1],i=i*p+a[y&(a[r]=a[s=y&s+e])+(a[s]=e)];return n.i=r,n.j=s,i}}function v(t,e){return e.i=t.i,e.j=t.j,e.S=t.S.slice(),e}function b(t,e){for(var i,r=t+"",s=0;s<r.length;)e[y&s]=y&(i^=19*e[y&s])+r.charCodeAt(s++);return x(e)}function x(t){return String.fromCharCode.apply(0,t)}o["seed"+f]=function(t,e,i){function r(){for(var t=a.g(m),e=c,i=0;t<d;)t=(t+i)*p,e*=p,i=a.g(1);for(;u<=t;)t/=2,e/=2,i>>>=1;return(t+i)/e}var s=[],t=b(function t(e,i){var r,s=[],a=_typeof$2(e);if(i&&"object"==a)for(r in e)try{s.push(t(e[r],i-1))}catch(t){}return s.length?s:"string"==a?e:e+"\0"}((e=!0===e?{entropy:!0}:e||{}).entropy?[t,x(n)]:null===t?(()=>{try{return h?x(h.randomBytes(p)):(e=new Uint8Array(p),(l.crypto||l.msCrypto).getRandomValues(e),x(e))}catch(t){var e=l.navigator,e=e&&e.plugins;return[+new Date,l,e,l.screen,x(n)]}})():t,3),s),a=new g(s);return r.int32=function(){return 0|a.g(4)},r.quick=function(){return a.g(4)/4294967296},r.double=r,b(x(a.S),n),(e.pass||i||function(t,e,i,r){return r&&(r.S&&v(r,a),t.state=function(){return v(a,{})}),i?(o[f]=t,e):t})(r,t,"global"in e?e.global:this==o,e.state)},b(o.random(),n)}function initialize$2(t){seedRandom([],t)}var propTypes={SHAPE:"shape"};function _typeof$1(t){return(_typeof$1="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var ExpressionManager=function(){var ob={},Math=BMMath,window=null,document=null,XMLHttpRequest=null,fetch=null,frames=null,_lottieGlobal={};function resetFrame(){_lottieGlobal={}}function $bm_isInstanceOfArray(t){return t.constructor===Array||t.constructor===Float32Array}function isNumerable(t,e){return"number"===t||e instanceof Number||"boolean"===t||"string"===t}function $bm_neg(t){var e=_typeof$1(t);if("number"===e||t instanceof Number||"boolean"===e)return-t;if($bm_isInstanceOfArray(t)){for(var i=t.length,r=[],s=0;s<i;s+=1)r[s]=-t[s];return r}return t.propType?t.v:-t}initialize$2(BMMath);var easeInBez=BezierFactory.getBezierEasing(.333,0,.833,.833,"easeIn").get,easeOutBez=BezierFactory.getBezierEasing(.167,.167,.667,1,"easeOut").get,easeInOutBez=BezierFactory.getBezierEasing(.33,0,.667,1,"easeInOut").get;function sum(t,e){var i=_typeof$1(t),r=_typeof$1(e);if(isNumerable(i,t)&&isNumerable(r,e)||"string"===i||"string"===r)return t+e;if($bm_isInstanceOfArray(t)&&isNumerable(r,e))return(t=t.slice(0))[0]+=e,t;if(isNumerable(i,t)&&$bm_isInstanceOfArray(e))return(e=e.slice(0))[0]=t+e[0],e;if($bm_isInstanceOfArray(t)&&$bm_isInstanceOfArray(e)){for(var s=0,a=t.length,n=e.length,o=[];s<a||s<n;)("number"==typeof t[s]||t[s]instanceof Number)&&("number"==typeof e[s]||e[s]instanceof Number)?o[s]=t[s]+e[s]:o[s]=void 0===e[s]?t[s]:t[s]||e[s],s+=1;return o}return 0}var add=sum;function sub(t,e){var i=_typeof$1(t),r=_typeof$1(e);if(isNumerable(i,t)&&isNumerable(r,e))return(t="string"===i?parseInt(t,10):t)-(e="string"===r?parseInt(e,10):e);if($bm_isInstanceOfArray(t)&&isNumerable(r,e))return(t=t.slice(0))[0]-=e,t;if(isNumerable(i,t)&&$bm_isInstanceOfArray(e))return(e=e.slice(0))[0]=t-e[0],e;if($bm_isInstanceOfArray(t)&&$bm_isInstanceOfArray(e)){for(var s=0,a=t.length,n=e.length,o=[];s<a||s<n;)("number"==typeof t[s]||t[s]instanceof Number)&&("number"==typeof e[s]||e[s]instanceof Number)?o[s]=t[s]-e[s]:o[s]=void 0===e[s]?t[s]:t[s]||e[s],s+=1;return o}return 0}function mul(t,e){var i,r,s,a=_typeof$1(t),n=_typeof$1(e);if(isNumerable(a,t)&&isNumerable(n,e))return t*e;if($bm_isInstanceOfArray(t)&&isNumerable(n,e)){for(s=t.length,i=createTypedArray("float32",s),r=0;r<s;r+=1)i[r]=t[r]*e;return i}if(isNumerable(a,t)&&$bm_isInstanceOfArray(e)){for(s=e.length,i=createTypedArray("float32",s),r=0;r<s;r+=1)i[r]=t*e[r];return i}return 0}function div(t,e){var i,r,s,a=_typeof$1(t),n=_typeof$1(e);if(isNumerable(a,t)&&isNumerable(n,e))return t/e;if($bm_isInstanceOfArray(t)&&isNumerable(n,e)){for(s=t.length,i=createTypedArray("float32",s),r=0;r<s;r+=1)i[r]=t[r]/e;return i}if(isNumerable(a,t)&&$bm_isInstanceOfArray(e)){for(s=e.length,i=createTypedArray("float32",s),r=0;r<s;r+=1)i[r]=t/e[r];return i}return 0}function mod(t,e){return(t="string"==typeof t?parseInt(t,10):t)%(e="string"==typeof e?parseInt(e,10):e)}var $bm_sum=sum,$bm_sub=sub,$bm_mul=mul,$bm_div=div,$bm_mod=mod;function clamp(t,e,i){var r;return i<e&&(r=i,i=e,e=r),Math.min(Math.max(t,e),i)}function radiansToDegrees(t){return t/degToRads}var radians_to_degrees=radiansToDegrees;function degreesToRadians(t){return t*degToRads}var degrees_to_radians=radiansToDegrees,helperLengthArray=[0,0,0,0,0,0];function length(t,e){if("number"==typeof t||t instanceof Number)return Math.abs(t-(e=e||0));for(var i=Math.min(t.length,(e=e||helperLengthArray).length),r=0,s=0;s<i;s+=1)r+=Math.pow(e[s]-t[s],2);return Math.sqrt(r)}function normalize(t){return div(t,length(t))}function rgbToHsl(t){var e,i=t[0],r=t[1],s=t[2],a=Math.max(i,r,s),n=Math.min(i,r,s),o=(a+n)/2;if(a===n)l=e=0;else{var h=a-n,l=.5<o?h/(2-a-n):h/(a+n);switch(a){case i:e=(r-s)/h+(r<s?6:0);break;case r:e=(s-i)/h+2;break;case s:e=(i-r)/h+4}e/=6}return[e,l,o,t[3]]}function hue2rgb(t,e,i){return i<0&&(i+=1),1<i&&--i,i<1/6?t+6*(e-t)*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}function hslToRgb(t){var e,i,r,s=t[0],a=t[1],n=t[2];return 0===a?i=r=e=n:(e=hue2rgb(a=2*n-(n=n<.5?n*(1+a):n+a-n*a),n,s+1/3),i=hue2rgb(a,n,s),r=hue2rgb(a,n,s-1/3)),[e,i,r,t[3]]}function linear(t,e,i,r,s){var a;if(void 0!==r&&void 0!==s||(r=e,s=i,e=0,i=1),i<e&&(a=i,i=e,e=a),t<=e)return r;if(i<=t)return s;var n=i===e?0:(t-e)/(i-e);if(!r.length)return r+(s-r)*n;for(var o=r.length,h=createTypedArray("float32",o),l=0;l<o;l+=1)h[l]=r[l]+(s[l]-r[l])*n;return h}function random(t,e){if(void 0===e&&(void 0===t?(t=0,e=1):(e=t,t=void 0)),e.length){for(var i=e.length,r=(t=t||createTypedArray("float32",i),createTypedArray("float32",i)),s=BMMath.random(),a=0;a<i;a+=1)r[a]=t[a]+s*(e[a]-t[a]);return r}return(t=void 0===t?0:t)+BMMath.random()*(e-t)}function createPath(t,e,i,r){for(var s,a,n=t.length,o=shapePool.newElement(),h=(o.setPathData(!!r,n),[0,0]),l=0;l<n;l+=1)s=e&&e[l]?e[l]:h,a=i&&i[l]?i[l]:h,o.setTripleAt(t[l][0],t[l][1],a[0]+t[l][0],a[1]+t[l][1],s[0]+t[l][0],s[1]+t[l][1],l,!0);return o}function initiateExpression(elem,data,property){function noOp(t){return t}if(!elem.globalData.renderConfig.runExpressions)return noOp;var val=data.x,needsVelocity=/velocity(?![\w\d])/.test(val),_needsRandom=-1!==val.indexOf("random"),elemType=elem.data.ty,transform,$bm_transform,content,effect,thisProperty=property,inPoint=(thisProperty._name=elem.data.nm,thisProperty.valueAtTime=thisProperty.getValueAtTime,Object.defineProperty(thisProperty,"value",{get:function(){return thisProperty.v}}),elem.comp.frameDuration=1/elem.comp.globalData.frameRate,elem.comp.displayStartTime=0,elem.data.ip/elem.comp.globalData.frameRate),outPoint=elem.data.op/elem.comp.globalData.frameRate,width=elem.data.sw||0,height=elem.data.sh||0,name=elem.data.nm,loopIn,loop_in,loopOut,loop_out,smooth,toWorld,fromWorld,fromComp,toComp,fromCompToSurface,position,rotation,anchorPoint,scale,thisLayer,thisComp,mask,valueAtTime,velocityAtTime,scoped_bm_rt,expression_function=eval("[function _expression_function(){"+val+";scoped_bm_rt=$bm_rt}]")[0],numKeys=property.kf?data.k.length:0,active=!this.data||!0!==this.data.hd,wiggle=function(t,e){for(var i=this.pv.length||1,r=createTypedArray("float32",i),s=Math.floor(5*time),a=0,n=0;a<s;){for(n=0;n<i;n+=1)r[n]+=-e+2*e*BMMath.random();a+=1}var o=5*time,h=o-Math.floor(o),l=createTypedArray("float32",i);if(1<i){for(n=0;n<i;n+=1)l[n]=this.pv[n]+r[n]+(-e+2*e*BMMath.random())*h;return l}return this.pv+r[0]+(-e+2*e*BMMath.random())*h}.bind(this);function loopInDuration(t,e){return loopIn(t,e,!0)}function loopOutDuration(t,e){return loopOut(t,e,!0)}thisProperty.loopIn&&(loopIn=thisProperty.loopIn.bind(thisProperty),loop_in=loopIn),thisProperty.loopOut&&(loopOut=thisProperty.loopOut.bind(thisProperty),loop_out=loopOut),thisProperty.smooth&&(smooth=thisProperty.smooth.bind(thisProperty)),this.getValueAtTime&&(valueAtTime=this.getValueAtTime.bind(this)),this.getVelocityAtTime&&(velocityAtTime=this.getVelocityAtTime.bind(this));var comp=elem.comp.globalData.projectInterface.bind(elem.comp.globalData.projectInterface),time,velocity,value,text,textIndex,textTotal,selectorValue;function lookAt(t,e){e=[e[0]-t[0],e[1]-t[1],e[2]-t[2]],t=Math.atan2(e[0],Math.sqrt(e[1]*e[1]+e[2]*e[2]))/degToRads;return[-Math.atan2(e[1],e[2])/degToRads,t,0]}function easeOut(t,e,i,r,s){return applyEase(easeOutBez,t,e,i,r,s)}function easeIn(t,e,i,r,s){return applyEase(easeInBez,t,e,i,r,s)}function ease(t,e,i,r,s){return applyEase(easeInOutBez,t,e,i,r,s)}function applyEase(t,e,i,r,s,a){void 0===s?(s=i,a=r):e=(e-i)/(r-i),1<e?e=1:e<0&&(e=0);var n=t(e);if($bm_isInstanceOfArray(s)){for(var o=s.length,h=createTypedArray("float32",o),l=0;l<o;l+=1)h[l]=(a[l]-s[l])*n+s[l];return h}return(a-s)*n+s}function nearestKey(t){var e,i,r,s=data.k.length;if(data.k.length&&"number"!=typeof data.k[0])if(i=-1,(t*=elem.comp.globalData.frameRate)<data.k[0].t)i=1,r=data.k[0].t;else{for(e=0;e<s-1;e+=1){if(t===data.k[e].t){i=e+1,r=data.k[e].t;break}if(t>data.k[e].t&&t<data.k[e+1].t){r=(t-data.k[e].t>data.k[e+1].t-t?(i=e+2,data.k[e+1]):(i=e+1,data.k[e])).t;break}}-1===i&&(i=e+1,r=data.k[e].t)}else r=i=0;var a={};return a.index=i,a.time=r/elem.comp.globalData.frameRate,a}function key(t){if(!data.k.length||"number"==typeof data.k[0])throw new Error("The property has no keyframe at index "+t);for(var e={time:data.k[--t].t/elem.comp.globalData.frameRate,value:[]},i=Object.prototype.hasOwnProperty.call(data.k[t],"s")?data.k[t].s:data.k[t-1].e,r=i.length,s=0;s<r;s+=1)e[s]=i[s],e.value[s]=i[s];return e}function framesToTime(t,e){return t/(e=e||elem.comp.globalData.frameRate)}function timeToFrames(t,e){return(t=t||0===t?t:time)*(e=e||elem.comp.globalData.frameRate)}function seedRandom(t){BMMath.seedrandom(randSeed+t)}function sourceRectAtTime(){return elem.sourceRectAtTime()}function substring(t,e){return"string"==typeof value?void 0===e?value.substring(t):value.substring(t,e):""}function substr(t,e){return"string"==typeof value?void 0===e?value.substr(t):value.substr(t,e):""}function posterizeTime(t){time=0===t?0:Math.floor(time*t)/t,value=valueAtTime(time)}var index=elem.data.ind,hasParent=!(!elem.hierarchy||!elem.hierarchy.length),parent,randSeed=Math.floor(1e6*Math.random()),globalData=elem.globalData;function executeExpression(t){return value=t,this.frameExpressionId===elem.globalData.frameId&&"textSelector"!==this.propType?value:("textSelector"===this.propType&&(textIndex=this.textIndex,textTotal=this.textTotal,selectorValue=this.selectorValue),thisLayer||(text=elem.layerInterface.text,thisLayer=elem.layerInterface,thisComp=elem.comp.compInterface,toWorld=thisLayer.toWorld.bind(thisLayer),fromWorld=thisLayer.fromWorld.bind(thisLayer),fromComp=thisLayer.fromComp.bind(thisLayer),toComp=thisLayer.toComp.bind(thisLayer),mask=thisLayer.mask?thisLayer.mask.bind(thisLayer):null,fromCompToSurface=fromComp),!transform&&(transform=elem.layerInterface("ADBE Transform Group"),$bm_transform=transform)&&(anchorPoint=transform.anchorPoint),4===elemType&&(content=content||thisLayer("ADBE Root Vectors Group")),effect=effect||thisLayer(4),(hasParent=!(!elem.hierarchy||!elem.hierarchy.length))&&!parent&&(parent=elem.hierarchy[0].layerInterface),time=this.comp.renderedFrame/this.comp.globalData.frameRate,_needsRandom&&seedRandom(randSeed+time),needsVelocity&&(velocity=velocityAtTime(time)),expression_function(),this.frameExpressionId=elem.globalData.frameId,scoped_bm_rt=scoped_bm_rt.propType===propTypes.SHAPE?scoped_bm_rt.v:scoped_bm_rt)}return executeExpression.__preventDeadCodeRemoval=[$bm_transform,anchorPoint,time,velocity,inPoint,outPoint,width,height,name,loop_in,loop_out,smooth,toComp,fromCompToSurface,toWorld,fromWorld,mask,position,rotation,scale,thisComp,numKeys,active,wiggle,loopInDuration,loopOutDuration,comp,lookAt,easeOut,easeIn,ease,nearestKey,key,text,textIndex,textTotal,selectorValue,framesToTime,timeToFrames,sourceRectAtTime,substring,substr,posterizeTime,index,globalData],executeExpression}return ob.initiateExpression=initiateExpression,ob.__preventDeadCodeRemoval=[window,document,XMLHttpRequest,fetch,frames,$bm_neg,add,$bm_sum,$bm_sub,$bm_mul,$bm_div,$bm_mod,clamp,radians_to_degrees,degreesToRadians,degrees_to_radians,normalize,rgbToHsl,hslToRgb,linear,random,createPath,_lottieGlobal],ob.resetFrame=resetFrame,ob}(),Expressions=(()=>{var t={};return t.initExpressions=function(t){var i=0,r=[];t.renderer.compInterface=CompExpressionInterface(t.renderer),t.renderer.globalData.projectInterface.registerComposition(t.renderer),t.renderer.globalData.pushExpression=function(){i+=1},t.renderer.globalData.popExpression=function(){if(0===--i){var t,e=r.length;for(t=0;t<e;t+=1)r[t].release();r.length=0}},t.renderer.globalData.registerExpressionProperty=function(t){-1===r.indexOf(t)&&r.push(t)}},t.resetFrame=ExpressionManager.resetFrame,t})(),MaskManagerInterface=(()=>{function t(t,e){this._mask=t,this._data=e}return Object.defineProperty(t.prototype,"maskPath",{get:function(){return this._mask.prop.k&&this._mask.prop.getValue(),this._mask.prop}}),Object.defineProperty(t.prototype,"maskOpacity",{get:function(){return this._mask.op.k&&this._mask.op.getValue(),100*this._mask.op.v}}),function(e){for(var i=createSizedArray(e.viewData.length),r=e.viewData.length,s=0;s<r;s+=1)i[s]=new t(e.viewData[s],e.masksProperties[s]);return function(t){for(s=0;s<r;){if(e.masksProperties[s].nm===t)return i[s];s+=1}return null}}})(),ExpressionPropertyInterface=(()=>{var p={pv:0,v:0,mult:1},m={pv:[0,0,0],v:[0,0,0],mult:1};function f(r,s,a){Object.defineProperty(r,"velocity",{get:function(){return s.getVelocityAtTime(s.comp.currentFrame)}}),r.numKeys=s.keyframes?s.keyframes.length:0,r.key=function(t){var e,i;return r.numKeys?(e="",e="s"in s.keyframes[t-1]?s.keyframes[t-1].s:"e"in s.keyframes[t-2]?s.keyframes[t-2].e:s.keyframes[t-2].s,(i="unidimensional"===a?new Number(e):Object.assign({},e)).time=s.keyframes[t-1].t/s.elem.comp.globalData.frameRate,i.value="unidimensional"===a?e[0]:e,i):0},r.valueAtTime=s.getValueAtTime,r.speedAtTime=s.getSpeedAtTime,r.velocityAtTime=s.getVelocityAtTime,r.propertyGroup=s.propertyGroup}function c(){return p}return function(t){var e,i,r,s,a,n,o,h,l;return t?"unidimensional"===t.propType?(i=1/(e=(e=t)&&"pv"in e?e:p).mult,r=e.pv*i,(s=new Number(r)).value=r,f(s,e,"unidimensional"),function(){return e.k&&e.getValue(),r=e.v*i,s.value!==r&&((s=new Number(r)).value=r,s[0]=r,f(s,e,"unidimensional")),s}):(n=1/(a=(a=t)&&"pv"in a?a:m).mult,o=a.data&&a.data.l||a.pv.length,h=createTypedArray("float32",o),l=createTypedArray("float32",o),h.value=l,f(h,a,"multidimensional"),function(){a.k&&a.getValue();for(var t=0;t<o;t+=1)l[t]=a.v[t]*n,h[t]=l[t];return h}):c}})(),TransformExpressionInterface=function(t){function e(t){switch(t){case"scale":case"Scale":case"ADBE Scale":case 6:return e.scale;case"rotation":case"Rotation":case"ADBE Rotation":case"ADBE Rotate Z":case 10:return e.rotation;case"ADBE Rotate X":return e.xRotation;case"ADBE Rotate Y":return e.yRotation;case"position":case"Position":case"ADBE Position":case 2:return e.position;case"ADBE Position_0":return e.xPosition;case"ADBE Position_1":return e.yPosition;case"ADBE Position_2":return e.zPosition;case"anchorPoint":case"AnchorPoint":case"Anchor Point":case"ADBE AnchorPoint":case 1:return e.anchorPoint;case"opacity":case"Opacity":case 11:return e.opacity;default:return null}}var i,r,s,a;return Object.defineProperty(e,"rotation",{get:ExpressionPropertyInterface(t.r||t.rz)}),Object.defineProperty(e,"zRotation",{get:ExpressionPropertyInterface(t.rz||t.r)}),Object.defineProperty(e,"xRotation",{get:ExpressionPropertyInterface(t.rx)}),Object.defineProperty(e,"yRotation",{get:ExpressionPropertyInterface(t.ry)}),Object.defineProperty(e,"scale",{get:ExpressionPropertyInterface(t.s)}),t.p?a=ExpressionPropertyInterface(t.p):(i=ExpressionPropertyInterface(t.px),r=ExpressionPropertyInterface(t.py),t.pz&&(s=ExpressionPropertyInterface(t.pz))),Object.defineProperty(e,"position",{get:function(){return t.p?a():[i(),r(),s?s():0]}}),Object.defineProperty(e,"xPosition",{get:ExpressionPropertyInterface(t.px)}),Object.defineProperty(e,"yPosition",{get:ExpressionPropertyInterface(t.py)}),Object.defineProperty(e,"zPosition",{get:ExpressionPropertyInterface(t.pz)}),Object.defineProperty(e,"anchorPoint",{get:ExpressionPropertyInterface(t.a)}),Object.defineProperty(e,"opacity",{get:ExpressionPropertyInterface(t.o)}),Object.defineProperty(e,"skew",{get:ExpressionPropertyInterface(t.sk)}),Object.defineProperty(e,"skewAxis",{get:ExpressionPropertyInterface(t.sa)}),Object.defineProperty(e,"orientation",{get:ExpressionPropertyInterface(t.or)}),e},LayerExpressionInterface=(()=>{function s(t){var e=new Matrix;return void 0!==t?this._elem.finalTransform.mProp.getValueAtTime(t).clone(e):this._elem.finalTransform.mProp.applyToMatrix(e),e}function a(t,e){e=this.getMatrix(e);return e.props[12]=0,e.props[13]=0,e.props[14]=0,this.applyPoint(e,t)}function n(t,e){e=this.getMatrix(e);return this.applyPoint(e,t)}function o(t,e){e=this.getMatrix(e);return e.props[12]=0,e.props[13]=0,e.props[14]=0,this.invertPoint(e,t)}function h(t,e){e=this.getMatrix(e);return this.invertPoint(e,t)}function l(t,e){if(this._elem.hierarchy&&this._elem.hierarchy.length)for(var i=this._elem.hierarchy.length,r=0;r<i;r+=1)this._elem.hierarchy[r].finalTransform.mProp.applyToMatrix(t);return t.applyToPointArray(e[0],e[1],e[2]||0)}function p(t,e){if(this._elem.hierarchy&&this._elem.hierarchy.length)for(var i=this._elem.hierarchy.length,r=0;r<i;r+=1)this._elem.hierarchy[r].finalTransform.mProp.applyToMatrix(t);return t.inversePoint(e)}function m(t){var e=new Matrix;if(e.reset(),this._elem.finalTransform.mProp.applyToMatrix(e),this._elem.hierarchy&&this._elem.hierarchy.length)for(var i=this._elem.hierarchy.length,r=0;r<i;r+=1)this._elem.hierarchy[r].finalTransform.mProp.applyToMatrix(e);return e.inversePoint(t)}function f(){return[1,1,1,1]}return function(e){function i(t){switch(t){case"ADBE Root Vectors Group":case"Contents":case 2:return i.shapeInterface;case 1:case 6:case"Transform":case"transform":case"ADBE Transform Group":return r;case 4:case"ADBE Effect Parade":case"effects":case"Effects":return i.effect;case"ADBE Text Properties":return i.textInterface;default:return null}}i.getMatrix=s,i.invertPoint=p,i.applyPoint=l,i.toWorld=n,i.toWorldVec=a,i.fromWorld=h,i.fromWorldVec=o,i.toComp=n,i.fromComp=m,i.sampleImage=f,i.sourceRectAtTime=e.sourceRectAtTime.bind(e);var r,t=getDescriptor(r=TransformExpressionInterface((i._elem=e).finalTransform.mProp),"anchorPoint");return Object.defineProperties(i,{hasParent:{get:function(){return e.hierarchy.length}},parent:{get:function(){return e.hierarchy[0].layerInterface}},rotation:getDescriptor(r,"rotation"),scale:getDescriptor(r,"scale"),position:getDescriptor(r,"position"),opacity:getDescriptor(r,"opacity"),anchorPoint:t,anchor_point:t,transform:{get:function(){return r}},active:{get:function(){return e.isInRange}}}),i.startTime=e.data.st,i.index=e.data.ind,i.source=e.data.refId,i.height=0===e.data.ty?e.data.h:100,i.width=0===e.data.ty?e.data.w:100,i.inPoint=e.data.ip/e.comp.globalData.frameRate,i.outPoint=e.data.op/e.comp.globalData.frameRate,i._name=e.data.nm,i.registerMaskInterface=function(t){i.mask=new MaskManagerInterface(t,e)},i.registerEffectsInterface=function(t){i.effect=t},i}})(),propertyGroupFactory=function(e,i){return function(t){return(t=void 0===t?1:t)<=0?e:i(t-1)}},PropertyInterface=function(t,e){var i={_name:t};return function(t){return(t=void 0===t?1:t)<=0?i:e(t-1)}},EffectsExpressionInterface=(()=>{function p(t,e,i,r){var s=ExpressionPropertyInterface(t.p);return t.p.setGroupProperty&&t.p.setGroupProperty(PropertyInterface("",r)),function(){return 10===e?i.comp.compInterface(t.p.v):s()}}return{createEffectsInterface:function(t,e){if(t.effectsManager){var i,r=[],s=t.data.ef,a=t.effectsManager.effectElements.length;for(i=0;i<a;i+=1)r.push(function t(s,e,i,r){function a(t){for(var e=s.ef,i=0,r=e.length;i<r;){if(t===e[i].nm||t===e[i].mn||t===e[i].ix)return 5===e[i].ty?o[i]:o[i]();i+=1}throw new Error}var n=propertyGroupFactory(a,i);var o=[];var h;var l=s.ef.length;for(h=0;h<l;h+=1)5===s.ef[h].ty?o.push(t(s.ef[h],e.effectElements[h],e.effectElements[h].propertyGroup,r)):o.push(p(e.effectElements[h],s.ef[h].ty,r,n));"ADBE Color Control"===s.mn&&Object.defineProperty(a,"color",{get:function(){return o[0]()}});Object.defineProperties(a,{numProperties:{get:function(){return s.np}},_name:{value:s.nm},propertyGroup:{value:n}});a.enabled=0!==s.en;a.active=a.enabled;return a}(s[i],t.effectsManager.effectElements[i],e,t));var n=t.data.ef||[],o=function(t){for(i=0,a=n.length;i<a;){if(t===n[i].nm||t===n[i].mn||t===n[i].ix)return r[i];i+=1}return null};return Object.defineProperty(o,"numProperties",{get:function(){return n.length}}),o}return null}}})(),ShapePathInterface=function(t,e,i){var r=e.sh;function s(t){return"Shape"===t||"shape"===t||"Path"===t||"path"===t||"ADBE Vector Shape"===t||2===t?s.path:null}e=propertyGroupFactory(s,i);return r.setGroupProperty(PropertyInterface("Path",e)),Object.defineProperties(s,{path:{get:function(){return r.k&&r.getValue(),r}},shape:{get:function(){return r.k&&r.getValue(),r}},_name:{value:t.nm},ix:{value:t.ix},propertyIndex:{value:t.ix},mn:{value:t.mn},propertyGroup:{value:i}}),s},ShapeExpressionInterface=(()=>{function n(t,e,i){for(var r=[],s=t?t.length:0,a=0;a<s;a+=1)"gr"===t[a].ty?r.push(((t,e,i)=>{function r(t){switch(t){case"ADBE Vectors Group":case"Contents":case 2:return r.content;default:return r.transform}}return r.propertyGroup=propertyGroupFactory(r,i),i=((t,e,i)=>{function r(t){for(var e=0,i=s.length;e<i;){if(s[e]._name===t||s[e].mn===t||s[e].propertyIndex===t||s[e].ix===t||s[e].ind===t)return s[e];e+=1}return"number"==typeof t?s[t-1]:null}r.propertyGroup=propertyGroupFactory(r,i),s=n(t.it,e.it,r.propertyGroup),r.numProperties=s.length;var s,i=o(t.it[t.it.length-1],e.it[e.it.length-1],r.propertyGroup);return r.transform=i,r.propertyIndex=t.cix,r._name=t.nm,r})(t,e,r.propertyGroup),e=o(t.it[t.it.length-1],e.it[e.it.length-1],r.propertyGroup),r.content=i,r.transform=e,Object.defineProperty(r,"_name",{get:function(){return t.nm}}),r.numProperties=t.np,r.propertyIndex=t.ix,r.nm=t.nm,r.mn=t.mn,r})(t[a],e[a],i)):"fl"===t[a].ty?r.push(((t,e,i)=>{function r(t){return"Color"===t||"color"===t?r.color:"Opacity"===t||"opacity"===t?r.opacity:null}return Object.defineProperties(r,{color:{get:ExpressionPropertyInterface(e.c)},opacity:{get:ExpressionPropertyInterface(e.o)},_name:{value:t.nm},mn:{value:t.mn}}),e.c.setGroupProperty(PropertyInterface("Color",i)),e.o.setGroupProperty(PropertyInterface("Opacity",i)),r})(t[a],e[a],i)):"st"===t[a].ty?r.push(((t,e,i)=>{var r,i=propertyGroupFactory(h,i),s=propertyGroupFactory(void 0,i),a=t.d?t.d.length:0,n={};for(r=0;r<a;r+=1){{o=void 0;var o=r;Object.defineProperty(n,t.d[o].nm,{get:ExpressionPropertyInterface(e.d.dataProps[o].p)})}e.d.dataProps[r].p.setGroupProperty(s)}function h(t){return"Color"===t||"color"===t?h.color:"Opacity"===t||"opacity"===t?h.opacity:"Stroke Width"===t||"stroke width"===t?h.strokeWidth:null}return Object.defineProperties(h,{color:{get:ExpressionPropertyInterface(e.c)},opacity:{get:ExpressionPropertyInterface(e.o)},strokeWidth:{get:ExpressionPropertyInterface(e.w)},dash:{get:function(){return n}},_name:{value:t.nm},mn:{value:t.mn}}),e.c.setGroupProperty(PropertyInterface("Color",i)),e.o.setGroupProperty(PropertyInterface("Opacity",i)),e.w.setGroupProperty(PropertyInterface("Stroke Width",i)),h})(t[a],e[a],i)):"tm"===t[a].ty?r.push(((e,t,i)=>{function r(t){return t===e.e.ix||"End"===t||"end"===t?r.end:t===e.s.ix?r.start:t===e.o.ix?r.offset:null}var s=propertyGroupFactory(r,i);return r.propertyIndex=e.ix,t.s.setGroupProperty(PropertyInterface("Start",s)),t.e.setGroupProperty(PropertyInterface("End",s)),t.o.setGroupProperty(PropertyInterface("Offset",s)),r.propertyIndex=e.ix,r.propertyGroup=i,Object.defineProperties(r,{start:{get:ExpressionPropertyInterface(t.s)},end:{get:ExpressionPropertyInterface(t.e)},offset:{get:ExpressionPropertyInterface(t.o)},_name:{value:e.nm}}),r.mn=e.mn,r})(t[a],e[a],i)):"tr"!==t[a].ty&&("el"===t[a].ty?r.push(((e,t,i)=>{function r(t){return e.p.ix===t?r.position:e.s.ix===t?r.size:null}return i=propertyGroupFactory(r,i),r.propertyIndex=e.ix,(t="tm"===t.sh.ty?t.sh.prop:t.sh).s.setGroupProperty(PropertyInterface("Size",i)),t.p.setGroupProperty(PropertyInterface("Position",i)),Object.defineProperties(r,{size:{get:ExpressionPropertyInterface(t.s)},position:{get:ExpressionPropertyInterface(t.p)},_name:{value:e.nm}}),r.mn=e.mn,r})(t[a],e[a],i)):"sr"===t[a].ty?r.push(((e,t,i)=>{function r(t){return e.p.ix===t?r.position:e.r.ix===t?r.rotation:e.pt.ix===t?r.points:e.or.ix===t||"ADBE Vector Star Outer Radius"===t?r.outerRadius:e.os.ix===t?r.outerRoundness:!e.ir||e.ir.ix!==t&&"ADBE Vector Star Inner Radius"!==t?e.is&&e.is.ix===t?r.innerRoundness:null:r.innerRadius}return i=propertyGroupFactory(r,i),t="tm"===t.sh.ty?t.sh.prop:t.sh,r.propertyIndex=e.ix,t.or.setGroupProperty(PropertyInterface("Outer Radius",i)),t.os.setGroupProperty(PropertyInterface("Outer Roundness",i)),t.pt.setGroupProperty(PropertyInterface("Points",i)),t.p.setGroupProperty(PropertyInterface("Position",i)),t.r.setGroupProperty(PropertyInterface("Rotation",i)),e.ir&&(t.ir.setGroupProperty(PropertyInterface("Inner Radius",i)),t.is.setGroupProperty(PropertyInterface("Inner Roundness",i))),Object.defineProperties(r,{position:{get:ExpressionPropertyInterface(t.p)},rotation:{get:ExpressionPropertyInterface(t.r)},points:{get:ExpressionPropertyInterface(t.pt)},outerRadius:{get:ExpressionPropertyInterface(t.or)},outerRoundness:{get:ExpressionPropertyInterface(t.os)},innerRadius:{get:ExpressionPropertyInterface(t.ir)},innerRoundness:{get:ExpressionPropertyInterface(t.is)},_name:{value:e.nm}}),r.mn=e.mn,r})(t[a],e[a],i)):"sh"===t[a].ty?r.push(ShapePathInterface(t[a],e[a],i)):"rc"===t[a].ty?r.push(((e,t,i)=>{function r(t){return e.p.ix===t?r.position:e.r.ix===t?r.roundness:e.s.ix===t||"Size"===t||"ADBE Vector Rect Size"===t?r.size:null}return i=propertyGroupFactory(r,i),t="tm"===t.sh.ty?t.sh.prop:t.sh,r.propertyIndex=e.ix,t.p.setGroupProperty(PropertyInterface("Position",i)),t.s.setGroupProperty(PropertyInterface("Size",i)),t.r.setGroupProperty(PropertyInterface("Rotation",i)),Object.defineProperties(r,{position:{get:ExpressionPropertyInterface(t.p)},roundness:{get:ExpressionPropertyInterface(t.r)},size:{get:ExpressionPropertyInterface(t.s)},_name:{value:e.nm}}),r.mn=e.mn,r})(t[a],e[a],i)):"rd"===t[a].ty?r.push(((e,t,i)=>{function r(t){return e.r.ix===t||"Round Corners 1"===t?r.radius:null}return i=propertyGroupFactory(r,i),r.propertyIndex=e.ix,t.rd.setGroupProperty(PropertyInterface("Radius",i)),Object.defineProperties(r,{radius:{get:ExpressionPropertyInterface(t.rd)},_name:{value:e.nm}}),r.mn=e.mn,r})(t[a],e[a],i)):"rp"===t[a].ty?r.push(((e,t,i)=>{function r(t){return e.c.ix===t||"Copies"===t?r.copies:e.o.ix===t||"Offset"===t?r.offset:null}return i=propertyGroupFactory(r,i),r.propertyIndex=e.ix,t.c.setGroupProperty(PropertyInterface("Copies",i)),t.o.setGroupProperty(PropertyInterface("Offset",i)),Object.defineProperties(r,{copies:{get:ExpressionPropertyInterface(t.c)},offset:{get:ExpressionPropertyInterface(t.o)},_name:{value:e.nm}}),r.mn=e.mn,r})(t[a],e[a],i)):"gf"===t[a].ty?r.push(((t,e,i)=>{function r(t){return"Start Point"===t||"start point"===t?r.startPoint:"End Point"===t||"end point"===t?r.endPoint:"Opacity"===t||"opacity"===t?r.opacity:null}return Object.defineProperties(r,{startPoint:{get:ExpressionPropertyInterface(e.s)},endPoint:{get:ExpressionPropertyInterface(e.e)},opacity:{get:ExpressionPropertyInterface(e.o)},type:{get:function(){return"a"}},_name:{value:t.nm},mn:{value:t.mn}}),e.s.setGroupProperty(PropertyInterface("Start Point",i)),e.e.setGroupProperty(PropertyInterface("End Point",i)),e.o.setGroupProperty(PropertyInterface("Opacity",i)),r})(t[a],e[a],i)):r.push((t[a],e[a],function(){return null})));return r}function o(e,t,i){function r(t){return e.a.ix===t||"Anchor Point"===t?r.anchorPoint:e.o.ix===t||"Opacity"===t?r.opacity:e.p.ix===t||"Position"===t?r.position:e.r.ix===t||"Rotation"===t||"ADBE Vector Rotation"===t?r.rotation:e.s.ix===t||"Scale"===t?r.scale:e.sk&&e.sk.ix===t||"Skew"===t?r.skew:e.sa&&e.sa.ix===t||"Skew Axis"===t?r.skewAxis:null}var s=propertyGroupFactory(r,i);return t.transform.mProps.o.setGroupProperty(PropertyInterface("Opacity",s)),t.transform.mProps.p.setGroupProperty(PropertyInterface("Position",s)),t.transform.mProps.a.setGroupProperty(PropertyInterface("Anchor Point",s)),t.transform.mProps.s.setGroupProperty(PropertyInterface("Scale",s)),t.transform.mProps.r.setGroupProperty(PropertyInterface("Rotation",s)),t.transform.mProps.sk&&(t.transform.mProps.sk.setGroupProperty(PropertyInterface("Skew",s)),t.transform.mProps.sa.setGroupProperty(PropertyInterface("Skew Angle",s))),t.transform.op.setGroupProperty(PropertyInterface("Opacity",s)),Object.defineProperties(r,{opacity:{get:ExpressionPropertyInterface(t.transform.mProps.o)},position:{get:ExpressionPropertyInterface(t.transform.mProps.p)},anchorPoint:{get:ExpressionPropertyInterface(t.transform.mProps.a)},scale:{get:ExpressionPropertyInterface(t.transform.mProps.s)},rotation:{get:ExpressionPropertyInterface(t.transform.mProps.r)},skew:{get:ExpressionPropertyInterface(t.transform.mProps.sk)},skewAxis:{get:ExpressionPropertyInterface(t.transform.mProps.sa)},_name:{value:e.nm}}),r.ty="tr",r.mn=e.mn,r.propertyGroup=i,r}return function(t,e,r){var s;function i(t){if("number"==typeof t)return 0===(t=void 0===t?1:t)?r:s[t-1];for(var e=0,i=s.length;e<i;){if(s[e]._name===t)return s[e];e+=1}return null}return i.propertyGroup=propertyGroupFactory(i,function(){return r}),s=n(t,e,i.propertyGroup),i.numProperties=s.length,i._name="Contents",i}})(),TextExpressionInterface=function(e){var i;function r(t){return"ADBE Text Document"!==t?null:r.sourceText}return Object.defineProperty(r,"sourceText",{get:function(){e.textProperty.getValue();var t=e.textProperty.currentData.t;return i&&t===i.value||((i=new String(t)).value=t||new String(t),Object.defineProperty(i,"style",{get:function(){return{fillColor:e.textProperty.currentData.fc}}})),i}}),r};function _typeof(t){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var FootageInterface=(()=>{var r=function(t){var i="",r=t.getFootageData();function s(t){var e;return r[t]?"object"===_typeof(r=r[i=t])?s:r:-1!==(e=t.indexOf(i))?(t=parseInt(t.substr(e+i.length),10),"object"===_typeof(r=r[t])?s:r):""}return function(){return i="",r=t.getFootageData(),s}};return function(t){function e(t){return"Data"===t?e.dataInterface:null}function i(t){return"Outline"===t?i.outlineInterface():null}return e._name="Data",e.dataInterface=(t=t,i._name="Outline",i.outlineInterface=r(t),i),e}})(),interfaces={layer:LayerExpressionInterface,effects:EffectsExpressionInterface,comp:CompExpressionInterface,shape:ShapeExpressionInterface,text:TextExpressionInterface,footage:FootageInterface};function getInterface(t){return interfaces[t]||null}var expressionHelpers={searchExpressions:function(t,e,i){e.x&&(i.k=!0,i.x=!0,i.initiateExpression=ExpressionManager.initiateExpression,i.effectsSequence.push(i.initiateExpression(t,e,i).bind(i)))},getSpeedAtTime:function(t){var e=this.getValueAtTime(t),i=this.getValueAtTime(t+-.01),r=0;if(e.length){for(var s=0;s<e.length;s+=1)r+=Math.pow(i[s]-e[s],2);r=100*Math.sqrt(r)}else r=0;return r},getVelocityAtTime:function(t){if(void 0!==this.vel)return this.vel;var e=this.getValueAtTime(t),i=this.getValueAtTime(t+-.001);if(e.length)for(var r=createTypedArray("float32",e.length),s=0;s<e.length;s+=1)r[s]=(i[s]-e[s])/-.001;else r=(i-e)/-.001;return r},getValueAtTime:function(t){return(t=(t*=this.elem.globalData.frameRate)-this.offsetTime)!==this._cachingAtTime.lastFrame&&(this._cachingAtTime.lastIndex=this._cachingAtTime.lastFrame<t?this._cachingAtTime.lastIndex:0,this._cachingAtTime.value=this.interpolateValue(t,this._cachingAtTime),this._cachingAtTime.lastFrame=t),this._cachingAtTime.value},getStaticValueAtTime:function(){return this.pv},setGroupProperty:function(t){this.propertyGroup=t}};function addPropertyDecorator(){function n(t,e,i){if(!this.k||!this.keyframes)return this.pv;t=t?t.toLowerCase():"";var r,s,a,n,o,h=this.comp.renderedFrame,l=this.keyframes,p=l[l.length-1].t;if(h<=p)return this.pv;if(i?s=p-(r=e?Math.abs(p-this.elem.comp.globalData.frameRate*e):Math.max(0,p-this.elem.data.ip)):((!e||e>l.length-1)&&(e=l.length-1),r=p-(s=l[l.length-1-e].t)),"pingpong"===t){if(Math.floor((h-s)/r)%2!=0)return this.getValueAtTime((r-(h-s)%r+s)/this.comp.globalData.frameRate,0)}else{if("offset"===t){var m=this.getValueAtTime(s/this.comp.globalData.frameRate,0),f=this.getValueAtTime(p/this.comp.globalData.frameRate,0),c=this.getValueAtTime(((h-s)%r+s)/this.comp.globalData.frameRate,0),d=Math.floor((h-s)/r);if(this.pv.length){for(n=(o=new Array(m.length)).length,a=0;a<n;a+=1)o[a]=(f[a]-m[a])*d+c[a];return o}return(f-m)*d+c}if("continue"===t){var u=this.getValueAtTime(p/this.comp.globalData.frameRate,0),y=this.getValueAtTime((p-.001)/this.comp.globalData.frameRate,0);if(this.pv.length){for(n=(o=new Array(u.length)).length,a=0;a<n;a+=1)o[a]=u[a]+(u[a]-y[a])*((h-p)/this.comp.globalData.frameRate)/5e-4;return o}return u+(h-p)/.001*(u-y)}}return this.getValueAtTime(((h-s)%r+s)/this.comp.globalData.frameRate,0)}function o(t,e,i){if(!this.k)return this.pv;t=t?t.toLowerCase():"";var r,s,a,n,o,h=this.comp.renderedFrame,l=this.keyframes,p=l[0].t;if(p<=h)return this.pv;if(i?s=p+(r=e?Math.abs(this.elem.comp.globalData.frameRate*e):Math.max(0,this.elem.data.op-p)):r=(s=l[e=!e||e>l.length-1?l.length-1:e].t)-p,"pingpong"===t){if(Math.floor((p-h)/r)%2==0)return this.getValueAtTime(((p-h)%r+p)/this.comp.globalData.frameRate,0)}else{if("offset"===t){var m=this.getValueAtTime(p/this.comp.globalData.frameRate,0),f=this.getValueAtTime(s/this.comp.globalData.frameRate,0),c=this.getValueAtTime((r-(p-h)%r+p)/this.comp.globalData.frameRate,0),d=Math.floor((p-h)/r)+1;if(this.pv.length){for(n=(o=new Array(m.length)).length,a=0;a<n;a+=1)o[a]=c[a]-(f[a]-m[a])*d;return o}return c-(f-m)*d}if("continue"===t){var u=this.getValueAtTime(p/this.comp.globalData.frameRate,0),y=this.getValueAtTime((p+.001)/this.comp.globalData.frameRate,0);if(this.pv.length){for(n=(o=new Array(u.length)).length,a=0;a<n;a+=1)o[a]=u[a]+(u[a]-y[a])*(p-h)/.001;return o}return u+(u-y)*(p-h)/.001}}return this.getValueAtTime((r-((p-h)%r+p))/this.comp.globalData.frameRate,0)}function h(t,e){if(!this.k)return this.pv;if(t=.5*(t||.4),(e=Math.floor(e||5))<=1)return this.pv;for(var i,r=this.comp.renderedFrame/this.comp.globalData.frameRate,s=r-t,a=1<e?(r+t-s)/(e-1):1,n=0,o=0,h=this.pv.length?createTypedArray("float32",this.pv.length):0;n<e;){if(i=this.getValueAtTime(s+n*a),this.pv.length)for(o=0;o<this.pv.length;o+=1)h[o]+=i[o];else h+=i;n+=1}if(this.pv.length)for(o=0;o<this.pv.length;o+=1)h[o]/=e;else h/=e;return h}var r=TransformPropertyFactory.getTransformProperty,l=(TransformPropertyFactory.getTransformProperty=function(t,e,i){t=r(t,e,i);return t.dynamicProperties.length?t.getValueAtTime=function(t){this._transformCachingAtTime||(this._transformCachingAtTime={v:new Matrix});var e,i,r,s,a=this._transformCachingAtTime.v;return a.cloneFromProps(this.pre.props),this.appliedTransformations<1&&(i=this.a.getValueAtTime(t),a.translate(-i[0]*this.a.mult,-i[1]*this.a.mult,i[2]*this.a.mult)),this.appliedTransformations<2&&(i=this.s.getValueAtTime(t),a.scale(i[0]*this.s.mult,i[1]*this.s.mult,i[2]*this.s.mult)),this.sk&&this.appliedTransformations<3&&(i=this.sk.getValueAtTime(t),e=this.sa.getValueAtTime(t),a.skewFromAxis(-i*this.sk.mult,e*this.sa.mult)),this.r&&this.appliedTransformations<4?(i=this.r.getValueAtTime(t),a.rotate(-i*this.r.mult)):!this.r&&this.appliedTransformations<4&&(e=this.rz.getValueAtTime(t),i=this.ry.getValueAtTime(t),r=this.rx.getValueAtTime(t),s=this.or.getValueAtTime(t),a.rotateZ(-e*this.rz.mult).rotateY(i*this.ry.mult).rotateX(r*this.rx.mult).rotateZ(-s[2]*this.or.mult).rotateY(s[1]*this.or.mult).rotateX(s[0]*this.or.mult)),this.data.p&&this.data.p.s?(e=this.px.getValueAtTime(t),i=this.py.getValueAtTime(t),this.data.p.z?(r=this.pz.getValueAtTime(t),a.translate(e*this.px.mult,i*this.py.mult,-r*this.pz.mult)):a.translate(e*this.px.mult,i*this.py.mult,0)):(s=this.p.getValueAtTime(t),a.translate(s[0]*this.p.mult,s[1]*this.p.mult,-s[2]*this.p.mult)),a}.bind(t):t.getValueAtTime=function(){return this.v.clone(new Matrix)}.bind(t),t.setGroupProperty=expressionHelpers.setGroupProperty,t},PropertyFactory.getProp);PropertyFactory.getProp=function(t,e,i,r,s){var r=l(t,e,i,r,s),a=(r.kf?r.getValueAtTime=expressionHelpers.getValueAtTime.bind(r):r.getValueAtTime=expressionHelpers.getStaticValueAtTime.bind(r),r.setGroupProperty=expressionHelpers.setGroupProperty,r.loopOut=n,r.loopIn=o,r.smooth=h,r.getVelocityAtTime=expressionHelpers.getVelocityAtTime.bind(r),r.getSpeedAtTime=expressionHelpers.getSpeedAtTime.bind(r),r.numKeys=1===e.a?e.k.length:0,r.propertyIndex=e.ix,0);return 0!==i&&(a=createTypedArray("float32",(1===e.a?e.k[0].s:e.k).length)),r._cachingAtTime={lastFrame:initialDefaultFrame,lastIndex:0,value:a},expressionHelpers.searchExpressions(t,e,r),r.k&&s.addDynamicProperty(r),r};var t=ShapePropertyFactory.getConstructorFunction(),e=ShapePropertyFactory.getKeyframedConstructorFunction();function i(){}i.prototype={vertices:function(t,e){this.k&&this.getValue();for(var i=this.v,r=(i=void 0!==e?this.getValueAtTime(e,0):i)._length,s=i[t],a=i.v,n=createSizedArray(r),o=0;o<r;o+=1)n[o]="i"===t||"o"===t?[s[o][0]-a[o][0],s[o][1]-a[o][1]]:[s[o][0],s[o][1]];return n},points:function(t){return this.vertices("v",t)},inTangents:function(t){return this.vertices("i",t)},outTangents:function(t){return this.vertices("o",t)},isClosed:function(){return this.v.c},pointOnPath:function(t,e){for(var i=this.v,e=(void 0!==e&&(i=this.getValueAtTime(e,0)),this._segmentsLength||(this._segmentsLength=bez.getSegmentsLength(i)),this._segmentsLength),r=e.lengths,s=e.totalLength*t,a=0,n=r.length,o=0;a<n;){if(o+r[a].addedLength>s){var h=a,l=i.c&&a===n-1?0:a+1,p=(s-o)/r[a].addedLength,m=bez.getPointInSegment(i.v[h],i.v[l],i.o[h],i.i[l],p,r[a]);break}o+=r[a].addedLength,a+=1}return m=m||(i.c?[i.v[0][0],i.v[0][1]]:[i.v[i._length-1][0],i.v[i._length-1][1]])},vectorOnPath:function(t,e,i){1==t?t=this.v.c:0==t&&(t=.999);var r=this.pointOnPath(t,e),t=this.pointOnPath(t+.001,e),e=t[0]-r[0],t=t[1]-r[1],r=Math.sqrt(Math.pow(e,2)+Math.pow(t,2));return 0===r?[0,0]:"tangent"===i?[e/r,t/r]:[-t/r,e/r]},tangentOnPath:function(t,e){return this.vectorOnPath(t,e,"tangent")},normalOnPath:function(t,e){return this.vectorOnPath(t,e,"normal")},setGroupProperty:expressionHelpers.setGroupProperty,getValueAtTime:expressionHelpers.getStaticValueAtTime},extendPrototype([i],t),extendPrototype([i],e),e.prototype.getValueAtTime=function(t){return this._cachingAtTime||(this._cachingAtTime={shapeValue:shapePool.clone(this.pv),lastIndex:0,lastTime:initialDefaultFrame}),(t=(t*=this.elem.globalData.frameRate)-this.offsetTime)!==this._cachingAtTime.lastTime&&(this._cachingAtTime.lastIndex=this._cachingAtTime.lastTime<t?this._caching.lastIndex:0,this._cachingAtTime.lastTime=t,this.interpolateShape(t,this._cachingAtTime.shapeValue,this._cachingAtTime)),this._cachingAtTime.shapeValue},e.prototype.initiateExpression=ExpressionManager.initiateExpression;var a=ShapePropertyFactory.getShapeProp;ShapePropertyFactory.getShapeProp=function(t,e,i,r,s){r=a(t,e,i,r,s);return r.propertyIndex=e.ix,r.lock=!1,3===i?expressionHelpers.searchExpressions(t,e.pt,r):4===i&&expressionHelpers.searchExpressions(t,e.ks,r),r.k&&t.addDynamicProperty(r),r}}function initialize$1(){addPropertyDecorator()}function addDecorator(){TextProperty.prototype.getExpressionValue=function(t,e){var i,e=this.calculateExpression(e);return t.t!==e?(this.copyData(i={},t),i.t=e.toString(),i.__complete=!1,i):t},TextProperty.prototype.searchProperty=function(){var t=this.searchKeyframes(),e=this.searchExpressions();return this.kf=t||e,this.kf},TextProperty.prototype.searchExpressions=function(){return this.data.d.x?(this.calculateExpression=ExpressionManager.initiateExpression.bind(this)(this.elem,this.data.d,this),this.addEffect(this.getExpressionValue.bind(this)),!0):null}}function initialize(){addDecorator()}function SVGComposableEffect(){}SVGComposableEffect.prototype={createMergeNode:function(t,e){var i,r,s=createNS("feMerge");for(s.setAttribute("result",t),r=0;r<e.length;r+=1)(i=createNS("feMergeNode")).setAttribute("in",e[r]),s.appendChild(i),s.appendChild(i);return s}};var linearFilterValue="0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0 0 0";function SVGTintFilter(t,e,i,r,s){this.filterManager=e;e=createNS("feColorMatrix"),e.setAttribute("type","matrix"),e.setAttribute("color-interpolation-filters","linearRGB"),e.setAttribute("values",linearFilterValue+" 1 0"),(this.linearFilter=e).setAttribute("result",r+"_tint_1"),t.appendChild(e),(e=createNS("feColorMatrix")).setAttribute("type","matrix"),e.setAttribute("color-interpolation-filters","sRGB"),e.setAttribute("values","1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 1 0"),e.setAttribute("result",r+"_tint_2"),t.appendChild(e),this.matrixFilter=e,e=this.createMergeNode(r,[s,r+"_tint_1",r+"_tint_2"]);t.appendChild(e)}function SVGFillFilter(t,e,i,r){this.filterManager=e;e=createNS("feColorMatrix");e.setAttribute("type","matrix"),e.setAttribute("color-interpolation-filters","sRGB"),e.setAttribute("values","1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 1 0"),e.setAttribute("result",r),t.appendChild(e),this.matrixFilter=e}function SVGStrokeEffect(t,e,i){this.initialized=!1,this.filterManager=e,this.elem=i,this.paths=[]}function SVGTritoneFilter(t,e,i,r){this.filterManager=e;e=createNS("feColorMatrix"),e.setAttribute("type","matrix"),e.setAttribute("color-interpolation-filters","linearRGB"),e.setAttribute("values","0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0 0 0 1 0"),t.appendChild(e),e=createNS("feComponentTransfer"),e.setAttribute("color-interpolation-filters","sRGB"),e.setAttribute("result",r),this.matrixFilter=e,r=createNS("feFuncR"),r.setAttribute("type","table"),e.appendChild(r),this.feFuncR=r,r=createNS("feFuncG"),r.setAttribute("type","table"),e.appendChild(r),this.feFuncG=r,r=createNS("feFuncB");r.setAttribute("type","table"),e.appendChild(r),this.feFuncB=r,t.appendChild(e)}function SVGProLevelsFilter(t,e,i,r){this.filterManager=e;var e=this.filterManager.effectElements,s=createNS("feComponentTransfer");(e[10].p.k||0!==e[10].p.v||e[11].p.k||1!==e[11].p.v||e[12].p.k||1!==e[12].p.v||e[13].p.k||0!==e[13].p.v||e[14].p.k||1!==e[14].p.v)&&(this.feFuncR=this.createFeFunc("feFuncR",s)),(e[17].p.k||0!==e[17].p.v||e[18].p.k||1!==e[18].p.v||e[19].p.k||1!==e[19].p.v||e[20].p.k||0!==e[20].p.v||e[21].p.k||1!==e[21].p.v)&&(this.feFuncG=this.createFeFunc("feFuncG",s)),(e[24].p.k||0!==e[24].p.v||e[25].p.k||1!==e[25].p.v||e[26].p.k||1!==e[26].p.v||e[27].p.k||0!==e[27].p.v||e[28].p.k||1!==e[28].p.v)&&(this.feFuncB=this.createFeFunc("feFuncB",s)),(e[31].p.k||0!==e[31].p.v||e[32].p.k||1!==e[32].p.v||e[33].p.k||1!==e[33].p.v||e[34].p.k||0!==e[34].p.v||e[35].p.k||1!==e[35].p.v)&&(this.feFuncA=this.createFeFunc("feFuncA",s)),(this.feFuncR||this.feFuncG||this.feFuncB||this.feFuncA)&&(s.setAttribute("color-interpolation-filters","sRGB"),t.appendChild(s)),(e[3].p.k||0!==e[3].p.v||e[4].p.k||1!==e[4].p.v||e[5].p.k||1!==e[5].p.v||e[6].p.k||0!==e[6].p.v||e[7].p.k||1!==e[7].p.v)&&((s=createNS("feComponentTransfer")).setAttribute("color-interpolation-filters","sRGB"),s.setAttribute("result",r),t.appendChild(s),this.feFuncRComposed=this.createFeFunc("feFuncR",s),this.feFuncGComposed=this.createFeFunc("feFuncG",s),this.feFuncBComposed=this.createFeFunc("feFuncB",s))}function SVGDropShadowEffect(t,e,i,r,s){var a=e.container.globalData.renderConfig.filterSize,n=e.data.fs||a,n=(t.setAttribute("x",n.x||a.x),t.setAttribute("y",n.y||a.y),t.setAttribute("width",n.width||a.width),t.setAttribute("height",n.height||a.height),this.filterManager=e,createNS("feGaussianBlur")),a=(n.setAttribute("in","SourceAlpha"),n.setAttribute("result",r+"_drop_shadow_1"),n.setAttribute("stdDeviation","0"),this.feGaussianBlur=n,t.appendChild(n),createNS("feOffset")),e=(a.setAttribute("dx","25"),a.setAttribute("dy","0"),a.setAttribute("in",r+"_drop_shadow_1"),a.setAttribute("result",r+"_drop_shadow_2"),this.feOffset=a,t.appendChild(a),createNS("feFlood")),n=(e.setAttribute("flood-color","#00ff00"),e.setAttribute("flood-opacity","1"),e.setAttribute("result",r+"_drop_shadow_3"),this.feFlood=e,t.appendChild(e),createNS("feComposite")),a=(n.setAttribute("in",r+"_drop_shadow_3"),n.setAttribute("in2",r+"_drop_shadow_2"),n.setAttribute("operator","in"),n.setAttribute("result",r+"_drop_shadow_4"),t.appendChild(n),this.createMergeNode(r,[r+"_drop_shadow_4",s]));t.appendChild(a)}extendPrototype([SVGComposableEffect],SVGTintFilter),SVGTintFilter.prototype.renderFrame=function(t){var e,i;(t||this.filterManager._mdf)&&(t=this.filterManager.effectElements[0].p.v,e=this.filterManager.effectElements[1].p.v,i=this.filterManager.effectElements[2].p.v/100,this.linearFilter.setAttribute("values",linearFilterValue+" "+i+" 0"),this.matrixFilter.setAttribute("values",e[0]-t[0]+" 0 0 0 "+t[0]+" "+(e[1]-t[1])+" 0 0 0 "+t[1]+" "+(e[2]-t[2])+" 0 0 0 "+t[2]+" 0 0 0 1 0"))},SVGFillFilter.prototype.renderFrame=function(t){var e;(t||this.filterManager._mdf)&&(t=this.filterManager.effectElements[2].p.v,e=this.filterManager.effectElements[6].p.v,this.matrixFilter.setAttribute("values","0 0 0 0 "+t[0]+" 0 0 0 0 "+t[1]+" 0 0 0 0 "+t[2]+" 0 0 0 "+e+" 0"))},SVGStrokeEffect.prototype.initialize=function(){var t,e,i,r,s=this.elem.layerElement.children||this.elem.layerElement.childNodes;for(1===this.filterManager.effectElements[1].p.v?(r=this.elem.maskManager.masksProperties.length,i=0):r=(i=this.filterManager.effectElements[0].p.v-1)+1,(e=createNS("g")).setAttribute("fill","none"),e.setAttribute("stroke-linecap","round"),e.setAttribute("stroke-dashoffset",1);i<r;i+=1)t=createNS("path"),e.appendChild(t),this.paths.push({p:t,m:i});if(3===this.filterManager.effectElements[10].p.v){var a=createNS("mask"),n=createElementID(),o=(a.setAttribute("id",n),a.setAttribute("mask-type","alpha"),a.appendChild(e),this.elem.globalData.defs.appendChild(a),createNS("g"));for(o.setAttribute("mask","url("+getLocationHref()+"#"+n+")");s[0];)o.appendChild(s[0]);this.elem.layerElement.appendChild(o),this.masker=a,e.setAttribute("stroke","#fff")}else if(1===this.filterManager.effectElements[10].p.v||2===this.filterManager.effectElements[10].p.v){if(2===this.filterManager.effectElements[10].p.v)for(s=this.elem.layerElement.children||this.elem.layerElement.childNodes;s.length;)this.elem.layerElement.removeChild(s[0]);this.elem.layerElement.appendChild(e),this.elem.layerElement.removeAttribute("mask"),e.setAttribute("stroke","#fff")}this.initialized=!0,this.pathMasker=e},SVGStrokeEffect.prototype.renderFrame=function(t){this.initialized||this.initialize();for(var e,i,r=this.paths.length,s=0;s<r;s+=1)if(-1!==this.paths[s].m&&(a=this.elem.maskManager.viewData[this.paths[s].m],e=this.paths[s].p,(t||this.filterManager._mdf||a.prop._mdf)&&e.setAttribute("d",a.lastPath),t||this.filterManager.effectElements[9].p._mdf||this.filterManager.effectElements[4].p._mdf||this.filterManager.effectElements[7].p._mdf||this.filterManager.effectElements[8].p._mdf||a.prop._mdf)){if(0!==this.filterManager.effectElements[7].p.v||100!==this.filterManager.effectElements[8].p.v){for(var a=.01*Math.min(this.filterManager.effectElements[7].p.v,this.filterManager.effectElements[8].p.v),n=.01*Math.max(this.filterManager.effectElements[7].p.v,this.filterManager.effectElements[8].p.v),o=e.getTotalLength(),h="0 0 0 "+o*a+" ",l=1+2*this.filterManager.effectElements[4].p.v*this.filterManager.effectElements[9].p.v*.01,p=Math.floor(o*(n-a)/l),m=0;m<p;m+=1)h+="1 "+2*this.filterManager.effectElements[4].p.v*this.filterManager.effectElements[9].p.v*.01+" ";h+="0 "+10*o+" 0 0"}else h="1 "+2*this.filterManager.effectElements[4].p.v*this.filterManager.effectElements[9].p.v*.01;e.setAttribute("stroke-dasharray",h)}(t||this.filterManager.effectElements[4].p._mdf)&&this.pathMasker.setAttribute("stroke-width",2*this.filterManager.effectElements[4].p.v),(t||this.filterManager.effectElements[6].p._mdf)&&this.pathMasker.setAttribute("opacity",this.filterManager.effectElements[6].p.v),1!==this.filterManager.effectElements[10].p.v&&2!==this.filterManager.effectElements[10].p.v||(t||this.filterManager.effectElements[3].p._mdf)&&(i=this.filterManager.effectElements[3].p.v,this.pathMasker.setAttribute("stroke","rgb("+bmFloor(255*i[0])+","+bmFloor(255*i[1])+","+bmFloor(255*i[2])+")"))},SVGTritoneFilter.prototype.renderFrame=function(t){var e,i,r,s;(t||this.filterManager._mdf)&&(t=this.filterManager.effectElements[0].p.v,e=this.filterManager.effectElements[1].p.v,i=(s=this.filterManager.effectElements[2].p.v)[0]+" "+e[0]+" "+t[0],r=s[1]+" "+e[1]+" "+t[1],s=s[2]+" "+e[2]+" "+t[2],this.feFuncR.setAttribute("tableValues",i),this.feFuncG.setAttribute("tableValues",r),this.feFuncB.setAttribute("tableValues",s))},SVGProLevelsFilter.prototype.createFeFunc=function(t,e){t=createNS(t);return t.setAttribute("type","table"),e.appendChild(t),t},SVGProLevelsFilter.prototype.getTableValue=function(t,e,i,r,s){for(var a,n=0,o=Math.min(t,e),h=Math.max(t,e),l=Array.call(null,{length:256}),p=0,m=s-r,f=e-t;n<=256;)a=(a=n/256)<=o?f<0?s:r:h<=a?f<0?r:s:r+m*Math.pow((a-t)/f,1/i),l[p]=a,p+=1,n+=256/255;return l.join(" ")},SVGProLevelsFilter.prototype.renderFrame=function(t){var e,i;(t||this.filterManager._mdf)&&(i=this.filterManager.effectElements,this.feFuncRComposed&&(t||i[3].p._mdf||i[4].p._mdf||i[5].p._mdf||i[6].p._mdf||i[7].p._mdf)&&(e=this.getTableValue(i[3].p.v,i[4].p.v,i[5].p.v,i[6].p.v,i[7].p.v),this.feFuncRComposed.setAttribute("tableValues",e),this.feFuncGComposed.setAttribute("tableValues",e),this.feFuncBComposed.setAttribute("tableValues",e)),this.feFuncR&&(t||i[10].p._mdf||i[11].p._mdf||i[12].p._mdf||i[13].p._mdf||i[14].p._mdf)&&(e=this.getTableValue(i[10].p.v,i[11].p.v,i[12].p.v,i[13].p.v,i[14].p.v),this.feFuncR.setAttribute("tableValues",e)),this.feFuncG&&(t||i[17].p._mdf||i[18].p._mdf||i[19].p._mdf||i[20].p._mdf||i[21].p._mdf)&&(e=this.getTableValue(i[17].p.v,i[18].p.v,i[19].p.v,i[20].p.v,i[21].p.v),this.feFuncG.setAttribute("tableValues",e)),this.feFuncB&&(t||i[24].p._mdf||i[25].p._mdf||i[26].p._mdf||i[27].p._mdf||i[28].p._mdf)&&(e=this.getTableValue(i[24].p.v,i[25].p.v,i[26].p.v,i[27].p.v,i[28].p.v),this.feFuncB.setAttribute("tableValues",e)),this.feFuncA)&&(t||i[31].p._mdf||i[32].p._mdf||i[33].p._mdf||i[34].p._mdf||i[35].p._mdf)&&(e=this.getTableValue(i[31].p.v,i[32].p.v,i[33].p.v,i[34].p.v,i[35].p.v),this.feFuncA.setAttribute("tableValues",e))},extendPrototype([SVGComposableEffect],SVGDropShadowEffect),SVGDropShadowEffect.prototype.renderFrame=function(t){var e,i;(t||this.filterManager._mdf)&&((t||this.filterManager.effectElements[4].p._mdf)&&this.feGaussianBlur.setAttribute("stdDeviation",this.filterManager.effectElements[4].p.v/4),(t||this.filterManager.effectElements[0].p._mdf)&&(i=this.filterManager.effectElements[0].p.v,this.feFlood.setAttribute("flood-color",rgbToHex(Math.round(255*i[0]),Math.round(255*i[1]),Math.round(255*i[2])))),(t||this.filterManager.effectElements[1].p._mdf)&&this.feFlood.setAttribute("flood-opacity",this.filterManager.effectElements[1].p.v/255),t||this.filterManager.effectElements[2].p._mdf||this.filterManager.effectElements[3].p._mdf)&&(i=this.filterManager.effectElements[3].p.v,t=(this.filterManager.effectElements[2].p.v-90)*degToRads,e=i*Math.cos(t),i=i*Math.sin(t),this.feOffset.setAttribute("dx",e),this.feOffset.setAttribute("dy",i))};var _svgMatteSymbols=[];function SVGMatte3Effect(t,e,i){this.initialized=!1,this.filterManager=e,this.filterElem=t,(this.elem=i).matteElement=createNS("g"),i.matteElement.appendChild(i.layerElement),i.matteElement.appendChild(i.transformedElement),i.baseElement=i.matteElement}function SVGGaussianBlurEffect(t,e,i,r){t.setAttribute("x","-100%"),t.setAttribute("y","-100%"),t.setAttribute("width","300%"),t.setAttribute("height","300%"),this.filterManager=e;e=createNS("feGaussianBlur");e.setAttribute("result",r),t.appendChild(e),this.feGaussianBlur=e}function TransformEffect(){}function SVGTransformEffect(t,e){this.init(e)}function CVTransformEffect(t){this.init(t)}return SVGMatte3Effect.prototype.findSymbol=function(t){for(var e=0,i=_svgMatteSymbols.length;e<i;){if(_svgMatteSymbols[e]===t)return _svgMatteSymbols[e];e+=1}return null},SVGMatte3Effect.prototype.replaceInParent=function(t,e){var i=t.layerElement.parentNode;if(i){for(var r,s=i.children,a=0,n=s.length;a<n&&s[a]!==t.layerElement;)a+=1;a<=n-2&&(r=s[a+1]);var o=createNS("use");o.setAttribute("href","#"+e),r?i.insertBefore(o,r):i.appendChild(o)}},SVGMatte3Effect.prototype.setElementAsMask=function(t,e){var i,r,s,a;this.findSymbol(e)||(i=createElementID(),(r=createNS("mask")).setAttribute("id",e.layerId),r.setAttribute("mask-type","alpha"),_svgMatteSymbols.push(e),(a=t.globalData.defs).appendChild(r),(s=createNS("symbol")).setAttribute("id",i),this.replaceInParent(e,i),s.appendChild(e.layerElement),a.appendChild(s),(a=createNS("use")).setAttribute("href","#"+i),r.appendChild(a),e.data.hd=!1,e.show()),t.setMatte(e.layerId)},SVGMatte3Effect.prototype.initialize=function(){for(var t=this.filterManager.effectElements[0].p.v,e=this.elem.comp.elements,i=0,r=e.length;i<r;)e[i]&&e[i].data.ind===t&&this.setElementAsMask(this.elem,e[i]),i+=1;this.initialized=!0},SVGMatte3Effect.prototype.renderFrame=function(){this.initialized||this.initialize()},SVGGaussianBlurEffect.prototype.renderFrame=function(t){var e;(t||this.filterManager._mdf)&&(t=.3*this.filterManager.effectElements[0].p.v,e=this.filterManager.effectElements[1].p.v,this.feGaussianBlur.setAttribute("stdDeviation",(3==e?0:t)+" "+(2==e?0:t)),e=1==this.filterManager.effectElements[2].p.v?"wrap":"duplicate",this.feGaussianBlur.setAttribute("edgeMode",e))},TransformEffect.prototype.init=function(t){this.effectsManager=t,this.type=effectTypes.TRANSFORM_EFFECT,this.matrix=new Matrix,this.opacity=-1,this._mdf=!1,this._opMdf=!1},TransformEffect.prototype.renderFrame=function(t){var e,i,r,s,a,n,o;this._opMdf=!1,this._mdf=!1,(t||this.effectsManager._mdf)&&(e=(t=this.effectsManager.effectElements)[0].p.v,i=t[1].p.v,s=1===t[2].p.v,r=t[3].p.v,s=s?r:t[4].p.v,a=t[5].p.v,n=t[6].p.v,o=t[7].p.v,this.matrix.reset(),this.matrix.translate(-e[0],-e[1],e[2]),this.matrix.scale(.01*s,.01*r,1),this.matrix.rotate(-o*degToRads),this.matrix.skewFromAxis(-a*degToRads,(n+90)*degToRads),this.matrix.translate(i[0],i[1],0),this._mdf=!0,this.opacity!==t[8].p.v)&&(this.opacity=t[8].p.v,this._opMdf=!0)},extendPrototype([TransformEffect],SVGTransformEffect),extendPrototype([TransformEffect],CVTransformEffect),registerRenderer("canvas",CanvasRenderer),registerRenderer("html",HybridRenderer),registerRenderer("svg",SVGRenderer),ShapeModifiers.registerModifier("tm",TrimModifier),ShapeModifiers.registerModifier("pb",PuckerAndBloatModifier),ShapeModifiers.registerModifier("rp",RepeaterModifier),ShapeModifiers.registerModifier("rd",RoundCornersModifier),ShapeModifiers.registerModifier("zz",ZigZagModifier),ShapeModifiers.registerModifier("op",OffsetPathModifier),setExpressionsPlugin(Expressions),setExpressionInterfaces(getInterface),initialize$1(),initialize(),registerEffect$1(20,SVGTintFilter,!0),registerEffect$1(21,SVGFillFilter,!0),registerEffect$1(22,SVGStrokeEffect,!1),registerEffect$1(23,SVGTritoneFilter,!0),registerEffect$1(24,SVGProLevelsFilter,!0),registerEffect$1(25,SVGDropShadowEffect,!0),registerEffect$1(28,SVGMatte3Effect,!1),registerEffect$1(29,SVGGaussianBlurEffect,!0),registerEffect$1(35,SVGTransformEffect,!1),registerEffect(35,CVTransformEffect),lottie}),{loadAnimation:loadAnimation}}({});onmessage=function(t){var t=t.data,e=t.type,i=t.payload;"load"===e?lottieInternal.loadAnimation(i):"pause"===e?animations[i.id]&&animations[i.id].animation.pause():"play"===e?animations[i.id]&&animations[i.id].animation.play():"stop"===e?animations[i.id]&&animations[i.id].animation.stop():"setSpeed"===e?animations[i.id]&&animations[i.id].animation.setSpeed(i.value):"setDirection"===e?animations[i.id]&&animations[i.id].animation.setDirection(i.value):"setLoop"===e?animations[i.id]&&animations[i.id].animation.setLoop(i.value):"goToAndPlay"===e?animations[i.id]&&animations[i.id].animation.goToAndPlay(i.value,i.isFrame):"goToAndStop"===e?animations[i.id]&&animations[i.id].animation.goToAndStop(i.value,i.isFrame):"setSubframe"===e?animations[i.id]&&animations[i.id].animation.setSubframe(i.value):"addEventListener"===e?animations[i.id]&&(animations[i.id].events[i.callbackId]={callback:t=function(){self.postMessage({type:"event",payload:{id:i.id,callbackId:i.callbackId,argument:arguments[0]}})}},animations[i.id].animation.addEventListener(i.eventName,t)):"removeEventListener"===e?animations[i.id]&&(t=animations[i.id].events[i.callbackId],animations[i.id].animation.removeEventListener(i.eventName,t)):"destroy"===e?animations[i.id]&&(animations[i.id].animation.destroy(),animations[i.id]=null):"resize"===e?animations[i.id]&&animations[i.id].animation.resize(i.width,i.height):"playSegments"===e?animations[i.id]&&animations[i.id].animation.playSegments(i.arr,i.forceFlag):"resetSegments"===e?animations[i.id]&&animations[i.id].animation.resetSegments(i.forceFlag):"updateDocumentData"===e&&animations[i.id].animation.updateDocumentData(i.path,i.documentData,i.index)}}function createWorker(t){t=new Blob(["("+t.toString()+"())"],{type:"text/javascript"}),t=URL.createObjectURL(t);return new Worker(t)}var lottie=(()=>{var h=createWorker(workerContent),i=0,s=0,P={},a={rendererSettings:{}};function E(t,e,i,r){var s,a,n="div"===t.type?document.createElement("div"):document.createElementNS(t.namespace,t.type);for(s in t.textContent&&(n.textContent=t.textContent),t.attributes)Object.prototype.hasOwnProperty.call(t.attributes,s)&&("href"===s?n.setAttributeNS("http://www.w3.org/1999/xlink",s,t.attributes[s]):n.setAttribute(s,t.attributes[s]),"id"===s)&&(i[t.attributes[s]]=n);for(a in t.style)Object.prototype.hasOwnProperty.call(t.style,a)&&(n.style[a]=t.style[a]);t.children.forEach(function(t){E(t,n,i)}),r?e.insertBefore(n,r):e.appendChild(n)}var e={DOMLoaded:function(t){var e=P[t.id];e._loaded=!0,e.pendingCallbacks.forEach(function(t){e.animInstance.addEventListener(t.eventName,t.callback),"DOMLoaded"===t.eventName&&t.callback()}),e.animInstance.totalFrames=t.totalFrames,e.animInstance.frameRate=t.frameRate,e.animInstance.firstFrame=t.firstFrame,e.animInstance.playDirection=t.playDirection,e.animInstance.currentFrame=t.isSubframeEnabled?t.currentRawFrame:~~t.currentRawFrame,t.timeCompleted!==t.totalFrames&&t.currentFrame>t.timeCompleted&&(e.animInstance.currentFrame=t.timeCompleted)},SVGloaded:function(t){var e=P[t.id],i=e.container;E(t.tree,i,e.elements)},SVGupdated:function(t){var e=t.elements,i=P[t.id];if(i){for(var r=i.elements,s=0;s<e.length;s+=1){for(var a,n=r[(a=e[s]).id],o=(m=f=l=p=h=o=void 0,a.elements),h=r,l=0;l<o.length;l+=1){var p,m,f=h[(p=o[l])[1]];f&&(p[2]&&(m=h[p[2]]),E(p[0],f,h,m),o.splice(l,1),--l)}y=c=u=d=void 0;for(var c,d=n,u=a.styles,y=0;y<u.length;y+=1)d.style[(c=u[y])[0]]=c[1];x=g=b=v=void 0;for(var g,v=n,b=a.attributes,x=0;x<b.length;x+=1)v.setAttribute((g=b[x])[0],g[1]);n=n,(a=a.textContent)&&(n.textContent=a)}i.animInstance.currentFrame=t.currentTime}},CanvasUpdated:function(t){P[t.id].instructionsHandler(t.instructions)},event:function(t){var e=P[t.id];e&&(e=e.callbacks)[t.callbackId]&&e[t.callbackId].callback(t.argument)},playing:function(t){(t=P[t.id])&&(t.animInstance.isPaused=!1)},paused:function(t){(t=P[t.id])&&(t.animInstance.isPaused=!0)}};return h.onmessage=function(t){e[t.data.type]&&e[t.data.type](t.data.payload)},{loadAnimation:function(t){var r,n="lottie_animationId_"+(i+=1),o={elements:{},callbacks:{},pendingCallbacks:[],status:"init"},e={id:n,isPaused:!0,pause:function(){h.postMessage({type:"pause",payload:{id:n}})},play:function(){h.postMessage({type:"play",payload:{id:n}})},stop:function(){h.postMessage({type:"stop",payload:{id:n}})},setSpeed:function(t){h.postMessage({type:"setSpeed",payload:{id:n,value:t}})},setDirection:function(t){h.postMessage({type:"setDirection",payload:{id:n,value:t}})},setLoop:function(t){h.postMessage({type:"setLoop",payload:{id:n,value:t}})},goToAndStop:function(t,e){h.postMessage({type:"goToAndStop",payload:{id:n,value:t,isFrame:e}})},goToAndPlay:function(t,e){h.postMessage({type:"goToAndPlay",payload:{id:n,value:t,isFrame:e}})},playSegments:function(t,e){h.postMessage({type:"playSegments",payload:{id:n,arr:t,forceFlag:e}})},setSubframe:function(t){h.postMessage({type:"setSubframe",payload:{id:n,value:t}})},addEventListener:function(t,e){var i;o._loaded?(i="callback_"+(s+=1),o.callbacks[i]={eventName:t,callback:e},h.postMessage({type:"addEventListener",payload:{id:n,callbackId:i,eventName:t}})):o.pendingCallbacks.push({eventName:t,callback:e})},removeEventListener:function(e,i){Object.keys(o.callbacks).forEach(function(t){o.callbacks[t].eventName!==e||o.callbacks[t].callback!==i&&i||(delete o.callbacks[t],h.postMessage({type:"removeEventListener",payload:{id:n,callbackId:t,eventName:e}}))})},destroy:function(){"init"===o.status?o.status="destroyable":(o.status="destroyed",P[n]=null,o.container&&(o.container.innerHTML=""),h.postMessage({type:"destroy",payload:{id:n}}))},resize:function(t,e){var i=window.devicePixelRatio||1;h.postMessage({type:"resize",payload:{id:n,width:t||(o.container?o.container.offsetWidth*i:0),height:e||(o.container?o.container.offsetHeight*i:0)}})},updateDocumentData:function(t,e,i){h.postMessage({type:"updateDocumentData",payload:{id:n,path:t,documentData:e,index:i}})}};return o.animInstance=e,r=t,new Promise(function(e,t){var i=Object.assign({},a,r);i.animType&&!i.renderer&&(i.renderer=i.animType),i.wrapper&&(i.container||(i.container=i.wrapper),delete i.wrapper),i.animationData?e(i):i.path?fetch(i.path).then(function(t){return t.json()}).then(function(t){i.animationData=t,delete i.path,e(i)}):t()}).then(function(t){var e,i,r,s,a;"destroyable"===o.status?o.animInstance.destroy():(o.status="loaded",e=[],t.container&&(o.container=t.container,delete t.container),"canvas"===t.renderer&&((i=t.rendererSettings.canvas)||(r=window.devicePixelRatio||1,i=document.createElement("canvas"),o.container.appendChild(i),i.width=(o.container?o.container.offsetWidth:t.animationData.w)*r,i.height=(o.container?o.container.offsetHeight:t.animationData.h)*r,i.style.width="100%",i.style.height="100%"),r=i,"undefined"==typeof OffscreenCanvas?(o.canvas=i,o.instructionsHandler=(s=i.getContext("2d"),a={beginPath:s.beginPath,closePath:s.closePath,rect:s.rect,clip:s.clip,clearRect:s.clearRect,setTransform:s.setTransform,moveTo:s.moveTo,bezierCurveTo:s.bezierCurveTo,lineTo:s.lineTo,fill:s.fill,save:s.save,restore:s.restore},function(t){for(var e=0;e<t.length;e+=1){var i=t[e],r=a[i.t];r?r.apply(s,i.a):s[i.t]=i.a}})):(i instanceof OffscreenCanvas||(r=i.transferControlToOffscreen(),t.rendererSettings.canvas=r),e.push(r))),P[n]=o,h.postMessage({type:"load",payload:{params:t,id:n}},e))}),e}}})();
return lottie;
}));