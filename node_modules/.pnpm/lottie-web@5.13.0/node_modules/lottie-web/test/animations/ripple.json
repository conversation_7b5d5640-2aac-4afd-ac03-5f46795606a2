{"v": "5.0.0", "fr": 60, "ip": 0, "op": 361, "w": 800, "h": 600, "nm": "ripple graph", "ddd": 0, "assets": [{"id": "comp_78", "layers": [{"ddd": 0, "ind": 1, "ty": 0, "nm": "grow bar", "refId": "comp_79", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [22, 290, 0], "ix": 2}, "a": {"a": 0, "k": [32, 205, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 64, "h": 410, "ip": 0, "op": 361, "st": -120, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 0, "nm": "grow bar", "refId": "comp_79", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [56.455, 290, 0], "ix": 2}, "a": {"a": 0, "k": [32, 205, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 64, "h": 410, "ip": 0, "op": 361, "st": -117, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 0, "nm": "grow bar", "refId": "comp_79", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [90.909, 290, 0], "ix": 2}, "a": {"a": 0, "k": [32, 205, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 64, "h": 410, "ip": 0, "op": 361, "st": -114, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 0, "nm": "grow bar", "refId": "comp_79", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [125.364, 290, 0], "ix": 2}, "a": {"a": 0, "k": [32, 205, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 64, "h": 410, "ip": 0, "op": 361, "st": -111, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 0, "nm": "grow bar", "refId": "comp_79", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [159.818, 290, 0], "ix": 2}, "a": {"a": 0, "k": [32, 205, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 64, "h": 410, "ip": 0, "op": 361, "st": -108, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 0, "nm": "grow bar", "refId": "comp_79", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [194.273, 290, 0], "ix": 2}, "a": {"a": 0, "k": [32, 205, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 64, "h": 410, "ip": 0, "op": 361, "st": -105, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 0, "nm": "grow bar", "refId": "comp_79", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [228.727, 290, 0], "ix": 2}, "a": {"a": 0, "k": [32, 205, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 64, "h": 410, "ip": 0, "op": 361, "st": -102, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 0, "nm": "grow bar", "refId": "comp_79", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [263.182, 290, 0], "ix": 2}, "a": {"a": 0, "k": [32, 205, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 64, "h": 410, "ip": 0, "op": 361, "st": -99, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 0, "nm": "grow bar", "refId": "comp_79", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [297.636, 290, 0], "ix": 2}, "a": {"a": 0, "k": [32, 205, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 64, "h": 410, "ip": 0, "op": 361, "st": -96, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 0, "nm": "grow bar", "refId": "comp_79", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [332.091, 290, 0], "ix": 2}, "a": {"a": 0, "k": [32, 205, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 64, "h": 410, "ip": 0, "op": 361, "st": -93, "bm": 0}, {"ddd": 0, "ind": 11, "ty": 0, "nm": "grow bar", "refId": "comp_79", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [366.545, 290, 0], "ix": 2}, "a": {"a": 0, "k": [32, 205, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 64, "h": 410, "ip": 0, "op": 361, "st": -90, "bm": 0}, {"ddd": 0, "ind": 12, "ty": 0, "nm": "grow bar", "refId": "comp_79", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [401, 290, 0], "ix": 2}, "a": {"a": 0, "k": [32, 205, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 64, "h": 410, "ip": 0, "op": 361, "st": -87, "bm": 0}, {"ddd": 0, "ind": 13, "ty": 0, "nm": "grow bar", "refId": "comp_79", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [435.455, 290, 0], "ix": 2}, "a": {"a": 0, "k": [32, 205, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 64, "h": 410, "ip": 0, "op": 361, "st": -84, "bm": 0}, {"ddd": 0, "ind": 14, "ty": 0, "nm": "grow bar", "refId": "comp_79", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [469.909, 290, 0], "ix": 2}, "a": {"a": 0, "k": [32, 205, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 64, "h": 410, "ip": 0, "op": 361, "st": -81, "bm": 0}, {"ddd": 0, "ind": 15, "ty": 0, "nm": "grow bar", "refId": "comp_79", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [504.364, 290, 0], "ix": 2}, "a": {"a": 0, "k": [32, 205, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 64, "h": 410, "ip": 0, "op": 361, "st": -78, "bm": 0}, {"ddd": 0, "ind": 16, "ty": 0, "nm": "grow bar", "refId": "comp_79", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [538.818, 290, 0], "ix": 2}, "a": {"a": 0, "k": [32, 205, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 64, "h": 410, "ip": 0, "op": 361, "st": -75, "bm": 0}, {"ddd": 0, "ind": 17, "ty": 0, "nm": "grow bar", "refId": "comp_79", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [573.273, 290, 0], "ix": 2}, "a": {"a": 0, "k": [32, 205, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 64, "h": 410, "ip": 0, "op": 361, "st": -72, "bm": 0}, {"ddd": 0, "ind": 18, "ty": 0, "nm": "grow bar", "refId": "comp_79", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [607.727, 290, 0], "ix": 2}, "a": {"a": 0, "k": [32, 205, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 64, "h": 410, "ip": 0, "op": 361, "st": -69, "bm": 0}, {"ddd": 0, "ind": 19, "ty": 0, "nm": "grow bar", "refId": "comp_79", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [642.182, 290, 0], "ix": 2}, "a": {"a": 0, "k": [32, 205, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 64, "h": 410, "ip": 0, "op": 361, "st": -66, "bm": 0}, {"ddd": 0, "ind": 20, "ty": 0, "nm": "grow bar", "refId": "comp_79", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [676.636, 290, 0], "ix": 2}, "a": {"a": 0, "k": [32, 205, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 64, "h": 410, "ip": 0, "op": 361, "st": -63, "bm": 0}, {"ddd": 0, "ind": 21, "ty": 0, "nm": "grow bar", "refId": "comp_79", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [711.091, 290, 0], "ix": 2}, "a": {"a": 0, "k": [32, 205, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 64, "h": 410, "ip": 0, "op": 361, "st": -60, "bm": 0}, {"ddd": 0, "ind": 22, "ty": 0, "nm": "grow bar", "refId": "comp_79", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [745.545, 290, 0], "ix": 2}, "a": {"a": 0, "k": [32, 205, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 64, "h": 410, "ip": 0, "op": 361, "st": -57, "bm": 0}, {"ddd": 0, "ind": 23, "ty": 0, "nm": "grow bar", "refId": "comp_79", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [780, 290, 0], "ix": 2}, "a": {"a": 0, "k": [32, 205, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 64, "h": 410, "ip": 0, "op": 361, "st": -54, "bm": 0}]}, {"id": "comp_79", "layers": [{"ddd": 0, "ind": 1, "ty": 3, "nm": "traceNull", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [590, -42, 0], "ix": 2, "x": "var $bm_rt;\nvar pathLayer = thisComp.layer('bar');\nvar progress = div(thisLayer.effect('Pseudo/ADBE Trace Path')('Pseudo/ADBE Trace Path-0001'), 100);\nvar pathToTrace = pathLayer('ADBE Root Vectors Group')(1)('ADBE Vector Shape');\n$bm_rt = pathLayer.toComp(pathToTrace.pointOnPath(progress));"}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "Trace Path", "np": 4, "mn": "Pseudo/ADBE Trace Path", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Progress", "mn": "Pseudo/ADBE Trace Path-0001", "ix": 1, "v": {"a": 1, "k": [{"i": {"x": [0.6], "y": [1]}, "o": {"x": [0.4], "y": [0]}, "n": ["0p6_1_0p4_0"], "t": 0, "s": [0], "e": [100]}, {"i": {"x": [0.6], "y": [1]}, "o": {"x": [0.4], "y": [0]}, "n": ["0p6_1_0p4_0"], "t": 60, "s": [100], "e": [0]}, {"t": 120}], "ix": 1, "x": "var $bm_rt;\nif (thisProperty.propertyGroup(1)('Pseudo/ADBE Trace Path-0002') == true && thisProperty.numKeys > 1) {\n    $bm_rt = thisProperty.loopOut('cycle');\n} else {\n    $bm_rt = value;\n}"}}, {"ty": 7, "nm": "Loop", "mn": "Pseudo/ADBE Trace Path-0002", "ix": 2, "v": {"a": 0, "k": 1, "ix": 2}}]}], "ip": 0, "op": 720, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "botDot", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": -90, "ix": 10}, "p": {"a": 0, "k": [292, 29, 0], "ix": 2, "x": "var $bm_rt;\nvar barLayer, barPath;\nbarLayer = thisComp.layer('bar');\nbarPath = barLayer.content('Path 1').path.points();\n$bm_rt = barLayer.toComp(barPath[1]);"}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [12, 12], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Ellipse Path 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.666666666667, 0.850980392157, 0.96862745098, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}], "ip": 0, "op": 720, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "topDot", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": -90, "ix": 10}, "p": {"a": 0, "k": [20, 105, 0], "ix": 2, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('traceNull').transform.position;"}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [12, 12], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Ellipse Path 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.666666666667, 0.850980392157, 0.96862745098, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}], "ip": 0, "op": 720, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "bar", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [20, 205, 0], "ix": 2}, "a": {"a": 0, "k": [184, 392, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[194, 233], [194, 553]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('traceNull').effect('Trace Path')('Progress');"}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "gs", "o": {"a": 0, "k": 100, "ix": 9}, "w": {"a": 0, "k": 2, "ix": 10}, "g": {"p": 5, "k": {"a": 0, "k": [0, 0.639, 0.816, 0.929, 0.254, 0.357, 0.496, 0.594, 0.507, 0.075, 0.176, 0.259, 0.754, 0.357, 0.496, 0.594, 1, 0.639, 0.816, 0.929, 0, 1, 0.253, 0.5, 0.505, 0, 0.752, 0.5, 1, 1], "ix": 8}}, "s": {"a": 0, "k": [258, 509], "ix": 4}, "e": {"a": 1, "k": [{"i": {"x": 0.6, "y": 1}, "o": {"x": 0.4, "y": 0}, "n": "0p6_1_0p4_0", "t": 0, "s": [396, -25], "e": [-7, -25], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.6, "y": 1}, "o": {"x": 0.4, "y": 0}, "n": "0p6_1_0p4_0", "t": 60, "s": [-7, -25], "e": [396, -25], "to": [0, 0], "ti": [0, 0]}, {"t": 120}], "ix": 5, "x": "var $bm_rt;\n$bm_rt = loopOut('cycle');"}, "t": 1, "lc": 1, "lj": 1, "ml": 4, "nm": "Gradient Stroke 1", "mn": "ADBE Vector Graphic - G-Stroke", "hd": false}], "ip": 0, "op": 720, "st": 0, "bm": 0}]}], "layers": [{"ddd": 0, "ind": 1, "ty": 0, "nm": "grow graph", "refId": "comp_78", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [411, 221, 0], "ix": 2}, "a": {"a": 0, "k": [400, 300, 0], "ix": 1}, "s": {"a": 0, "k": [-55, 55, 100], "ix": 6}}, "ao": 0, "w": 800, "h": 600, "ip": 0, "op": 361, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 0, "nm": "grow graph", "refId": "comp_78", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 180, "ix": 10}, "p": {"a": 0, "k": [411, 387, 0], "ix": 2}, "a": {"a": 0, "k": [400, 300, 0], "ix": 1}, "s": {"a": 0, "k": [-55, 55, 100], "ix": 6}}, "ao": 0, "w": 800, "h": 600, "ip": 0, "op": 361, "st": 0, "bm": 0}]}