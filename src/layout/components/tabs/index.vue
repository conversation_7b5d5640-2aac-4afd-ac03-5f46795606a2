<!--  -->
<template>
    <div class="tabs">
        <var-bottom-navigation>
            <var-bottom-navigation-item v-for="item in tabList" :key="item.name" :icon="activeRoute === item.name ? item.icon : item.unCheckedIcon"
                :badge="item.badge" @click="handleTabClick(item.name)" />
        </var-bottom-navigation>
    </div>
</template>

<script lang="ts" setup name="Tabs">
import { ref, reactive, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import messageChecked from '@/assets/tabs/message-checked.png'
import messageUnChecked from '@/assets/tabs/message-unchecked.png'
import homeChecked from '@/assets/tabs/home-checked.png'
import homeUnChecked from '@/assets/tabs/home-unchecked.png'

const router = useRouter()
const route = useRoute()
const activeRoute = computed(() => route.name)
const tabList = ref([
    {
        name: 'home',
        icon: homeChecked,
        unCheckedIcon: homeUnChecked,
        badge: true,
    },
    {
        name: 'message',
        icon: messageChecked,
        unCheckedIcon: messageUnChecked,
        badge: false,
    },
    {
        name: 'home1',
        icon: homeChecked,
        unCheckedIcon: homeUnChecked,
        badge: true,
    },
    {
        name: 'message',
        icon: messageChecked,
        unCheckedIcon: messageUnChecked,
        badge: false,
    },
    {
        name: 'message',
        icon: messageChecked,
        unCheckedIcon: messageUnChecked,
        badge: false,
    }
]
)
const badgeProps = reactive({
    type: 'primary',
    value: '66'
})
const handleTabClick = (name: string) => {
    router.push({ name })
}
</script>
<style lang="scss" scoped>
.tabs {
    width: 100%;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    margin: 0 auto;
    height: 50px;
    background: rgba(17, 17, 24, .72);
    backdrop-filter: blur(4.266667vw);
    z-index: 3000;
    filter: blur(0.2px);
    max-width: 750px;
    transition: height .5s;

    :deep(.var-bottom-navigation) {
        background: transparent;

        .var-bottom-navigation-item--active {
            background: transparent;
        }

        .var-icon {
            width: 45px;
            height: 45px;
        }
    }
}
</style>