<!-- 选择性别弹窗 -->
<template>
    <div class="discount">
        <var-popup position="center" v-model:show="showDiscountPopup" :safe-area="true" :default-style="false"
            :close-on-click-overlay="false">
            <div class="discount-dialog">
                <div class="discount-dialog-close">
                    <img src="@/assets/common/close-rate-icon.png" alt="" class="close-icon" @click="close">
                </div>

                <div class="discount-dialog-box">
                    <div class="discount-dialog-box-top">
                        <img src="@/assets/recharge/discount-gift.png" alt="" class="gift-icon">
                        <div class="discount-dialog-box-top-title">
                            NEW USERS OFFER
                        </div>
                        <div class="discount-dialog-box-top-content">
                            <div class="discount-dialog-box-top-content-item">
                                <div class="discount-dialog-box-top-content-item-top">
                                    Extra Coins
                                </div>
                                <div class="discount-dialog-box-top-content-item-content">
                                    <img src="@/assets/common/coins.png" alt="">
                                    <span>35</span>
                                </div>
                            </div>
                            <div class="discount-dialog-box-top-content-item">
                                <div class="discount-dialog-box-top-content-item-top">
                                    VIP Medal
                                </div>
                                <div class="discount-dialog-box-top-content-item-content svip-content">
                                    <img src="@/assets/profile/is-svip.png" alt="">
                                    <span>Permanent</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="discount-dialog-box-center">
                        <div class="discount-dialog-box-center-title">
                            REMAINING TIME
                        </div>
                        <div class="discount-dialog-box-center-time">
                            <div class="countdown-container">
                                <div class="countdown-item">
                                    <div class="countdown-digit">{{ hours[0] }}</div>
                                </div>
                                <div class="countdown-item">
                                    <div class="countdown-digit">{{ hours[1] }}</div>
                                </div>
                                <div class="countdown-separator">:</div>
                                <div class="countdown-item">
                                    <div class="countdown-digit">{{ minutes[0] }}</div>
                                </div>
                                <div class="countdown-item">
                                    <div class="countdown-digit">{{ minutes[1] }}</div>
                                </div>
                                <div class="countdown-separator">:</div>
                                <div class="countdown-item">
                                    <div class="countdown-digit">{{ seconds[0] }}</div>
                                </div>
                                <div class="countdown-item">
                                    <div class="countdown-digit">{{ seconds[1] }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="discount-dialog-box-bottom">
                        <div class="discount-dialog-box-bottom-box">
                            $1.99
                        </div>
                    </div>
                </div>
            </div>
        </var-popup>
    </div>
</template>

<script lang="ts">
import { nextTick } from 'process';


export default {
    name: 'discountPopup',
    props: {
        show: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            showDiscountPopup: this.show,
            hours: '00',
            minutes: '00',
            seconds: '00',
            countdownTimer: null as NodeJS.Timeout | null
        };
    },
    watch: {
        show: {
            handler(newVal) {
                this.showDiscountPopup = newVal
                if (newVal) {
                    this.$nextTick(() => {
                        const textTip = document.getElementsByClassName('discount-dialog-box-top-title')[0] as HTMLElement
                        console.log(textTip.clientWidth)
                        this.startCountdown()
                    })

                }
            },
            immediate: true
        }
    },
    mounted() {


    },
    beforeUnmount() {
        this.stopCountdown()
    },
    methods: {
        close() {
            this.showDiscountPopup = false
            this.$emit('close')
            this.$emit('update:show', false)
        },
        confirm() {
            this.$emit('confirm')
            this.close()
        },
        startCountdown() {
            // 设置倒计时时间（这里设置为1小时，你可以根据需要调整）
            let totalSeconds = 3600 // 1小时 = 3600秒

            const updateCountdown = () => {
                if (totalSeconds <= 0) {
                    this.hours = '00'
                    this.minutes = '00'
                    this.seconds = '00'
                    if (this.countdownTimer) {
                        clearInterval(this.countdownTimer)
                    }
                    return
                }

                const hours = Math.floor(totalSeconds / 3600)
                const minutes = Math.floor((totalSeconds % 3600) / 60)
                const seconds = totalSeconds % 60

                this.hours = hours.toString().padStart(2, '0')
                this.minutes = minutes.toString().padStart(2, '0')
                this.seconds = seconds.toString().padStart(2, '0')

                totalSeconds--
            }

            // 立即执行一次
            updateCountdown()

            // 每秒更新一次
            this.countdownTimer = setInterval(updateCountdown, 1000)
        },
        stopCountdown() {
            if (this.countdownTimer) {
                clearInterval(this.countdownTimer)
                this.countdownTimer = null
            }
        }
    }
}
</script>
<style lang='scss' scoped>
.discount-dialog {

    display: flex;
    flex-direction: column;
    justify-content: center;
    font-family: var(--var-font-family-urbanist);
    padding-bottom: 20px;


    .discount-dialog-close {
        display: flex;
        justify-content: flex-end;
        padding-right: 15px;
        box-sizing: border-box;

        .close-icon {
            width: 30px;
            height: 30px;
        }
    }

    .discount-dialog-box {
        width: 335px;
        height: auto;
        padding: 22px 15px 0;
        box-sizing: border-box;
        position: relative;

        .discount-dialog-box-top {
            padding: 18px 15px 0;
            box-sizing: border-box;
            width: 100%;
            height: 252px;
            background: url('@/assets/recharge/discount-top.png') no-repeat center center;
            background-size: 100% 100%;
            position: relative;
            z-index: 10;

            .gift-icon {
                width: 110px;
                height: 110px;
                position: absolute;
                top: -19px;
                right: -15px;
                z-index: 11;
            }

            .discount-dialog-box-top-title {
                width: max-content;
                font-size: 22px;
                font-weight: 900;
                line-height: 29px;
                color: #fff;
                font-style: italic;
                text-shadow: 0px 0px 6px #ED0B8F;
            }

            .discount-dialog-box-top-content {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-top: 21px;
                gap: 15px;
                position: relative;
                z-index: 22;

                &-item {
                    flex: 1;
                    padding: 1px;
                    height: 120px;
                    background-color: #fff;
                    border-radius: 12px;
                    box-sizing: border-box;
                    display: flex;
                    flex-direction: column;

                    &-top {
                        width: 100%;
                        padding: 6px 0;
                        font-size: 15px;
                        font-weight: 700;
                        line-height: 18px;
                        color: #640949;
                        background: linear-gradient(90deg, rgba(255, 180, 63, .8) 0%, rgba(255, 59, 181, .4) 50.96%, rgba(199, 74, 220, .4) 100%);
                        border-radius: 12px 12px 0 0;
                        text-align: center;
                    }

                    &-content {
                        flex: 1;
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        justify-content: center;

                        img {
                            width: 30px;
                            height: 30px;
                        }

                        span {
                            margin-top: 10px;
                            font-size: 18px;
                            font-weight: 700;
                            line-height: 22px;
                            color: $common-color;
                        }

                        &.svip-content {
                            img {
                                width: 40px;
                                height: 16px;
                            }

                            span {
                                margin-top: 17px;
                            }
                        }
                    }
                }
            }
        }

        .discount-dialog-box-center {
            margin-top: -17px;
            width: 100%;
            height: 100px;
            background: url('@/assets/recharge/discount-center.png') no-repeat center center;
            background-size: 100% 100%;
            position: relative;
            z-index: 9;
            padding: 19px 0 0;

            &-title {
                color: #1C2158;
                font-size: 16px;
                font-weight: 900;
                font-style: italic;
                text-align: center;
            }

            .discount-dialog-box-center-time {
                margin-top: 7px;

                .countdown-container {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    gap: 5px;

                    .countdown-item {
                        .countdown-digit {
                            width: 25px;
                            height: 30px;
                            background: #FF1A9E1A;
                            border: 1px solid $common-color;
                            border-radius: 6px;
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            font-size: 16px;
                            font-weight: 700;
                            color: $common-color;
                        }
                    }

                    .countdown-separator {
                        font-size: 20px;
                        font-weight: 700;
                        color: #FF3BB5;
                        margin: 0 2px;
                    }
                }
            }
        }

        .discount-dialog-box-bottom {

            margin-top: 30px;
            width: 100%;
            height: 66px;
            display: flex;
            justify-content: center;
            position: relative;

            &-box {
                width: 214px;
                height: 66px;
                background: url('@/assets/recharge/discount-bottom.png') no-repeat center center;
                background-size: 100% 100%;
                display: flex;
                justify-content: center;
                align-items: center;
                color: #fff;
                font-size: 18px;
                font-weight: 700px;
            }
        }
    }
}
</style>