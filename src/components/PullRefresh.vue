<template>
    <div class="pull-refresh" @touchstart="handleTouchStart" @touchmove="handleTouchMove" @touchend="handleTouchEnd" @scroll="handleScroll">

        <!-- 下拉刷新头部 -->
        <div class="pull-refresh-head" :style="{ transform: `translateY(${headTransform}px)` }">
            <div class="pull-refresh-content">
                <div v-if="status === 'pulling'" class="pull-status">
                    <div class="pull-icon">
                        <svg width="10vw" height="10vw" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 4L12 20M12 20L18 14M12 20L6 14" stroke="currentColor" stroke-width="2"
                                stroke-linecap="round" stroke-linejoin="round" />
                        </svg>
                    </div>
                </div>
                <div v-else-if="status === 'loosing'" class="pull-status">
                    <div class="pull-icon">
                        <svg width="10vw" height="10vw" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 20L12 4M12 4L6 10M12 4L18 10" stroke="currentColor" stroke-width="2"
                                stroke-linecap="round" stroke-linejoin="round" />
                        </svg>
                    </div>
                </div>
                <div v-else-if="status === 'loading'" class="pull-status">
                    <div class="pull-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                            viewBox="0 0 500 500" width="10vw" height="10vw" preserveAspectRatio="xMidYMid meet">
                            <defs>
                                <clipPath id="__lottie_element_17">
                                    <rect width="500" height="500" x="0" y="0"></rect>
                                </clipPath>
                                <linearGradient id="__lottie_element_23" spreadMethod="pad"
                                    gradientUnits="userSpaceOnUse" x1="161.125" y1="161.37899780273438"
                                    x2="-159.447998046875" y2="-158.66700744628906">
                                    <stop offset="0%" stop-color="rgb(255,6,202)"></stop>
                                    <stop offset="50%" stop-color="rgb(255,74,123)"></stop>
                                    <stop offset="100%" stop-color="rgb(255,142,45)"></stop>
                                </linearGradient>
                            </defs>
                            <g clip-path="url(#__lottie_element_17)">
                                <g transform="matrix(1,0,0,1,247.98899841308594,328.0950012207031)" opacity="0.18">
                                    <g opacity="1" transform="matrix(1,0,0,1,2.010999917984009,151.90499877929688)">
                                        <path fill="rgb(255,75,121)" fill-opacity="1"
                                            d=" M0,-20 C71.74700164794922,-20 130,-11.038000106811523 130,0 C130,11.038000106811523 71.74700164794922,20 0,20 C-71.74700164794922,20 -130,11.038000106811523 -130,0 C-130,-11.038000106811523 -71.74700164794922,-20 0,-20z">
                                        </path>
                                    </g>
                                </g>
                                <g transform="matrix(1,0,0,1,257.57598876953125,338.57598876953125)" opacity="1">
                                    <g opacity="1" transform="matrix(1,0,0,1,-7.576000213623047,-18.576000213623047)">
                                        <path fill="url(#__lottie_element_23)" fill-opacity="1"
                                            d=" M0,-160 C88.30400085449219,-160 160,-88.30400085449219 160,0 C160,88.30400085449219 88.30400085449219,160 0,160 C-88.30400085449219,160 -160,88.30400085449219 -160,0 C-160,-88.30400085449219 -88.30400085449219,-160 0,-160z">
                                        </path>
                                    </g>
                                </g>
                                <g transform="matrix(1,0,0,1,244.67698669433594,273.8659973144531)" opacity="1">
                                    <g opacity="1" transform="matrix(1,0,0,1,-56.676998138427734,36.13399887084961)">
                                        <path fill="rgb(255,255,255)" fill-opacity="1"
                                            d=" M18,-22 C18,-22 18,22 18,22 C18,31.934200286865234 9.934200286865234,40 0,40 C0,40 0,40 0,40 C-9.934200286865234,40 -18,31.934200286865234 -18,22 C-18,22 -18,-22 -18,-22 C-18,-31.934200286865234 -9.934200286865234,-40 0,-40 C0,-40 0,-40 0,-40 C9.934200286865234,-40 18,-31.934200286865234 18,-22z">
                                        </path>
                                    </g>
                                </g>
                                <g transform="matrix(1,0,0,1,368.677001953125,273.8659973144531)" opacity="1">
                                    <g opacity="1" transform="matrix(1,0,0,1,-56.676998138427734,36.13399887084961)">
                                        <path fill="rgb(255,255,255)" fill-opacity="1"
                                            d=" M18,-22 C18,-22 18,22 18,22 C18,31.934200286865234 9.934200286865234,40 0,40 C0,40 0,40 0,40 C-9.934200286865234,40 -18,31.934200286865234 -18,22 C-18,22 -18,-22 -18,-22 C-18,-31.934200286865234 -9.934200286865234,-40 0,-40 C0,-40 0,-40 0,-40 C9.934200286865234,-40 18,-31.934200286865234 18,-22z">
                                        </path>
                                    </g>
                                </g>
                            </g>
                        </svg>
                    </div>
                </div>
                <div v-else-if="status === 'success'" class="pull-status">
                    <div class="pull-icon">
                        <svg width="10vw" height="10vw" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M20 6L9 17L4 12" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" />
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="pull-refresh-content-wrapper" :style="{ transform: `translateY(${contentTransform}px)` }">
            <slot></slot>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import Lottie from 'lottie-web'
// Props
interface Props {
    modelValue?: boolean
    threshold?: number
    headHeight?: number
    animationDuration?: number
    successDuration?: number
    pullingText?: string
    loosingText?: string
    loadingText?: string
    successText?: string
    disabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
    modelValue: false,
    threshold: 60,
    headHeight: 50,
    animationDuration: 300,
    successDuration: 1500,
    pullingText: '下拉即可刷新',
    loosingText: '释放即可刷新',
    loadingText: '正在刷新...',
    successText: '刷新成功',
    disabled: false
})

// Emits
const emit = defineEmits<{
    'update:modelValue': [value: boolean]
    'refresh': []
}>()

// 状态管理
const status = ref<'normal' | 'pulling' | 'loosing' | 'loading' | 'success'>('normal')
const touchStartY = ref(0)
const touchCurrentY = ref(0)
const pullDistance = ref(0)
const isScrollable = ref(true)

// 计算属性
const headTransform = computed(() => {
    if (status.value === 'normal') {
        return -props.headHeight
    }
    if (status.value === 'loading' || status.value === 'success') {
        return 0
    }
    return Math.min(pullDistance.value - props.headHeight, 0)
})

const contentTransform = computed(() => {
    if (status.value === 'normal') {
        return 0
    }
    if (status.value === 'loading' || status.value === 'success') {
        return props.headHeight
    }
    return Math.max(pullDistance.value, 0)
})

// 检查是否可以滚动
const checkScrollable = (element: HTMLElement): boolean => {
    return element.scrollTop === 0
}

// 触摸开始
const handleTouchStart = (event: TouchEvent) => {
    if (props.disabled || props.modelValue) return

    const touch = event.touches[0]
    touchStartY.value = touch.clientY
    touchCurrentY.value = touch.clientY

    // 检查是否在顶部
    const scrollElement = event.currentTarget as HTMLElement
    const contentElement = scrollElement.querySelector('.pull-refresh-content-wrapper') as HTMLElement
    isScrollable.value = checkScrollable(contentElement)
}

// 触摸移动
const handleTouchMove = (event: TouchEvent) => {
    if (props.disabled || props.modelValue || !isScrollable.value) return

    const touch = event.touches[0]
    touchCurrentY.value = touch.clientY

    const deltaY = touchCurrentY.value - touchStartY.value

    if (deltaY > 0) {
        event.preventDefault()

        // 计算下拉距离，添加阻尼效果
        pullDistance.value = deltaY * 0.5

        if (pullDistance.value > props.threshold) {
            status.value = 'loosing'
        } else {
            status.value = 'pulling'
        }
    }
}

// 触摸结束
const handleTouchEnd = () => {
    if (props.disabled || props.modelValue || !isScrollable.value) return

    if (status.value === 'loosing') {
        // 触发刷新
        status.value = 'loading'
        emit('update:modelValue', true)
        emit('refresh')
    } else {
        // 回弹
        status.value = 'normal'
        pullDistance.value = 0
    }

    touchStartY.value = 0
    touchCurrentY.value = 0
}

// 监听 modelValue 变化
const handleModelValueChange = (newValue: boolean) => {
    if (!newValue && status.value === 'loading') {
        status.value = 'success'
        setTimeout(() => {
            status.value = 'normal'
            pullDistance.value = 0
        }, props.successDuration)
    }
}

const handleScroll = (event: any) => {
    console.log('滚动事件触发!', event.target)
}

// 监听 props.modelValue 变化
import { watch } from 'vue'
watch(() => props.modelValue, handleModelValueChange)
</script>

<style lang="scss" scoped>
.pull-refresh {
    position: relative;
    overflow: hidden;
    height: 100%;
}

.pull-refresh-head {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #1a1a1a;
    z-index: 10;
    transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.pull-refresh-content {
    display: flex;
    align-items: center;
    justify-content: center;
}

.pull-status {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ff6b9d;
}

.pull-icon {
    display: flex;
    align-items: center;
    justify-content: center;

    svg {
        color: #ff6b9d;
    }
}

.pull-refresh-content-wrapper {
    height: 100%;
    overflow-y: auto;
    transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    background: #1a1a1a;
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}

// 当正在下拉时，移除过渡效果，实现实时跟随
.pull-refresh:active {

    .pull-refresh-head,
    .pull-refresh-content-wrapper {
        transition: none;
    }
}
</style>
