<template>
    <div class="pull-refresh" @touchstart="handleTouchStart" @touchmove="handleTouchMove" @touchend="handleTouchEnd">

        <!-- 下拉刷新头部 -->
        <div class="pull-refresh-head" :style="{ transform: `translateY(${headTransform}px)` }">
            <div class="pull-refresh-content">
                <div v-if="status === 'pulling'" class="pull-status">
                    <div class="pull-icon">
                        <svg width="10vw" height="10vw" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 4L12 20M12 20L18 14M12 20L6 14" stroke="currentColor" stroke-width="2"
                                stroke-linecap="round" stroke-linejoin="round" />
                        </svg>
                    </div>
                </div>
                <div v-else-if="status === 'loosing'" class="pull-status">
                    <div class="pull-icon">
                        <svg width="10vw" height="10vw" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 20L12 4M12 4L6 10M12 4L18 10" stroke="currentColor" stroke-width="2"
                                stroke-linecap="round" stroke-linejoin="round" />
                        </svg>
                    </div>
                </div>
                <div v-else-if="status === 'loading'" class="pull-status">
                    <div class="pull-icon">
                        <div ref="lottieContainer" class="lottie-container"></div>
                    </div>
                </div>
                <div v-else-if="status === 'success'" class="pull-status">
                    <div class="pull-icon">
                        <svg width="10vw" height="10vw" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M20 6L9 17L4 12" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" />
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="pull-refresh-content-wrapper" :style="{ transform: `translateY(${contentTransform}px)` }">
            <slot></slot>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import Lottie from 'lottie-web'
import loadingAnimationData from '@/lottie/loading.json'
// Props
interface Props {
    modelValue?: boolean
    threshold?: number
    headHeight?: number
    animationDuration?: number
    successDuration?: number
    pullingText?: string
    loosingText?: string
    loadingText?: string
    successText?: string
    disabled?: boolean
    lottieAnimationData?: any // 添加 Lottie 动画数据属性
}

const props = withDefaults(defineProps<Props>(), {
    modelValue: false,
    threshold: 60,
    headHeight: 50,
    animationDuration: 300,
    successDuration: 3000000,
    pullingText: '下拉即可刷新',
    loosingText: '释放即可刷新',
    loadingText: '正在刷新...',
    successText: '刷新成功',
    disabled: false,
    lottieAnimationData: () => loadingAnimationData // 默认 Lottie 动画数据
})

// Emits
const emit = defineEmits<{
    'update:modelValue': [value: boolean]
    'refresh': []
}>()

// 状态管理
const status = ref<'normal' | 'pulling' | 'loosing' | 'loading' | 'success'>('normal')
const touchStartY = ref(0)
const touchCurrentY = ref(0)
const pullDistance = ref(0)
const isScrollable = ref(true)

// Lottie 相关
const lottieContainer = ref<HTMLElement>()
let lottieAnimation: any = null

// 计算属性
const headTransform = computed(() => {
    if (status.value === 'normal') {
        return -props.headHeight
    }
    if (status.value === 'loading' || status.value === 'success') {
        return 0
    }
    return Math.min(pullDistance.value - props.headHeight, 0)
})

const contentTransform = computed(() => {
    if (status.value === 'normal') {
        return 0
    }
    if (status.value === 'loading' || status.value === 'success') {
        return props.headHeight
    }
    return Math.max(pullDistance.value, 0)
})

// 检查是否可以滚动
const checkScrollable = (element: HTMLElement): boolean => {
    return element.scrollTop === 0
}

// 触摸开始
const handleTouchStart = (event: TouchEvent) => {
    if (props.disabled || props.modelValue) return

    const touch = event.touches[0]
    touchStartY.value = touch.clientY
    touchCurrentY.value = touch.clientY

    // 检查是否在顶部
    const scrollElement = event.currentTarget as HTMLElement
    const contentElement = scrollElement.querySelector('.pull-refresh-content-wrapper') as HTMLElement
    isScrollable.value = checkScrollable(contentElement)
}

// 触摸移动
const handleTouchMove = (event: TouchEvent) => {
    if (props.disabled || props.modelValue || !isScrollable.value) return

    const touch = event.touches[0]
    touchCurrentY.value = touch.clientY

    const deltaY = touchCurrentY.value - touchStartY.value

    if (deltaY > 0) {
        event.preventDefault()

        // 计算下拉距离，添加阻尼效果
        pullDistance.value = deltaY * 0.5

        if (pullDistance.value > props.threshold) {
            status.value = 'loosing'
        } else {
            status.value = 'pulling'
        }
    }
}

// 触摸结束
const handleTouchEnd = () => {
    if (props.disabled || props.modelValue || !isScrollable.value) return

    if (status.value === 'loosing') {
        // 触发刷新
        status.value = 'loading'
        emit('update:modelValue', true)
        emit('refresh')
    } else {
        // 回弹
        status.value = 'normal'
        pullDistance.value = 0
    }

    touchStartY.value = 0
    touchCurrentY.value = 0
}

// 监听 modelValue 变化
const handleModelValueChange = (newValue: boolean) => {
    if (!newValue && status.value === 'loading') {
        status.value = 'success'
        setTimeout(() => {
            status.value = 'normal'
            pullDistance.value = 0
        }, props.successDuration)
    }
}


// 初始化 Lottie 动画
const initLottieAnimation = () => {
  console.log('initLottieAnimation called', {
    hasContainer: !!lottieContainer.value,
    hasAnimation: !!lottieAnimation,
    hasData: !!props.lottieAnimationData,
    containerElement: lottieContainer.value
  })

  // 如果已经有动画实例，先销毁它
  if (lottieAnimation) {
    lottieAnimation.destroy()
    lottieAnimation = null
  }

  if (lottieContainer.value && props.lottieAnimationData) {
    try {
      console.log('Loading Lottie animation...')
      lottieAnimation = Lottie.loadAnimation({
        container: lottieContainer.value,
        renderer: 'svg',
        loop: true,
        autoplay: false,
        animationData: props.lottieAnimationData
      })

      lottieAnimation.addEventListener('error', (error: any) => {
        console.error('Lottie animation error:', error)
      })

      lottieAnimation.addEventListener('DOMLoaded', () => {
        console.log('Lottie animation loaded successfully')
      })

      console.log('Lottie animation instance created:', lottieAnimation)
    } catch (error) {
      console.error('Failed to load Lottie animation:', error)
    }
  } else {
    console.log('Cannot init Lottie: missing container or data')
  }
}

// 控制 Lottie 动画
const controlLottieAnimation = (shouldPlay: boolean) => {
  console.log('controlLottieAnimation called', { shouldPlay, hasAnimation: !!lottieAnimation })

  if (lottieAnimation) {
    if (shouldPlay) {
      console.log('Playing Lottie animation')
      lottieAnimation.play()
    } else {
      console.log('Stopping Lottie animation')
      lottieAnimation.stop()
    }
  } else {
    console.log('No Lottie animation instance available')
  }
}

// 监听 props.modelValue 变化
import { watch, onMounted, onUnmounted, nextTick } from 'vue'

// 监听状态变化来控制 Lottie 动画
watch(status, (newStatus) => {
  console.log('Status changed to:', newStatus)

  if (newStatus === 'loading') {
    // 确保容器存在后再初始化
    nextTick(() => {
      initLottieAnimation()
      // 延迟一点时间确保动画已经加载
      setTimeout(() => {
        controlLottieAnimation(true)
      }, 300)
    })
  } else {
    controlLottieAnimation(false)
  }
})

onMounted(() => {
  console.log('Component mounted')

  // 预加载 Lottie 动画，等待 DOM 完全渲染
  nextTick(() => {
    setTimeout(() => {
      console.log('onMounted - trying to init Lottie', {
        containerElement: lottieContainer.value,
        containerExists: !!lottieContainer.value,
        status: status.value
      })

      // 只有在 loading 状态时才初始化
      if (status.value === 'loading') {
        initLottieAnimation()
      }
    }, 200)
  })
})

onUnmounted(() => {
  if (lottieAnimation) {
    lottieAnimation.destroy()
    lottieAnimation = null
  }
})

watch(() => props.modelValue, handleModelValueChange)
</script>

<style lang="scss" scoped>
.pull-refresh {
    position: relative;
    overflow: hidden;
    height: 100%;
}

.pull-refresh-head {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #1a1a1a;
    z-index: 10;
    transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.pull-refresh-content {
    display: flex;
    align-items: center;
    justify-content: center;
}

.pull-status {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ff6b9d;
}

.pull-icon {
    display: flex;
    align-items: center;
    justify-content: center;

    svg {
        color: #ff6b9d;
    }
}

.lottie-container {
    width: 10vw;
    height: 10vw;
    display: flex;
    align-items: center;
    justify-content: center;

    svg {
        width: 100% !important;
        height: 100% !important;
    }
}

.pull-refresh-content-wrapper {
    height: 100%;
    overflow-y: auto;
    transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    background: #1a1a1a;
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}

// 当正在下拉时，移除过渡效果，实现实时跟随
.pull-refresh:active {

    .pull-refresh-head,
    .pull-refresh-content-wrapper {
        transition: none;
    }
}
</style>
