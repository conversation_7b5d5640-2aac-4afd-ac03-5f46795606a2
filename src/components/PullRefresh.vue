<template>
  <div class="pull-refresh" 
       @touchstart="handleTouchStart"
       @touchmove="handleTouchMove"
       @touchend="handleTouchEnd">
    
    <!-- 下拉刷新头部 -->
    <div class="pull-refresh-head" 
         :style="{ transform: `translateY(${headTransform}px)` }">
      <div class="pull-refresh-content">
        <div v-if="status === 'pulling'" class="pull-status">
          <div class="pull-icon">↓</div>
          <span class="pull-text">{{ pullingText }}</span>
        </div>
        <div v-else-if="status === 'loosing'" class="pull-status">
          <div class="pull-icon rotate">↑</div>
          <span class="pull-text">{{ loosingText }}</span>
        </div>
        <div v-else-if="status === 'loading'" class="pull-status">
          <div class="pull-icon loading">⟳</div>
          <span class="pull-text">{{ loadingText }}</span>
        </div>
        <div v-else-if="status === 'success'" class="pull-status">
          <div class="pull-icon">✓</div>
          <span class="pull-text">{{ successText }}</span>
        </div>
      </div>
    </div>
    
    <!-- 内容区域 -->
    <div class="pull-refresh-content-wrapper" 
         :style="{ transform: `translateY(${contentTransform}px)` }">
      <slot></slot>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'

// Props
interface Props {
  modelValue?: boolean
  threshold?: number
  headHeight?: number
  animationDuration?: number
  successDuration?: number
  pullingText?: string
  loosingText?: string
  loadingText?: string
  successText?: string
  disabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  threshold: 60,
  headHeight: 50,
  animationDuration: 300,
  successDuration: 1500,
  pullingText: '下拉即可刷新',
  loosingText: '释放即可刷新',
  loadingText: '正在刷新...',
  successText: '刷新成功',
  disabled: false
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'refresh': []
}>()

// 状态管理
const status = ref<'normal' | 'pulling' | 'loosing' | 'loading' | 'success'>('normal')
const touchStartY = ref(0)
const touchCurrentY = ref(0)
const pullDistance = ref(0)
const isScrollable = ref(true)

// 计算属性
const headTransform = computed(() => {
  if (status.value === 'normal') {
    return -props.headHeight
  }
  if (status.value === 'loading' || status.value === 'success') {
    return 0
  }
  return Math.min(pullDistance.value - props.headHeight, 0)
})

const contentTransform = computed(() => {
  if (status.value === 'normal') {
    return 0
  }
  if (status.value === 'loading' || status.value === 'success') {
    return props.headHeight
  }
  return Math.max(pullDistance.value, 0)
})

// 检查是否可以滚动
const checkScrollable = (element: HTMLElement): boolean => {
  return element.scrollTop === 0
}

// 触摸开始
const handleTouchStart = (event: TouchEvent) => {
  if (props.disabled || props.modelValue) return
  
  const touch = event.touches[0]
  touchStartY.value = touch.clientY
  touchCurrentY.value = touch.clientY
  
  // 检查是否在顶部
  const scrollElement = event.currentTarget as HTMLElement
  const contentElement = scrollElement.querySelector('.pull-refresh-content-wrapper') as HTMLElement
  isScrollable.value = checkScrollable(contentElement)
}

// 触摸移动
const handleTouchMove = (event: TouchEvent) => {
  if (props.disabled || props.modelValue || !isScrollable.value) return
  
  const touch = event.touches[0]
  touchCurrentY.value = touch.clientY
  
  const deltaY = touchCurrentY.value - touchStartY.value
  
  if (deltaY > 0) {
    event.preventDefault()
    
    // 计算下拉距离，添加阻尼效果
    pullDistance.value = deltaY * 0.5
    
    if (pullDistance.value > props.threshold) {
      status.value = 'loosing'
    } else {
      status.value = 'pulling'
    }
  }
}

// 触摸结束
const handleTouchEnd = () => {
  if (props.disabled || props.modelValue || !isScrollable.value) return
  
  if (status.value === 'loosing') {
    // 触发刷新
    status.value = 'loading'
    emit('update:modelValue', true)
    emit('refresh')
  } else {
    // 回弹
    status.value = 'normal'
    pullDistance.value = 0
  }
  
  touchStartY.value = 0
  touchCurrentY.value = 0
}

// 监听 modelValue 变化
const handleModelValueChange = (newValue: boolean) => {
  if (!newValue && status.value === 'loading') {
    status.value = 'success'
    setTimeout(() => {
      status.value = 'normal'
      pullDistance.value = 0
    }, props.successDuration)
  }
}

// 监听 props.modelValue 变化
import { watch } from 'vue'
watch(() => props.modelValue, handleModelValueChange)
</script>

<style lang="scss" scoped>
.pull-refresh {
  position: relative;
  overflow: hidden;
  height: 100%;
}

.pull-refresh-head {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #1a1a1a;
  z-index: 10;
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.pull-refresh-content {
  display: flex;
  align-items: center;
  justify-content: center;
}

.pull-status {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #ff6b9d;
}

.pull-icon {
  font-size: 18px;
  font-weight: bold;
  transition: transform 0.3s ease;
  
  &.rotate {
    transform: rotate(180deg);
  }
  
  &.loading {
    animation: rotate 1s linear infinite;
  }
}

.pull-text {
  font-size: 14px;
  font-weight: 500;
}

.pull-refresh-content-wrapper {
  height: 100%;
  overflow-y: auto;
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  background: #1a1a1a;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 当正在下拉时，移除过渡效果，实现实时跟随
.pull-refresh:active {
  .pull-refresh-head,
  .pull-refresh-content-wrapper {
    transition: none;
  }
}
</style>
