<template>
    <div class="live-content">
        <!-- 特色直播区域 -->
        <div class="featured-live">
            <div class="featured-card">
                <div class="live-image">
                    <img src="https://picsum.photos/300/400?random=1" alt="Hanna" />
                    <div class="multi-beam-tag">Multi-beam</div>
                    <div class="viewers-count">129</div>
                </div>
                <div class="live-info">
                    <div class="host-name">
                        <span>Hanna</span>
                        <span class="trophy">🏆 Hanna</span>
                    </div>
                    <div class="participants">
                        <div class="participant" v-for="i in 4" :key="i">
                            <img :src="`https://picsum.photos/30/30?random=${i + 10}`" alt="participant" />
                        </div>
                    </div>
                </div>
            </div>

            <div class="featured-card">
                <div class="live-image">
                    <img src="https://picsum.photos/300/400?random=2" alt="Dariri" />
                    <div class="multi-beam-tag">Multi-beam</div>
                    <div class="viewers-count">72</div>
                </div>
                <div class="live-info">
                    <div class="host-name">
                        <span>Dariri</span>
                        <button class="join-btn">Join 👊</button>
                    </div>
                    <div class="participants">
                        <div class="participant" v-for="i in 4" :key="i">
                            <img :src="`https://picsum.photos/30/30?random=${i + 20}`" alt="participant" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <var-swipe 
            class="swipe-example" 
            :loop="true" 
            :autoplay="3000" 
            :indicator="false"
            @click.stop
            @touchstart.stop
            @touchmove.stop
            @touchend.stop
        >
            <var-swipe-item v-for="(item, index) in swiperList" :key="index">
                <img class="swipe-example-image" :src="item.img" :alt="`Slide ${index + 1}`">
            </var-swipe-item>
            <template #indicator="{ index, length, to }">
                <div class="swipe-example-indicators">
                    <div class="swipe-example-indicator" v-for="(l, idx) in length" :key="idx"
                        :class="{ 'swipe-example-active-indicator': idx === index }" @click.stop="to(idx)">
                    </div>
                </div>
            </template>
        </var-swipe>
        <!-- 小型直播卡片 -->
        <div class="small-live-cards">
            <div class="small-card">
                <div class="card-image">
                    <div class="placeholder-avatar">👁️</div>
                    <div class="live-tag">Live</div>
                    <div class="viewers">9</div>
                </div>
                <div class="card-info">
                    <div class="name">Monica</div>
                    <div class="description">let's go play and talk 🔥 77</div>
                </div>
            </div>

            <div class="small-card">
                <div class="card-image">
                    <div class="placeholder-avatar">👁️</div>
                    <div class="live-tag">Live</div>
                    <div class="hot-tag">Hot</div>
                    <div class="viewers">37</div>
                </div>
                <div class="card-info">
                    <div class="name">🍒 cherry doll</div>
                    <div class="description">TWERK in private 😘😳</div>
                </div>
            </div>
        </div>

        <!-- 快速匹配区域 -->
        <div class="quick-match-section">
            <div class="match-cards">
                <div class="match-card">
                    <div class="placeholder-avatar">👁️</div>
                    <div class="live-tag">Live</div>
                    <div class="viewers">17</div>
                </div>

                <div class="match-card">
                    <div class="placeholder-avatar">👁️</div>
                    <div class="premium-tag">🔒 Premium</div>
                    <div class="viewers">2</div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
// 组件逻辑可以在这里添加
import { ref, onMounted } from 'vue'
import img1 from '@/assets/1.png'
import img2 from '@/assets/2.gif'
import img3 from '@/assets/3.png'
import img4 from '@/assets/4.gif'

const swiperList = ref([
    {
        img: img1,
    },
    {
        img: img2,
    },
    {
        img: img3,
    },
    {
        img: img4,
    }
])

onMounted(() => {
    console.log('LiveContent mounted, swiperList:', swiperList.value)
})


</script>

<style lang="scss" scoped>
// Live 样式
.live-content {
    .featured-live {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 12px;
        margin-bottom: 10px;
    }

    .featured-card {
        background: #2a2a2a;
        border-radius: 12px;
        overflow: hidden;
        position: relative;
    }

    .live-image {
        position: relative;
        height: 200px;

        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .multi-beam-tag {
            position: absolute;
            top: 8px;
            left: 8px;
            background: rgba(255, 107, 157, 0.9);
            color: #fff;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 600;
        }

        .viewers-count {
            position: absolute;
            top: 8px;
            right: 8px;
            background: rgba(0, 0, 0, 0.7);
            color: #fff;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
        }
    }

    .live-info {
        padding: 12px;

        .host-name {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;

            span {
                font-size: 14px;
                font-weight: 600;

                &.trophy {
                    color: #ffd700;
                    font-size: 12px;
                }
            }

            .join-btn {
                background: #ff6b9d;
                color: #fff;
                border: none;
                padding: 4px 8px;
                border-radius: 12px;
                font-size: 10px;
                cursor: pointer;
            }
        }

        .participants {
            display: flex;
            gap: 4px;

            .participant {
                width: 24px;
                height: 24px;
                border-radius: 50%;
                overflow: hidden;
                border: 2px solid #fff;

                img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
            }
        }
    }

    .small-live-cards {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 12px;
        margin-bottom: 20px;
    }

    .small-card {
        background: #2a2a2a;
        border-radius: 12px;
        padding: 12px;
        position: relative;
    }

    .card-image {
        position: relative;
        height: 120px;
        margin-bottom: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #3a3a3a;
        border-radius: 8px;

        .placeholder-avatar {
            font-size: 40px;
            color: #666;
        }

        .live-tag {
            position: absolute;
            top: 8px;
            left: 8px;
            background: #ff3b30;
            color: #fff;
            padding: 2px 6px;
            border-radius: 8px;
            font-size: 10px;
            font-weight: 600;
        }

        .hot-tag {
            position: absolute;
            top: 8px;
            left: 50px;
            background: #ff6b9d;
            color: #fff;
            padding: 2px 6px;
            border-radius: 8px;
            font-size: 10px;
            font-weight: 600;
        }

        .viewers {
            position: absolute;
            top: 8px;
            right: 8px;
            background: rgba(0, 0, 0, 0.7);
            color: #fff;
            padding: 2px 6px;
            border-radius: 8px;
            font-size: 10px;
            font-weight: 600;
        }
    }

    .card-info {
        .name {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .description {
            font-size: 12px;
            color: #999;
            line-height: 1.3;
        }
    }

    .quick-match-section {
        position: relative;
        margin-bottom: 20px;
    }

    .match-cards {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 12px;
        margin-bottom: 16px;
    }

    .match-card {
        background: #2a2a2a;
        border-radius: 12px;
        padding: 12px;
        position: relative;
        height: 100px;
        display: flex;
        align-items: center;
        justify-content: center;

        .placeholder-avatar {
            font-size: 30px;
            color: #666;
        }

        .live-tag {
            position: absolute;
            top: 8px;
            left: 8px;
            background: #ff3b30;
            color: #fff;
            padding: 2px 6px;
            border-radius: 8px;
            font-size: 10px;
            font-weight: 600;
        }

        .premium-tag {
            position: absolute;
            top: 8px;
            left: 8px;
            background: #ffd700;
            color: #000;
            padding: 2px 6px;
            border-radius: 8px;
            font-size: 10px;
            font-weight: 600;
        }

        .viewers {
            position: absolute;
            top: 8px;
            right: 8px;
            background: rgba(0, 0, 0, 0.7);
            color: #fff;
            padding: 2px 6px;
            border-radius: 8px;
            font-size: 10px;
            font-weight: 600;
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .featured-live {
        grid-template-columns: 1fr;
    }

    .small-live-cards {
        grid-template-columns: 1fr;
    }

    .match-cards {
        grid-template-columns: 1fr;
    }
}
.swipe-example {
    height: 115px;
    margin-bottom: 20px;
    border-radius: 12px;
    overflow: hidden;
    position: relative;
    /* 防止触摸事件冒泡到父元素 */
    touch-action: pan-y;
    
    :deep(.var-swipe) {
        height: 100%;
        /* 确保轮播容器能正确处理触摸事件 */
        touch-action: pan-x;
    }
    
    :deep(.var-swipe-item) {
        width: 100%;
        height: 115px;
        
        .swipe-example-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            /* 防止图片拖拽 */
            pointer-events: none;
            user-select: none;
        }
    }
    
    :deep(.swipe-example-indicators) {
        position: absolute;
        bottom: 10px;
        left: 50%;
        transform: translateX(-50%);
        display: flex;
        gap: 6px;
        z-index: 10;
        /* 确保指示器可以点击 */
        pointer-events: auto;
    }
    
    :deep(.swipe-example-indicator) {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.5);
        cursor: pointer;
        transition: all 0.3s ease;
        
        &.swipe-example-active-indicator {
            background: #fff;
            transform: scale(1.2);
        }
    }
}
</style>