/* 渐变移动动画 */
@keyframes gradientMove {
	0% {
		background-position: 0% 50%;
	}
	50% {
		background-position: 100% 50%;
	}
	100% {
		background-position: 0% 50%;
	}
}

/* 光晕扫过动画 */
@keyframes shimmer {
	0% {
		left: -100%;
	}
	100% {
		left: 100%;
	}
}

/* 脉冲动画 */
@keyframes pulse {
	0% {
		width: 0;
		height: 0;
		opacity: 1;
	}
	100% {
		width: 200px;
		height: 200px;
		opacity: 0;
	}
}
/* 旋转渐变动画 (可选) 
// @keyframes conicRotate {
//     from {
//         transform: rotate(0deg);
//     }
//     to {
//         transform: rotate(360deg);
//     }
// }

// // CSS 自定义属性渐变旋转 (可选)
// @keyframes gradientRotate {
//     to {
//         --gradient-angle: 360deg;
//     }
// }
*/
