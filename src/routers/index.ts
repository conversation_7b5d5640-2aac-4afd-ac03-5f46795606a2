import { createRouter, createWebHistory } from 'vue-router'
import type { AppRouteRecordRaw } from './type'
import type { RouteRecordRaw } from 'vue-router'
import Layout from '@/layout/layout.vue'

// 路由配置
const routes: AppRouteRecordRaw[] = [
    {
        path: '/',
        redirect: '/tabs/tab1'
    },
    // {
    //     path: '/home',
    //     name: 'Home',
    //     component: () => import('@/views/home/<USER>'),
    //     meta: {
    //         title: '首页',
    //         keepAlive: true
    //     }
    // },
    {
        path: '/detail',
        name: 'Detail',
        component: () => import('@/views/detail/index.vue'),
        meta: {
            title: '图片详情',
            keepAlive: false
        }
    },
    {
        path: '/tabs',
        name: 'Tabs',
        component: Layout,
        meta: {
            title: '标签页'
        },
        children: [
            {
                path: 'tab1',
                name: 'home',
                component: () => import('@/views/home/<USER>'),
                meta: {
                    title: '标签页1',
                    keepAlive: true
                }
            },
            {
                path: 'tab2',
                name: 'message',
                component: () => import('@/views/detail/index.vue'),
                meta: {
                    title: '标签页2',
                    keepAlive: true
                }
            },
            {
                path: 'tab3',
                name: 'Tab3',
                component: () => import('@/views/home/<USER>'),
                meta: {
                    title: '标签页3',
                    keepAlive: true
                }
            },
            {
                path: 'tab4',
                name: 'Tab4',
                component: () => import('@/views/home/<USER>'),
                meta: {
                    title: '标签页4',
                    keepAlive: true
                }
            },
        ]
    },
    // 404 页面
    {
        path: '/:pathMatch(.*)*',
        name: 'NotFound',
        component: () => import('@/views/error/404.vue'),
        meta: {
            title: '页面不存在'
        }
    }
]

// 创建路由实例
const router = createRouter({
    history: createWebHistory(),
    routes: routes as RouteRecordRaw[],
    scrollBehavior(to, from, savedPosition) {
        if (savedPosition) {
            return savedPosition
        } else {
            return { top: 0, behavior: 'smooth' }
        }
    }
})

// 路由守卫
router.beforeEach((to, from, next) => {
    // 设置页面标题
    const title = to.meta?.title
    if (title) {
        document.title = `${title} - Live Web`
    }
    next()
})

export default router