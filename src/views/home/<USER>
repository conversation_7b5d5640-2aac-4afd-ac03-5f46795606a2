<!--  -->
<template>
    <div class="home-container">
        <!-- 顶部导航栏 -->
        <div class="header">
            <div class="tab-titles">
                <div v-for="(tab, index) in tabs" :key="tab.name" class="tab-title"
                    :class="{ active: activeTab === index }" @click="switchTab(index)">
                    {{ tab.title }}
                </div>
            </div>
            <div class="header-actions">
                <div class="search-icon">🔍</div>
                <div class="trophy-icon">🏆</div>
            </div>
        </div>

        <!-- Swiper 容器 -->
        <div class="swiper-container">
            <var-swipe v-model:active="activeTab" @change="onSwipeChange" class="mySwipe">
                <var-swipe-item v-for="(tab, index) in tabs" :key="tab.name">
                    <var-pull-refresh v-model="refreshLoading[index]" @refresh="onRefresh(index)"
                        :success-duration="1000" :animation-duration="300" :head-height="70">
                        <div class="slide-content">
                            <div class="content-wrapper"
                                :class="{ 'content-wrapper--rebounding': refreshLoading[index] }"
                                :style="{ transform: pullTransforms[index] }">
                                <!-- Live 内容 -->
                                <div v-if="tab.name === 'live'" class="live-content">
                                    <!-- 特色直播区域 -->
                                    <div class="featured-live">
                                        <div class="featured-card">
                                            <div class="live-image">
                                                <img src="https://picsum.photos/300/400?random=1" alt="Hanna" />
                                                <div class="multi-beam-tag">Multi-beam</div>
                                                <div class="viewers-count">129</div>
                                            </div>
                                            <div class="live-info">
                                                <div class="host-name">
                                                    <span>Hanna</span>
                                                    <span class="trophy">🏆 Hanna</span>
                                                </div>
                                                <div class="participants">
                                                    <div class="participant" v-for="i in 4" :key="i">
                                                        <img :src="`https://picsum.photos/30/30?random=${i + 10}`"
                                                            alt="participant" />
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="featured-card">
                                            <div class="live-image">
                                                <img src="https://picsum.photos/300/400?random=2" alt="Dariri" />
                                                <div class="multi-beam-tag">Multi-beam</div>
                                                <div class="viewers-count">72</div>
                                            </div>
                                            <div class="live-info">
                                                <div class="host-name">
                                                    <span>Dariri</span>
                                                    <button class="join-btn">Join 👊</button>
                                                </div>
                                                <div class="participants">
                                                    <div class="participant" v-for="i in 4" :key="i">
                                                        <img :src="`https://picsum.photos/30/30?random=${i + 20}`"
                                                            alt="participant" />
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 推广横幅 -->
                                    <div class="promo-banner">
                                        <div class="flags">
                                            <span>🇹🇷</span>
                                            <span>🇻🇳</span>
                                            <span>🇯🇵</span>
                                            <span>🇺🇸</span>
                                        </div>
                                        <div class="banner-content">
                                            <h2>WE WANT YOU!</h2>
                                            <p>Bring new hosts & Earn up to 20% commission 💰</p>
                                            <button class="learn-more-btn">Learn More</button>
                                        </div>
                                        <div class="banner-icons">
                                            <div class="camera-icon">📹</div>
                                            <div class="chinese-icon">中</div>
                                        </div>
                                    </div>

                                    <!-- 小型直播卡片 -->
                                    <div class="small-live-cards">
                                        <div class="small-card">
                                            <div class="card-image">
                                                <div class="placeholder-avatar">👁️</div>
                                                <div class="live-tag">Live</div>
                                                <div class="viewers">9</div>
                                            </div>
                                            <div class="card-info">
                                                <div class="name">Monica</div>
                                                <div class="description">let's go play and talk 🔥 77</div>
                                            </div>
                                        </div>

                                        <div class="small-card">
                                            <div class="card-image">
                                                <div class="placeholder-avatar">👁️</div>
                                                <div class="live-tag">Live</div>
                                                <div class="hot-tag">Hot</div>
                                                <div class="viewers">37</div>
                                            </div>
                                            <div class="card-info">
                                                <div class="name">🍒 cherry doll</div>
                                                <div class="description">TWERK in private 😘😳</div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 快速匹配区域 -->
                                    <div class="quick-match-section">
                                        <div class="match-cards">
                                            <div class="match-card">
                                                <div class="placeholder-avatar">👁️</div>
                                                <div class="live-tag">Live</div>
                                                <div class="viewers">17</div>
                                            </div>

                                            <div class="match-card">
                                                <div class="placeholder-avatar">👁️</div>
                                                <div class="premium-tag">🔒 Premium</div>
                                                <div class="viewers">2</div>
                                            </div>
                                        </div>

                                        <div class="quick-match-btn">
                                            <div class="match-avatar">
                                                <img src="https://picsum.photos/60/60?random=30" alt="match" />
                                            </div>
                                            <div class="match-text">
                                                <div class="match-title">Quick Match</div>
                                                <div class="match-subtitle">266 hosts online</div>
                                            </div>
                                        </div>

                                        <div class="match-instruction">
                                            <span>Click Match to randomly match charming hosts, try now-</span>
                                            <div class="instruction-close">
                                                <span class="teddy-bear">🧸</span>
                                                <span class="close-x">✕</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Hot 内容 -->
                                <div v-if="tab.name === 'hot'" class="hot-content">
                                    <div class="hot-item" v-for="i in 8" :key="i">
                                        <div class="hot-card">
                                            <div class="hot-image">
                                                <img src="https://picsum.photos/300/200?random=2" alt="hot" />
                                                <div class="hot-badge">🔥</div>
                                            </div>
                                            <div class="hot-info">
                                                <h3>热门话题 {{ i }}</h3>
                                                <p>这是当前最热门的话题内容...</p>
                                                <div class="hot-stats">
                                                    <span>🔥 {{ Math.floor(Math.random() * 10000) + 1000 }}</span>
                                                    <span>💬 {{ Math.floor(Math.random() * 500) + 100 }}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- New 内容 -->
                                <div v-if="tab.name === 'new'" class="new-content">
                                    <div class="new-item" v-for="i in 12" :key="i">
                                        <div class="new-card">
                                            <div class="new-avatar">
                                                <img src="https://picsum.photos/50/50?random=3" alt="user" />
                                            </div>
                                            <div class="new-content-text">
                                                <h4>用户 {{ i }}</h4>
                                                <p>发布了新的内容，快来查看吧！</p>
                                                <div class="new-time">{{ Math.floor(Math.random() * 60) + 1 }}分钟前</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Following 内容 -->
                                <div v-if="tab.name === 'following'" class="following-content">
                                    <div class="following-item" v-for="i in 6" :key="i">
                                        <div class="following-card">
                                            <div class="following-header">
                                                <img src="https://picsum.photos/40/40?random=4" alt="following" />
                                                <div class="following-info">
                                                    <h4>关注用户 {{ i }}</h4>
                                                    <span>刚刚更新了动态</span>
                                                </div>
                                            </div>
                                            <div class="following-content">
                                                <p>分享了一些有趣的内容，值得一看！</p>
                                                <div class="following-image">
                                                    <img src="https://picsum.photos/250/150?random=5" alt="content" />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </var-pull-refresh>
                </var-swipe-item>
            </var-swipe>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'

// 标签页数据
const tabs = ref([
    { name: 'live', title: 'Live' },
    { name: 'hot', title: 'Hot' },
    { name: 'new', title: 'New' },
    { name: 'following', title: 'Following' }
])

const activeTab = ref(0)

// 下拉刷新状态管理
const refreshLoading = ref([false, false, false, false])
const pullTransforms = ref(['translateY(0px)', 'translateY(0px)', 'translateY(0px)', 'translateY(0px)'])

// MutationObserver 实例
let observers: MutationObserver[] = []

// Swipe 事件处理
const onSwipeChange = (index: number) => {
    activeTab.value = index
}

// 切换标签页
const switchTab = (index: number) => {
    activeTab.value = index
}

// 监听下拉刷新控制元素的位置变化
const observePullRefreshControl = (index: number) => {
    nextTick(() => {
        const pullRefreshElements = document.querySelectorAll('.var-pull-refresh')
        const pullRefreshElement = pullRefreshElements[index] as HTMLElement

        if (pullRefreshElement) {
            const controlElement = pullRefreshElement.querySelector('.var-pull-refresh__control') as HTMLElement

            if (controlElement) {
                const observer = new MutationObserver(() => {
                    const style = controlElement.getAttribute('style') || ''

                    // 解析 transform 中的 Y 值
                    const translateMatch = style.match(/translate3d\(0px,\s*([^,]+),/)
                    if (translateMatch) {
                        const yValue = translateMatch[1].trim()

                        // 如果不是默认的隐藏位置，说明正在下拉
                        if (!yValue.includes('-') && !yValue.includes('125%')) {
                            pullTransforms.value[index] = `translateY(${yValue})`
                        } else {
                            pullTransforms.value[index] = 'translateY(0px)'
                        }
                    }
                })

                observer.observe(controlElement, {
                    attributes: true,
                    attributeFilter: ['style']
                })

                observers[index] = observer
            }
        }
    })
}

// 组件挂载后开始监听
onMounted(() => {
    tabs.value.forEach((_, index) => {
        observePullRefreshControl(index)
    })
})

// 组件卸载时清理监听器
onUnmounted(() => {
    observers.forEach(observer => {
        if (observer) {
            observer.disconnect()
        }
    })
})

// 下拉刷新处理
const onRefresh = async (index: number) => {
    refreshLoading.value[index] = true
    console.log(`刷新 ${tabs.value[index].title} 页面`)

    // 模拟异步请求
    await new Promise(resolve => setTimeout(resolve, 1500))

    // 这里可以添加具体的刷新逻辑
    // 例如：重新获取数据、更新内容等

    // 刷新完成后，显示成功状态
    console.log(11111)
    refreshLoading.value[index] = false
}
</script>

<style lang="scss" scoped>
.home-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
    background: #1a1a1a;
    color: #fff;
}

.header {
    background: rgba(26, 26, 26, 0.95);
    padding: 12px 16px;
    position: sticky;
    top: 0;
    z-index: 100;
    display: flex;
    justify-content: space-between;
    align-items: center;
    backdrop-filter: blur(10px);
}

.tab-titles {
    display: flex;
    gap: 24px;
}

.tab-title {
    padding: 8px 0;
    font-size: 16px;
    font-weight: 500;
    color: #999;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;

    &.active {
        color: #ff6b9d;
        font-weight: 600;

        &::after {
            content: '';
            position: absolute;
            bottom: -12px;
            left: 0;
            right: 0;
            height: 2px;
            background: #ff6b9d;
            border-radius: 1px;
        }
    }

    &:hover {
        color: #ff6b9d;
    }
}

.header-actions {
    display: flex;
    gap: 16px;
    font-size: 20px;
}

.swiper-container {
    flex: 1;
    overflow: hidden;
}

.mySwipe {
    height: 100%;
}





.slide-content {
    height: 100%;
    overflow-y: auto;
    background: #1a1a1a;
}

.content-wrapper {
    padding: 16px;
    max-width: 750px;
    margin: 0 auto;
    will-change: transform;
    // 默认没有过渡，实现实时跟随
    transition: none;

    // 当处于回弹状态时添加过渡效果
    &.content-wrapper--rebounding {
        transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }
}

// Live 样式
.live-content {
    .featured-live {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 12px;
        margin-bottom: 20px;
    }

    .featured-card {
        background: #2a2a2a;
        border-radius: 12px;
        overflow: hidden;
        position: relative;
    }

    .live-image {
        position: relative;
        height: 200px;

        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .multi-beam-tag {
            position: absolute;
            top: 8px;
            left: 8px;
            background: rgba(255, 107, 157, 0.9);
            color: #fff;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 600;
        }

        .viewers-count {
            position: absolute;
            top: 8px;
            right: 8px;
            background: rgba(0, 0, 0, 0.7);
            color: #fff;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
        }
    }

    .live-info {
        padding: 12px;

        .host-name {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;

            span {
                font-size: 14px;
                font-weight: 600;

                &.trophy {
                    color: #ffd700;
                    font-size: 12px;
                }
            }

            .join-btn {
                background: #ff6b9d;
                color: #fff;
                border: none;
                padding: 4px 8px;
                border-radius: 12px;
                font-size: 10px;
                cursor: pointer;
            }
        }

        .participants {
            display: flex;
            gap: 4px;

            .participant {
                width: 24px;
                height: 24px;
                border-radius: 50%;
                overflow: hidden;
                border: 2px solid #fff;

                img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
            }
        }
    }

    .promo-banner {
        background: linear-gradient(135deg, #8b5cf6, #1e1b4b);
        border-radius: 16px;
        padding: 20px;
        margin-bottom: 20px;
        position: relative;
        overflow: hidden;

        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1" fill="rgba(255,255,255,0.1)"/></svg>');
            opacity: 0.3;
        }

        .flags {
            display: flex;
            gap: 8px;
            margin-bottom: 12px;
            font-size: 16px;
        }

        .banner-content {
            text-align: center;
            position: relative;
            z-index: 1;

            h2 {
                font-size: 24px;
                font-weight: bold;
                margin: 0 0 8px 0;
                color: #fff;
            }

            p {
                font-size: 14px;
                margin: 0 0 16px 0;
                color: rgba(255, 255, 255, 0.9);
            }

            .learn-more-btn {
                background: #ff6b9d;
                color: #fff;
                border: none;
                padding: 12px 24px;
                border-radius: 25px;
                font-size: 14px;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.3s ease;

                &:hover {
                    background: #ff5a8a;
                    transform: translateY(-2px);
                }
            }
        }

        .banner-icons {
            position: absolute;
            top: 20px;
            right: 20px;
            display: flex;
            flex-direction: column;
            gap: 8px;
            font-size: 20px;
        }
    }

    .small-live-cards {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 12px;
        margin-bottom: 20px;
    }

    .small-card {
        background: #2a2a2a;
        border-radius: 12px;
        padding: 12px;
        position: relative;
    }

    .card-image {
        position: relative;
        height: 120px;
        margin-bottom: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #3a3a3a;
        border-radius: 8px;

        .placeholder-avatar {
            font-size: 40px;
            color: #666;
        }

        .live-tag {
            position: absolute;
            top: 8px;
            left: 8px;
            background: #ff3b30;
            color: #fff;
            padding: 2px 6px;
            border-radius: 8px;
            font-size: 10px;
            font-weight: 600;
        }

        .hot-tag {
            position: absolute;
            top: 8px;
            left: 50px;
            background: #ff6b9d;
            color: #fff;
            padding: 2px 6px;
            border-radius: 8px;
            font-size: 10px;
            font-weight: 600;
        }

        .viewers {
            position: absolute;
            top: 8px;
            right: 8px;
            background: rgba(0, 0, 0, 0.7);
            color: #fff;
            padding: 2px 6px;
            border-radius: 8px;
            font-size: 10px;
            font-weight: 600;
        }
    }

    .card-info {
        .name {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .description {
            font-size: 12px;
            color: #999;
            line-height: 1.3;
        }
    }

    .quick-match-section {
        position: relative;
        margin-bottom: 20px;
    }

    .match-cards {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 12px;
        margin-bottom: 16px;
    }

    .match-card {
        background: #2a2a2a;
        border-radius: 12px;
        padding: 12px;
        position: relative;
        height: 100px;
        display: flex;
        align-items: center;
        justify-content: center;

        .placeholder-avatar {
            font-size: 30px;
            color: #666;
        }

        .live-tag {
            position: absolute;
            top: 8px;
            left: 8px;
            background: #ff3b30;
            color: #fff;
            padding: 2px 6px;
            border-radius: 8px;
            font-size: 10px;
            font-weight: 600;
        }

        .premium-tag {
            position: absolute;
            top: 8px;
            left: 8px;
            background: #ffd700;
            color: #000;
            padding: 2px 6px;
            border-radius: 8px;
            font-size: 10px;
            font-weight: 600;
        }

        .viewers {
            position: absolute;
            top: 8px;
            right: 8px;
            background: rgba(0, 0, 0, 0.7);
            color: #fff;
            padding: 2px 6px;
            border-radius: 8px;
            font-size: 10px;
            font-weight: 600;
        }
    }

    .quick-match-btn {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: linear-gradient(135deg, #ff6b35, #ff6b9d);
        border-radius: 25px;
        padding: 16px 24px;
        display: flex;
        align-items: center;
        gap: 12px;
        cursor: pointer;
        box-shadow: 0 8px 24px rgba(255, 107, 157, 0.3);
        transition: all 0.3s ease;

        &:hover {
            transform: translate(-50%, -50%) scale(1.05);
            box-shadow: 0 12px 32px rgba(255, 107, 157, 0.4);
        }

        .match-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            overflow: hidden;
            border: 2px solid #fff;

            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }
        }

        .match-text {
            .match-title {
                font-size: 16px;
                font-weight: 600;
                color: #fff;
                margin-bottom: 2px;
            }

            .match-subtitle {
                font-size: 12px;
                color: rgba(255, 255, 255, 0.8);
            }
        }
    }

    .match-instruction {
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 20px;
        padding: 8px 16px;
        margin-top: 60px;

        span {
            font-size: 12px;
            color: #ccc;
        }

        .instruction-close {
            display: flex;
            align-items: center;
            gap: 8px;

            .teddy-bear {
                font-size: 16px;
                filter: drop-shadow(0 0 4px rgba(255, 255, 255, 0.5));
            }

            .close-x {
                font-size: 14px;
                color: #999;
                cursor: pointer;

                &:hover {
                    color: #fff;
                }
            }
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .content-wrapper {
        padding: 12px;
    }

    .tab-title {
        font-size: 14px;
    }

    .featured-live {
        grid-template-columns: 1fr;
    }

    .small-live-cards {
        grid-template-columns: 1fr;
    }

    .match-cards {
        grid-template-columns: 1fr;
    }
}
</style>