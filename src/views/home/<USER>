<!--  -->
<template>
    <div class="home-container">
        <!-- 测试按钮 -->
        <div style="position: fixed; top: 10px; right: 10px; z-index: 1000;">
            <button @click="checkScrollElements" style="background: #ff6b9d; color: white; border: none; padding: 8px 12px; border-radius: 4px; cursor: pointer; margin-right: 10px;">
                检查滚动元素
            </button>
            <button @click="testScroll" style="background: #4CAF50; color: white; border: none; padding: 8px 12px; border-radius: 4px; cursor: pointer;">
                测试滚动
            </button>
        </div>
        <!-- 顶部导航栏 -->
        <div class="header">
            <div class="tab-titles">
                <div v-for="(tab, index) in tabs" :key="tab.name" class="tab-title"
                    :class="{ active: activeTab === index }" @click="switchTab(index)">
                    {{ tab.title }}
                </div>
            </div>
            <div class="header-actions">
                <div class="search-icon"><img src="@/assets/common/search.png" alt=""></div>
                <div class="trophy-icon"><img src="@/assets/common/cup.png" alt=""></div>
            </div>
        </div>

        <!-- Swiper 容器 -->
        <div class="swiper-container">
            <var-swipe ref="mySwipe" @change="onSwipeChange" :loop="false" class="mySwipe" :indicator="false">
                <!-- Live 标签页 -->
                <var-swipe-item>
                    <PullRefresh v-model="refreshLoading[0]" @refresh="onRefresh(0)" :threshold="60" :head-height="50"
                        :success-duration="1500">
                        <var-list loading-text="正在努力输出" finished-text="一滴都没有了" error-text="出错了出错了"
                            :finished="finished[0]" v-model:loading="loading[0]" @load="load(0)" @scroll="handleScroll">
                            <div class="slide-content">
                                <div class="content-wrapper">
                                    <div class="content-wrapper-history">
                                        <div class="history-item" v-for="i in 10" :key="i">
                                            <div class="history-item-bg">
                                                <div class="history-item-bg-img">
                                                    <img src="@/assets/default.png" alt="" class="img-bg">
                                                </div>
                                                <div class="history-item-bg-is-live">
                                                    Live
                                                </div>
                                            </div>
                                            <span class="history-item-text">SAYRA💎</span>
                                        </div>
                                    </div>
                                    <LiveContent></LiveContent>
                                </div>
                            </div>
                        </var-list>
                    </PullRefresh>
                </var-swipe-item>

                <!-- Hot 标签页 -->
                <var-swipe-item>
                    <PullRefresh v-model="refreshLoading[1]" @refresh="onRefresh(1)" :threshold="60" :head-height="50"
                        :success-duration="1500">
                        <var-list loading-text="正在努力输出" finished-text="一滴都没有了" error-text="出错了出错了"
                            :finished="finished[1]" v-model:loading="loading[1]" @load="load(1)" @scroll="handleScroll">
                            <div class="slide-content">
                                <div class="content-wrapper">
                                    <div class="hot-content">
                                        <div class="hot-item" v-for="i in 8" :key="i">
                                            <div class="hot-card">
                                                <div class="hot-image">
                                                    <img src="https://picsum.photos/300/200?random=2" alt="hot" />
                                                    <div class="hot-badge">🔥</div>
                                                </div>
                                                <div class="hot-info">
                                                    <h3>热门话题 {{ i }}</h3>
                                                    <p>这是当前最热门的话题内容...</p>
                                                    <div class="hot-stats">
                                                        <span>🔥 {{ Math.floor(Math.random() * 10000) + 1000 }}</span>
                                                        <span>💬 {{ Math.floor(Math.random() * 500) + 100 }}</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </var-list>
                    </PullRefresh>
                </var-swipe-item>

                <!-- New 标签页 -->
                <var-swipe-item>
                    <PullRefresh v-model="refreshLoading[2]" @refresh="onRefresh(2)" :threshold="60" :head-height="50"
                        :success-duration="1500">
                        <var-list loading-text="正在努力输出" finished-text="一滴都没有了" error-text="出错了出错了"
                            :finished="finished[2]" v-model:loading="loading[2]" @load="load(2)" @scroll="handleScroll">
                            <div class="slide-content">
                                <div class="content-wrapper">
                                    <div class="new-content">
                                        <div class="new-item" v-for="i in 12" :key="i">
                                            <div class="new-card">
                                                <div class="new-avatar">
                                                    <img src="https://picsum.photos/50/50?random=3" alt="user" />
                                                </div>
                                                <div class="new-content-text">
                                                    <h4>用户 {{ i }}</h4>
                                                    <p>发布了新的内容，快来查看吧！</p>
                                                    <div class="new-time">{{ Math.floor(Math.random() * 60) + 1 }}分钟前
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </var-list>
                    </PullRefresh>
                </var-swipe-item>

                <!-- Following 标签页 -->
                <var-swipe-item>
                    <PullRefresh v-model="refreshLoading[3]" @refresh="onRefresh(3)" :threshold="60" :head-height="50"
                        :success-duration="1500">
                        <var-list loading-text="正在努力输出" finished-text="一滴都没有了" error-text="出错了出错了"
                            :finished="finished[3]" v-model:loading="loading[3]" @load="load(3)" @scroll="handleScroll">
                            <div class="slide-content">
                                <div class="content-wrapper">
                                    <div class="following-content">
                                        <div class="following-item" v-for="i in 6" :key="i">
                                            <div class="following-card">
                                                <div class="following-header">
                                                    <img src="https://picsum.photos/40/40?random=4" alt="following" />
                                                    <div class="following-info">
                                                        <h4>关注用户 {{ i }}</h4>
                                                        <span>刚刚更新了动态</span>
                                                    </div>
                                                </div>
                                                <div class="following-content">
                                                    <p>分享了一些有趣的内容，值得一看！</p>
                                                    <div class="following-image">
                                                        <img src="https://picsum.photos/250/150?random=5"
                                                            alt="content" />
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </var-list>
                    </PullRefresh>
                </var-swipe-item>
            </var-swipe>
        </div>
        <!--底部的提示-->
        <div class="hunting-match-wrap">
            <div class="quick-match-btn-wrap">
                <div class="quick-match-btn">
                    <div class="match-avatar">
                        <swiper :modules="[Autoplay, EffectCreative]" :effect="'creative'" :creative-effect="{
                            next: {
                                translate: [15, -15, -200],
                                scale: .8,
                                opacity: 1,
                                rotate: [0, 0, 0],
                            },
                            prev: {
                                translate: [0, 0, -200],
                                scale: 1,
                                opacity: 1,
                                rotate: [0, 0, 0]
                            }
                        }" :grab-cursor="true" :autoplay="{
                            delay: 3000,
                            disableOnInteraction: false,
                        }" :loop="true" :slides-per-view="1.5" :centered-slides="true"
                            class="avatar-swiper swiper-creative swiper-3d">
                            <swiper-slide v-for="(avatar, index) in matchAvatars" :key="index" class="avatar-slide">
                                <img :src="avatar.url" :alt="avatar.name" />
                            </swiper-slide>
                        </swiper>
                    </div>
                    <div class="match-text">
                        <div class="match-title">Quick Match</div>
                        <div class="match-subtitle"><span class="online-hosts">{{ onlineHosts }}</span> hosts online
                        </div>
                    </div>
                </div>
            </div>
            <div class="match-instruction">
                <span>Click Match to randomly match charming-</span>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, nextTick } from 'vue'
import PullRefresh from '@/components/PullRefresh.vue'
import LiveContent from '@/components/LiveContent.vue'
import { Swiper, SwiperSlide } from 'swiper/vue'
import { Autoplay, EffectCreative } from 'swiper/modules'
import 'swiper/css'
import 'swiper/css/effect-creative'

const mySwipe = ref<any>(null)

// 标签页数据
const tabs = ref([
    { name: 'live', title: 'Live' },
    { name: 'hot', title: 'Hot' },
    { name: 'new', title: 'New' },
    { name: 'following', title: 'Following' }
])

const activeTab = ref(0)



// 下拉刷新状态管理
const refreshLoading = ref([false, false, false, false])

// 上拉加载状态管理
const loading = ref([false, false, false, false])
const finished = ref([false, false, false, false])

// 匹配头像数据
const matchAvatars = ref([
    { url: 'https://picsum.photos/60/60?random=30', name: 'Host 1' },
    { url: 'https://picsum.photos/60/60?random=31', name: 'Host 2' },
    { url: 'https://picsum.photos/60/60?random=32', name: 'Host 3' },
    { url: 'https://picsum.photos/60/60?random=33', name: 'Host 4' },
    { url: 'https://picsum.photos/60/60?random=34', name: 'Host 5' },
    { url: 'https://picsum.photos/60/60?random=35', name: 'Host 6' },
    { url: 'https://picsum.photos/60/60?random=36', name: 'Host 7' },
    { url: 'https://picsum.photos/60/60?random=37', name: 'Host 8' },
    { url: 'https://picsum.photos/60/60?random=38', name: 'Host 9' },
    { url: 'https://picsum.photos/60/60?random=39', name: 'Host 10' },
    { url: 'https://picsum.photos/60/60?random=40', name: 'Host 11' },
])

const onlineHosts = ref(273)



// Swipe 事件处理
const onSwipeChange = (index: number) => {
    activeTab.value = index

}

// 切换标签页
const switchTab = (index: number) => {
    activeTab.value = index
    // 如果需要通过 ref 控制 swipe，可以取消注释下面这行
    mySwipe.value?.to(index)
}



// 下拉刷新处理
const onRefresh = async (index: number) => {
    refreshLoading.value[index] = true
    console.log(`刷新 ${tabs.value[index].title} 页面`)

    // 模拟异步请求
    await new Promise(resolve => setTimeout(resolve, 1500))

    // 这里可以添加具体的刷新逻辑
    // 例如：重新获取数据、更新内容等

    // 刷新完成后，显示成功状态
    refreshLoading.value[index] = false

    console.log(`${tabs.value[index].title} 页面刷新完成`)
}

// 上拉加载处理
const load = async (index: number) => {
    loading.value[index] = true
    console.log(`加载更多 ${tabs.value[index].title} 内容`)

    // 模拟异步请求
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 这里可以添加具体的加载逻辑
    // 例如：获取更多数据、添加到列表等

    // 加载完成后，更新状态
    loading.value[index] = false

    // 模拟数据加载完毕（这里可以根据实际数据判断）
    if (Math.random() > 0.7) {
        finished.value[index] = true
    }
}
const handleScroll = (event: any) => {
    const target = event.target
    
    console.log('=== 滚动事件触发 ===')
    console.log('滚动元素标签名:', target.tagName)
    console.log('滚动元素类名:', target.className)
    console.log('滚动元素ID:', target.id)
    console.log('滚动元素:', target)
    
    // 获取元素的完整路径
    let elementPath = []
    let currentElement = target
    while (currentElement && currentElement !== document.body) {
        elementPath.unshift(`${currentElement.tagName}${currentElement.className ? '.' + currentElement.className.split(' ').join('.') : ''}`)
        currentElement = currentElement.parentElement
    }
    console.log('元素路径:', elementPath.join(' > '))
    
    console.log('滚动位置:', target.scrollTop)
    console.log('滚动高度:', target.scrollHeight)
    console.log('可视高度:', target.clientHeight)
    console.log('内容高度:', target.scrollHeight - target.clientHeight)
    
    // 检查是否真的可以滚动
    if (target.scrollHeight > target.clientHeight) {
        console.log('✅ 元素可以滚动')
        const scrollPercentage = Math.round((target.scrollTop / (target.scrollHeight - target.clientHeight)) * 100)
        console.log('滚动百分比:', scrollPercentage + '%')
        
        // 检查是否滚动到底部
        if (target.scrollTop + target.clientHeight >= target.scrollHeight - 10) {
            console.log('🔥 滚动到底部!')
        }
    } else {
        console.log('❌ 元素不可滚动')
    }
    
    console.log('=== 滚动事件结束 ===\n')
}

onMounted(() => {
    console.log('=== 组件已挂载，开始检查滚动元素 ===')
    
    setTimeout(() => {
        console.log('\n🔍 检查 var-list 组件...')
        const varLists = document.querySelectorAll('.var-list')
        console.log('找到 var-list 元素数量:', varLists.length)
        
        varLists.forEach((list, index) => {
            console.log(`\n--- var-list ${index} ---`)
            console.log('元素:', list)
            console.log('标签名:', list.tagName)
            console.log('类名:', list.className)
            console.log('滚动高度:', list.scrollHeight)
            console.log('可视高度:', list.clientHeight)
            console.log('是否可滚动:', list.scrollHeight > list.clientHeight ? '✅ 是' : '❌ 否')
            
            // 查找内部的所有元素
            const allElements = list.querySelectorAll('*')
            console.log(`内部元素总数: ${allElements.length}`)
            
            // 查找可滚动的元素
            const scrollableElements: Array<{
                element: Element;
                tagName: string;
                className: string;
                scrollHeight: number;
                clientHeight: number;
            }> = []
            allElements.forEach((el: any) => {
                if (el.scrollHeight > el.clientHeight) {
                    scrollableElements.push({
                        element: el,
                        tagName: el.tagName,
                        className: el.className,
                        scrollHeight: el.scrollHeight,
                        clientHeight: el.clientHeight
                    })
                }
            })
            
            if (scrollableElements.length > 0) {
                console.log(`找到 ${scrollableElements.length} 个可滚动元素:`)
                scrollableElements.forEach((item, idx) => {
                    console.log(`  ${idx + 1}. ${item.tagName}${item.className ? '.' + item.className : ''}`)
                    console.log(`     滚动高度: ${item.scrollHeight}, 可视高度: ${item.clientHeight}`)
                })
            } else {
                console.log('❌ 没有找到可滚动元素')
            }
        })
        
        console.log('\n🔍 检查 slide-content 元素...')
        const slideContents = document.querySelectorAll('.slide-content')
        console.log('找到 slide-content 元素数量:', slideContents.length)
        
        slideContents.forEach((content, index) => {
            console.log(`\n--- slide-content ${index} ---`)
            console.log('元素:', content)
            console.log('标签名:', content.tagName)
            console.log('类名:', content.className)
            console.log('滚动高度:', content.scrollHeight)
            console.log('可视高度:', content.clientHeight)
            console.log('是否可滚动:', content.scrollHeight > content.clientHeight ? '✅ 是' : '❌ 否')
        })
        
        console.log('\n=== 滚动元素检查完成 ===')
    }, 2000)
})
</script>

<style lang="scss" scoped>
.home-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
    background: #1a1a1a;
    color: #fff;
}

.header {
    background: rgba(26, 26, 26, 0.95);
    padding: 15px 15px 0;
    position: sticky;
    top: 0;
    z-index: 100;
    display: flex;
    justify-content: space-between;
    align-items: center;
    backdrop-filter: blur(10px);
}

.tab-titles {
    display: flex;
    gap: 15px;

    .tab-title {
        padding: 8px 0;
        font-size: 18px;
        font-weight: 600;
        line-height: 21px;
        color: rgba(255, 255, 255, 0.7);
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;

        &.active {
            color: #fff;
            font-weight: 600;

            &::after {
                content: '';
                position: absolute;
                bottom: -5px;
                left: 0;
                right: 0;
                margin: 0 auto;
                width: 10px;
                height: 3px;
                background: linear-gradient(90deg, #FF812B 0%, #FF0BC2 100%);
                border-radius: 2px;
            }
        }
    }
}



.header-actions {
    display: flex;
    gap: 20px;

    img {
        width: 24px;
        height: 24px;
    }
}

.swiper-container {
    flex: 1;
    overflow: hidden;
}

.mySwipe {
    height: 100%;
}





.slide-content {
    height: 100%;
    overflow-y: auto;
    background: #1a1a1a;
}

.content-wrapper {

    padding: 0px 15px;
    // max-width: 750px;
    margin: 0 auto;
    margin-top: 15px;

    .content-wrapper-history {
        display: flex;
        gap: 10px;
        overflow-x: auto;
        margin-bottom: 20px;

        .history-item {
            flex-shrink: 0;
            width: 54px;
            height: auto;

            .history-item-bg {
                width: 100%;
                height: 54px;
                border-radius: 50%;
                background: linear-gradient(90deg, #FF812B 0%, #FF0BC2 100%);
                border-radius: 50%;
                padding: 1.5px;
                position: relative;
                display: flex;
                justify-content: center;
                align-items: center;

                .history-item-bg-img {
                    width: 100%;
                    padding: 1.5px;
                    background-color: #1a1a1a;
                    border-radius: 50%;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    .img-bg {
                        width: 100%;
                        height: 100%;
                        border-radius: 50%;
                    }
                }

                .history-item-bg-is-live {
                    position: absolute;
                    bottom: 0;
                    padding: 1px 7px;
                    background: #FF1A9E;
                    border-radius: 4px;
                    font-size: 10px;
                    font-weight: 600;
                    color: #fff;
                    line-height: 12px;
                    text-align: center;
                }
            }

            .history-item-text {
                font-size: 12px;
                font-weight: 600;
                color: rgba(255, 255, 255, 0.7);
                line-height: 14px;
                text-align: center;
                margin-top: 7px;
                white-space: nowrap;
            }
        }
    }
}

.hunting-match-wrap {
    position: fixed;
    width: 100%;
    height: 109px;
    bottom: var(--bottom-navigation-height);
    left: 0;
    right: 0;

    .quick-match-btn-wrap {
        width: 100%;
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 5px;
        position: relative;

        .quick-match-btn {
            min-width: 160px;
            background: linear-gradient(96deg, #ffc83c 0%, #ff45cb 41%, #ca55e2 100%);
            border-radius: 30px;
            border: 1px solid rgba(255, 255, 255, .42);
            box-sizing: border-box;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 22px 8px 9px;

            &:hover {
                box-shadow: 0 12px 32px rgba(255, 107, 157, 0.4);
            }

            .match-avatar {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                overflow: visible;
                // border: 2px solid #fff;
                position: relative;

                .avatar-swiper {
                    // width: 100%;
                    // height: 100%;
                    border-radius: 50%;
                    overflow: visible;
                    perspective: 800px;
                    margin-top: 10px;

                    :deep(.swiper-wrapper) {
                        border-radius: 50%;
                        transform-style: preserve-3d;
                    }

                    :deep(.swiper-slide) {
                        border-radius: 50%;
                        overflow: hidden;
                        transform-style: preserve-3d;
                        backface-visibility: hidden;
                        width: 27px !important;
                        height: 27px !important;
                        // border: 2px solid #fff;
                        // box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
                        transition: all 0.3s ease;
                    }

                    :deep(.swiper-slide-active) {
                        // box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
                        transform: scale(1.05);
                    }
                }

                .avatar-slide {
                    width: 100%;
                    height: 100%;
                    border-radius: 50%;
                    overflow: hidden;

                    img {
                        width: 100%;
                        height: 100%;
                        object-fit: contain;
                        border-radius: 50%;
                        border: 1px solid #fff;
                    }
                }
            }

            .match-text {
                margin-left: 7px;

                .match-title {
                    font-size: 15px;
                    font-weight: 600;
                    color: #fff;
                    line-height: 18px;
                    // background: linear-gradient(153deg, #ffffff, #f9f2bb);
                    // -webkit-background-clip: text;
                    // color: transparent;
                    // box-sizing: border-box;
                    // transform: translate(-3px);
                }

                .match-subtitle {
                    font-size: 12px;
                    color: #fff;
                    line-height: 14px;

                    .online-hosts {
                        color: #FFF316;
                    }
                }
            }
        }
    }


    .match-instruction {
        width: 100%;
        position: absolute;
        left: 0;
        bottom: 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: #1d1d3b;
        padding: 8px 16px;

        span {
            font-size: 12px;
            color: #ccc;
        }

        .instruction-close {
            display: flex;
            align-items: center;
            gap: 8px;

            .teddy-bear {
                font-size: 16px;
                filter: drop-shadow(0 0 4px rgba(255, 255, 255, 0.5));
            }

            .close-x {
                font-size: 14px;
                color: #999;
                cursor: pointer;

                &:hover {
                    color: #fff;
                }
            }
        }
    }
}


// 响应式设计
// @media (max-width: 768px) {
//     .content-wrapper {
//         padding: 12px;
//     }

//     .tab-title {
//         font-size: 14px;
//     }

//     .featured-live {
//         grid-template-columns: 1fr;
//     }

//     .small-live-cards {
//         grid-template-columns: 1fr;
//     }

//     .match-cards {
//         grid-template-columns: 1fr;
//     }
// }</style>