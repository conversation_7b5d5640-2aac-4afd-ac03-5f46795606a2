<template>
    <div class="live-container">
        <swiper 
            :direction="'vertical'" 
            :slides-per-view="1" 
            :space-between="0" 
            :mousewheel="true" 
            :keyboard="true"
            :loop="false" 
            :centered-slides="true" 
            :initial-slide="1" 
            @swiper="onSwiper" 
            @slideChange="onSlideChange"
            class="live-swiper"
        >
            <swiper-slide 
                v-for="(live, index) in visibleLiveList" 
                :key="`${live.id}-${live.uniqueKey}`"
                class="live-slide"
            >
                <div class="live-item">
                    <video 
                        :src="live.videoUrl" 
                        :poster="live.coverUrl" 
                        class="live-video"
                        :autoplay="currentIndex === index" 
                        :muted="currentIndex !== index" 
                        loop 
                        playsinline
                        webkit-playsinline 
                        @loadedmetadata="onVideoLoaded" 
                        @error="onVideoError"
                    ></video>
                </div>
            </swiper-slide>
        </swiper>
    </div>
</template>

<script lang="ts">
import { Swiper, SwiperSlide } from 'swiper/vue'
import { Mousewheel, Keyboard, Autoplay } from 'swiper/modules'
import 'swiper/css'
import 'swiper/css/mousewheel'
import 'swiper/css/keyboard'

export default {
    name: 'AnchorLiveModule',
    components: {
        Swiper,
        SwiperSlide
    },
    data() {
        return {
            currentIndex: 0,
            visibleLiveList: [] as any[],
            swiperInstance: null as any,
            allLiveList: [
                {
                    id: 1,
                    videoUrl: 'https://example.com/live1.mp4',
                    coverUrl: 'https://picsum.photos/300/200?random=1',
                    uniqueKey: 1
                },
                {
                    id: 2,
                    videoUrl: 'https://example.com/live2.mp4',
                    coverUrl: 'https://picsum.photos/300/200?random=2',
                    uniqueKey: 2
                },
                {
                    id: 3,
                    videoUrl: 'https://example.com/live3.mp4',
                    coverUrl: 'https://picsum.photos/300/200?random=3',
                    uniqueKey: 3
                }
            ],
            // 模拟后端数据状态
            hasMoreUp: true,    // 向上还有更多数据
            hasMoreDown: true,  // 向下还有更多数据
            isLoading: false    // 是否正在加载
        }
    },
    mounted() {
        this.initLiveData()
    },
    methods: {
        initLiveData() {
            // 初始化直播数据，永远只显示3个
            this.updateVisibleLiveList(1) // 从第二个开始（索引1）
            console.log('初始化直播数据')
        },

        updateVisibleLiveList(centerIndex: number) {
            const totalLength = this.allLiveList.length
            if (totalLength === 0) return

            // 计算三个可见的直播索引
            const prevIndex = (centerIndex - 1 + totalLength) % totalLength
            const nextIndex = (centerIndex + 1) % totalLength

            // 更新 uniqueKey 以确保 Vue 能正确识别不同的 slide
            this.visibleLiveList = [
                { ...this.allLiveList[prevIndex], uniqueKey: Date.now() - 2 },
                { ...this.allLiveList[centerIndex], uniqueKey: Date.now() - 1 },
                { ...this.allLiveList[nextIndex], uniqueKey: Date.now() }
            ]
        },

        onSwiper(swiper: any) {
            console.log('Swiper初始化完成', swiper)
            this.swiperInstance = swiper
        },

        onSlideChange(swiper: any) {
            this.currentIndex = swiper.activeIndex
            console.log('切换到直播:', this.currentIndex)

            // 暂停其他视频，播放当前视频
            this.pauseAllVideos()
            this.playCurrentVideo()

            // 检查是否需要加载更多数据
            this.checkAndLoadMore(swiper)
        },

        pauseAllVideos() {
            const videos = document.querySelectorAll('.live-video') as NodeListOf<HTMLVideoElement>
            videos.forEach(video => {
                video.pause()
            })
        },

        playCurrentVideo() {
            const currentVideo = document.querySelector(`.live-slide:nth-child(${this.currentIndex + 1}) .live-video`) as HTMLVideoElement
            if (currentVideo) {
                currentVideo.play().catch(err => {
                    console.log('自动播放失败:', err)
                })
            }
        },

        onVideoLoaded(event: Event) {
            const video = event.target as HTMLVideoElement
            console.log('视频加载完成:', video.src)
        },

        onVideoError(event: Event) {
            const video = event.target as HTMLVideoElement
            console.error('视频加载失败:', video.src)
        },

        checkAndLoadMore(swiper: any) {
            const currentIndex = swiper.activeIndex
            const totalSlides = this.visibleLiveList.length

            // 检查是否需要向下加载更多数据
            if (currentIndex >= totalSlides - 1 && this.hasMoreDown && !this.isLoading) {
                console.log('接近底部，向下加载更多数据')
                this.loadMoreLives('down')
            }

            // 检查是否需要向上加载更多数据
            if (currentIndex <= 0 && this.hasMoreUp && !this.isLoading) {
                console.log('接近顶部，向上加载更多数据')
                this.loadMoreLives('up')
            }
        },

        loadMoreLives(direction: 'up' | 'down') {
            if (this.isLoading) {
                console.log('正在加载中，跳过请求')
                return
            }

            // 检查是否还有更多数据
            if (direction === 'down' && !this.hasMoreDown) {
                console.log('向下没有更多数据了')
                return
            }
            if (direction === 'up' && !this.hasMoreUp) {
                console.log('向上没有更多数据了')
                return
            }

            this.isLoading = true
            console.log(`开始${direction === 'down' ? '向下' : '向上'}加载数据`)

            // 模拟异步加载更多直播数据
            setTimeout(() => {
                const newLives = [
                    {
                        id: direction === 'down' ? this.allLiveList.length + 1 : this.allLiveList[0].id - 1,
                        videoUrl: `https://example.com/live${direction === 'down' ? this.allLiveList.length + 1 : this.allLiveList[0].id - 1}.mp4`,
                        coverUrl: `https://picsum.photos/300/200?random=${direction === 'down' ? this.allLiveList.length + 1 : this.allLiveList[0].id - 1}`,
                        uniqueKey: Date.now() + Math.random()
                    },
                    {
                        id: direction === 'down' ? this.allLiveList.length + 2 : this.allLiveList[0].id - 2,
                        videoUrl: `https://example.com/live${direction === 'down' ? this.allLiveList.length + 2 : this.allLiveList[0].id - 2}.mp4`,
                        coverUrl: `https://picsum.photos/300/200?random=${direction === 'down' ? this.allLiveList.length + 2 : this.allLiveList[0].id - 2}`,
                        uniqueKey: Date.now() + Math.random()
                    }
                ]
                
                if (direction === 'down') {
                    // 向下加载：添加到所有直播列表末尾
                    this.allLiveList.push(...newLives)
                    
                    // 模拟数据耗尽的情况（比如加载到第10个就没了）
                    if (this.allLiveList.length >= 10) {
                        this.hasMoreDown = false
                        console.log('向下数据已加载完毕')
                    }
                } else {
                    // 向上加载：添加到所有直播列表开头
                    this.allLiveList.unshift(...newLives)
                    
                    // 模拟数据耗尽的情况（比如加载到第-5个就没了）
                    if (this.allLiveList[0].id <= -5) {
                        this.hasMoreUp = false
                        console.log('向上数据已加载完毕')
                    }
                }
                
                this.isLoading = false
                console.log(`${direction === 'down' ? '向下' : '向上'}加载完成，新增:`, newLives.length)
            }, 1000) // 模拟1秒加载时间
        }
    }
}
</script>

<style lang="scss" scoped>
.live-container {
    width: 100%;
    height: 100vh;
    position: relative;
    background: #000;
    overflow: hidden;
}

.live-swiper {
    width: 100%;
    height: 100%;
}

.live-slide {
    width: 100%;
    height: 100vh;
    position: relative;
}

.live-item {
    width: 100%;
    height: 100%;
    position: relative;
}

.live-video {
    width: 100%;
    height: 100%;
    object-fit: cover;
}
</style>
