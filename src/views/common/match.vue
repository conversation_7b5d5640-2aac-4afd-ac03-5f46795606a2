<!-- 匹配 -->
<template>
    <div class="app-container match-page">
        <!-- 关闭按钮 -->
        <div class="close-btn" @click="handleClose">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M18 6L6 18M6 6L18 18" stroke="white" stroke-width="2" stroke-linecap="round"
                    stroke-linejoin="round" />
            </svg>
        </div>

        <!-- 轮播图容器 -->
        <div class="swiper-container">
            <swiper
                :slides-per-view="1.2"
                :space-between="30"
                :centered-slides="true"
                :loop="true"
                :autoplay="{
                    delay: 3000,
                    disableOnInteraction: false,
                }"
                :grab-cursor="true"
                :pagination="{ clickable: true }"
                :modules="modules"
                class="match-swiper"
            >
                <swiper-slide v-for="(user, index) in users" :key="index" class="match-slide">
                    <div class="user-card">
                        <!-- 用户头像 -->
                        <div class="user-avatar">
                            <img :src="user.avatar" :alt="user.name" />
                            <!-- 在线状态指示器 -->
                            <div class="online-indicator" v-if="user.isOnline"></div>
                        </div>

                        <!-- 用户信息 -->
                        <div class="user-info">
                            <div class="user-name">
                                {{ user.name }}
                                <span class="age">{{ user.age }}</span>
                            </div>
                            <div class="user-question">{{ user.question }}</div>
                        </div>

                        <!-- 操作按钮 -->
                        <div class="action-buttons">
                            <button class="reject-btn" @click="handleReject(user)">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                    <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2"
                                        stroke-linecap="round" />
                                </svg>
                            </button>
                            <button class="like-btn" @click="handleLike(user)">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                    <path
                                        d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"
                                        fill="currentColor" />
                                </svg>
                            </button>
                        </div>
                    </div>
                </swiper-slide>
            </swiper>
        </div>

        <!-- 底部文案 -->
        <div class="bottom-text">
            <h2>We guarantee that real people will answer the calls~</h2>
            <p class="matching-text">Matching.</p>
        </div>

        <!-- 规则按钮 -->
        <div class="rule-btn">
            <button class="rule-button">
                <span class="rule-icon">📋</span>
                Rule
            </button>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { Swiper, SwiperSlide } from 'swiper/vue'
import { Autoplay, Pagination } from 'swiper/modules'
import 'swiper/css'
import 'swiper/css/pagination'

// Swiper 模块配置
const modules = [Autoplay, Pagination]

// 模拟用户数据
const users = ref([
    {
        id: 1,
        name: 'Sasha',
        age: 25,
        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=400&h=600&fit=crop&crop=face',
        question: 'What real connection feels like?',
        isOnline: true
    },
    {
        id: 2,
        name: 'Emma',
        age: 23,
        avatar: 'https://images.unsplash.com/photo-1517841905240-472988babdf9?w=400&h=600&fit=crop&crop=face',
        question: 'Looking for genuine conversations',
        isOnline: true
    },
    {
        id: 3,
        name: 'Olivia',
        age: 27,
        avatar: 'https://images.unsplash.com/photo-1524504388940-b1c1722653e1?w=400&h=600&fit=crop&crop=face',
        question: 'Ready for something real?',
        isOnline: false
    },
    {
        id: 4,
        name: 'Sophie',
        age: 24,
        avatar: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=400&h=600&fit=crop&crop=face',
        question: 'What makes you smile?',
        isOnline: true
    }
])

// 处理关闭
const handleClose = () => {
    console.log('Close match page')
    // 这里可以添加路由跳转或关闭逻辑
}

// 处理拒绝
const handleReject = (user: any) => {
    console.log('Reject user:', user.name)
    // 这里可以添加拒绝逻辑
}

// 处理喜欢
const handleLike = (user: any) => {
    console.log('Like user:', user.name)
    // 这里可以添加喜欢逻辑
}
</script>

<style lang="scss" scoped>
.match-page {
    height: 100%;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;
    overflow: hidden;

    // 关闭按钮
    .close-btn {
        position: absolute;
        top: 40px;
        right: 20px;
        width: 40px;
        height: 40px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
        z-index: 10;

        &:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }
    }

    // 轮播图容器
    .swiper-container {
        width: 100%;
        max-width: 400px;
        height: 600px;
        margin: 40px 0;
        overflow: hidden;
    }

    .match-swiper {
        width: 100%;
        height: 100%;
        overflow: visible;

        :deep(.swiper-wrapper) {
            align-items: center;
        }

        :deep(.swiper-slide) {
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            opacity: 0.7;
            transform: scale(0.85);

            &.swiper-slide-active {
                opacity: 1;
                transform: scale(1);
                z-index: 2;
            }

            &.swiper-slide-prev,
            &.swiper-slide-next {
                opacity: 0.5;
                transform: scale(0.75);
                z-index: 1;
            }
        }
    }

    // 用户卡片
    .user-card {
        width: 300px;
        height: 500px;
        background: rgba(255, 255, 255, 0.95);
        border-radius: 20px;
        padding: 20px;
        display: flex;
        flex-direction: column;
        align-items: center;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(10px);
        position: relative;
        overflow: hidden;

        // 用户头像
        .user-avatar {
            width: 260px;
            height: 330px;
            border-radius: 15px;
            overflow: hidden;
            position: relative;
            margin-bottom: 20px;

            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }

            // 在线状态指示器
            .online-indicator {
                position: absolute;
                bottom: 15px;
                right: 15px;
                width: 20px;
                height: 20px;
                background: #4CAF50;
                border-radius: 50%;
                border: 3px solid white;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
            }
        }

        // 用户信息
        .user-info {
            text-align: center;
            margin-bottom: 20px;
            flex: 1;

            .user-name {
                font-size: 24px;
                font-weight: bold;
                color: #333;
                margin-bottom: 8px;
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 8px;

                .age {
                    font-size: 18px;
                    color: #666;
                    font-weight: normal;
                }
            }

            .user-question {
                font-size: 16px;
                color: #666;
                line-height: 1.4;
                max-width: 250px;
            }
        }

        // 操作按钮
        .action-buttons {
            display: flex;
            gap: 20px;
            align-items: center;

            button {
                width: 60px;
                height: 60px;
                border-radius: 50%;
                border: none;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: all 0.3s ease;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

                &:hover {
                    transform: scale(1.1);
                }

                &:active {
                    transform: scale(0.95);
                }
            }

            .reject-btn {
                background: #ff4757;
                color: white;

                &:hover {
                    background: #ff3742;
                    box-shadow: 0 6px 16px rgba(255, 71, 87, 0.4);
                }
            }

            .like-btn {
                background: #2ed573;
                color: white;

                &:hover {
                    background: #26d065;
                    box-shadow: 0 6px 16px rgba(46, 213, 115, 0.4);
                }
            }
        }
    }

    // 底部文案
    .bottom-text {
        text-align: center;
        color: white;
        margin-top: 20px;

        h2 {
            font-size: 18px;
            font-weight: 500;
            margin-bottom: 10px;
            line-height: 1.4;
        }

        .matching-text {
            font-size: 16px;
            color: #ff6b9d;
            font-weight: 600;
            margin: 0;
        }
    }

    // 规则按钮
    .rule-btn {
        position: absolute;
        bottom: 40px;
        left: 20px;

        .rule-button {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 25px;
            padding: 12px 20px;
            color: white;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);

            .rule-icon {
                font-size: 16px;
            }

            &:hover {
                background: rgba(255, 255, 255, 0.3);
                transform: translateY(-2px);
            }
        }
    }
}

// Swiper 分页器样式
:deep(.swiper-pagination) {
    bottom: -40px !important;

    .swiper-pagination-bullet {
        background: rgba(255, 255, 255, 0.5);
        opacity: 1;

        &.swiper-pagination-bullet-active {
            background: white;
        }
    }
}

// 响应式设计
@media (max-width: 480px) {
    .match-page {
        padding: 10px;

        .user-card {
            width: 260px;
            height: 460px;

            .user-avatar {
                width: 220px;
                height: 280px;
            }
        }

        .swiper-container {
            max-width: 280px;
            height: 480px;
        }
    }
}
</style>