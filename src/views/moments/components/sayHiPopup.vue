<!--  -->
<template>
    <var-popup position="center" v-model:show="showSayHiPopup" teleport="body" :default-style="false"
        class="say-hi-popup">
        <div class="say-hi-dialog">
            <div class="greet-header">
            </div>
            <div class="greet-content">
                <div class="greet-header-bg"></div>
                <img src="@/assets/popup/close-say-hi.webp" alt="" class="greet-header-close-img" @click="closeSayHiPopup">
                <div class="greet-content-box">
                    <div class="greet-content-box-price">
                        <span class="price-text">2</span>
                        <img src="@/assets/common/coins.png" alt="" class="price-icon">
                    </div>
                    <div class="greet-triangle">

                    </div>
                    <div class="greet-content-box-tip">
                        <span>Messages with
                            <span class="hi-text">"Hi"</span>
                            appear at the top of recipient inbox
                        </span>
                    </div>
                    <!--账户余额-->
                    <div class="greet-content-box-wallet">
                        <span>My Wallet:</span>
                        <span class="wallet-text">0</span>
                    </div>
                    <!--充值按钮-->
                    <div class="greet-content-box-recharge">
                        <div class="recharge-btn">
                            <span>Recharge</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </var-popup>
</template>

<script lang="ts" setup name="sayHiPopup">
import { ref, watch } from 'vue'

const props = defineProps({
    show: {
        type: Boolean,
        default: false
    }
})

const emit = defineEmits(['update:show'])

const showSayHiPopup = ref(props.show)

// 监听 props.show 的变化
watch(() => props.show, (newVal) => {
    showSayHiPopup.value = newVal
})

// 监听内部状态变化，向父组件发送事件
watch(showSayHiPopup, (newVal) => {
    emit('update:show', newVal)
})

const closeSayHiPopup = () => {
    showSayHiPopup.value = false
}
</script>
<style lang="scss" scoped>
.say-hi-dialog {
    width: 300px;

    .greet-header {
        width: 100%;
        height: 72px;
    }

    .greet-content {
        width: 100%;
        min-height: 269px;
        background-color: #fefefd;
        border-radius: 24px;
        padding-top: 86px;

        .greet-header-bg {
            position: absolute;
            top: 0;
            left: 0;
            z-index: 5;
            pointer-events: none;
            width: 100%;
            height: 220px;
            background-size: 100% auto;
            background-repeat: no-repeat;
            background-image: url('@/assets/popup/say-hi.webp');
            background-position: top;
        }
        .greet-header-close-img{
            position: absolute;
            top: 80px;
            right: 10px;
            width: 26px;
            height: 26px;
            cursor: pointer;
            z-index: 10;
        }
        .greet-content-box {
            width: 100%;
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            z-index: 10;

            .greet-content-box-price {
                display: flex;
                justify-content: center;
                align-items: center;

                .price-text {
                    font-family: var(--font-family-type3);
                    font-size: 24px;
                    color: #ff527a;
                    padding: 2px;
                }

                .price-icon {
                    width: 20px;
                    height: 20px;
                }
            }

            .greet-triangle {
                width: 0px;
                height: 0px;
                position: absolute;
                // left: 112px;
                top: 28px;
                transform: rotate(45deg);

                &::before {
                    content: '';
                    width: 0;
                    border: 6px solid #ffebf5;
                    border-left: 6px solid transparent;
                    border-top: 6px solid transparent;
                    border-bottom-right-radius: 4px;
                    transform: rotate(180deg);
                    position: absolute;
                    top: 0;
                    left: 0;
                }
            }

            .greet-content-box-tip {
                margin-top: 7px;
                width: 224px;
                font-size: 14px;
                padding: 10px 14px 6px;
                background: #ffebf5;
                border-radius: 16px;
                text-align: center;
                line-height: 20px;
                font-family: var(--font-family-type3);

                .hi-text {
                    font-style: italic;
                    color: #ff527a;
                }
            }

            .greet-content-box-wallet {
                margin-top: 40px;
                font-size: 14px;
                color: #999;

                .wallet-text {
                    margin-left: 2px;
                    font-weight: 700;
                    color: $common-color;
                }
            }

            .greet-content-box-recharge {
                width: 100%;
                padding: 0 24px;

                .recharge-btn {
                    margin-top: 12px;
                    margin-bottom: 20px;
                    font-family: var(--font-family-type5);
                    width: 100%;
                    height: 56px;
                    font-size: 16px;
                    color: #fff;
                    display: flex;
                    flex-direction: row;
                    justify-content: center;
                    align-items: center;
                    background: linear-gradient(90deg, #ff3561 0%, #ff06ca 100%);
                    border-radius: 16px;
                }
            }
        }
    }
}
</style>