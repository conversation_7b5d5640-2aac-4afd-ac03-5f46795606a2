<!-- app-container -->
<template>
    <div class="app-container">

        <!-- 顶部导航栏 -->
        <div class="app-container-header">
            <img src="@/assets/anchor/go-back.png" alt="" class="left-icon" @click="goBack">
            <img src="@/assets/anchor/more.png" alt="" class="right-icon" @click="showMore">
        </div>

        <!-- 轮播容器 -->
        <div class="anchor-detail">
            <swiper :slides-per-view="1" :space-between="0" :loop="false" :autoplay="{
                delay: 5000,
                disableOnInteraction: false,
            }" :modules="modules" @slide-change="onSlideChange" @autoplay-time-left="onAutoplayTimeLeft"
                class="story-swiper" ref="swiperRef">
                <swiper-slide v-for="(image, index) in swiperList" :key="index" class="story-slide">
                    <div class="image-container">
                        <img :src="image" :alt="`Story ${index + 1}`" class="story-image" />

                    </div>
                </swiper-slide>
                <!-- 底部缩略图导航 -->
                <div class="thumbnail-nav">
                    <div class="thumbnail-container">
                        <div v-for="(thumb, thumbIndex) in swiperList" :key="thumbIndex" class="thumbnail-item"
                            :class="{ active: thumbIndex === currentSlide }" @click="goToSlide(thumbIndex)">
                            <img :src="thumb" :alt="`Thumbnail ${thumbIndex + 1}`" />
                        </div>
                    </div>
                    <div class="slide-counter">{{ currentSlide + 1 }}/{{ swiperList.length }}</div>
                </div>
            </swiper>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from 'vue'
import img1 from '@/assets/test/1.jpg'
import img2 from '@/assets/test/2.jpg'
import img3 from '@/assets/test/3.jpg'
import img4 from '@/assets/test/4.jpg'
import img5 from '@/assets/test/5.jpg'
import img6 from '@/assets/test/6.jpg'
import img7 from '@/assets/test/7.jpg'
import { Swiper, SwiperSlide } from 'swiper/vue'
import { Autoplay } from 'swiper/modules'
import 'swiper/css'
import 'swiper/css/autoplay'

// 响应式数据
const swiperList = ref([img1, img2, img3, img4, img5, img6, img7])
const currentSlide = ref(0)
const isLiked = ref(false)
const swiperRef = ref<any>(null)
const progressWidth = ref(0)
const modules = [Autoplay]

// 方法
const goBack = () => {
    console.log('返回')
    // 这里可以添加路由跳转逻辑
}

const showMore = () => {
    console.log('更多选项')
    // 这里可以添加更多选项的逻辑
}

const onSlideChange = (swiper: any) => {
    currentSlide.value = swiper.activeIndex
    progressWidth.value = 0
}

const onAutoplayTimeLeft = (_swiper: any, _time: number, progress: number) => {
    progressWidth.value = (1 - progress) * 100
}

const getProgressWidth = (index: number) => {
    if (index < currentSlide.value) {
        return '100%'
    } else if (index === currentSlide.value) {
        return `${progressWidth.value}%`
    } else {
        return '0%'
    }
}

const goToSlide = (index: number) => {
    if (swiperRef.value && swiperRef.value.swiper) {
        swiperRef.value.swiper.slideTo(index)
    }
}

const toggleLike = () => {
    isLiked.value = !isLiked.value
    console.log('点赞状态:', isLiked.value)
}

onMounted(() => {
    // 组件挂载后的逻辑
})

onUnmounted(() => {
    // 组件卸载前的清理逻辑
})
</script>
<style lang="scss" scoped>
.app-container {
    position: relative;
    width: 100%;
    height: 100vh;
    background: #000;
    overflow-y: auto;

    // 顶部导航栏
    .app-container-header {
        position: absolute;
        width: 100%;
        height: 44px;
        left: 0;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 15px;
        box-sizing: border-box;
        z-index: 10;

        .left-icon,
        .right-icon {
            width: 24px;
            height: 24px;
            cursor: pointer;
            filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
            transition: transform 0.2s ease;

            &:hover {
                transform: scale(1.1);
            }
        }

        .right-icon {
            width: 30px;
            height: 30px;
        }
    }

    // 轮播容器
    .anchor-detail {
        width: 100%;
        height: 530px;

        .story-swiper {
            width: 100%;
            height: 100%;

            .story-slide {
                width: 100%;
                height: 100%;

                .image-container {
                    position: relative;
                    width: 100%;
                    height: 100%;

                    .story-image {
                        width: 100%;
                        height: 100%;
                        object-fit: cover;
                        object-position: center;
                        border-radius: 0 0;
                    }
                }
            }

            // 底部缩略图导航
            .thumbnail-nav {
                position: absolute;
                bottom: 10px;
                right: 15px;
                z-index: 5;
                display: flex;
                align-items: center;

                .thumbnail-container {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    padding: 8px 12px;
                    max-width: 188px;
                    border-radius: 20px;
                    overflow-x: auto;

                    .thumbnail-item {
                        flex-shrink: 0;
                        width: 40px;
                        height: 40px;
                        border-radius: 6px;
                        overflow: hidden;
                        cursor: pointer;
                        border: 2px solid transparent;
                        transition: all 0.3s ease;

                        &.active {
                            border: 1px solid #fff;
                        }

                        img {
                            width: 100%;
                            height: 100%;
                            object-fit: cover;
                        }
                    }
                }

                .slide-counter {
                    color: #fff;
                    font-size: 14px;
                    font-weight: 500;
                    margin-left: 10px;
                    font-family: var(--font-family-urbanist);
                }
            }
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .app-container {
        .thumbnail-nav .thumbnail-container {
            .thumbnail-item {
                width: 28px;
                height: 28px;
            }
        }

        .action-buttons {
            right: 10px;
            bottom: 80px;

            .action-btn {
                width: 44px;
                height: 44px;
            }
        }
    }
}
</style>