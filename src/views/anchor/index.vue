<!-- app-container -->
<template>
    <div class="app-container">
        <div class="app-container-header">
            <img src="@/assets/anchor/go-back.png" alt="" class="left-icon">
            <img src="@/assets/anchor/more.png" alt="" class="right-icon">
        </div>
        <div class="anchor-detail">
            <swiper :space-between="-200" :centered-slides="true" :loop="true" :autoplay="{
                delay: 3000,
                disableOnInteraction: false,
            }" :grab-cursor="false" :allowTouchMove="false" :watchSlidesProgress="true"
                class="match-swiper swiper-creative swiper-3d swipe-card">
                <swiper-slide v-for="(user, index) in users" :key="index" class="match-slide">
                    <div class="user-card">
                        <!-- 用户头像 -->
                        <div class="user-avatar">
                            <img :src="user.avatar" :alt="user.name" />
                            <!-- 在线状态指示器 -->
                            <div class="online-indicator" v-if="user.isOnline"></div>
                        </div>
                    </div>
                </swiper-slide>
            </swiper>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import img1 from '@/assets/test/1.jpg'
import img2 from '@/assets/test/2.jpg'
import img3 from '@/assets/test/3.jpg'
import img4 from '@/assets/test/4.jpg'
import img5 from '@/assets/test/5.jpg'
import img6 from '@/assets/test/6.jpg'
import img7 from '@/assets/test/7.jpg'
import { Swiper, SwiperSlide } from 'swiper/vue'
import { Autoplay } from 'swiper/modules'
import 'swiper/css'
import 'swiper/css/effect-creative'

const swiperList = ref([img1, img2, img3, img4, img5, img6, img7])
</script>
<style lang="scss" scoped>
.app-container {
    overflow-y: auto;

    .app-container-header {
        position: absolute;
        width: 100%;
        height: 44px;
        top: 0;
        left: 0;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 15px;
        box-sizing: border-box;

        .left-icon {
            width: 24px;
            height: 24px;
        }

        .right-icon {
            width: 30px;
            height: 30px;
        }
    }
}
</style>