<!-- 钱包 -->
<template>
    <div class="app-container recharge-container">
        <div class="recharge-header">
            <div class="recharge-header-left" @click="goBack">
               <img src="@/assets/common/back.png" alt="" class="recharge-header-left-icon">
            </div>
            <div class="recharge-header-right">
                <span class="recharge-header-right-text">My balance</span>
                <img src="@/assets/common/coins.png" alt="" class="recharge-header-right-img">
                <span class="recharge-header-right-num">{{ 0 }}</span>
            </div>
        </div>
        <!--svip-->
        <div class="recharge-svip">
            <div class="recharge-svip-top">
                <span class="recharge-svip-top-text">Svip Package</span>
                <span class="recharge-svip-top-more">
                    More
                    <img src="@/assets/common/more.png" alt="" class="recharge-svip-top-more-img">
                </span>
            </div>
            <!--svip-coins-->
            <div class="recharge-svip-coins">
                <div class="recharge-svip-coins-left">
                    <div class="recharge-svip-coins-left-top">
                        170 coins & Free msgs
                    </div>
                    <div class="recharge-svip-coins-left-bottom">
                        Claim free coins
                    </div>
                </div>
                <div class="recharge-svip-coins-right">
                    <div class="recharge-svip-coins-right-button">
                        $19.99/Mo
                    </div>
                </div>
            </div>

        </div>
        <!---占位图片-->
        <img src="@/assets/recharge/purchase-bg.png" alt="" class="recharge-placeholder">
        <!--下面的充值List-->
        <div class="recharge-svip-list">
            <div class="recharge-svip-list-item" v-for="item in rechargeList" :key="item.id"
                :class="{ 'recharge-svip-list-item-special': item.isSpecial }">
                <div class="recharge-svip-list-item-top">
                    <img src="@/assets/common/coins.png" alt="" class="recharge-svip-list-item-top-img">
                    <span class="recharge-svip-list-item-top-text">{{ item.coins }}</span>
                </div>
                <div class="recharge-svip-list-item-bottom">
                    {{ item.price }}
                </div>
                <div class="recharge-svip-list-item-special-offer" v-if="item.isSpecial">
                    <div class="recharge-svip-list-item-special-offer-left">
                        <span class="recharge-svip-list-item-special-offer-left-text">{{ item.speicalOffer ? item.speicalOffer : 'Special offer' }}</span>
                    </div>
                    <div class="recharge-svip-list-item-special-offer-right">
                        <span class="recharge-svip-list-item-special-offer-right-text">20:20</span>
                    </div>
                </div>
            </div>
        </div>
        <!--connect-us-->
        <div class="connect-us">
            <img src="@/assets/common/connect-us.png" alt="" class="connect-us-img">
            <span class="connect-us-text">If you encounter payment issues, please </span>
            <span class="connect-us-text-link">contact us.</span>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
const router = useRouter()
const rechargeList = ref([
    {
        id: 1,
        coins: '170',
        price: '$19',
        isSpecial: true,
        speicalOffer: '+100 coins'
    },
    {
        id: 2,
        coins: '170',
        price: '$19',
        isSpecial: true,
        speicalOffer: ''
    },
    {
        id: 3,
        coins: '170',
        price: '$19',
        isSpecial: false,
    },
    {
        id: 4,
        coins: '170',
        price: '$19',
        isSpecial: false,
    },
    {
        id: 5,
        coins: '170',
        price: '$19',
        isSpecial: false,
    },
])
const goBack = () => {
    router.back()
}
</script>
<style lang="scss" scoped>
.recharge-container {
    width: 100%;
    height: 100%;
    overflow-y: auto;
    padding-top: var(--safe-area-top);
    padding-bottom: var(--safe-area-bottom);
    padding: 0 16px;
    box-sizing: border-box;
    font-family: var(--font-family-urbanist);
    .recharge-header {
        width: 100%;
        padding: 11px 0;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .recharge-header-left {
            font-size: 18px;
            font-weight: 600;
            line-height: 21px;
            color: #fff;
            &-icon{
                width: 24px;
                height: 24px;
            }
        }

        .recharge-header-right {
            display: flex;
            align-items: center;

            &-text {
                font-size: 14px;
                font-weight: 500;
                color: rgba($color: #fff, $alpha: .5);
                margin-right: 6px;

            }

            &-img {
                width: 16px;
                height: 16px;
                margin-right: 4px;
            }

            &-num {
                font-size: 18px;
                font-weight: 600;
                color: #fff;
            }
        }
    }

    .recharge-svip {
        margin-top: 3px;
        background: #282625;
        border-radius: 10px;

        .recharge-svip-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 9px 15px 0 31px;

            &-text {
                font-size: 16px;
                font-weight: 900;
                color: #fff;
                line-height: 19px;
                font-style: italic;
            }

            &-more {
                display: flex;
                align-items: center;
                font-size: 12px;
                font-weight: 600;
                color: rgba($color: #fff, $alpha: .5);
                line-height: 14px;

                &-img {
                    width: 18px;
                    height: 18px;
                    margin-left: 2px;
                }
            }
        }

        .recharge-svip-coins {
            margin-top: 8px;
            padding: 12px 15px 9px 12px;
            background: linear-gradient(90deg, #FEEECE 0%, #FFFFED 24.04%, #FBF1D6 43.75%, #FFFFE3 62.02%, #F8D9A9 100%);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: space-between;

            &-left {
                &-top {
                    font-size: 18px;
                    font-weight: 700;
                    color: #000;
                    line-height: 22px;
                }

                &-bottom {
                    margin-top: 3px;
                    font-size: 12px;
                    font-weight: 600;
                    color: #000;
                    line-height: 14px;
                    font-style: italic;
                }
            }

            &-right {
                &-button {
                    padding: 7px 10px;
                    background-color: #1A170E;
                    border-radius: 16px;
                    color: #F9DE9B;
                    font-size: 14px;
                    font-weight: 500;
                    line-height: 17px;
                }
            }
        }
    }
    .recharge-placeholder{
        margin-top: 20px;
        width: 100%;
        border-radius: 10px;
    }
    .recharge-svip-list {
        margin-top: 22px;
        width: 100%;
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 5px;

        &-item {
            width: 100%;
            height: 110px;
            background-color: #1E1D38;
            border-radius: 10px;
            border: 1px solid #1E1D38;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            position: relative;

            &-top {
                flex: 1;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;

                &-img {
                    width: 16px;
                    height: 16px;
                    margin-bottom: 4px;
                }

                &-text {
                    color: #fff;
                    font-size: 16px;
                    font-weight: 700;
                    line-height: 19px;
                }
            }

            &-bottom {
                width: 100%;
                height: 24px;
                text-align: center;
                line-height: 24px;
                border-radius: 0 0 10px 10px;
                background-color: rgba($color: #fff, $alpha: .1);
                color: #fff;
                font-size: 14px;
                font-weight: 500;
            }
        }

        &-item-special {
            border-color: #FE3555;
            .recharge-svip-list-item-special-offer{
                position: absolute;
                top: 0;
                left: -1px;
                width: 100%;
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding-right: 4px;
                &-left{
                    border-radius: 10px 10px 10px 0;
                    background-color: #FE3555;
                    padding: 2px 5px;
                    &-text{
                        display: block;
                        font-size: 10px;
                        line-height: 12px;
                        font-weight: 700;
                        color: #fff;
                    }
                }
                &-right{
                    &-text{
                        display: block;
                        color: #FE3555;
                        font-size: 10px;
                        line-height: 12px;
                        font-weight: 700;
                    }
                }
            }
        }
    }
    .connect-us{
        position: fixed;
        bottom: env(safe-area-inset-bottom);
        left: 0;
        right: 0;
        display: flex;
        align-items: center;
        background-color: #1B1B34;
        padding:3px 0px 3px 15px;
        font-size: 12px;
        font-weight: 600;
        color: rgba($color: #fff, $alpha: .5);
        line-height: 14px;
        &-img{
            width: 24px;
            height: 24px;
            margin-right: 5px;
        }
        &-text-link{
            margin-left: 4px;
            color: #fff;
        }
    }
}
</style>