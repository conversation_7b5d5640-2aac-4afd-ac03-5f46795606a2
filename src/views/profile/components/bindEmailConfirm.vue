<!-- 选择性别弹窗 -->
<template>
    <div class="bind-email-confirm">
        <var-popup position="center" v-model:show="selectAgeDialogShow"  :safe-area="true"
            :default-style="false" :close-on-click-overlay="false">
            <div class="bind-email-confirm-dialog">
                <div class="bind-email-confirm-dialog-content">
                    <div class="bind-email-confirm-dialog-content-title">
                        Please confirm your email to
                        receive the valid verification
                        code
                    </div>
                    <div class="bind-email-confirm-dialog-content-button">
                        <div class="bind-email-confirm-dialog-content-button-item" @click="close">
                            <span>Edit Email</span>
                        </div>
                        <div class="bind-email-confirm-dialog-content-button-item confirm-button" @click="confirm">
                            <span>Confirm</span>
                        </div>
                    </div>
                </div>
            </div>
        </var-popup>
    </div>
</template>

<script lang="ts">

export default {
    name: 'bindEmailConfirm',
    props: {
        show: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            columns: [
                Array.from({ length: 82 }, (_, i) => ({
                    text: i + 18,
                    value: i + 18
                }))
            ],
            selectedAge: 18,
            selectAgeDialogShow: this.show
        };
    },
    watch: {
        show: {
            handler(newVal) {
                this.selectAgeDialogShow = newVal
            },
            immediate: true
        }
    },
    mounted() {

    },
    methods: {
        close() {
            this.selectAgeDialogShow = false
            this.$emit('close')
            this.$emit('update:show', false)
        },
        confirm() {

            this.$emit('confirm')
            this.close()
        }
    }
}
</script>
<style lang='scss' scoped>
.bind-email-confirm-dialog {
    display: flex;
    justify-content: center;
    font-family: var(--var-font-family-type3);

    .bind-email-confirm-dialog-content {
        height: 100%;
        width: 80%;
        padding: 40px 30px;
        border-radius: 16px;
        background: rgb(24, 25, 47);

        &-title {
            padding: 0 20px;
            box-sizing: border-box;
            font-size: 14px;
            font-weight: 500;
            line-height: 17px;
            color: rgba($color: #fff, $alpha: .7);
            text-align: center;
        }

        &-button {
            margin-top: 30px;
            display: flex;
            justify-content: space-between;
            gap:10px;
            .bind-email-confirm-dialog-content-button-item {
                flex: 1;
                height: 44px;
                display: flex;
                justify-content: center;
                align-items: center;
                border-radius: 6px;
                background: rgba($color: #fff, $alpha: .05);
                border-radius: 8px;
                color: #fff;
                font-size: 16px;
                font-weight: 700; 
                &.confirm-button{
                   background: linear-gradient(90deg, #FF812B 0%, #FF0BC2 100%);
                }
            }
        }
    }
}
</style>