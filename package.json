{"name": "vite-project", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@varlet/ui": "^3.11.0", "@vueuse/core": "^13.1.0", "pinia": "^3.0.2", "swiper": "^11.2.10", "vue": "^3.5.13", "vue-i18n": "^11.1.3", "vue-router": "^4.5.0"}, "devDependencies": {"@types/node": "^22.13.15", "@vitejs/plugin-legacy": "^6.1.0", "@vitejs/plugin-vue": "^5.2.1", "@vue/tsconfig": "^0.7.0", "autoprefixer": "10.4.17", "postcss": "8.4.31", "postcss-pxtorem": "^6.1.0", "sass": "^1.86.3", "typescript": "~5.7.2", "vite": "^6.2.0", "vue-tsc": "^2.2.4"}}